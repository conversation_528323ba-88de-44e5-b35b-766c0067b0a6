<template>
	<view class="content">
		<view class="header">
			<image src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png"></image>
		</view>

		<view class="list">
			<view class="list-call">
				<image class="img" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-phone.png"></image>
				<input class="sl-input" v-model="phone" type="number" maxlength="11" placeholder="手机号" />
			</view>
			<view class="list-call">
				<image class="img" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yzm.png"></image>
				<input class="sl-input" v-model="code" type="number" maxlength="10" placeholder="验证码" />
				<view class="yzm" :class="{ yzms: second>0 }" @tap="getcode">{{yanzhengma}}</view>
			</view>
			<text class="cf262462 lh30">首次授权登录需绑定您的手机号</text>

			<!-- <view class="list-call">
        <image class="img" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/4.png"></image>
        <input class="sl-input" v-model="invitation" type="text" maxlength="12" placeholder="邀请码" />
      </view> -->

		</view>

		<view class="button-login btnStyle" hover-class="button-hover" @tap="bindLogin">
			<text>绑定手机号</text>
		</view>

		<view class="agreement cf262462">
			<image @tap="agreementSuccess"
				:src="agreement==true?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-select.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-noselect.png'"></image>
				<text style="text-indent: 16rpx;" @tap="agreementSuccess"> 同意</text>
			<navigator
				@click="goview('https://agent.xiaoyujia.com/%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.html')">
				《服务协议》</navigator>
			<navigator
				@click="goview('https://agent.xiaoyujia.com/%E5%B9%B3%E5%8F%B0%E9%9A%90%E7%A7%81%E5%8D%8F%E8%AE%AE.html')">
				《隐私协议》</navigator>
		</view>
	</view>
</template>

<script>
	var _this, js;
	export default {
		onLoad() {
			_this = this;

		},
		onUnload() {
			clearInterval(js)
			this.second = 0;
		},
		data() {
			return {
				phone: '',
				password: '',
				code: '',
				// invitation: '',
				agreement: false,
				showPassword: false,
				second: 0
			};
		},
		computed: {
			yanzhengma() {
				if (this.second == 0) {
					return '获取验证码';
				} else {
					if (this.second < 10) {
						return '重新获取0' + this.second;
					} else {
						return '重新获取' + this.second;
					}
				}
			}
		},
		onUnload() {
			this.clear()
		},
		methods: {
			goview(ur) {
				let param = {
					url: ur
				}
				let data = JSON.stringify(param);
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
			clear() {
				clearInterval(js)
				js = null
				this.second = 0
			},
			display() {
				this.showPassword = !this.showPassword
			},
			agreementSuccess() {
				this.agreement = !this.agreement;
			},
			getcode() {
				if (this.phone.length != 11) {
					uni.showToast({
						icon: 'none',
						title: '手机号不正确'
					});
					return;
				}
				if (this.second > 0) {
					return;
				}
				this.second = 60;
				// //请求业务
				// js = setInterval(function() {
				//   _this.second--;
				//   if (_this.second == 0) {
				//     _this.clear()
				//   }
				// }, 1000)
				uni.request({
					url: 'https://api.xiaoyujia.com/member/sendLoginCode', //仅为示例，并非真实接口地址。
					data: {
						phone: this.phone,
					},
					method: 'POST',
					dataType: 'json',
					success: (res) => {
						if (res.data.code != 0) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: "短信发送成功"
							});
							js = setInterval(function() {
								_this.second--;
								if (_this.second == 0) {
									_this.clear()
								}
							}, 1000)
						}
					},
					fail() {
						this.second == 0
					}
				});
			},
			bindLogin() {
				if (this.agreement == false) {
					uni.showToast({
						icon: 'none',
						title: '请先阅读《软件用户协议》'
					});
					return;
				}
				if (this.phone.length != 11) {
					uni.showToast({
						icon: 'none',
						title: '手机号不正确'
					});
					return;
				}
				let openId = uni.getStorageSync("openId");
				let unionId = uni.getStorageSync("unionId");
				let headimgurl = uni.getStorageSync("headimgurl");
				let name = uni.getStorageSync("nickname");
				let param = {
					account: this.phone,
					textCode: this.code,
				}
				if (openId && unionId) {
					param.openId = openId;
					param.unionId = unionId;
					param.headImg = headimgurl;
					param.name = name;
					param.regResource = 'lianmemgACN';
				}
				console.log(param)
				uni.request({
					url: 'https://api.xiaoyujia.com/member/memberRegByOpenWeiXin',
					data: param,
					method: 'POST',
					dataType: 'json',
					success: (res) => {
						if (res.data.code != 0) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						} else {
							//成功后的逻辑
							uni.setStorageSync("account", res.data.data.account)
							uni.setStorageSync("memberId", res.data.data.id)
							uni.setStorageSync("memberName", res.data.data.name)
							uni.setStorageSync("birthDate", res.data.data.birthDate)
							uni.setStorageSync("memberSex", res.data.data.sex)
							let headImg = res.data.data.headImg;
							if (headImg == null) {
								headImg =
									'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png';
							}
							uni.setStorageSync("memberHeadImg", headImg)
							uni.reLaunch({
								url: "/pages/index/pageMain"
							});
						}
					}
				});

			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.header {
		width: 161rpx;
		height: 161rpx;
		border-radius: 50%;
		margin-top: 30rpx;
		margin-left: auto;
		margin-right: auto;
	}

	.header image {
		width: 161rpx;
		height: 161rpx;
		border-radius: 50%;
	}

	.list {
		display: flex;
		flex-direction: column;
		padding-top: 50rpx;
		padding-left: 70rpx;
		padding-right: 70rpx;
	}

	.list-call {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		color: #333333;
		border-bottom: 0.5px solid #e2e2e2;
	}

	.list-call .img {
		width: 40rpx;
		height: 40rpx;
	}

	.list-call .sl-input {
		flex: 1;
		text-align: left;
		font-size: 32rpx;
		margin-left: 16rpx;
	}

	.yzm {
		font-size: 24rpx;
		line-height: 64rpx;
		padding-left: 10rpx;
		padding-right: 10rpx;
		width: auto;
		height: 64rpx;
		border-radius: 50rpx;
	}

	.yzms {
		color: #999999 !important;
		border: 1rpx solid #999999;
	}

	.button-login {
		font-size: 34rpx;
		width: 470rpx;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 50rpx;
		text-align: center;
		margin-left: auto;
		margin-right: auto;
		margin-top: 100rpx;
	}

	.agreement {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 80rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.agreement image {
		width: 40rpx;
		height: 40rpx;
	}
</style>
