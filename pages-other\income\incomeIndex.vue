<template>
	<view class="">
		<view class="cardStyle w95 mg-at f16 cf" v-if="showFlag">
			<view class="w85 mg-at flac-row-b" style="margin-bottom: 20rpx;">
				<view class="text-l f18" @click="pickerShow = true">{{storeName || '未知'}}</view>
				<view v-if="storeData.days">{{storeData.days}}天</view>
				<u-icon name="setting" size="25" color="#fff" @click="goto('./setPage')"></u-icon>
			</view>
			<u-popup :show="pickerShow" @close="pickerShow=false">
				<uni-search-bar class="w85 mg-at" placeholder="门店搜索" bgColor="#f6f8fa" :radius="100" v-model="keyword"
					cancelButton="none" @search="searchStore" @confirm="searchStore" @input="searchStore">
				</uni-search-bar>
				<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
					<u-radio-group iconPlacement="left" v-model="storeId" placement="column" borderBottom>
						<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, i) in selectColumns" :key="i"
							:label="item.label" :name="item.key" @change="changePicker(item)" />
					</u-radio-group>
				</scroll-view>
			</u-popup>
			<view class="w85 mg-at flac-row-b">
				<view class="">
					<view class="lh50">余额（元）</view>
					<u-loading-icon v-if="amountMoney==null" text="计算中" textSize="15"></u-loading-icon>
					<view v-if="amountMoney!=null" class="f20 fb">{{amountMoney || '0.00'}}</view>
				</view>
				<view class="">
					<view class="lh50">累计金额（元）</view>
					<u-loading-icon v-if="!countMoney" text="计算中" textSize="15"></u-loading-icon>
					<view class="f20 fb" v-if="countMoney">{{(countMoney?countMoney:0.00) || '0.00'}}</view>
				</view>
			</view>
			<view class="w85 mg-at flac-row-b" style="margin-top: 40rpx;">
				<view class="w6 text-l f14" @click="goto('./balanceDetail')">查看余额明细变动</view>
				<view class="btnStyle text-c" v-if="zhuanChuFlag" style="background-color: transparent;"
					@click="goto('./rollout')">立即转出</view>
			</view>
		</view>
		<view class="bgStyle w100 mg-at bacf flac-row" style="padding: 20rpx 40rpx;flex-wrap: wrap;">
			<view class="lh30 flex-col-c" style="width: 25%;margin: 20rpx 0;" v-for="(item,i) in navList" :key="i"
				@click="goto(item.url)" v-if="item.show">
				<u-icon size="25" :name="item.icon"></u-icon>
				<view class="">{{item.name || ''}}</view>
			</view>
		</view>
		<view class="bgStyle w95 mg-at bacf f16">
			<view class="w9 mg-at flac-row-b">
				<view class="">收支明细<text class="f14 c9">(七天)</text></view>
				<view class="" @click="goto('./incomeAll')">查看全部</view>
			</view>
			<view class="flac-row-b" v-for="(item,i) in incomeList" :key="i" style="margin: 20rpx auto;"
				v-if="incomeList.length">
				<view class="lh30" style="margin-left: 40rpx;">
					订单收入—{{item.productName || '未知'}}
					<view class="c9">{{item.paySettlementTime || ''}}</view>
				</view>
				<view class="flac-row-a" @click="gotoDetail(item.billNo)">
					<view class="">
						{{ '+' + (item.amount || '')}}
						<view class="text-r" style="color: #187bd8;">成功</view>
					</view>
					<u-icon name="arrow-right" size="20" customStyle="margin: auto 20rpx"></u-icon>
				</view>
			</view>
			<view class="w9 mg-at">
				<uni-pagination :total="total" :current="current" @change="changePage" />
				<view class="example-info lh35 text-c">当前页：{{ current || 1 }}，数据总量：{{ total || 0 }}条</view>
			</view>
		</view>
		<view>
			<u-popup mode="center" :show="popupShow" @close="popupShow=true" @open="open">
				<view class="bacf" style="width: 600rpx;height: 340rpx;">
					<uni-table class="tableStyle" style="margin: 0;">
						<uni-tr>
							<uni-td align="center">
								检测到未填写公司名称！
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<u-input v-model="storeData.companyName" placeholder="请输入公司名称如:xxx有限公司"></u-input>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<u-button text="确认" @click="updateStore()" color="#1e1848">
								</u-button>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roleId: uni.getStorageSync("roleId"),
				countMoney: 0.00,
				amountMoney: null,
				keyword: '',
				popupShow: false,
				selectColumns: [],
				selectColumnsCath: [],
				pickerShow: false,
				showFlag: true,
				zhuanChuFlag: true,
				storeData: {},
				total: 0,
				storeId: '',
				storeName: uni.getStorageSync("selectStoreName"),
				current: 1,
				navList: [{
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jyfw.png',
					name: '经营范围',
					url: './manage/mapIndex',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jycm.png',
					name: '经营参谋',
					url: './manage/index',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1688540394759保证金.png',
					name: '保证金',
					url: './margin',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yhk.png',
					name: '门店信息',
					url: './cardEdit',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1699234930980资质证书.png',
					name: '授权证书',
					url: './storeCertificate',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-dbsx.png',
					name: '待办事项',
					url: './manage/toDoList',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jyfx.png',
					name: '经营分析',
					url: './manage/analysis',
					show: true
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1742199701247电子发票.png',
					name: '开具发票',
					url: './issueAnInvoice',
					show: true
				}],
				incomeList: []
			};
		},
		onLoad(option) {
			this.getStoreListData()
		},
		methods: {
			updateStore() {
				this.http({
					url: "updateStoreData",
					method: 'POST',
					data: this.storeData,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.popupShow = false
						if (res.code == 0) {
							uni.showToast({
								title: '保存成功!',
								icon: 'none'
							})
							setTimeout(() => {
								this.getStoreData()
							}, 1000)
						} else {
							uni.showToast({
								title: '保存失败!',
								icon: 'none'
							})
						}
					}
				})
			},
			searchStore() {
				if (!this.keyword) {
					this.selectColumns = this.selectColumnsCath
				} else {
					let storeArr = []
					this.selectColumns.forEach(item => {
						if (item.label.includes(this.keyword)) {
							storeArr.push(item)
						}
					})

					this.selectColumns = storeArr
				}
			},
			getStoreData() {
				this.http({
					url: "getStoreData",
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.storeData = res.data
							uni.setStorageSync('storeType', res.data.storeType)
							if (uni.getStorageSync('roleId') !== 1 && res.data.storeType !== 2 && res.data.storeType !== 5) {
								if (res.data.storeType !== 3) {
									this.showFlag = false
								} else {
									this.navList[1].show = false
									this.showFlag = true
									this.zhuanChuFlag = false
								}
								this.navList[2].show = false
								this.navList[3].show = false
								this.navList[4].show = false
							}
							uni.setStorageSync('lat', this.storeData.lat)
							uni.setStorageSync('lng', this.storeData.lng)
							uni.setStorageSync('radius', this.storeData.radius)
						}
					}
				})
			},
			changePicker(e) {
				uni.setStorageSync("selectStoreName", e.label)
				uni.setStorageSync("selectStoreId", e.key)
				this.storeName = e.label
				this.storeId = e.key
				this.pickerShow = false
				this.getIncomeData()
				this.countStoreMoney()
				this.getStoreData()
			},
			changePage(e) {
				this.current = e.current
				this.getIncomeData()
			},
			getNewStoreListData() {
				this.http({
					url: "getStoreListData",
					data: {
						employeeId: uni.getStorageSync("employeeId"),
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.selectColumns = res.data
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			getStoreListData() {
				this.http({
					url: "getStoreListData",
					data: {
						employeeId: uni.getStorageSync("employeeId"),
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.selectColumns = res.data
							this.selectColumnsCath = res.data
							let index = 0
							if (uni.getStorageSync("roleId") == 1) {
								for (var i = 0; i < res.data.length; i++) {
									if (res.data[i].key == 1058) {
										index = i
										break;
									}
								}
							}
							this.storeName = res.data[index].label
							this.storeId = res.data[index].key
							uni.setStorageSync("selectStoreName", res.data[index].label)
							uni.setStorageSync("selectStoreId", res.data[index].key)
							this.getIncomeData()
							this.countStoreMoney()
							this.getStoreData()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages-work/index'
								})
							}, 1000)
						}
					}
				})
			},

			countStoreMoney() {
				this.http({
					// outsideUrl: "http://localhost:15012/countStoreMoney",
					url: "countStoreMoney",
					data: {
						storeId: this.storeId,
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.amountMoney = res.data.respMoney
							this.countMoney = res.data.count
						}
					}
				})
			},
			getIncomeData() {
				this.http({
					// outsideUrl: "http://localhost:15012/getIncomeData",
					url: "getIncomeData",
					data: {
						employeeId: uni.getStorageSync("employeeId"),
						storeId: this.storeId,
						size: 10,
						current: this.current,
						getType: 1,
						stateTime: '',
						endTime: ''
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.incomeList = res.data.incomeList.records
							this.total = res.data.total
						}
					}
				})
			},
			goto(url) {
				// 获取订阅消息授权
				// #ifdef  MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: [
						"lun1tYM3bRBqPfaFlwov7ctVQZqwFSjfzchKU8feJI4",
					],
					success: res => {
						console.log("用户同意进行小程序消息订阅！")
					},
					fail: res => {}
				})
				// #endif

				uni.setStorageSync('navCurrentCath', 0)
				if (url === './cardEdit' || url === './rollout') {
					if (!this.storeData.bankCode || !this.storeData.bankType || !this.storeData.accountName) {
						uni.showModal({
							title: '提示',
							content: '检测到银行卡等信息不完整，请填写完整后后操作!',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: './cardBinding'
									})
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}.bind(this)
						});
					} else {
						uni.navigateTo({
							url: url
						})
					}
				} else if (url === './storeCertificate') {
					if (!this.storeData.companyName) {
						return this.popupShow = true
					}
					uni.navigateTo({
						url: './storeCertificate?id=' + this.storeData.id
					})
				} else {
					uni.navigateTo({
						url: url
					})
				}
			},
			gotoDetail(val) {
				uni.navigateTo({
					url: './incomeDetail?billNo=' + val + '&type=1'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.cardStyle {
		padding: 40rpx 0;
		background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
		border-radius: 20rpx;

		.btnStyle {
			border: 2rpx solid #eee;
			border-radius: 40rpx;
			padding: 10rpx 40rpx;
		}
	}

	.bgStyle {
		border-radius: 20rpx;
		margin: 20rpx auto;
		padding: 30rpx 0
	}
</style>
<style lang="scss" scoped>
	/deep/ .u-icon {
		justify-content: center;
	}
</style>
<style>
	page {
		background-color: #f5f5f5;
	}
</style>