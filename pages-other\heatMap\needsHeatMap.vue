<template>
	<view>
		<!-- longitude（类型为Number，没有默认值，表示中心经度）
      latitude（类型为Number，没有默认值，表示中心纬度）
      scale（类型为Number，默认值为16，缩放级别取值范围为5-18）
      markers（类型为Array数组，类型为数组即表示地图上可以有多个，没有默认值，表示标记点）
      show-location（类型为Boolean，表示显示带有方向的当前定位点）
      polygon polygon指定一系列坐标点，根据points坐标数据生成闭合多边形
      @markertap-表示点击标记点时触发，e.detail={markerId}
      @labeltap-表示点击label时触发，e.detail = {markerId}
      @callouttap-表示点击标记点对应的气泡时触发，e.detail = {markerId} -->
		<view class="map-container">
			<map style="width: 100%; height: 93.4vh;" :show-location='true' ref="map" id="map" :latitude="latitude"
				:longitude="longitude" :markers="markers" :circles="circles" :scale="scale" @callouttap='callouttap'>
				<cover-view class="cover-view">
					<!-- <cover-view @click="goPage('./selectCity')">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/location.png" />
						<cover-view>{{dataInfo.cityName}}</cover-view>
					</cover-view> -->
					<cover-view @click="showLabel">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1754894548860标签 (1).png" />
						<cover-view>标签</cover-view>
					</cover-view>
					<cover-view @click="showRegion">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1754894587420区域筛选.png" />
						<cover-view>区域</cover-view>
					</cover-view>
					<cover-view @click="refresh">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/refresh.png" />
						<cover-view>刷新</cover-view>
					</cover-view>
					<cover-view @click="showModal = true">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/search.png" />
						<cover-view>时间</cover-view>
					</cover-view>
				</cover-view>
				<cover-view class="w10 mg-at bacf popupStyle" v-show="showPopup">
					<cover-view class="w9 mg-at flac-row-b">
						<cover-view class="f18 fb lh50">{{title}}</cover-view>
						<cover-image class="closeImg"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/close.png"
							@click="showPopup = false" />
					</cover-view>
					<cover-view class="w9 mg-at flac-row">
						<cover-view class="f16 fb lh35">客户名称：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{name}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row">
						<cover-view class="f16 fb lh35">服务项目：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{productName}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row">
						<cover-view class="f16 fb lh35">派发方式：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{1}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">经纪人：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{agent}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">归属门店：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{storeName}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">创建时间：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{startTime}}</cover-view>
					</cover-view>
				</cover-view>
			</map>
		</view>
		<u-popup :show="showModal" mode="top" @close="showModal=false" @open="open">
			<uni-datetime-picker style="width: 100%;"  :end="nowTime" v-model="range" type="daterange" rangeSeparator="至"
			                     @change='changeSelect()' />
			<view class="btn-big">
					<button @click="getNeedsData(),showModal=false" style="margin-top: 50rpx;">点击查询</button>
			</view>
		</u-popup>
	</view>
</template>

<script>
	const regex = /\((.+?)\)/g; // 小括号
	export default {
		data() {
			return {
				// 可设置
				// 选址保护距离（单位：米）
				circularRange: 1000,
				showModal: false,
				latitude: '24.489451', //纬度
				longitude: '118.101811', //经度
				scale: 4, //缩放级别
				dataList: [],
				showPopup: false,
				nowTime: Number(new Date()),
				range: ["", ""],
				needsList: [],
				title: '',
				labelShow: 0,
				regionShow: 0,
				name: '',
				startTime: '',
				storeName: '',
				agent: '',
				productName: '',
				markers: [],
				dataInfo: {
					cityName: '全国',
					cityId: 0,
					areaId: 0,
					cityNum: uni.getStorageSync('cityNum')
				},
				circles: [],
				url: ''
			}
		},
		onLoad(e) {
			//初始值 默认为最新7天的数据
			let startTime = new Date(); let endTime = new Date();
			startTime.setDate(startTime.getDate() - 6);
			endTime.setDate(endTime.getDate()+1);
			this.range[0] = this.$u.timeFormat(startTime.getTime(), 'yyyy-mm-dd');
			this.range[1] = this.$u.timeFormat(endTime.getTime(), 'yyyy-mm-dd');
			this.getstoreScope()
			this.getNeedsData()

		},
		// watch: {
		//   range(newval) {
		// 	this.getIncomeData()
		//   },
		// },
		methods: {
			showLabel(){
				if(this.labelShow==1){
					this.labelShow = 0
					this.markers = []
					this.getstoreScope()
					this.getNeedsData()
				}else{
					this.labelShow = 1
					this.markerNeeds()
				}
			},
			showRegion(){
				if(this.regionShow==1){
					this.regionShow = 0
					// this.markers = []
				}else{
					this.regionShow = 1
				}
				this.getstoreScope()
			},
			getNeedsData() {
				this.markers = []
				this.http({
					url: 'getHeatMapNeedsList',
					data: {
						stateTime: this.range[0],
						endTime: this.range[1]
					},
					method: 'GET',
					success: (res) => {
						let arr = res.data
						let newArry = []
						newArry = arr.map((item, index) => {
							return Object.assign({}, {
								'id': item.id,
								'lng': item.lng,
								'lat': item.lat,
								'storeName': item.storeName, 
								'startTime': item.startTime, 
								'name': item.name, 
								"employeeName": item.employeeName, 
								'employeeNo': item.employeeNo, 
								'productName': item.productName, 
							})
						})
						this.needsList = newArry
						this.markerNeeds()
					}
				});
			},
			//气泡点击事件
			callouttap(e) {
				console.log('callouttap', e)
				let index = parseInt(e.detail.markerId)
				this.title = this.needsList[index].id
				let obj = this.needsList[index]
				this.productName = obj.productName
				this.agent = obj.employeeName+'('+obj.employeeNo+')'
				this.storeName = obj.storeName
				this.startTime = obj.startTime
				this.name = obj.name
				this.showPopup = true
				console.log('你点击了气泡标签', index)
			},
			markerNeeds() { //标识
				let marker = []
				for (var i = 0; i < this.needsList.length; i++) {
					marker = marker.concat({
						id: i,
						longitude: this.needsList[i].lng,
						latitude: this.needsList[i].lat,
						iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1749194051501点.png', //显示的图标
						rotate: 0, // 旋转度数
						width: 35, //宽
						height: 30, //高
						callout: { //自定义标记点上方的气泡窗口 点击有效  
							content: this.labelShow==1?this.needsList[i].id+'':'', //文本
							color: '#ffffff', //文字颜色
							fontSize: 8, //文本大小
							borderRadius: 1, //边框圆角
							padding: '6', //文本边缘留白
							bgColor: '#406390', //背景颜色
							display: 'ALWAYS', //‘BYCLICK’:点击显示; ‘ALWAYS’:常显
						},
					})
				}
				this.markers = this.markers.concat(marker)
			},
			getCityInfo() {
				uni.request({
					url: 'https://ortherapi.xiaoyujia.com/store/storecity',
					method: 'GET',
					success: (res) => {
						console.log(res)
						uni.setStorageSync('cityNum', res.data.length)
						uni.setStorageSync('citylist', res.data)
					}
				});
			},
			getstoreScope() {
				this.http({
					url: 'getAllOpenStore',
					method: 'GET',
					data: {},
					success: (res) => {
						if (res.code == 0) {
							this.dataList = res.data
							// this.dataList = res.data.list
							this.latitude = this.dataList[0].lat
							this.longitude = this.dataList[0].lng
							this.storeName = this.dataList[0].storeName
							this.storeName = this.storeName;
							if (this.dataInfo.cityName != '全国') {
								if (this.dataInfo.areaId == 0) {
									this.dataInfo.cityName = this.dataList[0].city
								} else {
									this.dataInfo.cityName = this.dataList[0].city + this.dataList[0].area
								}
								this.scale = 12
							}
							this.dataInfo.storeNum = this.dataList.length
						} else {
							uni.showToast({
								icon: 'none',
								title: '该地区暂无门店'
							})
							// this.dataInfo = {
							// 	cityName: '全国',
							// 	cityId: 0,
							// 	areaId: 0
							// }
							// this.getstoreScope()
						}
						this.marker()
					},
					fail: (err) => {
						uni.showToast({
							icon: 'none',
							title: res.data.msg
						})
					}
				});
			},
			marker(i) { //标识
				let marker = [],
					circle = []
				for (var i = 0; i < this.dataList.length; i++) {
					let color = this.dataList[i].storeType==5?'#fa2408':
								this.dataList[i].storeType==2?'#242123':'#ffffff'
					let bgColor = this.dataList[i].storeType==5?'#ffffff':
								  this.dataList[i].storeType==2?'#f4ee7a':'#406390'
					marker = marker.concat({
						id: this.dataList[i].id,
						longitude: this.dataList[i].lng,
						latitude: this.dataList[i].lat,
						iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/xyjLogo.png', //显示的图标
						rotate: 0, // 旋转度数
						width: 30, //宽
						height: 25, //高
						callout: { //自定义标记点上方的气泡窗口 点击有效  
							content: this.dataList[i].storeName, //文本
							color: color, //文字颜色
							fontSize: 10, //文本大小
							borderRadius: 5, //边框圆角
							padding: '4', //文本边缘留白
							bgColor: bgColor, //背景颜色
							display: 'ALWAYS', //‘BYCLICK’:点击显示; ‘ALWAYS’:常显
						},
					})
					let circleColor = this.dataList[i].storeType==5?'#fa2408':
									  this.dataList[i].storeType==2?'#f229f4':'#00aaff'
					circle = circle.concat({
						color: circleColor,
						id: this.dataList[i].id,
						longitude: this.dataList[i].lng,
						latitude: this.dataList[i].lat,
						fillColor: '#9db0de33',
						radius: this.regionShow==1?this.dataList[i].radius * 1000:0,
						strokeWidth: 1,
					})
				}
				this.markers = this.markers.concat(marker)
				this.circles = circle
			},
			refresh() {
				this.dataInfo = {
					cityName: '全国',
					cityId: 0,
					areaId: 0,
					cityNum: uni.getStorageSync('cityNum')
				}
				this.scale = 4
				this.inputInfo = {}
				this.getstoreScope()
			},
			submitInput() {
				if (!this.url) {
					return uni.showToast({
						icon: 'none',
						title: '地址链接不能为空'
					})
				}
				uni.request({
					url: 'https://ortherapi.xiaoyujia.com/jwd',
					method: 'POST',
					data: {
						"url": this.url
					},
					success: (res) => {
						console.log(res)
						this.markers.push({
							longitude: res.data.long,
							latitude: res.data.lat,
							iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/xyjLogo.png', //显示的图标
							rotate: 0, // 旋转度数
							width: 30, //宽
							height: 25, //高
							callout: { //自定义标记点上方的气泡窗口 点击有效  
								content: res.data.name, //文本
								color: '#ffffff', //文字颜色
								fontSize: 10, //文本大小
								borderRadius: 5, //边框圆角
								padding: '4', //文本边缘留白
								bgColor: '#55aa7f', //背景颜色
								display: 'ALWAYS', //‘BYCLICK’:点击显示; ‘ALWAYS’:常显
							},
						})
						this.circles.push({
							color: '#55aa7f',
							longitude: res.data.long,
							latitude: res.data.lat,
							fillColor: '#ffaa7f33',
							radius: this.circularRange,
							strokeWidth: 1,
						})
						this.showModal = false
						this.url = ''
					}
				});
				console.log(this.inputInfo, '--------------')
				// if (!this.inputInfo.latVal || !this.inputInfo.lngVal) {
				// 	return uni.showToast({
				// 		icon: 'none',
				// 		title: '经纬度不能为空'
				// 	})
				// }
				// if(!this.inputInfo.nameVal) {
				// 	return uni.showToast({
				// 		icon: 'none',
				// 		title: '名称不能为空'
				// 	})
				// }
				
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			goview(ur) {
				let param = {
					url: ur
				}
				let data = JSON.stringify(param);
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.map-container {
		// margin-top: -40rpx;
		position: relative;
		overflow: hidden;
		// border-radius: 50rpx 50rpx 0 0;

		.cover-view {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			/* width: 80rpx;
			height: 160rpx; */
			padding: 20rpx;
			color: #4F575F;
			font-weight: 400;
			background-color: #fff;
			// background-size: 120rpx 120rpx;
			// background-position: center center;
			position: absolute;
			top: 30rpx;
			right: 30rpx;
			border-radius: 15rpx;
		}
		
		.popupStyle {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 999;
			padding: 40rpx 0;
		
			.closeImg {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.cover-image {
			width: 40rpx;
			height: 40rpx;
			margin: 20rpx auto;
		}
	}

	.tip-bottom {
		position: fixed;
		bottom: 0;
		z-index: 999;
		box-shadow: rgba(0, 0, 0, 0.2) 5rpx 0rpx 20rpx 5rpx;
	}
	
	.btn-big {
		padding-bottom: 60rpx;
	
		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	/deep/ .u-input {
		margin: 20rpx auto !important;
	}
	
</style>