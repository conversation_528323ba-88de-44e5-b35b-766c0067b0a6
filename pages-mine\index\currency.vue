<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-empty v-if="baomuInfo.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<img :src="blankImg" mode="widthFix" />
		<u-gap height="80"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			test() {
				return this.$refs.uNotify.success('xxx')
				return this.$refs.uNotify.warning('xxx')
				return this.$refs.uNotify.error('xxx')
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '家政创业，加盟开店，分佣领钱，首选家姐',
					path: '/pages/index/index',
					mpId: 'wxd64d55d779f303d0'
				}
			},
			onShareTimeline(res) {
				return {
					title: '家政创业，加盟开店，分佣领钱，首选家姐',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
		},
		watch: {
			scrollTop: {
				handler(newValue, oldVal) {

				},
				deep: true
			}
		},
		onLoad() {},
	}
</script>
<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.model {
		// 红色按钮
		color: #ff4d4b;
		// 绿色提示
		stop-color: #19be6b;
		// 橙色按钮
		background-color: #1e1848;
		// 黄
		scrollbar-color: #f6cc70;
		// 灰色文字（按钮）
		color: #909399;
		color: #1e1848;
		// 下边框
		border-bottom: #f4f4f5 2px solid;
		// 阴影
		box-shadow: 5rpx 10rpx 20rpx #dedede;
	}
</style>