<template>
	<view class="page">
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="inform" style="width: 88%;margin: 40rpx auto;">
			<view class="f24 fb lh40 flac-row" style="flex-wrap: wrap;">
				{{unionInform.informTitle}}
				<uni-tag text="待办" v-if="unionInform.informCollect==1" type="error"
					style="margin:-20rpx 0 0 10rpx;"></uni-tag>
			</view>
			<view class="f15 c6 lh40 inform-content" style="margin-bottom: 20rpx;">
				<text>通知来源：{{unionInform.senderName || "管理员"}}</text>
				<text>发布时间：{{unionInform.creTime}}</text>
			</view>

			<!-- 显示富文本内容 -->
			<view v-if="unionInform.informContentText">
				<view class="f18 fb lh40" style="margin-bottom: 20rpx;">
					{{unionInform.informContent|| ''}}
				</view>
				<view v-html="unionInform.informContentText" style="padding: 0rpx 0 20rpx 0;">
				</view>
			</view>
			<view v-else>
				<view class="f16 c0 lh25" @click="openUrl(unionInform.informUrl)">
					<view v-if="shortContent">
						{{unionInform.informContent}}
						<text style="color: #00aaff;text-decoration:underline;margin-left: 10rpx;"
							v-if="unionInform.informUrl">查看详情</text>
					</view>
					<view v-else>
						<view v-html="formatContent(unionInform.informContent)" style="padding: 0rpx 0 20rpx 0;">
						</view>
						<text style="color: #00aaff;text-decoration:underline;margin-left: 10rpx;"
							v-if="unionInform.informUrl">查看详情</text>
					</view>
				</view>
				<view class="f16 c0 lh25 fred">
					{{unionInform.informRemark?'（'+unionInform.informRemark+'）':''}}
				</view>
				<img :src="unionInform.informImg||blankImg" alt="" v-if="unionInform.informImg" mode="widthFix" />
			</view>
		</view>

		<!-- 内容详情 -->
		<view class="flex-col f16 lh30 c0" style="width: 88%;margin: 40rpx auto;" v-for="(item,index) in contentList"
			:key="index">
			<view>
				<text>{{item.contentText||''}}</text>
				<text class="fred">{{item.contentRemark?'（'+item.contentRemark+'）':''}}</text>
			</view>
			<view v-if="item.contentImg" @click="openImgPreview(item.contentImg)">
				<img class="content-img" :src="item.contentImg||blankImg" mode="widthFix" />
			</view>
			<view v-if="item.contentUrl">
				<text style="color: #00aaff;text-decoration:underline;margin-left: 10rpx;"
					@click="openUrl(item.contentUrl)">查看详情</text>
			</view>
		</view>

		<u-gap height="120"></u-gap>

		<view class="w10 mg-at fixed-bottom flac-row-b" v-if="unionInform.publicNotice!=1&&isShare==0">
			<button class="w5 mg-at radius0 border-c1e1848" :class="unionInform.informState==1?'bgc1e1848 cf6cc70':''"
				@click="clickRead(0)">{{unionInform.informState==1?'已读':'未读'}}</button>
			<button class="w5 mg-at radius0 border-c3a6464" :class="unionInform.informCollect==1?'bgc3a6464 cf6cc70':''"
				@click="clickCollect">{{unionInform.informCollect==1?'待办中':'设为待办'}}</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				unionInformId: null,
				memberId: uni.getStorageSync("memberId") || 0,
				unionInform: {},
				contentList: [],
				shortContent: true,
				isShare: 0,
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openUrl(url) {
				if (!url) {
					return
				}

				let data = JSON.stringify({
					url: url
				})
				uni.navigateTo({
					url: url,
					fail: res => {
						uni.navigateTo({
							url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
						})
					}
				})
			},
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			clickRead(value) {
				let state = this.unionInform.informState == 1 ? 0 : 1
				if (value == 1) {
					state = 1
				}
				this.http({
					url: 'updateUnionInform',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.unionInformId,
						informState: state,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let tips = ["消息通知已设为未读", "消息通知已设为已读"]
							if (value == 0) {
								this.$refs.uNotify.success(tips[state])
							}
							this.unionInform.informState = state
						}
					}
				})
			},
			clickCollect() {
				this.http({
					url: 'collectUnionInform',
					method: 'GET',
					hideLoading: true,
					path: this.unionInformId,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success(res.msg)
							this.unionInform.informCollect = this.unionInform.informCollect == 1 ? 0 : 1
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 格式化内容
			formatContent(content) {
				if (!content) {
					result = "暂无详情"
					return result
				}
				let textBegin =
					"<span style='font-size: 16px;width: 100%; line-height: 30px; display: block; margin: 8px auto'>"
				let textEnd = "</span>"
				let imgBegin = "<img alt='图片' style='display: block; width: 100%; height: auto; margin: 10px auto;' src='"
				let imgEnd = "'>"
				let videoBegin =
					"<video type='video/mp4' autoplay='true' style='display: block; width: 100%; height: auto; margin: 10px auto;' src='"
				let videoEnd = "'></video>"
				let result = ''
				let data = content.split('|')
				for (let i in data) {
					if ((data[i]).includes(".png") || (data[i]).includes(".jpg") ||
						(data[i]).includes(".jpeg") || (data[i]).includes(".gif")) {
						result = result + imgBegin + data[i] + imgEnd
					} else if ((data[i]).includes(".mp4")) {
						result = result + videoBegin + data[i] + videoEnd
					} else {
						result = result + textBegin + data[i] + textEnd
					}
				}
				return result
			},
			listUnionInformContent() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/unionInformContent/listUnionInformContent',
					method: 'GET',
					hideLoading: true,
					path: this.unionInformId,
					success: res => {
						if (res.status == 200) {
							this.contentList = res.data
						}
					}
				})
			},
			getUnionInform() {
				this.http({
					url: 'getUnionInform',
					method: 'GET',
					hideLoading: true,
					path: this.unionInformId,
					success: res => {
						if (res.code == 0) {
							this.unionInform = res.data
							// 打开详情，默认设为已读状态
							if (this.unionInform.informState == 0) {
								this.clickRead(1)
							}

							if (this.unionInform.informContent && this.unionInform.informContent.includes(
									'|')) {
								this.shortContent = false
							}
							this.listUnionInformContent()
						}
					}
				})
			},
			// 分享
			onShareAppMessage(res) {
				let title = this.unionInform.informTitle
				return {
					title: title,
					path: '/pages-mine/inform/inform-detail?id=' + this.unionInformId + '&isShare=1',
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
		onLoad(options) {
			this.unionInformId = options.id
			this.isShare = options.isShare || 0
			this.getUnionInform()
		},
	}
</script>

<style lang="scss" scoped>
	.page {
		height: auto;
	}

	.border-c1e1848 {
		border: 2rpx solid #1e1848;
	}

	.border-c3a6464 {
		border: 2rpx solid #3a6464;
	}

	.cf6cc70 {
		color: #f6cc70;
	}

	.bgc1e1848 {
		background: #1e1848;
	}

	.bgc3a6464 {
		background-color: #3a6464;
	}

	.fred {
		color: #ff4d4b;
	}

	.inform {
		img {
			display: block;
			width: 90%;
			height: auto;
			margin: 40rpx auto;
		}
	}

	.inform-content {
		display: flex;
		flex-direction: column;

		text {
			display: block;
			line-height: 50rpx;
			height: 50rpx;
		}
	}

	.content-img {
		display: block;
		margin: 20rpx auto;
		width: 100%;
	}
</style>