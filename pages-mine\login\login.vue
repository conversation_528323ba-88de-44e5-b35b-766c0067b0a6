<template>
	<view class="content" v-if="showPage">
		<!-- #ifdef MP-WEIXIN -->
		<!-- 小程序隐私协议 -->
		<privacyPopup @agree="agreePrivacyHandle" @disagree="disagreePrivacyHandle" :show-pop="showPrivacy">
		</privacyPopup>
		<!-- #endif -->

		<view v-if="!showLoginType">
			<view class="introduce-img">
				<img :src="logo" mode="widthFix" @click="openImgPreview()"
					@longpress="showAccountLogin=!showAccountLogin" />
			</view>
			<view class="agreement1">
				<image @tap="agreementSuccess"
					:src="agreement==true?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'">
				</image>
				<text> 同意</text>
				<navigator @click="goview('https://agent.xiaoyujia.com/ServiceAgreement.html')">
					《服务协议》</navigator>
				<navigator @click="showPrivacy=true">
					《隐私协议》</navigator>
			</view>

			<!-- #ifdef MP-WEIXIN -->
			<view class="btn-big" @longpress="showLoginType = true">
				<u-button :customStyle="btnStyle" shape="circle" text="手机号快捷登录" openType="getPhoneNumber"
					@getphonenumber="getPhoneNumber">
				</u-button>
			</view>
			<view class="btn-big" v-if="showAccountLogin">
				<u-button :customStyle="btnStyle" shape="circle" text="验证码登录" @click="showLoginType = true"></u-button>
			</view>
			<!-- #endif -->

			<!-- #ifdef  MP-TOUTIAO -->
			<!-- 			<view class="btn-big">
				<u-button :customStyle="btnStyle" shape="circle" text="抖音授权登录" @click="getTTuserInfo"
					openType="getPhoneNumber" @getphonenumber="getPhoneNumberHandler">
				</u-button>
			</view> -->
			<!-- #endif -->

			<!-- #ifdef  H5 || APP-NVUE || APP-PLUS -->
			<view class="btn-big" v-if="showAccountLogin">
				<u-button :customStyle="btnStyle" shape="circle" text="验证码登录" @click="showLoginType = true"></u-button>
			</view>
			<view class="btn-big-plain">
				<u-button :customStyle="btnStyle" shape="circle" :plain="true" text="游客登录"
					@click="openIndex()"></u-button>
			</view>
			<!-- #endif -->
		</view>


		<view v-if="showLoginType">
			<view class="header">
				<image :src="headImg"></image>
			</view>

			<view class="list">

				<!-- <u-subsection :list="list" :current="curNow" @change="sectionChange"></u-subsection> -->
				<u-tabs itemStyle="padding-left: 25px; padding-right: 29px; height: 34px;" lineColor="#fdd472"
					:list="list" @click="sectionChange"></u-tabs>

				<view v-if="curNow == 0">
					<view class="list-call">
						<image class="img"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-phone.png">
						</image>
						<input class="sl-input" v-model="phone" type="number" maxlength="11" placeholder="手机号" />
					</view>
					<view class="list-call">
						<image class="img"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yzm.png">
						</image>
						<input class="sl-input" v-model="code" type="number" maxlength="10" placeholder="验证码" />
						<view class="yzm" :class="{ yzms: second>0 }" @tap="getcode">{{yanzhengma}}</view>
					</view>
				</view>
				<view v-if="curNow == 1">
					<view class="list-call">
						<image class="img"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-phone.png">
						</image>
						<input class="sl-input" v-model="phone" type="number" maxlength="11" placeholder="输入手机号" />
					</view>
					<view class="list-call">
						<image class="img"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yzm.png">
						</image>
						<input class="sl-input" v-model="password" type="text" maxlength="32" placeholder="输入密码"
							password="true" />
					</view>
				</view>

			</view>

			<view class="button-login" hover-class="button-hover" @click="bindLogin">
				<text>登录</text>
			</view>

			<view class="agreenment">
				<navigator url="/pages-mine/login/forget" open-type="navigate">忘记密码</navigator>
				<text>|</text>
				<navigator url="/pages-mine/login/reg" open-type="navigate">注册账户</navigator>
			</view>
			<!-- </view> -->

			<view class="agreement1">
				<image @tap="agreementSuccess"
					:src="agreement==true?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'">
				</image>
				<text> 同意</text>
				<navigator @click="goview('https://agent.xiaoyujia.com/ServiceAgreement.html')">
					《服务协议》</navigator>
				<navigator @click="showPrivacy=true">
					《隐私协议》</navigator>
			</view>

			<!-- <u-button icon="chat" text="微信授权登录" color="RGB(92,189,118)" @click="getWX()"></u-button> -->
			<view class="t-e cl">
				<!-- #ifdef  APP-PLUS -->
				<view class="t-g" @click="getWX()">
					<image src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/wx2.png">
					</image>
				</view>
				<!-- #endif -->

				<!-- #ifdef  MP-WEIXIN -->
				<view class="t-g">
					<u-button icon="chat" text="手机号快捷登录" color="RGB(92,189,118)" openType="getPhoneNumber"
						@getphonenumber="getPhoneNumber"></u-button>
				</view>
				<!-- #endif -->

			</view>

		</view>
	</view>
</template>

<script>
	import privacyPopup from '@/pages-mine/common/components/xw-privacy-popup/privacy-popup.vue'
	// import privacyPopup from '@/components/xw-privacy-popup/privacy-popup.vue'
	var _this, js;
	var afterLoginUrl = "/pages/index/index"
	var welcomePage = "/pages/index/welcome"
	export default {
		components: {
			privacyPopup: privacyPopup
		},
		data() {
			return {
				// 可设置
				// 是否自动弹出隐私协议
				showPrivacy: true,
				// 是否开启验证码登录
				showAccountLogin: true,

				btnStyle: {
					color: '#fdd472',
					backgroundColor: '#1e1848',
					borderColor: '#1e1848',
				},

				inviteeId: null,
				inviteeId1: null,
				memberId: null,
				employeeType: null,
				employeeId: null,
				isEmployee: false,
				isBaomu: false,
				agent: '',
				phone: '',
				password: '',
				agreement: false,
				flagB: 1,
				flagC: 1,
				index: false,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				introduceImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1667641665684introduce_img.png",
				logo: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/home/<USER>",
				list: [{
					name: '短信登录',
					status: 0
				}, {
					name: '账号密码登录',
					status: 1
				}],
				curNow: 0,
				second: 0,
				code: '',
				showLoginType: false,
				showPage: false,
				dyInfo: {
					code: '',
					anonymousCode: ''
				}
			};
		},
		onLoad(option) {
			// 进入小程序后检查登录状态，若已登录则跳转
			let memberId = uni.getStorageSync('memberId')
			console.log("检查用户ID：" + memberId)
			if (memberId !== null && memberId !== "") {
				return uni.reLaunch({
					url: afterLoginUrl
				});
			}

			this.showPage = true
			// #ifdef H5 || MP-TOUTIAO
			this.showLoginType = true
			// #endif

			// 尝试获取邀请员工信息
			let id = uni.getStorageSync('introducerId') || null
			let id1 = uni.getStorageSync('introducerId1') || null
			if (id != null) {
				this.inviteeId = id
				this.inviteeId1 = id1
				console.log("登录页面输出从邀请二维码获取的邀请人信息（会员Id和员工Id）：", id + "," + id1)
			}
		},
		computed: {
			yanzhengma() {
				if (this.second == 0) {
					return '获取验证码';
				} else {
					if (this.second < 10) {
						return '重新获取0' + this.second;
					} else {
						return '重新获取' + this.second;
					}
				}
			}
		},
		methods: {
			agreePrivacyHandle() {
				console.log('用户同意隐私协议')
				this.agreement = true
				this.showPrivacy = false
			},
			disagreePrivacyHandle() {
				console.log('用户拒绝隐私协议')
				this.agreement = false
				this.showPrivacy = false
			},
			// 进入首页
			openIndex() {
				uni.reLaunch({
					url: afterLoginUrl
				})
			},
			// 打开图片预览
			openImgPreview() {
				let data = []
				data.push(this.logo)
				uni.previewImage({
					urls: data,
					current: this.logo
				})
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			// 添加邀请信息
			addMemberInvitation() {
				if (this.inviteeId !== null) {
					this.http({
						url: 'addMemberInvitation',
						method: 'POST',
						data: {
							memberId: this.inviteeId,
							inviteeId: uni.getStorageSync("memberId"),
							employeeId: this.inviteeId1,
						},
						header: {
							'content-type': 'application/json;charset=UTF-8'
						},
						success: res => {
							if (res.code == 0) {
								console.log("添加邀请信息-请求成功！")
							} else {
								console.log("添加邀请信息-返回错误！")
							}
						},
						fail: err => {
							console.log('添加邀请信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 检查员工身份，跳转到不同页面
			checkEmployee() {
				uni.setStorageSync("nearStoreId", -1)
				this.http({
					url: 'getEmployeeByMemberId',
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							this.employeeId = res.data.id
							let id = res.data.introducer
							// 将需要的员工信息存入缓存中
							uni.setStorageSync("token", res.data.token)
							uni.setStorageSync("employeeId", this.employeeId)
							uni.setStorageSync("roleId", res.data.roleId)
							this.employeeType = res.data.employeeType
							uni.setStorageSync("employeeType", res.data.employeeType)
							uni.setStorageSync("employeeName", res.data.realName)
							uni.setStorageSync("employeeHeadImg", res.data.headPortrait)
							uni.setStorageSync("employeeNo", res.data.no)
							uni.setStorageSync("employeeState", res.data.state)
							uni.setStorageSync("storeId", res.data.storeId)
							uni.setStorageSync("introducerIdExisted", id)
							uni.setStorageSync("storeType", res.data.storeType)
							uni.setStorageSync("storeName", res.data.storeName)
							this.isEmployee = true
							// if (res.data.employeeType == 10) {
							// 	this.isBaomu = true
							// }
							if (res.data.isBaomu == 1) {
								this.isBaomu = true
								// 初始化保姆信息
								this.checkBaomuAndInit()
							}
						} else {
							uni.setStorageSync("employeeState", -1)
						}
						uni.setStorageSync("isEmployee", this.isEmployee)
						uni.setStorageSync("isBaomu", this.isBaomu)
						if (!this.isEmployee && !uni.getStorageSync('redirectUrl')) {
							console.log("即将直接跳转到主页！")
							return uni.reLaunch({
								url: welcomePage
							});
						}
					},
				})
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				this.http({
					url: 'getBaomuCollectByMemberId',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync("memberId"),
						nearStoreId: -1,
					},
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync("baomuId", res.data.baomuId)
							console.log('通过会员ID获取保姆关联信息-成功！')
							console.log("初始化的保姆ID为" + this.baomuId)
						} else {
							this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
						}
					}
				})
			},
			sectionChange(e) {
				this.curNow = e.status;
			},
			// #ifdef MP-WEIXIN || APP-PLUS
			getPhoneNumber(e) {
				console.log('输出登录信息', e)
				if (e.detail.errMsg != 'getPhoneNumber:ok') {
					return this.$refs.uNotify.error('取消授权登录')
				}
				if (!this.agreement) {
					return this.$refs.uNotify.warning('请先同意《服务协议》《隐私协议》')
				}

				wx.login({
					success: res => {
						this.weiXinOpenLogin(e, res)
					}
				});
			},
			// 微信小程序授权登录
			weiXinOpenLogin(e, loginInfo) {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/member/weiXinOpenLogin',
					method: 'POST',
					hideLoading: true,
					data: {
						source: 'xyjAcn',
						sourceType: 63,
						code: e.detail.code,
						jsCode: loginInfo.code
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							//登录成功后的逻辑
							uni.setStorageSync("account", res.data.account)
							uni.setStorageSync("memberId", res.data.id)
							uni.setStorageSync("memberName", res.data.name)
							uni.setStorageSync("memberSex", res.data.sex)
							uni.setStorageSync("openId", res.data.openId)
							let headImg = res.data.headImg;
							if (headImg == null) {
								headImg = this.headImg
							}
							uni.setStorageSync("memberHeadImg", headImg)
							// 添加邀请人信息，同时检查员工身份
							this.addMemberInvitation()
							this.checkEmployee()
							// this.toFranchise()
							// 微信登录成功后跳转到指定页面，并尝试跳转到缓存页面
							this.reditToUrl()
						} else {
							return uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
				})
			},
			// #endif

			// #ifdef MP-TOUTIAO
			getTTuserInfo() {
				let that = this
				uni.login({
					provider: 'toutiao',
					success: loginRes => {
						console.log('获取的抖音登录信息：', loginRes)
						that.$set(that.dyInfo, "code", loginRes.code)
						that.$set(that.dyInfo, "anonymousCode", loginRes.anonymousCode)
						// uni.getUserInfo({
						// 	success: res => {
						// 		console.log('获取的用户数据：', res)
						// 		console.log('用户名：', res.userInfo.nickName)
						// 		console.log('用户头像：', res.userInfo.avatarUrl)
						// 		console.log('用户性别：', res.userInfo.gender)
						// 	}
						// });
					}
				});
			},
			getPhoneNumberHandler(e) {
				console.log("登录同意授权回调")
				let param = {
					source: 'xyjAcnTT',
					sourceType: 58,
					code: this.dyInfo.code,
					anonymousCode: this.dyInfo.anonymousCode,
					encryptedData: e.detail.encryptedData,
					iv: e.detail.iv
				}
				this.touTiaoOpenLogin(param)
			},
			touTiaoOpenLogin(param) {
				console.log("头条系小程序授权登录！")
				uni.request({
					url: 'https://api2.xiaoyujia.com/member/touTiaoOpenLogin',
					method: 'POST',
					data: param,
					dataType: 'json',
					sslVerify: false,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.data.code == 0) {
							//登录成功后的逻辑
							uni.setStorageSync("account", res.data.data.account)
							uni.setStorageSync("memberId", res.data.data.id)
							uni.setStorageSync("memberName", res.data.data.name)
							uni.setStorageSync("memberSex", res.data.data.sex)
							let headImg = res.data.data.headImg;
							if (headImg == null) {
								headImg = this.headImg
							}
							uni.setStorageSync("memberHeadImg", headImg)
							// 添加邀请人信息，同时检查员工身份
							this.addMemberInvitation()
							this.checkEmployee()
							// this.toFranchise()
							// 微信登录成功后跳转到指定页面，并尝试跳转到缓存页面
							this.reditToUrl()
						} else {
							return uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: err => {
						console.log(res)
					}
				})
			},
			// #endif 

			getOpenLogin(re) {
				uni.showLoading({
					title: '加载中'
				});
				console.log(re)
				this.http({
					url: 'member/getOpenIdByMember?openId=' + re.data.openid,
					method: 'GET',
					hideLoading: true,
					success: res => {
						console.log(res.code)
						if (res.code == 0) {
							//成功后的逻辑
							uni.setStorageSync("account", res.data.account)
							uni.setStorageSync("memberId", res.data.id)
							uni.setStorageSync("memberName", res.data.name)
							uni.setStorageSync("birthDate", res.data.birthDate)
							uni.setStorageSync("memberSex", res.data.sex)
							let headImg = res.data.headImg;
							if (headImg == null) {
								headImg =
									'https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1640310423012%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20211224094645.png';
							}
							uni.setStorageSync("memberHeadImg", headImg)
							uni.hideLoading();

							// 添加邀请人信息，同时检查员工身份
							this.addMemberInvitation()
							this.checkEmployee()
							// this.toFranchise()
							// 短信登录成功后跳转到指定页面，并尝试跳转到缓存页面
							this.reditToUrl()
						} else {
							uni.showToast({
								title: "未绑定用户账号，即将跳转",
								icon: 'none',
								duration: 5000
							});
							uni.setStorageSync("openId", re.data.openid)
							uni.setStorageSync("headimgurl", re.data.headimgurl)
							uni.setStorageSync("name", re.data.nickname)
							uni.setStorageSync("unionId", re.data.unionid)
							uni.hideLoading();
							// uni.setStorageSync("unionId", 'oxv54tziCT63xLAQ8N10M8tsGu1M')
							// uni.setStorageSync("name", 'ssdsd')
							// uni.setStorageSync("openId", 'oEnCr52FUvj0X217Cfp7-FXf2I80')
							// uni.setStorageSync("headimgurl", "")

							setTimeout(function() {
								uni.redirectTo({
									url: '/pages-mine/login/openloginreg'
								})
							}, 1000)

							return;
						}
						console.log(res)
					},
					fail: err => {
						console.log(res)
					}
				})
			},
			getcode() {
				let _this = this;
				if (this.phone.length != 11) {
					uni.showToast({
						icon: 'none',
						title: '手机号不正确'
					});
					return;
				}
				if (this.second > 0) {
					return;
				}
				this.second = 60;
				//请求业务
				// js = setInterval(function() {
				//   _this.second--;
				//   if (_this.second == 0) {
				//     _this.clear()
				//   }
				// }, 1000)
				uni.request({
					url: 'https://api2.xiaoyujia.com/member/sendLoginCode', //仅为示例，并非真实接口地址。
					data: {
						phone: this.phone,
					},
					method: 'POST',
					dataType: 'json',
					success: (res) => {
						if (res.data.code != 0) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: "短信发送成功"
							});
							js = setInterval(function() {
								_this.second--;
								if (_this.second == 0) {
									_this.clear()
								}
							}, 1000)
						}
					},
					fail() {
						this.second == 0
					}
				});
			},
			// 有缓存页面则直接重定向，没有则定向到默认页面
			reditToUrl() {
				let timer = setTimeout(() => {
					if (this.employeeType == 10 && !this.isBaomu) {
						uni.reLaunch({
							url: '/pages-work/business/businessIndex'
						})
						return
					}
					console.log("输出现在的缓存页面：", uni.getStorageSync('redirectUrl'))
					if (uni.getStorageSync('redirectUrl')) {
						let page = uni.getStorageSync('redirectUrl')
						uni.removeStorageSync('redirectUrl')
						return uni.reLaunch({
							url: page
						})
					} else {
						return uni.reLaunch({
							url: afterLoginUrl
						});
					}
				}, 800);
			},
			goview(ur) {
				let param = {
					url: ur
				}
				let data = JSON.stringify(param);
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
			agreementSuccess() {
				this.agreement = !this.agreement;
			},
			getIfEmployeeByMemberId() {
				this.http({
					url: "getIfEmployeeByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						agent: this.agent,
					},
					success: res => {
						if (res.code == 0) {
							if (res.data == 2) {
								this.flagB = 2
								uni.reLaunch({
									url: "/pages-work/index",
								});
							}
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			getFranchiseMsgById() {
				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							//跳转工作台
							if (res.data.flag == 1 && res.data.status == 2 &&
								(res.data.authenticationType == 1 && res.data.bankCardNo ||
									res.data.authenticationType == 2 && res.data.personBankCard)) {
								this.flagC = 2
								uni.reLaunch({
									url: "/pages-work/index"
								});
							}
							//审核页面
							else if (res.data.flag == 1 && (res.data.status == 0 || res.data.status == 1 ||
									res.data.status == 2) &&
								!(res.data.authenticationType == 1 && res.data.bankCardNo ||
									res.data.authenticationType == 2 && res.data.personBankCard)) {
								this.flagC = 2
								uni.reLaunch({
									url: "/pages-mine/franchise/enter/platformAudit"
								});
							}
							//已缴费跳转主体信息
							else if (res.data.flag == 1) {
								this.flagC = 2
								uni.reLaunch({
									url: "/pages-mine/franchise/enter/bodyMsg"
								});
							}
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			toFranchise() {
				if (!uni.getStorageSync('redirectUrl')) {
					this.getIfEmployeeByMemberId()
					if (this.flagB == 1) {
						this.getFranchiseMsgById()
					}
					if (this.flagC == 1) {
						return uni.reLaunch({
							url: "/pages-mine/franchise/enter/topUpMoney"
						});
					}

				}
			},
			bindLogin() {
				let type = 1;
				if (this.phone.length != 11) {
					uni.showToast({
						icon: 'none',
						title: '手机号不正确'
					});
					return;
				}
				if (this.curNow == 1) {
					if (!this.password) {
						return uni.showToast({
							icon: 'none',
							title: '请输入密码'
						});
					}
					type = 2;
				}
				if (this.agreement == false) {
					uni.showToast({
						icon: 'none',
						title: '请先阅读《软件用户协议》'
					});
					return;
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/member/memberLogin',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						account: this.phone,
						password: this.password,
						loginType: type,
						textCode: this.code,
						channel: 'xyjAcn'
					},
					success: res => {
						if (res.code != 0) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						} else {
							//登录成功后的逻辑
							uni.setStorageSync("account", res.data.account)
							uni.setStorageSync("memberId", res.data.id)
							uni.setStorageSync("bindTel", res.data.bindTel)
							uni.setStorageSync("memberName", res.data.name)
							uni.setStorageSync("birthDate", res.data.birthDate)
							uni.setStorageSync("memberSex", res.data.sex)
							uni.setStorageSync("memberHeadImg", res.data.headImg || '')
							this.getUnionMerchantData()

							// 添加邀请人信息，同时检查员工身份
							this.addMemberInvitation()
							this.checkEmployee()
							// this.toFranchise()
							console.log("输出现在的缓存页面：", uni.getStorageSync('redirectUrl'))
							// 账号密码登录成功后跳转到指定页面，并尝试跳转到缓存页面
							this.reditToUrl()
						}
					}
				});

			},
			getUnionMerchantData() {
				this.http({
					url: "getUnionMerchantData",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId")
					},
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync("merchantCode", res.data.merchantCode)
						}
					}
				})
			},
			clear() {
				clearInterval(js)
				js = null
				this.second = 0
			},
		}
	}
</script>

<style lang="scss">
	.content {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.header {
		width: 161rpx;
		height: 161rpx;
		border-radius: 50%;
		margin-top: 30rpx;
		margin-left: auto;
		margin-right: auto;
	}

	.header image {
		width: 161rpx;
		height: 161rpx;
		border-radius: 50%;
	}

	.head-title {
		margin: 0rpx auto;
		width: 100%;

		text {
			display: block;
			text-align: center;
			font-size: 54rpx;
			font-weight: bold;
		}
	}

	.introduce-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 100%;
			height: auto;
			margin: 0 auto;
		}
	}

	.btn-big,
	.btn-big-plain {
		width: 80%;
		height: 90rpx;
		margin-left: 10%;
		margin: 40rpx auto;

		button {
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.list {
		display: flex;
		flex-direction: column;
		padding-top: 50rpx;
		padding-left: 70rpx;
		padding-right: 70rpx;
	}

	.list-call {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		color: #333333;
		border-bottom: 0.5px solid #e2e2e2;
	}

	.list-call .img {
		width: 40rpx;
		height: 40rpx;
	}

	.list-call .sl-input {
		flex: 1;
		text-align: left;
		font-size: 32rpx;
		margin-left: 16rpx;
	}

	.button-login {
		font-size: 34rpx;
		width: 470rpx;
		height: 100rpx;
		border-radius: 50rpx;
		line-height: 100rpx;
		text-align: center;
		margin-left: auto;
		margin-right: auto;
		margin-top: 100rpx;
	}

	.agreenment {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 80rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.agreenment text {
		font-size: 24rpx;
		margin-left: 15rpx;
		margin-right: 15rpx;
	}

	.agreement1 {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 80rpx;
		// color: #FFA800;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.agreement1 image {
		width: 40rpx;
		height: 40rpx;
	}

	.yzm {
		// color: #FF7D13;
		color: #1e1848;
		font-size: 24rpx;
		line-height: 64rpx;
		padding-left: 10rpx;
		padding-right: 10rpx;
		width: auto;
		height: 64rpx;
		border: 1rpx solid #1e1848;
		border-radius: 50rpx;
	}

	.yzms {
		color: #999999 !important;
		border: 1rpx solid #999999;
	}

	.t-e {
		text-align: center;
		width: 250rpx;
		margin: 180rpx auto 0;
	}

	.t-e image {
		width: 80rpx;
		height: 80rpx;
	}



	.t-g {
		float: left;
		width: 100%;
	}

	.head-img {
		width: 100%;
		height: 300rpx;

		img {
			display: block;
			width: 200rpx;
			height: 200rpx;
			margin: 100rpx auto;
			border-radius: 50%;

		}
	}
</style>