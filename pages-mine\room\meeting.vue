<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="back-img">
			<img src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QTC_back.png" alt="">
		</view>

		<view class="w10 mg-at" style="position: absolute;top:40%;left: 0;">
			<view class="cf text-c lh50">
				<view class="f24 lsp4">视频面试</view>
				<view class="f16">当前房间号：{{roomId || '-'}}</view>
			</view>
			<view class="btn" style="margin-top: 300rpx;">
				<button class="w6 f16 lh40" open-type="share">分享面试</button>
				<button class="w6 f16 lh40" @click="newVersion?joinRoom():goView()">前往面试</button>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 是否校验房间用户（false：不校验，任何人都允许进入 true：校验，无房间权限不允许加入）
				checkAuth: false,
				// 是否自动加入（false：需要手动输入房间，再点击进入 true：有传参房号直接自动加入）
				autoJoin: false,
				// 是否新版本
				newVersion: true,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				roomId: null,
				userId: null,
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				meetingRoom: null
			}
		},
		methods: {
			getMeetingRoomById(value) {
				if ((!this.roomId || !this.memberId) && !this.userId) {
					return false
				}

				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/getMeetingRoomById',
					method: 'GET',
					path: this.roomId,
					success: res => {
						if (res.code == 0) {
							this.meetingRoom = res.data
							if (value == 1 || this.autoJoin) {
								if (this.userId) {
									this.openRoom()
									return
								}
								this.http({
									outsideUrl: 'https://api.xiaoyujia.com/acn/getMeetingRoomUser',
									header: {
										'content-type': "application/json;charset=UTF-8"
									},
									method: 'POST',
									data: {
										roomId: this.roomId,
										memberId: this.memberId || 0
									},
									success: res => {
										if (res.code == 0) {
											this.userId = res.data.id
											this.openRoom()
										} else {
											if (!this.checkAuth) {
												this.insertMeetingRoomUser()
											} else {
												this.$refs.uNotify.error("抱歉，您暂无权限加入该房间！请检查房间号！")
											}
										}
									}
								})
							}
						} else {
							this.$refs.uNotify.error("抱歉，当前房间不存在！请重新输入房间号！")
						}
					}
				})
			},
			// 创建房间用户
			insertMeetingRoomUser() {
				if (!this.memberId && !this.userId) {
					this.$refs.uNotify.error("用户id不存在，请先登录！")
					return
				}

				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/insertMeetingRoomUser',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						roomId: this.roomId,
						memberId: this.memberId
					},
					success: res => {
						if (res.code == 0) {
							this.userId = res.data.id
							this.openRoom()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 进入房间
			async joinRoom() {
				console.log('xxx1')
				this.getMeetingRoomById(1)
			},
			// 打开房间
			openRoom() {
				console.log('xxx2')
				let url = "https://pv.xiaoyujia.com/meetingRoom.html?userId=" + this.userId
				let param = {
					url: url
				}
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(JSON.stringify(param))}`
				})
			},
			// 跳转到会议
			goView() {
				let url = "https://pv.xiaoyujia.com/pages-mine/meetingRoom/index?roomId=" + this.roomId + '&memberId=' +
					this
					.memberId + '&employeeId=' + this.employeeId
				let param = {
					url: url
				}
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(JSON.stringify(param))}`
				})
			},
			checkVersion() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/getMeetingRoomById/1',
					method: 'GET',
					path: '',
					success: res => {
						if (res.code == 0) {
							this.newVersion = res.data.roomType == 1 ? true : false
						}
					}
				})
			},
			// 登录状态检查
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', '/pages-mine/interview/meeting')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
					return false
				} else {
					this.checkVersion()
					return true
				}
			},
			// 分享
			onShareAppMessage(res) {
				return {
					title: '欢迎参加家姐联盟-视频面试，快来这里找最适合你的工作吧！',
					path: '/pages-mine/room/meeting?roomId=' + this.roomId,
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
		onLoad(options) {
			this.roomId = options.roomId || ""
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.roomId = obj.id
			}
			this.checkLogin()
		},
	}
</script>
<style lang="scss" scoped>
	.back-img {
		position: fixed;

		img {
			width: 750rpx;
			height: 100vh;
		}
	}

	.btn {
		width: 100%;

		button {
			color: #fff;
			background-color: #2979ff;
			border-radius: 80rpx;
			height: 80rpx;
			margin: 40rpx auto;
		}
	}
</style>