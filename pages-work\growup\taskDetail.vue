<template>
  <view class="page" style="padding-top: 20rpx;">
	   <view class="w88 mg-at bacf radius10" style="padding: 20rpx;margin: 30rpx auto;" v-if="unionTask.timeLimit">
	     <view class="w95 flac-row-b border-bottom-2se">
	       <view class="f16 fb lh45 t-indent">任务时间</view>
	       <view class="c9 text-c">任务限时{{unionTask.timeLimit}}小时内完成</view>
	     </view>
	     <view class="w95 mg-at f15 c6 lh25" style="margin: 20rpx;">
	      <u-count-down v-if="unionTask.timeLimit"
	              :time="differMs"
	              format="DD:HH:mm:ss"
	              autoStart
	              millisecond
	              @change="onChange"
	          >
	              <view  v-if="unionTask.timeLimit">
	      			   <text>距离任务时限：</text>
	                  <text class="time__item" style="font-size: 35rpx;color: red;font-weight: bold;">{{ timeData.days }}&nbsp;天</text>
	                  <text class="time__item" style="font-size: 35rpx;color: red;font-weight: bold;">{{ timeData.hours>10?timeData.hours:'0'+timeData.hours}}&nbsp;时</text>
	                  <text class="time__item" style="font-size: 35rpx;color: red;font-weight: bold;">{{ timeData.minutes }}&nbsp;分</text>
	                  <text class="time__item" style="font-size: 35rpx;color: red;font-weight: bold;">{{ timeData.seconds }}&nbsp;秒</text>
					  <text>,到期后将回退至上一任务!请尽快完成!</text>	
				  </view>
	          </u-count-down>
	     </view>
	   </view>
	<view class="w88 mg-at bacf radius10" style="padding: 20rpx;margin: 30rpx auto;">
      <view class="w95 flac-row-b border-bottom-2se">
        <view class="f16 fb lh45 t-indent">达成方式</view>
        <view class="c9 text-c">{{unionTask.taskTitle}}</view>
      </view>
      <view class="w95 mg-at f15 c6 lh25" style="margin: 20rpx;">
        <view class="">{{unionTask.taskIntroduce}}</view>
        <view class="flac-row" style="align-items: unset;margin-top: 30rpx;" v-for="(item,i) in methodslist" :key="i">
          <view class="dot" style="margin-top: 8rpx;margin-right: 10rpx;">{{i+1}}</view>
          <view class="">{{item.content}}</view>
        </view>
      </view>
    </view>
	<view class="w88 mg-at bacf radius10" style="padding: 20rpx;margin: 30rpx auto;" v-if="unionTask.reward!=null">
	  <view class="w95 flac-row-b border-bottom-2se">
	    <view class="f16 fb lh45 t-indent">任务奖励</view>
	  </view>
	  <view class="w95 mg-at f15 c6 lh25" style="margin: 20rpx;">
	    <view class="">任务完成后奖励{{unionTask.reward==0?'积分':unionTask.reward==1?'实物':'奖金'}}
		{{unionTask.reward==0?unionTask.rewardNumber+'个':unionTask.reward==1?unionTask.rewardThing:unionTask.rewardNumber+'元'}}</view>
	  </view>
	</view>
	<view class="w88 mg-at bacf radius10" style="padding: 20rpx;margin: 30rpx auto;" 
	v-if="unionTask.establishWhere==0||unionTask.establishWhere==1||
		  unionTask.establishWhere==2||unionTask.establishWhere==3||
		  unionTask.establishWhere==4">
	  <view class="w95 flac-row-b border-bottom-2se">
	    <view class="f16 fb lh45 t-indent">任务进度</view>
		  <view class="c9 text-c">已完成{{unionTask.nowSchedule}}/{{unionTask.reachFlag}}</view>
	  </view>
	  <view class="w95 mg-at f15 c6 lh25" style="margin: 20rpx;">
	    <u-line-progress :percentage="unionTask.schedule" activeColor="#ff0000"></u-line-progress>
	  </view>
	</view>
    <view class="w88 mg-at bacf radius10" v-if="unionTask.taskType==0" style="padding: 40rpx 20rpx;margin: 30rpx auto;">
      <view class="w95 mg-at flac-row">
        <u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-rwgl.png"
          size="25"></u-icon>
        <view class="t-indent f16 fb">任务流程</view>
      </view>
      <view class="w95 mg-at f15 c6 lh25" style="margin: 20rpx;">
        <view class="" style="margin: 30rpx auto;" v-for="(item,i) in strategylist" :key="i">
          <view class="w8 " style="align-items: unset;">
            <view class="t-indent">步骤{{i+1}}：{{item.formTitle}}</view>
          </view>
		  <view>
		  <u-input v-model="item.completeResult" v-if="item.formType==0" />
		  </view>
		  <view @click="uploadImg(i)">
			 <u-upload v-if="item.formType==1" maxCount="5" :fileList="item.fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple accept="all"></u-upload>
		  </view>
        </view>
      </view>
    </view>
	<!-- 操作确认弹窗 -->
	<view>
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>
	</view>
	<u-button style="width: 80%;" @click="submitForm(unionTask)" 
									:text="unionTask.taskType==0?'提交':
									unionTask.taskType==1?'去完成':'更新任务状态'" color="#1e1848" customStyle="color:#f6cc70"></u-button>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        datalist: {},
		taskId: '',
		fileList1: [],
		timeData: {},
        status: '',
		checkTitle: "",
		checkText: "",
		unionTask: {},
		imgEventId: null,
		differMs: '',
		planType: null,
		flowId: null,
        methodslist: [],
		strategyIndex: null,
        strategylist:[],
		timeLimit: ''
      };
    },
    onLoad(e) {
      this.taskId = e.taskId
      this.flowId = e.flowId
      this.planType = e.planType
      this.timeLimit = e.timeLimit
	  this.getTaskForm()
    },
    methods: {
		startTaskLog(){
			this.http({
				url: "startTaskLog",
				method: 'POST',
				data: {
					creater: uni.getStorageSync("merchantCode"),
					taskId: this.taskId,
					flowId: this.flowId
				},
				header: {
					"content-type": "application/json;charset=UTF-8"
				},
				success: res=>{
					if(res.code==0){
						if(res.data.flag==2){
							this.differMs = parseInt(res.data.differMs)
						}else if(res.data.flag==1){
							uni.showToast({
								icon:'none',
								title: '任务超时!'
							})
						 	setTimeout(()=>{
							return	uni.redirectTo({
									url: '/pages-work/growup/index'
								})
							},2000)
						}
					}
				}
			})
		},
		onChange(e) {
		    this.timeData = e
		},
		submitForm(val){
			if(val.taskType==0){
			for (var i = 0; i < this.strategylist.length; i++) {
				if(!this.strategylist[i].completeResult){
					return uni.showToast({
							icon:'none',
							title: '请补充完步骤'+ (i+1) +'后再提交!'
						})
				}
			}
			}
			if(val.taskType==1){
				let url = val.linkUrl+"?taskId=" + val.id
				if(val.imgFlowFlag==1){
					url = "./carousel?taskId=" + val.id
				}else {
					if(val.appid){
					 return	wx.navigateToMiniProgram({
							appId: val.appid, // pord
							path: val.linkUrl+'?taskId'+val.id,
							envVersion: 'release',
							success(res) {
								// 打开成功
							}
						})
					}
				}
			 return	uni.navigateTo({
					url: url
				})
			}
			this.http({
				url: "completeUnionTask",
				method: 'POST',
				data: {
					merchantCode: uni.getStorageSync("merchantCode"),
					taskId: this.taskId,
					taskFormList: this.strategylist,
					planType: this.planType,
					storeId: uni.getStorageSync('storeId')
				},
				header: {
					"content-type": "application/json;charset=UTF-8"
				},
				success: res=>{
					if(res.code==0){
						let title = "任务已完成!"
						if(this.unionTask.reward!=null){
							title = "任务已完成!正在自动领取奖励中..."
						}
						uni.showToast({
							icon:'none',
							title: title
						})
						setTimeout(()=>{
							uni.redirectTo({
								url: '/pages-work/growup/index'
							})
						},2000)
					}else {
						uni.showToast({
							icon:'none',
							title: res.msg
						})
					}
				}
			})
		},
		uploadImg(i){
			this.strategyIndex = i
		},
		uploadFilePromise(url) {
			return new Promise((resolve, reject) => {
				let a = uni.uploadFile({
					url: 'https://api.xiaoyujia.com/system/imageUpload',
					filePath: url,
					name: 'file',
					formData: {},
					success: (res) => {
						setTimeout(() => {
							resolve(res.data)
						}, 1000)
					}
				});
			})
		},
		// 删除图片
		deletePic(event) {
			this.event = event
			this.imgEventId = event.index
			this.openCheck(1, "确认删除该附件图片吗？")
		},
		// 打开确认框
		openCheck(checkType, checkTitle, checkText) {
			this.checkType = checkType
			this.checkTitle = checkTitle
			this.checkText = checkText
			this.$refs.popupCheck.open(this.type)
		},
		// 确认框功能
		popupCheck() {
			if (this.checkType == 1) {
				this.strategylist[this.strategyIndex].fileList1.splice(this.imgEventId,1)
				this.strategylist[this.strategyIndex].completeResult = ''
				let imgs = '';
				if (this.strategylist[this.strategyIndex].fileList1.length > 0) {
					for (var i = 0; i < this.strategylist[this.strategyIndex].fileList1.length; i++) {
						imgs += this.strategylist[this.strategyIndex].fileList1[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
					this.strategylist[this.strategyIndex].completeResult = imgs
				}
				this[`fileList${this.event.name}`].splice(this.event.index, 1)
			}
		},
		// 新增图片
		async afterRead(event) {
			// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
			let lists = [].concat(event.file)
			let fileListLen = this[`fileList${event.name}`].length
			lists.map((item) => {
				this[`fileList${event.name}`].push({
					...item,
					status: 'uploading',
					message: '上传中'
				})
			})
			for (let i = 0; i < lists.length; i++) {
				const result = await this.uploadFilePromise(lists[i].url)
				let obj = JSON.parse(result);
				let item = this[`fileList${event.name}`][fileListLen]
				this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
					status: 'success',
					message: '',
					url: obj.data
				}))
				if (this.strategylist[this.strategyIndex].completeResult) {
					this.strategylist[this.strategyIndex].completeResult += ","+obj.data
				}else {
					this.strategylist[this.strategyIndex].completeResult = obj.data
				}
				let obj1 = {
					url: obj.data
				}
				this.strategylist[this.strategyIndex].fileList1.push(obj1)
				fileListLen++
			}
		},
		getTaskForm(){
			this.http({
				url: 'getTaskForm',
				method: 'GET',
				data: {
					taskId: this.taskId,
					planType: this.planType,
					storeId: uni.getStorageSync('storeId'),
					merchantCode: uni.getStorageSync('merchantCode')
				},
				success: res=>{
					if(res.code==0){
						this.unionTask = res.data.unionTask
						this.strategylist = res.data.list
						if(res.data.completeFlag){
							let title = "该任务已完成!"
							if(this.unionTask.reward!=null){
								title = "任务已完成!正在自动领取奖励中..."
							}
							uni.showToast({
								icon:'none',
								title: title
							})
							setTimeout(()=>{
							   return uni.redirectTo({
									url: '/pages-work/growup/index'
								})
							},2000)
						}else {
							if(this.timeLimit!='null'){
								this.startTaskLog()
							}
						}
					}
				}
			})
		},
    }
  }
</script>

<style lang="scss" scoped>
  .page {
	  width: 100%;min-height: 100vh;
    background-color: #e3f6fd;
  }

  .tagStyle {
    color: #d58145;
    background-color: #fdf7e7;
    padding: 5rpx 15rpx;
  }

  .dot {
    background: #f1f2f3;
    border-radius: 40rpx;
    font-size: 14px;
    width: 50rpx;
    height: 40rpx;
    text-align: center;
    line-height: 40rpx;
  }
  .btnStyle {
    background: #fff;
    border: 2rpx solid #55aaff;
    color: #55aaff;
    padding: 0rpx 15rpx;
    border-radius: 10rpx;
  }
  .btnStyle2 {
    background: #fff;
    border: 2rpx solid #999;
    color: #999;
    padding: 0rpx 15rpx;
    border-radius: 10rpx;
  }
</style>