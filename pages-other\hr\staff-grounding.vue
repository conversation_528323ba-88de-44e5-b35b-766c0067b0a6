<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<view class="staff-tab" style="padding-bottom: 400rpx;">
			<view class="tab">
				<view class="img-upload">
					<img :src="checkStr(trialStaff.headPortrait)!='-'?trialStaff.headPortrait:blankImg"
						@click="openImgPreview()" />
				</view>
				<view class="tab-head">
					<text>姓名</text>
				</view>
				<view class="tab-inputbox">
					<view><input class="single-input" type="text" v-model="trialStaff.name" placeholder="暂未填写姓名"
							:disabled="true" />
					</view>
				</view>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>所属门店</text>
				</view>
				<view class="tab-picker" @click="openPickerMine(0)">
					<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
					<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
					<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
				</view>
			</view>

			<view class="tab" v-if="departList.length!=0">
				<view class="tab-head">
					<text>所属部门</text>
				</view>
				<view class="tab-picker" @click="openPickerMine(1)">
					<text class="picker-text" v-if="departName == ''">点击选择部门</text>
					<text class="picker-text" v-if="departName !== ''">{{ departName }}</text>
					<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
				</view>
			</view>

			<view style="mine-height: 120rpx;">
				<view class="tab-head">
					<text>时间管理</text>
					<text style="font-weight: 100;color: #1e1848;font-size: 32rpx;margin-left: 15rpx;"
						@click="openTime()">去设置</text>
				</view>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>自动派单</text>
				</view>
				<view class="flac-row" style="margin: 0 20rpx;">
					<view class="flac-row" style="margin: 20rpx 20rpx;" @click="trialStaff.isAutoPd=true">
						<img :src="trialStaff.isAutoPd?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
							style=" width: 50rpx;height: 50rpx;" />
						<text style="margin: 20rpx 0;">是</text>
					</view>
					<view class="flac-row" style="margin: 20rpx 20rpx;" @click="trialStaff.isAutoPd=false">
						<img :src="!trialStaff.isAutoPd?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
							style=" width: 50rpx;height: 50rpx;" />
						<text style="margin: 20rpx 0;">否</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 按钮组 -->
		<view class="btn-bottom-float-group">
			<view>
				<button :style="!isGrounding?'background-color: #1e1848;color: #f6cc70;':''"
					@click="changeState(0)">上架</button>
			</view>
			<view>
				<button :style="isGrounding?'background-color: #1e1848;color: #f6cc70;':''"
					@click="changeState(1)">下架</button>
			</view>
		</view>

		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />


	</view>
</template>

<script>
	export default {
		props: {
			startLoadMore: {
				type: Number
			}
		},
		data() {
			return {
				// 可设置

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},

				// 员工信息
				trialId: null,
				employeeId: null,
				processId: null,
				trialStaff: {},
				employee: {},
				record: {},
				list: {},
				popupShow: false,
				isGrounding: false,

				storeName: "",
				departName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				storeList: [],
				departList: [],
				memberId: uni.getStorageSync('memberId'),
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			// 打开头像预览
			openImgPreview() {
				let data = []
				let img = this.trialStaff.headPortrait
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开选择器
			openPickerMine(value) {
				let id = 1
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
					id = this.trialStaff.storeId || id
				} else if (value == 1) {
					this.pickerMineName = "name"
					this.pickerMineList = this.departList
					id = this.trialStaff.departId || id
				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				// 初始化选择器位置
				for (let i = 0; i < this.pickerMineList.length; i++) {
					if (id == this.pickerMineList[i].id) {
						this.pickerIndex = i
						break
					}
				}
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				let name = this.pickerMineList[index][this.pickerMineName]
				let id = this.pickerMineList[index].id
				if (value == 0) {
					this.storeName = name
					this.trialStaff.storeId = id
				} else if (value == 1) {
					this.departName = name
					this.trialStaff.departId = id
				}
				this.showPickerMine = false
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 初始化选择器
			initPickerMine(value, id, name, list) {
				for (let i = 0; i < list.length; i++) {
					if (id == list[i].id) {
						let param = value == 0 ? 'storeName' : 'departName'
						this[param] = list[i][name]
						this.pickerIndex = i
						break
					}
				}
			},
			// 打开时间管理
			openTime() {
				uni.navigateTo({
					url: '/pages-work/business/time/timeIndex?employeeId=' + this.trialStaff.employeeId
				})
			},
			// 上下架
			changeState(index) {
				let state = true
				if (index == 0) {
					if (this.isGrounding) {
						this.$refs.uNotify.error("当前已经处于上架状态！")
						state = false
					}
				} else if (index == 1) {
					if (!this.isGrounding) {
						this.$refs.uNotify.error("当前已经处于下架状态！")
						state = false
					}
				}
				if (state) {
					this.updateStaffState(index)
				}
			},
			updateStaffState(index) {
				let tips = index == 0 ? "上架" : "下架"
				let state = index == 0 ? 1 : 2
				if (state == 1) {
					if (this.pickerIndex == -1) {
						this.$refs.uNotify.error("请选择上架门店！")
						return
					}

					if (this.departList.length != 0 && this.departName == '') {
						this.$refs.uNotify.error("请选择上架部门！")
						return
					}
				}

				this.http({
					url: "updateStaffState",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						trialId: this.trialId,
						state: state,
						workType: this.trialStaff.workType,
						storeId: this.trialStaff.storeId,
						departId: this.trialStaff.departId,
						operatorId: uni.getStorageSync("employeeId") || 0
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success(tips + "成功！")
							this.trialStaff.state = state
							this.isGrounding = !this.isGrounding
						} else {
							if (!res.msg.includes('时间模板')) {
								this.$refs.uNotify.error(res.msg)
							} else {
								uni.showModal({
									title: '是否立即去云平台修改？',
									content: res.msg,
									success: res => {
										if (res.confirm) {
											let url =
												'http://yun.xiaoyujia.com/Employee/TimeMan?id=' +
												this.trialStaff
												.employeeId
											uni.setClipboardData({
												data: url,
												success: () => {
													this.$refs.uNotify.success(
														'云平台链接已复制，可粘贴至浏览器快速修改!')
												}
											})
										}
									}
								});
							}
						}
					}
				})
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/store/getByList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.storeList = res.data
							let id = this.trialStaff.storeId
							this.initPickerMine(0, id, 'storeName', this.storeList)
							this.getDepartList()
						}
					}
				});
			},
			// 获取部门列表
			getDepartList() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/system/getDeptByStoreId',
					path: this.trialStaff.storeId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.departList = res.data
							let id = this.trialStaff.departId
							this.initPickerMine(1, id, 'name', this.departList)
						}
					}
				});
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "-"
				} else {
					return str
				}
			},
			//员工信息
			getTrialStaffByTrialId() {
				this.http({
					url: 'getTrialStaffByTrialId',
					data: {
						trialId: this.trialId
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.trialStaff = res.data
							this.processId = this.trialStaff.processId
							this.employeeId = this.trialStaff.employeeId
							if (this.trialStaff.state == 1) {
								this.isGrounding = true
							}
							this.getEmployee()
						}
					}
				});
			},
			getEmployee() {
				this.http({
					url: 'getEmployeeDtoById',
					path: this.employeeId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.employee = res.data
						} else {
							this.$set(this.employee, "employeeWorkType", 0)
						}
					}
				});

			},
		},
		watch: {

		},
		onReachBottom() {

		},
		onShow() {

		},
		onLoad(options) {
			this.trialId = parseInt(options.id)
			this.getTrialStaffByTrialId()
			this.getStoreList()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/hrbp.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.img-upload {
		img {
			display: block;
			width: 200rpx;
			height: 200rpx;
			margin: 100rpx auto;
			border-radius: 50%;
		}
	}

	.head-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
	}

	// 底部固定按钮
	.btn-bottom-float-group {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		display: flex;

		view {
			width: 50%;
			height: 120rpx;

			button {
				width: 80%;
				height: 80rpx;
				margin: 0rpx auto;
				line-height: 80rpx;
				color: #1e1848;
				background-color: #fff;
				border: #1e1848 2rpx solid;
				border-radius: 50rpx;
				font-size: 32rpx;
				box-shadow: 4rpx 4rpx 10rpx #dedede;
			}
		}

	}

	.btn-group {}
</style>