<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-picker :show="show2" @cancel="show2 = false" ref="uPicker" :columns="columns" @confirm="confirm"
			@change="changeHandler1"></u-picker>

		<!-- 下方弹出选择器 -->
		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 简历分数 -->
		<view class="score-tips">
			<view class="score-tips-title">
				<view class="score-title-head">
					<text>简历&nbsp;</text>
					<text style="font-size:60rpx;color: red;font-weight: bold;">{{resumeScore}}</text>
					<text>&nbsp;分</text>
					<view class="preview-btn" @click="openPreview()">
						<text>预览</text>
					</view>
					<view class="preview-btn" @click="openPut()" v-if="isRole">
						<text>提审</text>
					</view>
				</view>
				<view @click="openAccount()">
					<view class="score-title-bottom">
						<text>快速完善个人简历，就可以让老师审核接单啦</text>
					</view>
					<view class="score-title-bottom" style="color: #ff4d4b;font-weight: bold;"
						v-if="isPortraitUnqualified">
						<text>*当前头像不符合规范，请重新上传！</text>
					</view>
					<view class="score-title-bottom" style="color: #ff4d4b;font-weight: bold;" @click="openResume()">
						<text>*适用于快速完善上架，点此填写完整简历</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 学历选择器 -->
		<u-picker :show="show" @cancel="show = false" :columns="educationList" @confirm="confirmState(0)"
			@change="changeHandler" keyName="label" :defaultIndex="defaultIndex"></u-picker>

		<!-- 人脸识别相机 -->
		<cameraPage :showCameraPage="showCameraPage" @submitCameraPhoto="submitCameraPhoto"
			@closeCameraPage="closeCameraPage" />

		<!-- 上传头像提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="popup-title">
				<text>头像照片示例</text>
			</view>
			<view class="popup-tips">
				<text style="color: #909399;text-align: left;">穿着整洁，头发整齐，站在白墙前拍摄照片，你的头像会更好看。</text>
			</view>
			<view class="popup-img" @longpress="uploadHeadPortrait(1)">
				<img :src="headUplpadTips" alt="" @click="uploadHeadPortrait(0)" mode="widthFix">
			</view>
			<view class="popup-tips" @longpress="uploadHeadPortrait(1)">
				<text :style="headImg !==''?'color: #19be6b;':'color: #ff4d4b;'">上传真实照片可获得10分</text>
				<text style="color: #ff4d4b;">若经审核头像不合格则-30分</text>
				<text style="color: #ff4d4b;">*无法上传可以尝试长按这里哦</text>
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;">
				<button style="margin: 40rpx auto 60rpx auto;" @click="uploadHeadPortrait(0)">我知道了</button>
			</view>
		</u-popup>

		<!-- 详细住址弹框 -->
		<u-popup :show="showAddressDetail" mode="bottom" @close="showAddressDetail = false">
			<view class="filter-title">
				<text>详细住址</text>
			</view>

			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200"
							v-model="employee.address" placeholder="请输入现居地详细地址" height="100" count></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 地址越详细，附近派单准确率越高哦</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employee.address=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="showAddressDetail=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<view class="tab-tips" style="background-color: #ff4d4b;" v-if="isPortraitUnqualified">
			<text>当前头像不符合规范，请重新上传！</text>
		</view>

		<view v-if="!showCameraPage">
			<view class="resume-tab">
				<view class="tab">
					<view class="img-upload">
						<img class="head-img" :src="headImg|| blankImg" @click="popupShow = true"
							@longpress="openImgPreview(headImg)" />
					</view>
					<view class="tab-head">
						<text>姓名</text>
						<text style="margin-right: 0rpx;padding-right: 0rpx;" @click="openIdentification">身份认证</text>
						<!-- <text
						:style="employee.realName!==null&&employee.realName!==''?'color: #19be6b;':'color: #ff4d4b;'">5分</text> -->
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employee.realName"
								placeholder="可通过上传身份证修改" disabled="true" @click="openRealNameTips" /></view>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab" style="padding: 20rpx 20rpx 40rpx 0rpx;">
					<view class="tab-head" @click="openCert()">
						<text>我的证件</text>
						<text :style="isHealthUpload||isHealthUpload1?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
						<text style="margin-right: 0rpx;padding-right: 0rpx;">去上传</text>
					</view>
					<view class="info-item flac-row" v-if="employeeInfo.idCard">
						<text @click="openCert()">身份证：已上传</text>
						<uni-icons type="eye" style="margin-left: 10rpx;" size="22" @click="openImgPreview(idCardImg)">
						</uni-icons>
					</view>
					<view class="info-item flac-row" v-for="(item,index1) in certificateList" :key="index1"
						v-if="showCert(index1)">
						<text @click="openCert()">{{item.title}}：已上传</text>
						<uni-icons type="eye" style="margin-left: 10rpx;" size="22"
							@click="openImgPreview(item.certificateImg)">
						</uni-icons>
					</view>

					<view class="info-item flac-row" v-if="isHealthUpload">
						<text @click="openCert()">体检表：已上传</text>
						<uni-icons type="eye" style="margin-left: 10rpx;" size="22" @click="openImgPreview(healthImg)">
						</uni-icons>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的现居住地</text>
						<text :style="checkStr(employee.address)!='暂无'?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
						<text @click="showAddressDetail = true"
							style="margin-right: 0rpx;padding-right: 0rpx;">详细住址</text>
						<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
							color="#909399" @click="showAddressDetail = true">
						</uni-icons>
					</view>

					<pickers @address="address">
						<view class="tab-picker">
							<text class="picker-text">{{ employee.address?employee.address:'点击选择地址' }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</pickers>
				</view>
			</view>

			<!-- 学历-选框 -->
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的学历</text>
						<text :style="education!==''&&education!=='默认状态'?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
					</view>
					<view class="tab-picker" @click="show = true">
						<text class="picker-text" v-if="education == ''">点击选择学历</text>
						<text class="picker-text" v-if="education !== ''">{{ education }}</text>
						<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 工作情况-滑动选择 -->
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您做家政多少年？</text>
						<text :style="employeeInfo.workYear?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="number"
								v-model="employeeInfo.workYear" placeholder="暂未填写工作年限" /></view>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>找什么样的工作？</text>
						<text :style="workModelCount!==0?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<view class="tab-checkbox" v-for="(tabOption, index) in workModelList" :key="index">
						<view class="checkbox" :class="{ activeBox: tabOption.isCheck == true }">
							<text @click="choiceWorkModel(index)">{{ tabOption.text }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 工作地点栏目 -->
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>找哪里的工作？</text>
						<text :style="baomuExpectedWork.expectedAddress?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<!-- 		<view class="tab-picker" @click="show2 = true">
						<text
							class="picker-text">{{ baomuExpectedWork.expectedAddress?baomuExpectedWork.expectedAddress:'选择详细地址' }}</text>
						<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
					</view> -->
					<view class="tab-tips-small" @click="show2 = true"><text>意向地点可多选，点此添加</text></view>
					<view class="tab-inputbox-high">
						<u--textarea class="multiline-input" confirmType="done" maxlength="50"
							v-model="baomuExpectedWork.expectedAddress" placeholder="填写您的意向工作地址" height="150"
							count></u--textarea>
					</view>
					<view class="tab-head-smail">
						<text>范围偏好</text>
					</view>
					<view class="tab-picker" @click="openPickerMine(0)">
						<text class="picker-text">{{ workSpace }}</text>
						<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>最低月薪</text>
						<text :style="salaryLow?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<view class="tab-salary">
						<input class="salary-input" type="number" v-model="salaryLow" placeholder="您希望的工资" />
						<text class="salary-input-unit">元</text>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的一句话自我介绍</text>
						<text :style="introduce.length>25?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<view class="tab-tips-small"><text>例如：您的个人情况、专长或特长</text></view>
					<view class="tab-inputbox-high">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="introduce"
							:placeholder="introduceTips" height="150" count></u--textarea>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab" @click="openEx(index1)">
					<view class="tab-head">
						<text>工作经历</text>
						<text :style="baomuWorkExperienceList.length>=1?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
					</view>
					<view class="info-item" v-for="(work, index1) in baomuWorkExperienceList" :key="index1">
						<view class="flac-row">
							<text class="w8"
								style="font-weight: bold;">工作{{index1+1}}：{{formatWorkType(work.workType)}}</text>
							<view class="w2 flac-row-c">
								<text style="text-align: right;">修改</text>
								<uni-icons type="forward" size="18"></uni-icons>
							</view>
						</view>
						<text>&ensp;&ensp;&ensp;{{formatDate(work.startWorkTime)}}-{{formatDate(work.endWorkTime)}}</text>
						<view class="work-content">
							<text style="line-height: 50rpx;">{{work.workContent}}</text>
						</view>
						<u-gap height="10"></u-gap>
					</view>
					<u-empty v-if="baomuWorkExperienceList.length==0" text="暂未上传"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>

			<!-- 家庭成员-选择栏目 -->
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的家庭成员？</text>
						<text
							:style="baomuInfo.urgentPhone&&baomuInfo.urgent?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
					</view>
					<view class="tab-checkbox" v-for="(item, index) in familyList" :key="index">
						<view class="checkbox" :class="{ activeBox: item.value == choiceNum }">
							<text v-model="item.value" @click="choiceNum = item.value">{{ item.urgentType }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 家庭成员-姓名与电话输入 -->
			<view class="resume-tab" v-if="choiceNum != -1">
				<view class="tab">
					<view class="tab-head">
						<text>您{{ familyList[choiceNum].urgentType }}的姓名是？</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="baomuInfo.urgent"
								placeholder="点击输入姓名(也可用xx先生/小姐称呼哦～)" /></view>
					</view>
				</view>
			</view>
			<view class="resume-tab" v-if="choiceNum != -1">
				<view class="tab">
					<view class="tab-head">
						<text>您{{ familyList[choiceNum].urgentType }}的手机号是？</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="baomuInfo.urgentPhone"
								placeholder="点击输入手机号" /></view>
					</view>
				</view>
			</view>

			<view class="resume-tab" @click="openPhoto()">
				<view class="tab-head">
					<text>工作照</text>
					<text :style="photeList.length?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
					<text style="margin-right: 0rpx;padding-right: 0rpx;">去上传</text>
				</view>
				<view style="display: flex;flex-wrap: wrap;padding: 20rpx 5%;">
					<view class="photo-item" v-for="(item,index1) of photeList" :key="index1"
						v-if="index1<maxShowPhoto||showAllPhoto">
						<img :src="item.certificateImg" width="40" height="36" />
					</view>
				</view>
				<view v-if="photeList.length>maxShowPhoto" style="padding-right: 60rpx;text-align: right;"
					@click="showAllPhoto=!showAllPhoto">
					<uni-icons :type="showAllPhoto?'bottom':'top'" labelColor="#000"></uni-icons>
					<text>{{showAllPhoto?'展开更多':'收起'}}</text>
				</view>
				<view class="" v-if="!photeList.length" style="padding-bottom: 40rpx;">
					<u-empty text="暂未上传" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="btn-big"><button @click="trySave()">确 认</button></view>

			<!-- 快速填写 -->
			<view class="lh35 text-c" style="position: fixed;display: block;right: 00rpx;top: 76vh;
						width: 160rpx;height: 70rpx;box-shadow: 2rpx 2rpx 10rpx #909399;color: #fff;
						border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;background-color: rgba(30,24,72,0.7);"
				@click="openResume()">
				详细填写
			</view>
		</view>
	</view>
</template>

<script>
	import pickers from "@/pages-mine/common/components/ming-picker/ming-picker.vue"
	import cameraPage from "@/pages-mine/common/components/cameraPage.vue"
	export default {
		components: {
			pickers,
			cameraPage
		},
		data() {
			return {
				// 可配置选项
				// 是否开启人脸识别相机
				isOpenCamera: true,
				// 简历分预览限制
				limitScore: 60,
				// 展示所有照片
				showAllPhoto: false,
				// 最多展示照片数
				maxShowPhoto: 6,
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				isRole: false,

				roleId: uni.getStorageSync('roleId') || null,
				isGetScore: false,
				certificateList: [],
				isHealthUpload: false,
				isHealthUpload1: false,
				isAuthenticated: false,
				isPortraitUnqualified: false,
				idCardImg: '',
				healthImg: '',
				showAddressDetail: false,
				scrollTop: 0,
				show: false,
				show1: false,
				show2: false,
				showCameraPage: false,
				showClipper: false,
				showPickerMine: false,
				popupShow: false,
				isBaomuuInfo: true,
				memberId: uni.getStorageSync('memberId') || null,
				baomuId: null,
				employeeId: null,
				memberName: '',
				headImg: '',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png',
				headUplpadTips: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/head_upload_tips.png",
				baomuWorkType: '',
				changeNum: 0,
				choiceCount: 0,
				choiceCount1: 0,
				choiceCount2: 0,
				choiceCount3: 0,
				choiceCount4: 0,
				choiceCount5: 0,
				defaultIndex: [0],
				defaultIndex1: [0],
				education: '',
				familyMember: '',
				educationIndex: 0,
				familyIndex: 0,
				salaryLow: '',
				salaryHigh: '',
				imageStyles: {
					width: 200,
					height: 110,
					border: {
						width: 2,
						style: 'dashed',
						radius: '100'
					}
				},
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				// 保姆信息
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null
				},
				// 员工信息
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					addressCode: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				employeeInfo: {
					employeeId: this.baomuId,
					hometown: null,
					education: null,
					familyMember: null,
					married: null
				},
				baomuExpectedWork: {
					baomuId: this.baomuId,
					workModel: null,
					elderly: null,
					watchBaby: null,
					cookingSkills: null,
					otherSkills: null,
					salaryExpectation: null,
					siteId: null
				},
				baomuWorkExperienceList: [],
				educationList: [
					[{
							value: 0,
							label: '无',
						},
						{
							value: 1,
							label: '默认状态',
						},
						{
							value: 3,
							label: '小学',
						},
						{
							value: 4,
							label: '中学',
						},
						{
							value: 5,
							label: '高中',
						},
						{
							value: 8,
							label: '中专',
						},
						{
							value: 6,
							label: '大专',
						},
						{
							value: 7,
							label: '本科及以上',
						},
						{
							value: 9,
							label: '研究生',
						}
					]
				],
				// 婚姻情况
				marriedList: [{
					value: 0,
					label: '未婚未育'
				}, {
					value: 1,
					label: '已婚未育'
				}, {
					value: 2,
					label: '已婚已育'
				}, {
					value: 3,
					label: '未婚已孕'
				}, {
					value: 4,
					label: '离异'
				}, {
					value: 5,
					label: '其它'
				}],
				languagesList: [{
					text: '普通话',
					isCheck: 0
				}, {
					text: '闽南话',
					isCheck: 0
				}, {
					text: '英语',
					isCheck: 0
				}, {
					text: '粤语',
					isCheck: 0
				}, {
					text: '客家话',
					isCheck: 0
				}],
				workModelCount: 0,
				// 找哪种类型的工作？
				workModelList: [{
						tabTitle: "住家保姆能力",
						text: '住家保姆',
						typeValue: 201,
						isCheck: false
					},
					{
						tabTitle: "不住家保姆能力",
						text: '不住家保姆',
						typeValue: 202,
						isCheck: false
					},
					{
						tabTitle: "单餐保姆能力",
						text: '单餐保姆',
						typeValue: 203,
						isCheck: false
					},
					{
						tabTitle: "月嫂能力",
						text: '月嫂',
						typeValue: 196,
						isCheck: false
					},
					{
						tabTitle: "育儿嫂能力",
						text: '育儿嫂',
						typeValue: 197,
						isCheck: false
					},
					{
						tabTitle: "护工能力",
						text: '护工',
						typeValue: 198,
						isCheck: false
					},
					{
						tabTitle: "陪读师能力",
						text: '陪读师',
						typeValue: 194,
						isCheck: false
					},
					{
						tabTitle: "陪诊师能力",
						text: '陪诊师',
						typeValue: 195,
						isCheck: false
					},
					{
						tabTitle: "豪宅管家能力",
						text: '豪宅管家',
						typeValue: 206,
						isCheck: false
					},
					{
						tabTitle: "保洁能力",
						text: '保洁师',
						typeValue: 190,
						isCheck: false
					},
					{
						tabTitle: "整理师能力",
						text: '整理师',
						typeValue: 199,
						isCheck: false
					},
					{
						tabTitle: "搬运能力",
						text: '搬家师',
						typeValue: 191,
						isCheck: false
					},
					{
						tabTitle: "清洗师能力",
						text: '清洗师',
						typeValue: 192,
						isCheck: false
					},
					{
						tabTitle: "维修师能力",
						text: '维修师',
						typeValue: 204,
						isCheck: false
					},
					{
						tabTitle: "疏通能力",
						text: '疏通师',
						typeValue: 193,
						isCheck: false
					},
				],
				resumeScore: 0,
				workSpace: '',
				workSpaceIndex: 0,
				workSpaceList: [],

				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				columns: [],
				columnData: [],

				introduce: '',
				introduceTips: '如：我是泉州人，高中毕业，擅长做煲汤、家常菜、家乡菜，有六年家政工作经验，希望能找到一份长期稳定的钟点、白班工作',

				choiceNum: -1,
				familyList: [{
						urgentType: '配偶',
						value: 0
					},
					{
						urgentType: '子女',
						value: 1
					},
					{
						urgentType: '父母',
						value: 2
					},
					{
						urgentType: '朋友',
						value: 3
					},
					{
						urgentType: '亲戚',
						value: 4
					},
					// {
					// 	urgentType: '其他',
					// 	value: 5
					// }
				],
				photeList: []
			};
		},
		methods: {
			// 打开简历预览
			openPreview() {
				let limitScore = this.limitScore
				if (this.resumeScore >= limitScore) {
					// 员工详情预览
					let url = "/pages-mine/works/employee-detail?baomuId=" + this.baomuId
					return uni.navigateTo({
						url: url
					})
				} else {
					this.$refs.uNotify.error("简历分超过" + limitScore + "分才可以预览哦！")
				}
			},
			// 打开简历
			openResume() {
				uni.navigateTo({
					url: '/pages-mine/resume/resume?baomuId=' + this.baomuId
				})
			},
			// 打开提审
			openPut() {
				uni.navigateTo({
					url: '/pages-other/employee/workSkill?baomuId=' + this.baomuId
				})
			},
			// 打开工作经历
			openEx(index) {
				let url = '/pages-mine/resume/experience?baomuId=' + this.baomuId
				uni.navigateTo({
					url: url
				})
			},
			openCert() {
				uni.navigateTo({
					url: '/pages-mine/resume/certificate/certificate?baomuId=' + this.baomuId
				})
			},
			openPhoto() {
				uni.navigateTo({
					url: '/pages-mine/resume/photo?baomuId=' + this.baomuId
				})
			},
			address(e) {
				let code = ""
				this.employee.address = ''
				for (var i = 0; i < e.value.length; i++) {
					this.employee.address += e.value[i]
					if (e.code[i] != "") {
						code = e.code[i]
					}
				}
				// 存储地址代码
				this.employee.addressCode = code
				console.log("输出：", e)
				console.log("输出地区代码：", code)
			},
			// 打开头像预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			// 打开身份认证页面
			openIdentification() {
				uni.navigateTo({
					url: '/pages-mine/resume/certificate/identification?baomuId=' + this.baomuId,
				});
			},
			openRealNameTips() {
				this.$refs.uNotify.error('仅通过身份认证修改！')
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 确认学历选择
			confirmState(index) {
				if (index == 0) {
					this.education = this.educationList[0][this.educationIndex].label
					this.employeeInfo.education = this.educationList[0][this.educationIndex].value
					this.show = false
				} else if (index == 1) {

				}
			},
			// 打开选择器
			openPickerMine(value) {
				let id = 1
				if (value == 0) {
					this.pickerMineName = "name"
					this.pickerMineList = this.workSpaceList
					id = this.baomuExpectedWork.siteId || id
				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				// 初始化选择器位置
				for (let i = 0; i < this.pickerMineList.length; i++) {
					if (id == this.pickerMineList[i].id) {
						this.pickerIndex = i
						break
					}
				}
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				let name = this.pickerMineList[index][this.pickerMineName]
				let id = this.pickerMineList[index].id
				if (value == 0) {
					this.workSpace = name
					this.baomuExpectedWork.siteId = id
				}
				this.showPickerMine = false
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 初始化选择器
			initPickerMine(value, id, name, list) {
				for (let i = 0; i < list.length; i++) {
					if (id == list[i].id) {
						let param = value == 0 ? 'workSpace' : 'name'
						this[param] = list[i][name]
						this.pickerIndex = i
						break
					}
				}
			},
			// 拼接多选框中的文本内容
			checkBoxToExpectedWork() {
				let workModel = ''
				let otherSkills = ''
				for (let item of this.workModelList) {
					if (item.isCheck == 1) {
						workModel = workModel + item.text + ','
					}
				}
				// 去掉末尾逗号
				if (workModel !== '') {
					workModel = workModel.substring(0, workModel.length - 1)
				}
				// 未选择则返回空值
				this.baomuExpectedWork.workModel = workModel
				this.baomuInfo.workType = workModel
			},
			// 选择工作类型（一级选项）
			choiceWorkModel(index) {
				if (this.workModelList[index].isCheck == true) {
					this.workModelList[index].isCheck = false
					this.workModelCount--
				} else {
					this.workModelList[index].isCheck = true
					this.workModelCount++
				}
			},
			showCert(index) {
				let type = this.certificateList[index].certificateType
				let title = this.certificateList[index].title
				let result = true

				// 不出现特定类型照片
				if (type <= 2 || type == 8 || type == 25 || type == 98 || type == 99) {
					result = false
				}

				if (title == null) {
					result = false
				}
				return result
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? '0' + MM : MM
				let d = date.getDate()
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d
			},
			// 格式化家庭成员类型
			formatUrgentType(urgentType) {
				if (urgentType !== null) {
					for (let item of this.familyList) {
						if (item.urgentType == urgentType) {
							this.choiceNum = item.value
						}
					}
				}
			},
			// 将工作类型格式化到工作类型选项（一级选项）
			formatWorkModel() {
				let count = 0
				let workModel = this.baomuExpectedWork.workModel
				if (workModel == null || workModel == "") {
					return
				}

				let array = workModel.split(',')
				for (let item of this.workModelList) {
					array.map(item1 => {
						if (item1 == item.text) {
							item.isCheck = true
							count++
						}
					})
				}
				this.workModelCount = count
			},
			formatWorkType(value) {
				let str = "暂无"
				switch (value) {
					case 100:
						str = "其他"
						break
					case 10:
						str = "保姆"
						break
					case 20:
						str = "月嫂"
						break
					case 30:
						str = "育儿嫂"
						break
					case 40:
						str = "护工"
						break
					case 50:
						str = "保洁师"
						break
					case 60:
						str = "搬家师"
						break
					case 70:
						str = "清洗师"
						break
					case 80:
						str = "维修师"
						break
					case 90:
						str = "疏通师"
						break
				}
				return str
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.educationIndex = index
			},
			changeHandler1(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				// 当第一列值发生变化时，变化第二列(后一列)对应的选项
				if (columnIndex === 0) {
					// picker为选择器this实例，变化第二列对应的选项
					picker.setColumnValues(1, this.columnData[index])
				}
			},
			// 回调参数为包含columnIndex、value、values
			confirm(e) {
				this.show2 = false
				let isBlank = this.baomuExpectedWork.expectedAddress ? false : true
				if (!isBlank) {
					this.baomuExpectedWork.expectedAddress += '、'
				}
				this.baomuExpectedWork.expectedAddress += e.value[0] + e.value[1]
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 打开对应的详细页面
			openMineDetail(url) {
				console.log(url);
				uni.navigateTo({
					url: url
				});
			},
			// 尝试保存
			trySave() {
				this.openCheck(0, '确定保存个人信息吗？', '内容越贴近实际越好哦～');
			},
			// 更新个人信息（更新前进行校验）
			save() {
				if (this.isBaomu) {
					if (!this.checkBoxToBaomuInfo()) {
						// this.$refs.uNotify.error('请补充完整你的个人信息哦～')
					} else if (this.checkInputData()) {
						this.checkBaomuAndInit()
						if (this.baomuId == null) {
							let timer = setTimeout(() => {
								if (this.baomuId !== null) {
									this.updateInfo()
								}
							}, 1500);
						} else {
							this.updateInfo()
						}
					}
				} else {
					// 如果是员工则创建员工（将来需要改方法）
					if (this.checkInputData()) {
						this.checkBaomuAndInit()
						if (this.baomuId == null) {
							let timer = setTimeout(() => {
								if (this.baomuId !== null) {
									this.updateInfo()
								}
							}, 1500);
						} else {
							this.updateInfo()
						}
					}
				}
				this.checkGetScore()
			},
			// 更新所有信息
			updateInfo() {
				this.updateEmployee()
				this.updateEmployeeInfo()
				this.updateBaomuInfo()
				this.updateBaomuExpectedWork()
				this.getResumeScore()
			},
			// 检查输入的内容
			checkInputData() {
				if (this.employee.realName == "" || this.employee.realName == null) {
					this.$refs.uNotify.error('请填写你的姓名哦～')
					return false
				}
				if (this.employee.phone == "" || this.employee.phone == null) {
					this.$refs.uNotify.error('请填写你的手机号哦～')
					return false
				}
				if (this.employee.address == "" || this.employee.address == null) {
					this.$refs.uNotify.error('请补充完整你的现居地哦～')
					return false
				}
				if (this.educationIndex == 1) {
					this.$refs.uNotify.error('请补充完整你的学历哦～')
				}

				if (this.baomuInfo.urgentPhone && !uni.$u.test.mobile(this.baomuInfo.urgentPhone)) {
					this.$refs.uNotify.error('请输入正确的手机号码哦～')
					return false
				}

				return true
			},
			// 格式化学历信息
			formatEducation() {
				for (let i = 0; i < this.educationList[0].length; i++) {
					let item = this.educationList[0][i]
					if (item.value == this.employeeInfo.education) {
						this.educationIndex = i
						this.education = item.label
						this.defaultIndex = []
						this.defaultIndex.push(i)
					}
				}
			},
			closeCameraPage(flag) {
				console.log("关闭人脸相机！")
				this.showCameraPage = flag
			},
			// 提交照片
			submitCameraPhoto(tempFilePaths) {
				const url = 'https://api2.xiaoyujia.com/system/imageUpload'
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						this.headImg = tempFilePaths[0]
						let result = JSON.parse(res.data)
						this.employee.headPortrait = result.data
						this.isPortraitUnqualified = false
						this.employee.remark = ""
						this.showCameraPage = false
						console.log('上传图片后返回文件地址:', result.data)
						this.updateEmployee()
					}
				});
			},
			// 上传头像
			uploadHeadPortrait(index) {
				if (this.employee.state == 1 && this.employee.putTime && !this.roleId) {
					// return this.$refs.uNotify.warning('您已成功入驻并通过平台认证，头像暂时不支持修改哦，可以开始接单啦！')
				}
				this.popupShow = false

				// #ifdef  MP-WEIXIN
				// 打开人脸识别相机
				if (index == 0 && this.isOpenCamera) {
					this.showCameraPage = true
					return
				}
				// #endif

				const url = 'https://api2.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos'
							},
							dataType: 'json',
							success: res => {
								this.headImg = tempFilePaths[0]
								let result = JSON.parse(res.data)
								this.employee.headPortrait = result.data
								this.isPortraitUnqualified = false
								this.employee.remark = ""
								console.log('上传图片后返回文件地址:', result.data)
								this.updateEmployee()
							}
						});
					}
				});
			},
			// 更新员工
			updateEmployee() {
				this.$set(this.employee, 'id', this.baomuId);
				this.http({
					url: 'updateBaomu',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.employee,
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync('emeployHeadImg', this.employee.headPortrait)
							console.log('输出头像:', uni.getStorageSync('emeployHeadImg'))
							this.$refs.uNotify.success('个人信息更新成功！')
							this.changeNum = 0
							uni.setStorageSync("isUpdateResume", true)
							// let timer = setTimeout(() => {
							// 	return uni.navigateBack()
							// }, 1500);
						} else {
							this.$refs.uNotify.error('更新员工失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 更新员工信息
			updateEmployeeInfo() {
				this.$set(this.employeeInfo, 'employeeId', this.baomuId)
				this.http({
					url: 'updateEmployeeInfo',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					method: 'POST',
					data: this.employeeInfo,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.changeNum = 0
							} else {}
						} else {
							this.$refs.uNotify.error('更新员工信息失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 更新保姆求职意向
			updateBaomuExpectedWork() {
				this.checkBoxToExpectedWork()
				this.baomuExpectedWork.salaryExpectation = this.salaryLow != '' ? parseInt(this.salaryLow) : 0
				this.$set(this.baomuExpectedWork, 'baomuId', this.baomuId)
				this.http({
					url: 'updateBaomuExpectedWork',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuExpectedWork,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('求职意向更新成功！')
						}
					},
				});
			},
			// 更新保姆信息
			updateBaomuInfo() {
				this.$set(this.baomuInfo, 'baomuId', this.baomuId)
				this.$set(this.baomuInfo, 'introduce', this.introduce || '')
				this.http({
					url: 'updateBaomuInfo',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuInfo,
					success: res => {
						if (res.code == 0) {
							this.changeNum = 0
						} else {
							this.$refs.uNotify.error('更新保姆信息失败，返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取保姆求职意向
			getBaomuExpectedWork() {
				this.http({
					url: 'getBaomuExpectedWork',
					method: 'POST',
					hideLoading: true,
					data: {
						baomuId: this.baomuId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.baomuExpectedWork = res.data
							this.formatWorkModel()
							this.salaryLow = res.data.salaryExpectation || ''
						}
						this.getIntentionSite()
						this.getDictLocationByDepth()
					},
				});
			},
			getCertificateByEmployeeId() {
				this.http({
					url: 'getCertificateByEmployeeId',
					method: 'GET',
					path: this.baomuId || 0,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.certificateList = res.data
							for (let item of this.certificateList) {
								if (item.certificateType == 3) {
									this.isHealthUpload1 = true
								} else if (item.certificateType == 8) {
									this.isHealthUpload = true
									this.healthImg = item.certificateImg
								} else if (item.certificateType == 1) {
									this.idCardImg = item.certificateImg
								}
							}
						}
					},
				});
			},
			// 获取个人照片列表
			getMyPhotoList() {
				this.http({
					url: 'getCertificateByCertType',
					method: 'POST',
					data: {
						employeeId: this.baomuId || 0,
						certificateType: 25
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.photeList = res.data
						}
					}
				})
			},
			getIntentionSite() {
				this.http({
					url: 'getIntentionSite',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.workSpaceList = res.data[0]
							this.initPickerMine(0, this.baomuExpectedWork.siteId, 'name', this.workSpaceList)
						}
					},
				});
			},
			// 获取地址字典列表
			getDictLocationByDepth() {
				this.http({
					url: "getDictLocationByDepth",
					method: 'GET',
					hideLoading: true,
					path: 2,
					success: res => {
						if (res.code == 0) {
							this.columns = []
							this.columnData = []
							let list = res.data
							let data = []
							list.forEach(item => {
								if (item.children) {
									let data1 = item.children.map(it => it.label)
									this.columnData.push(data1)
									data.push(item.label)
								}
							})
							this.columns.push(data)
							this.columns.push(this.columnData[0])
						}
					}
				})
			},
			// 获取简历分
			getResumeScore() {
				this.http({
					url: 'getResumeScore',
					path: this.baomuId || -1,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.resumeScore = res.data.scoreSum
						}
					}
				});
			},
			// 检查是否得分
			checkGetScore() {
				let isGetScore = true
				if (this.employee.headPortrait == null || this.employee.headPortrait == "") {
					isGetScore = false
				}
				// if (this.employee.realName == null || this.employee.realName == "") {
				// 	isGetScore = false
				// }
				if (this.employee.address == null || this.employee.address == "") {
					isGetScore = false
				}
				if (this.educationIndex == 1) {
					isGetScore = false
				}
				if (this.employeeInfo.workYear == null) {
					isGetScore = false
				}
				let remark = this.employee.remark || ""
				if (remark.includes("头像不合格")) {
					isGetScore = false
					this.isPortraitUnqualified = true
				}

				this.isGetScore = isGetScore
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data

								this.employee = baomuDetail.employee
								this.headImg = this.employee.headPortrait
								this.employeeInfo = baomuDetail.employeeInfo
								this.formatEducation()

								this.baomuInfo = baomuDetail.baomuInfo
								this.introduce = this.baomuInfo.introduce || ''
								this.formatUrgentType(this.baomuInfo.urgentType)
								this.baomuWorkExperienceList = baomuDetail.baomuWorkExperience
								this.checkGetScore()
							}
						}
					});
				}
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				if (this.baomuId == null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync('baomuId', this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log('初始化的保姆ID为' + this.baomuId)
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						}
					});
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：保存个人信息
				if (this.checkType == 0) {
					this.save()
				}
			},
			// 检查员工身份
			checkEmployee() {
				if (this.baomuId !== uni.getStorageSync("baomuId")) {
					this.isBaomuuInfo = false
				}
				this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
				this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				if (this.isEmployee) {
					if (this.isBaomu) {
						this.isBaomuuInfo = true
					} else {
						this.isBaomuuInfo = false
					}
				}
			},
		},
		onLoad(options) {
			if (options.baomuId) {
				this.isRole = true
			}
			this.baomuId = options.baomuId || uni.getStorageSync('employeeId') || null
			this.checkEmployee()
			this.getBaomuDetail()
			this.getResumeScore()
			this.getBaomuExpectedWork()
			this.getCertificateByEmployeeId()
			this.getMyPhotoList()
		},
	};
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";


	page {
		height: 100%;
		background-color: #ffffff;
	}

	// 提示弹窗标题
	.popup-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	// 提示图片
	.popup-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 600rpx;
			height: auto;
			margin: 40rpx auto 0 auto;
		}
	}

	// 上传提示
	.popup-tips {
		width: 80%;
		line-height: 60rpx;
		margin: 20rpx auto 0 auto;

		text {
			display: block;
			font-size: 36rpx;
			text-align: center;
		}
	}

	.img-upload {
		width: 200rpx;
		height: 200rpx;
		margin: 100rpx auto;
	}

	.head-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
	}

	// 简历分数栏目
	.score-tips {
		width: 100%;
		height: auto;
		margin: 0 auto;
		display: flex;
		flex-direction: row;
		color: #ffffff;
		padding: 20rpx 20rpx;
		background: linear-gradient(rgba(38, 56, 128, 0.8), rgba(58, 100, 100, 0.8));
	}


	.id-tips {
		width: 90%;
		height: 140rpx;
		margin: 0 auto;
		display: flex;
		flex-direction: row;
		color: #ffffff;
		background-color: #1e1848;
		border-radius: 20rpx;
		bottom: 20rpx;
		text-align: center;
	}

	.tips-title,
	.score-tips-title {
		width: 62%;
		height: auto;
		display: flex;
		flex-direction: column;
		font-size: 32rpx;
		padding: 20rpx 20rpx;
		line-height: 50rpx;
	}

	.score-tips-title {
		width: 100%;
	}

	.title-head,
	.score-title-head {
		font-size: 40rpx;
	}

	.title-bottom,
	.score-title-bottom {
		font-size: 32rpx;
		height: 50rpx;
	}

	// 简历预览
	.preview-btn {
		float: right;
		margin-right: 40rpx;
		height: 50rpx;
		line-height: 50rpx;
		width: 140rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		text-align: center;
		font-size: 36rpx;

		text {
			color: #1e1848;
		}
	}

	// 工资选择框
	.tab-salary {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	.tab-salary {
		width: 70%;
		margin-left: 70rpx;
	}


	// 工资输入框样式
	.salary-input,
	.salary-input1 {
		display: block;
		float: left;
		// 两个输入框时启用
		// width: 30%;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: center;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}

	.salary-input-interval,
	.salary-input-unit {
		display: block;
		float: left;
		text-align: center;
		height: 80rpx;
		line-height: 100rpx;
		margin-left: 40rpx;
	}

	.salary-input-unit {
		display: block;
		float: right;
		font-size: 36rpx;
		margin-right: -80rpx;
	}

	// 栏目信息内容
	.info-item {
		width: 90%;
		margin: 0 5%;
		height: auto;
		line-height: 80rpx;

		text {
			display: block;
			font-size: 36rpx;
		}
	}

	// 照片栏目
	.photo-item {
		width: 33.3%;
		height: auto;

		img {
			display: block;
			width: 180rpx;
			height: 180rpx;
			margin: 20rpx auto;
		}
	}
</style>