<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 添加员工 -->
		<u-popup :show="popupShowNew" mode="bottom" @close="popupShowNew = false">
			<view class="filter-title">
				<text>创建直播间</text>
			</view>

			<view class="filter-content" style="height: 950rpx;">
				<view class="filter-tab">
					<view class="tab-title">
						<text>直播标题</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="liveRoom.roomTitle"
								placeholder="请填写直播标题" /></view>
					</view>

					<view class="tab-title">
						<text>直播预告</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="liveRoom.roomAnnounce"
								placeholder="请填写直播预告" /></view>
					</view>

					<view class="tab-title">
						<text>直播内容</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="liveRoom.roomContent"
								placeholder="请填写直播内容" /></view>
					</view>

					<view style="width: 86%;padding: 0 7%;">
						<text style="display: block;font-size: 32rpx;color: #909399;">
							添加直播间后将自动成为该直播间的主播，并拥有直播的权利！</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left"
						@click="liveRoom.roomTitle='';liveRoom.roomAnnounce='';liveRoom.roomContent=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="addRoom()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>


		<view class="listStyle flac-row" v-for="(item,index) of list" :key="index">
			<view class="w3">
				<img :src="item.productImg|| blankImg"
					style="display:block;width: 200rpx;height: 120rpx;margin: 0rpx auto;border-radius: 10rpx">
			</view>
			<view class="w7 flex-col f15 lh25">
				<text class="fb">{{item.roomTitle|| '-'}}</text>
				<text class="text-content f14">{{item.roomContent||'暂无直播内容介绍'}}</text>
				<view class="flac-row">
					<text class="w6" style="color: #ff4d4b;">直播间ID:{{item.id||'-'}}</text>
					<view class="w4 btnStyle1">
						<button @click="openRoom(index)">开始直播</button>
					</view>
				</view>
			</view>

		</view>

		<u-empty v-if="list.length==0" text="暂无直播间" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="btn-big">
			<button @click="popupShowNew = true">创建直播间</button>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 直播间创建权限
				createAtuh: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_course_img.png',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,

				popupShowNew: false,
				liveRoom: {
					roomTitle: '',
					roomAnnounce: '',
					roomContent: '',
					roomImg: '',
					createMemberId: null,
					createEmployeeId: null
				},
				list: []
			}
		},
		methods: {
			openRoom(index) {
				let roomId = this.list[index].id
				uni.navigateTo({
					url: '/pages-mine/room/live?roomId=' + roomId
				})
			},
			// 校验权限
			checkAuth() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/checkUserAuth',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId,
						authType: 1
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.createAtuh = true
							}
						}
					}
				})
			},
			// 添加直播间
			addRoom() {
				if (!this.createAtuh) {
					this.$refs.uNotify.error('很抱歉，您暂无创建直播间权限！')
					return
				}

				if (!this.liveRoom.roomTitle) {
					this.$refs.uNotify.error('请将直播间信息填写完整！')
					return
				}
				this.liveRoom.createMemberId = this.memberId
				this.liveRoom.createEmployeeId = this.employeeId
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/insertLiveRoom',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.liveRoom,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('房间创建成功！')
							this.popupShowNew = false
							this.liveRoom()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 获取直播间列表
			listLiveRoom() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/listLiveRoom',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						createMemberId: this.memberId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {}
			},
		},
		onLoad(options) {
			this.checkAuth()
			this.listLiveRoom()
		},
	}
</script>
<style lang="scss" scoped>
	@import "@/pages-mine/common/css/tab-menu.scss";

	.listStyle {
		width: 100%;
		height: 200rpx;
		font-size: 36rpx;
		line-height: 60rpx;
		margin: 0 0 40rpx 0;
	}

	.btn-big {
		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.btnStyle1 {
		button {
			width: 160rpx;
			height: 50rpx;
			line-height: 50rpx;
			font-size: 30rpx;
			color: #f6cc70;
			background-color: #1e1848;
			padding: 0;
		}
	}
</style>