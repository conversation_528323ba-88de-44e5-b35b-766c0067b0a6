<template>
	<view class="">
		<view class="w25 flac-row fr" style="margin: 20rpx auto;">
			<image class="w15" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
				mode="widthFix"></image>
			<uni-data-select :clear="false" v-model="dateVal" :localdata="dateList" @change="changeD"></uni-data-select>
		</view>
		<u-empty text="暂无兑换记录!" width="100" textSize="18" v-if="!list.length"
			customStyle="padding-top:200rpx;margin-left: 200rpx">
		</u-empty>
		
		<view class="w9 mg-at flac-row-b listStyle" v-for="(item,i) in list" v-if="list.length" :key="i" @click="goPage(item)">
			<image class="w2" style="border-radius: 15rpx;" :src="item.productPicture" mode="widthFix"></image>
			<view class="f14 c6 lh20 t-indent">
				<view class="c0 fb lh30">{{item.productName}}</view>
				<view class="">兑换积分：{{item.exchangeIntegral}}</view>
				<view class="">订单号：{{item.billNo}}</view>
				<view class="">兑换时间：{{item.createTime}}</view>
			</view>
			<image style="width: 12%;"
				:src="item.ifDeliveryFlag == 2 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/icon_wff.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/icon_yff.png' "
				mode="widthFix"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dateVal: 2,
				dateList: [{
						value: 0,
						text: "近1月"
					},
					{
						value: 1,
						text: "近3月"
					},
					{
						value: 2,
						text: "近半年"
					}
				],
				list: []
			};
		},
		onLoad() {
			this.getMemberExchangeLog()
		},
		methods: {
			getMemberExchangeLog() {
				this.http({
					url: 'getMemberExchangeLog',
					method: 'GET',
					data: {
						memberId: uni.getStorageSync('memberId'),
						dateType: this.dateVal
					},
					success: res => {
						if (res.code == 0) {
							this.list =  res.data
						} else {
							let msg = res.msg;
							if (!msg) {
								msg = '系统错误';
							}
							this.$toast.toast(msg);
						}
					}
				})
			},
			changeD(e) {
				console.log("e:", e)
			},
			goPage(item) {
				uni.navigateTo({
					url: './logisticsDetails?productData=' + JSON.stringify(item)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/.uni-select {
		border: none;
		border-radius: 0;
		padding: 0 25rpx;
	}

	/deep/ .uniui-bottom:before,
	/deep/.uniui-top:before {
		content: "";
	}

	/deep/.uni-select__input-text {
		font-size: 28rpx;
	}

	.listStyle {
		width: 85%;
		margin: 30rpx auto;
		border: 2rpx solid #ccc;
		padding: 20rpx 30rpx;
		border-radius: 10rpx;
	}
</style>