<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="bacf f16" style="padding: 2rpx 40rpx;" v-if="isAdmin">
			<view class="fb" style="padding-top: 30rpx;">门店信息</view>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">企业名称</view>
				</u-col>
				<u-col span="6">
					<u--text :lines="1" :text="infoList.companyName || ''" color="#999" align="right" />
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">企业类型</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.companyType || ''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">法定代表人</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.legalRepresentative || ''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">统一社会信用代码</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.businessCode || ''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">营业执照</view>
				</u-col>
				<u-col span="6">
					<view class="red text-r fb" @click="yyzzUrlFlag=true">点击查看</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">门头照</view>
				</u-col>
				<u-col span="6">
					<view class="red text-r fb" @click="storeHeadImgFlag=true">点击查看</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">店内照</view>
				</u-col>
				<u-col span="6">
					<view class="red text-r fb" @click="storeInImgFlag=true">点击查看</view>
				</u-col>
			</u-row>
		</view>

		<view class="bacf f16" style="padding: 2rpx 40rpx;margin-top: 40rpx;" v-if="isAdmin">
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="fb">银行卡账号</view>
				</u-col>
				<u-col span="6">
					<view class="red text-r fb" @click="changeCard">更改绑定</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">银行卡户名</view>
				</u-col>
				<u-col span="6">
					<u--text :lines="1" :text="infoList.accountName || ''" color="#999" align="right" />
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">银行卡号</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.bankCode || ''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">所属银行</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.bankName || ''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">开户地</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.accountOpenAddress ||''}}</view>
				</u-col>
			</u-row>
			<u-row customStyle="margin: 40rpx auto">
				<u-col span="6">
					<view class="">开户支行</view>
				</u-col>
				<u-col span="6">
					<view class="c9 text-r">{{infoList.accountOpenBank || ''}}</view>
				</u-col>
			</u-row>
		</view>
		<u-popup :show="yyzzUrlFlag" @close="yyzzUrlFlag = false">
			<view>
				<uni-section title="营业执照" type="line" padding>
					</u-search>
					<u-empty text="暂未上传营业执照!" width="100" textSize="18" v-if="!infoList.businessLicenseImg"
						customStyle="padding-top:200rpx">
						<view style="height: 200rpx;"></view>
					</u-empty>
					<scroll-view v-if="infoList.businessLicenseImg" scroll-y="true" class="scroll-Y"
						style="height: calc(80vh - 80px);">
						<image style="width: 100%;" class="imgBox" :src="infoList.businessLicenseImg" mode="widthFix">
						</image>
					</scroll-view>
					<u-button v-if="infoList.businessLicenseImg" text="保存到相册" shape="circle" color="#1e1848"
						@click="saveImg" throttleTime="2000"
						customStyle="width:90%;margin:50rpx auto;letter-spacing: 2rpx;color:#f6cc70"></u-button>
				</uni-section>
			</view>
		</u-popup>

		<u-popup :show="storeHeadImgFlag" @close="storeHeadImgFlag = false">
			<view>
				<uni-section title="门头照" type="line" padding>
					</u-search>
					<scroll-view scroll-y="true" class="scroll-Y" style="height: calc(80vh - 80px);">
						<img style="width: 100%;" class="imgBox" :src="infoList.storeHeadImg||imgUpload" mode="widthFix"
							@click="uploadImg(0)">
						</img>
					</scroll-view>
					<u-button text="更新门头照" shape="circle" color="#1e1848" @click="updateStoreImg" throttleTime="2000"
						customStyle="width:90%;margin:50rpx auto;letter-spacing: 2rpx;color:#f6cc70"></u-button>
				</uni-section>
			</view>
		</u-popup>

		<u-popup :show="storeInImgFlag" @close="storeInImgFlag = false">
			<view>
				<uni-section title="店内照" type="line" padding>
					</u-search>
					<scroll-view scroll-y="true" class="scroll-Y" style="height: calc(80vh - 80px);">
						<img style="width: 100%;" class="imgBox" :src="infoList.storeInImg||imgUpload" mode="widthFix"
							@click="uploadImg(1)">
						</img>
					</scroll-view>
					<u-button text="更新店内照" shape="circle" color="#1e1848" @click="updateStoreImg" throttleTime="2000"
						customStyle="width:90%;margin:50rpx auto;letter-spacing: 2rpx;color:#f6cc70"></u-button>
				</uni-section>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isAdmin: true,

				yyzzUrlFlag: false,
				storeInImgFlag: false,
				storeHeadImgFlag: false,
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				memberId: uni.getStorageSync("memberId") || null,
				infoList: {},
			};
		},
		mounted() {
			let roleId = uni.getStorageSync('roleId') || 0
			let roleIdList = [1, 66, 77, 95]
			if (roleIdList.findIndex(item => item == roleId) == -1) {
				this.isAdmin = false
				return this.$refs.uNotify.warning('暂无权限访问该页面！')
			}
			this.getStoreData()
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				if (obj.t && this.isAdmin) {
					this.storeHeadImgFlag = true
				}
			}
		},
		methods: {
			saveImg() {
				uni.downloadFile({
					url: this.infoList.businessLicenseImg,
					success: res => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: res => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: err => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			uploadImg(value) {
				const url = 'https://api2.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.infoList.storeHeadImg = result.data
								} else if (value == 1) {
									this.infoList.storeInImg = result.data
								}
							}
						});
					}
				});
			},
			getStoreData() {
				this.http({
					url: "getStoreData",
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					hideLoading: true,
					method: "GET",
					success: res => {
						if (res.code == 0) {
							this.infoList = res.data
						}
					}
				})
			},
			updateStoreImg(value) {
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/system/updateStoreImg",
					data: {
						id: uni.getStorageSync('selectStoreId'),
						storeInImg: this.infoList.storeInImg,
						storeHeadImgFlag: this.infoList.storeHeadImgFlag,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					method: "POST",
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('更新成功！')
							this.storeInImgFlag = false
							this.storeHeadImgFlag = false
						}
					}
				})
			},
			changeCard() {
				uni.navigateTo({
					url: './cardBinding'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	page {
		background-color: #f6f6f6;
	}
</style>