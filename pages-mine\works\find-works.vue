<template>
	<view v-if="show">

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 下方弹出选择器 -->
		<u-picker :show="showPicker" @cancel="showPicker = false" :columns="storeList" @confirm="confirmState"
			@change="changeHandler" keyName="storeName"></u-picker>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<view class="top-tab2">
			<text @click="weChatCodeFlag=true">企业微信码</text>
		</view>

		<view class="top-tab" v-if="isAdmin">
			<text @click="openMineNeed()" @longpress="openWorkSearch()">我的需求</text>
		</view>

		<view class="btn-top">
			<text @click="openBaoMuGuanjia()" v-if="isAdmin">想快速成单来这里</text>
			<text v-else>寻找合适线索，快速接单吧</text>
		</view>

		<view class="popup-picker" v-if="popupShowOrderBy">
			<view class="picker-triangle"></view>
			<view class="picker-tab flac-col">
				<view v-for="(item,index) in orderByList" :key="index" @click="choiceOrderBy(index)">
					{{item.text}}
				</view>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab" v-if="isAdmin">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="请输入线索编号、客户电话等"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">
							<!-- 							<view class="tab" v-if="isAdmin">
								<view class="tab-title-choice">
									<text>我的线索</text>
									<img @click="isMineOrderNeed=!isMineOrderNeed"
										:src="isMineOrderNeed?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
										mode="widthFix" />
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>所属门店</text>
								</view>
								<view class="tab-picker" @click="openPicker(0)">
									<text class="picker-text">{{ searchCondition.storeName }}</text>
									<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
								</view>
							</view> -->

							<view class="tab" v-if="isAdmin">
								<view class="tab-title">
									<text>需求状态</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in flowStatusList" :key="index">
									<view class="checkbox" :class="{activeBox: index==choiceFlowStatusIndex}">
										<text @click="choiceBox(2,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作地点</text>
								</view>
								<view class="tab-inputbox">
									<view class="tab-input"><input class="single-input" v-model="searchCondition.street"
											placeholder="请输入搜索地点" /></view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作类型</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in workTypeList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.value==choiceWorTypeIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(1,index)">{{tabList.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工资区间</text>
								</view>
								<view class="tab-range">
									<input class="range-input" type="number" v-model="searchCondition.salaryLow"
										placeholder="最低" />
									<text class="range-input-interval"> — </text>
									<input class="range-input1" type="number" v-model="searchCondition.salaryHigh"
										placeholder="最高" />
									<text class="range-input-unit">元</text>
								</view>
							</view>

							<u-gap height="120"></u-gap>
						</scroll-view>
					</view>
				</view>

				<view class="filter-button w82">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="startFilter()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 我的需求弹窗 -->
		<u-popup :show="popupShowMine" mode="bottom" @close="popupShowMine = false">
			<view class="filter-title">
				<text>我的需求</text>
			</view>

			<view class="filter-content">
				<view class="filter-tab">
					<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
						<view class="need-tab" v-for="(order, index) in orderNeedsChoiceList" :key="index">
							<view class="tab-head-bar">
								<text class="bar-title">{{order.productName}}</text>
								<text class="bar-price">{{formatSalary(order.salary,order.afterSalesFee)}}</text>
							</view>
							<view class="tab-content" @click="openWorksDetail(index)">
								<view class="tab-item">
									<view class="item-title">
										<text>所属门店：</text>
									</view>
									<view class="item-text">
										<text>{{checkStr(order.sname)}}</text>
									</view>
								</view>

								<view class="tab-item">
									<view class="item-title">
										<text>工作地点：</text>
									</view>
									<view class="item-text">
										<text>{{formatAddress(order.street)}}</text>
									</view>
								</view>

								<view class="tab-item">
									<view class="item-title">
										<text>工作内容：</text>
									</view>
									<view class="item-text">
										<text>{{checkStr(order.workContent)}}</text>
									</view>
								</view>

								<view class="tab-item">
									<view class="item-title">
										<text>工作要求：</text>
									</view>
									<view class="item-text">
										<text>{{checkStr(order.workRequire)}}</text>
									</view>
								</view>

								<view class="tab-item" v-if="isAdmin">
									<view class="item-title">
										<text>需求状态：</text>
									</view>
									<view class="item-text">
										<text>{{formatFlowStatus(order.flowStatus)}}</text>
									</view>
								</view>

							</view>
						</view>

						<u-empty v-if="orderNeedsChoiceIdList.length==0" text="暂未选择"
							icon="http://cdn.uviewui.com/uview/empty/data.png" />
						<u-gap height="60"></u-gap>
					</scroll-view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<button class="filter-button-left" @click="cleanFilterNeed()">重置
					</button>
				</view>
				<view style="width: 60%;height: 120rpx;">
					<button class="filter-button-right" open-type='share'>去发布
					</button>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>线索日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{checkStr(item.title)}}</text>
					</view>
					<text v-if="formatLongStr(item.message)!='暂无'">{{formatLongStr(item.message)}}</text>
					<text>时间：{{item.createTime}}</text>
					<text>操作人：{{checkStr(item.operator)}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<u-popup :show="popupShowOrder" mode="center" @close="popupShowOrder = false">
			<view @click="saveToPhone()">
				<img :src="shareImg" style="width: 600rpx;height: auto;display: block;" mode="widthFix" />
			</view>
		</u-popup>

		<!-- 头部菜单栏 -->
		<view class="head-menu">
			<view class="flac-row-b">
				<view class="w8" style="margin-left: 40rpx;">
					<u-sticky>
						<u-tabs :list="menuList" @click="choiceMenu" :current="current" lineWidth="22" lineHeight="8"
							:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
						  color: '#1e1848',
						  fontWeight: 'bold',
						  transform: 'scale(1.1)'
					  }" :inactiveStyle="{
						  color: '#333',
						  transform: 'scale(1.05)'
					  }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
						</u-tabs>
					</u-sticky>
				</view>
				<view class="w2 flac-row" @click="popupShow=true" v-if="!isShare" style="line-height: ;">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
						size="15"></u-icon>
					<text class="f16 t-indent">筛选</text>
				</view>
			</view>

			<view class="flac-row-b">
				<view class="choice-menu1">
					<view class="choice-item" style="width: 140rpx;" v-for="(choiceList, index) in choiceList"
						:key="index" @click="choiceTab(index)" v-if="choiceList.show">
						<text :class="{activeChoice: choiceIndex == index}"
							class="choice-title">{{choiceList.choiceTitle}}</text>
					</view>
				</view>

				<view class="flac-row-b f16" style="margin-right: 40rpx;" @click="popupShowOrderBy=!popupShowOrderBy">
					<text>{{orderByList[choiceOrderByIndex].text}}</text>
					<uni-icons :type="popupShowOrderBy?'top':'bottom'" size="20"></uni-icons>
				</view>
			</view>

			<uni-transition mode-class="fade" :show="popupShowFilter">
				<view class="filter-popup" v-show="popupShowFilter">
					<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
						<!-- 		<view class="tab" id="fiter0">
							<view class="tab-title">
								<text>全国（省/市）</text>
							</view>
							<view class="tab-checkbox" v-for="(tabList,index) in workTypeList" :key="index">
								<view class="checkbox" :class="{activeBox: tabList.value==choiceWorTypeIndex}">
									<text v-model="tabList.value"
										@click="choiceBox(1,index)">{{tabList.showText}}</text>
								</view>
							</view>
						</view> -->
						<view class="tab" id="fiter0" v-if="choiceIndex==0">
							<uni-search-bar class="w85 mg-at" placeholder="城市搜索" bgColor="#f6f8fa" :radius="100"
								v-model="searchStreet" cancelButton="none" @search="search" @confirm="search"
								@input="change">
								<uni-icons slot="searchIcon" color="#2261ff" size="18" type="search" />
							</uni-search-bar>
							<view class="flac-row-b" style="flex-wrap: wrap;align-items: unset;height: 1000rpx;">
								<view class="w3 f15 lh60 text-c navStyle">
									<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
										<view class="w10 mg-at flac-row" v-for="(item,i) in cityList" :key="i"
											@click="selectCity(i)">
											<view class="w10 mg-at lineStyle" v-if="i == choiceCityIndex"></view>
											<view :class="i == choiceCityIndex ? 'w10 mg-at btnStyle' : 'w10 mg-at c6'">
												{{item.name}}
											</view>
										</view>
									</scroll-view>
								</view>
								<view class="w7">
									<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
										<u-radio-group iconPlacement="right" placement="column" borderBottom>
											<u-radio :customStyle="{margin: '30rpx 20rpx'}" label="全部"
												@change="selectArea(-1)"></u-radio>
											<u-radio :customStyle="{margin: '30rpx 20rpx'}"
												v-for="(item, i) in areaList" :key="i" :label="item.name"
												:name="item.id" @change="selectArea(i)" />
										</u-radio-group>
									</scroll-view>
								</view>
							</view>
						</view>

						<view class="tab" id="fiter1" v-if="choiceIndex==1">
							<view class="tab-checkbox" v-for="(tabList,index) in workTypeList" :key="index">
								<view class="checkbox" :class="{activeBox: tabList.value==choiceWorTypeIndex}">
									<text v-model="tabList.value"
										@click="choiceBox(1,index)">{{tabList.showText}}</text>
								</view>
							</view>
						</view>

						<view class="tab" id="fiter2" v-if="choiceIndex==2">
							<view class="tab-checkbox" v-for="(tabList,index) in salaryList" :key="index">
								<view class="checkbox" :class="{activeBox: tabList.value==choiceSalaryIndex}">
									<text v-model="tabList.value" @click="choiceBox(3,index)">{{tabList.text}}</text>
								</view>
							</view>
							<view class="tab-range">
								<input class="range-input" type="number" v-model="searchCondition.salaryLow"
									placeholder="最低" />
								<text class="range-input-interval"> — </text>
								<input class="range-input1" type="number" v-model="searchCondition.salaryHigh"
									placeholder="最高" />
								<text class="range-input-unit">元</text>
							</view>
						</view>
					</scroll-view>
					<view class="btn-group">
						<button @click="cleanFilter()"
							style="border: #dedede 1px solid;background-color: #f9f9f9;color: #000;">重置</button>
						<button @click="startFilter()">确定</button>
					</view>
				</view>
			</uni-transition>

		</view>

		<!-- 消息气泡 -->
		<view style="height: 60rpx;" v-if="total!=0">
			<uni-transition mode-class="fade" :duration="tipsBoxDuration" :show="tipsBoxShow&&total!=0">
				<view class="tips-box" style="width: 94%;margin: 0 3%;">
					<text>{{formatTipsBox(tipsBoxIndex)}}</text>
				</view>
			</uni-transition>
		</view>


		<!-- 需求信息栏目 -->
		<view class="need-tab" v-for="(order, index) in orderNeedsList" :key="index">
			<view class="tab-head-bar">
				<view v-if="isAllowChoice" style="margin: 0 40rpx;">
					<img @click="choiceItem(index)" style="width: 60rpx;height: 60rpx;"
						:src="order.isCheck?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
						mode="widthFix" />
				</view>
				<text class="bar-title">{{order.productName}}</text>
				<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#1e1848"
					@click="openLog(index)" v-if="isAdmin&&isMineOrderNeed">
				</uni-icons>
				<uni-icons @click="playAudio(index)" type="sound-filled" style="margin-left: 10rpx;" size="20"
					color="#1e1848" v-if="checkStr(order.introduceVoice)!='暂无'">
				</uni-icons>
				<text class="bar-price">{{formatSalary(order.salary,order.afterSalesFee)}}</text>
			</view>
			<view class="tab-content">
				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>所属门店：</text>
					</view>
					<view class="item-text">
						<text>{{checkStr(order.sname)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作地点：</text>
					</view>
					<view class="item-text">
						<text>{{formatAddress(order.street)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作内容：</text>
					</view>
					<view class="item-text">
						<text>{{checkStr(order.workContent)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作要求：</text>
					</view>
					<view class="item-text">
						<text>{{checkStr(order.workRequire)}}</text>
					</view>
				</view>

				<view class="tab-item" v-if="isAdmin" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>需求状态：</text>
					</view>
					<view class="item-text">
						<text>{{formatFlowStatus(order.flowStatus)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)" v-if="order.workTime">
					<view class="item-title">
						<text>{{(order.productId == 66|| order.productId==67)?'预产日期：':'上户日期：'}}</text>
					</view>
					<view class="item-text">
						<text>{{formatDate(order.workTime)}}</text>
					</view>
				</view>

				<freeAudio startPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_play.png'
					endPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_stop.png'
					audioId='audio1' :url='order.introduceVoice'
					v-if="order.introduceVoice&&isShowAudio&&showAudioIndex==index" activeColor="#1e1848"
					:isPlay="isShowAudio" />

				<!-- 				<view class="tab-item">
					<view class="item-title">
						<text>其它备注：</text>
					</view>
					<view class="item-text">
						<text>{{formatRemarkStr(order.agentRemark)}}</text>
					</view>
				</view> -->

			</view>

			<view class="tab-bottom">
				<view class="bottom-img" @click="openClewPage(index)">
					<img :src="order.agentHeadImg||blankHeadImg">
				</view>
				<view class="f14 bottom-title" @click="openClewPage(index)">
					<text class="lh30">{{formatStr(0,1,order.realName)}}老师</text>
					<text class="f12 c9">{{formatDate(order.startTime)}}发布</text>
				</view>
				<view class="bottom-button">
					<button @click="openWorksDetail(index)">我要接单</button>
				</view>
			</view>
		</view>

		<u-popup :show="weChatCodeFlag" @close="weChatCodeFlag = false">
			<uni-section title="微信二维码" type="line" padding></uni-section>
			<view style="margin-left: 20rpx;" @click="uploadImg">点击更新微信二维码</view>
			<scroll-view scroll-y="true" class="scroll-Y" style="height:80vh;">
				<image :src="weChatCodeImgUrl  ? weChatCodeImgUrl : src1" class="w10" mode="widthFix"
					@click="uploadImg"></image>
				<image v-if="!weChatCodeImgUrl"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1704962742017b80d187f1330c74099582095b927879.jpg"
					class="w10" mode="widthFix" @click="uploadImg"></image>

			</scroll-view>
		</u-popup>

		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>

		<u-empty v-if="orderNeedsList.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

	</view>
</template>

<script>
	import freeAudio from '@/pages-mine/common/components/free-audio.vue'
	export default {
		components: {
			freeAudio
		},
		name: 'findWorks',
		props: {
			startLoadMore: {
				type: Number
			},
			showHeadLine: {
				type: Boolean,
				default: true,
			}
		},
		data() {
			return {
				weChatCodeImgUrl: uni.getStorageSync('weChatCodeImgUrl'),
				// 可配置选项
				// 控制测试数据是否显示
				showTestData: false,
				weChatCodeFlag: false,
				src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17048801043158533717a20af16e170cd00823ca5a13.png',
				// 查询时间范围（单位：天）
				dateRange: 7,
				// 查询位置范围（单位：公里）
				districtRange: 3,
				/// 是否管理员
				isAdmin: false,
				// 是否可供选择
				isAllowChoice: false,
				// 是否为分享链接
				isShare: false,
				// 显示语音播报组件
				isShowAudio: true,

				// 是否显示消息气泡
				tipsBoxShow: true,
				// 气泡消息轮播数量
				tipsBoxLimit: 30,
				// 气泡消息轮播间隔（单位：s）
				tipsBoxInterval: 5,
				// 动画速度（单位：ms）
				tipsBoxDuration: 800,

				tipsBoxPlayTime: 0,
				tipsBoxIndex: 0,
				flagShare: 0,
				showAudioIndex: -1,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,
				showPicker: false,
				show: false,
				popupShow: false,
				popupShowMine: false,
				popupShowLog: false,
				popupShowOrder: false,
				popupShowFilter: false,
				popupShowOrderBy: false,

				searchText: "",
				choiceWorTypeIndex: 0,
				choiceFlowStatusIndex: 0,
				choiceSalaryIndex: 0,
				choiceOrderByIndex: 0,
				choiceCityIndex: 0,

				choiceWorType: "",
				salaryLow: null,
				salaryHigh: null,
				memberId: null,
				baomuId: null,
				employeeId: null,
				employee: {
					lat: null,
					lng: null
				},

				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				current: 0,
				choiceIndex: 0,
				total: 0,
				totalNow: 0,
				storeIndex: 0,
				storeList: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				shareImg: '',

				cityList: uni.getStorageSync('citylist'),
				getClist: uni.getStorageSync('citylist'),
				areaList: [],
				cityId: 0,
				areaId: 0,
				cityName: '',
				searchStreet: '',
				menuList: [{
						index: 0,
						name: '推荐工作',
					},
					{
						index: 1,
						name: '附近工作',
					}
				],
				choiceList: [{
						choiceTitle: "区域",
						value: 0,
						show: true
					}, {
						choiceTitle: "工种",
						value: 1,
						show: true
					},
					{
						choiceTitle: "工资",
						value: 2,
						show: true
					},
				],
				productList: [{
						name: '保姆',
						productIdList: [76, 77, 78, 79, 172, 226, 290],
						index: 0,
						name: '最新工作',
						orderBy: 'ons.startTime DESC'
					},
					{
						name: '月嫂',
						productIdList: [66, 67, 68],
						index: 1,
						name: '人气工作',
						orderBy: 'ons.shareCount DESC'
					},
					{
						name: '育儿嫂',
						productIdList: [999],
					},
					{
						name: '保洁',
						productIdList: [66, 67, 78, 79, 282, 322, 332, 336, 356],
					},
					{
						name: '护工',
						productIdList: [223, 230],
					},
					{
						name: '陪读师',
						productIdList: [336],
					}
				],
				orderByList: [{
					index: 0,
					text: '时间',
					value: 'ons.startTime DESC'
				}, {
					index: 1,
					text: '薪资',
					value: 'ons.salary DESC'
				}, {
					index: 2,
					text: '人气',
					value: 'ons.shareCount DESC'
				}],
				workTypeList: [{
						text: '',
						showText: "不限",
						value: 0
					}, {
						text: '住家',
						showText: "住家",
						value: 1
					}, {
						text: '不住家',
						showText: "不住家",
						value: 2
					},
					{
						text: '单餐',
						showText: "单餐",
						value: 3
					}, {
						text: '中餐',
						showText: "单餐(中)",
						value: 4
					}, {
						text: '晚餐',
						showText: "单餐(晚)",
						value: 5
					}, {
						text: '月嫂',
						showText: "月嫂",
						value: 6
					}, {
						text: '育儿嫂',
						showText: "育儿嫂",
						value: 7
					}, {
						text: '钟点',
						showText: "钟点",
						value: 8
					}
				],
				remarkList: [{
						text: "带宝宝（带睡)",
						showText: "宝宝带睡",
						isCheck: 0
					},
					{
						text: "带宝宝（不带睡)",
						showText: "宝宝不带睡",
						isCheck: 0
					},
					{
						text: "照顾产妇",
						showText: "照顾产妇",
						isCheck: 0
					},
					{
						text: "看护病人",
						showText: "看护病人",
						isCheck: 0
					},
					{
						text: "看护老人",
						showText: "看护老人",
						isCheck: 0
					},
					{
						text: "做饭",
						showText: "做饭",
						isCheck: 0
					},
					{
						text: "纯做饭",
						showText: "纯做饭",
						isCheck: 0
					},
					{
						text: "做卫生",
						showText: "做卫生",
						isCheck: 0
					},
					{
						text: "纯做卫生",
						showText: "纯做卫生",
						isCheck: 0
					},
				],
				flowStatusList: [{
					text: '不限',
					value: -1
				}, {
					text: '电联',
					value: 0
				}, {
					text: '需求',
					value: 1
				}, {
					text: '预算',
					value: 2
				}, {
					text: '匹配',
					value: 3
				}, {
					text: '面试',
					value: 4
				}, {
					text: '签约',
					value: 5
				}, {
					text: '结算',
					value: 6
				}],
				// 薪资范围
				salaryList: [{
					value: 0,
					text: '不限',
					salaryLow: null,
					salaryHigh: null,
				}, {
					value: 1,
					text: '1000-3000',
					salaryLow: 1000,
					salaryHigh: 3000,
				}, {
					value: 2,
					text: '3000-6000',
					salaryLow: 3001,
					salaryHigh: 6000,
				}, {
					value: 3,
					text: '6000-8000',
					salaryLow: 6100,
					salaryHigh: 8000,
				}, {
					value: 4,
					text: '8000-10000',
					salaryLow: 8001,
					salaryHigh: 10000,
				}, {
					value: 5,
					text: '10000-15000',
					salaryLow: 10001,
					salaryHigh: 15000,
				}, {
					value: 6,
					text: '15000以上',
					salaryLow: 15001,
					salaryHigh: null,
				}],
				searchCondition: {
					storeName: "",
					// search: "",
					search: "",
					status: null,
					productName: "",
					agentId: null,
					channel: null,
					productId: null,
					lat: null,
					lng: null,
					district: null,
					dateRange: null,
					remarkId: null,
					salaryLow: null,
					salaryHigh: null,
					isPush: null,
					street: '',
					// isPush: 1,
					flowStatus: -1,
					isAgencyFee: null,
					isSalary: 1,
					isStoreName: null,
					idList: null,
					isMine: null,
					orderBy: 'ons.startTime DESC',
					current: 1,
					size: 20
				},
				isMineOrderNeed: false,
				searchParam: '',
				orderList: [],
				orderNeedsList: [],
				orderNeedsChoiceList: [],
				orderNeedsChoiceIdList: [],
				idList: [],
				shareContent: {
					title: '',
					path: '',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: ''
				},
				firstOrderNeeds: {},
				checkType: 0,
				checkTitle: "",
				checkText: "",
				logList: []
			}
		},
		methods: {
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					uni.chooseImage({
						success: (chooseImageRes) => {
							const tempFilePaths = chooseImageRes.tempFilePaths;
							uni.uploadFile({
								url: 'https://api.xiaoyujia.com/system/imageUpload',
								filePath: tempFilePaths[0],
								name: 'file',
								formData: {},
								dataType: 'json',
								success: (uploadFileRes) => {
									let result = JSON.parse(uploadFileRes.data)
									this.http({
										url: 'uploadWeChatCodeImgUrl',
										data: {
											id: uni.getStorageSync("employeeId"),
											weChatCodeImgUrl: result.data
										},
										header: {
											"content-type": "application/json;charset=UTF-8"
										},
										method: 'POST',
										success: res => {
											uni.showToast({
												title: res.msg,
												icon: 'none'
											})
											if (res.code == 0) {
												uni.setStorageSync('weChatCodeImgUrl',
													result.data)
												this.weChatCodeImgUrl = result.data
											}
										}
									})
								}
							});
						}
					});

				}
			},
			uploadImg() {
				this.weChatCodeFlag = false
				this.openCheck(1, "是否更新微信二维码?")
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			change(e) {
				if (e == '') {
					this.selectCity(0)
				}
			},
			search(e) {
				console.log(e)
				let value = e.value
				// value自动接收输入框中的内容
				if (value == '') {
					//如果输入的值为空则加载所有的列表
					uni.showToast({
						icon: 'none',
						title: '无匹配内容'
					})
				} else {
					//先清空展示的数据
					this.cityList = []
					this.areaList = []
					let haveData = false
					//然后开始循环全部数据
					for (var i = 0; i < this.getClist.length; i++) {
						//判断数据里面是否有符合输入的内容  不符合返回-1 只需要大于或等于0就是符合
						//（核心所在，其它都是根据需求来自己写）
						if (this.getClist[i].name.indexOf(value) >= 0) {
							this.cityList.push(this.getClist[i])
							haveData = true
						}
					}
					if (!haveData) {
						this.cityList = uni.getStorageSync('citylist')
						this.selectCity(0)
						uni.showToast({
							icon: 'none',
							title: '无匹配内容'
						})
					}
					this.selectCity(0)
				}
			},
			// 打开选择器
			openPicker(index) {
				if (index == 0) {
					this.showPicker = true
					this.popupShow = false
				}
			},
			// 选择器确认
			confirmState(e) {
				this.showPicker = false
				this.popupShow = true
				this.searchCondition.storeName = this.storeList[0][this.storeIndex].storeName
			},
			// 选择器选项变化
			changeHandler(e) {
				const {
					index
				} = e;
				this.storeIndex = index
			},
			// 选择菜单（一级）
			choiceMenu(e) {
				this.stopAudio()
				this.current = e.index
				this.refreshList()
			},
			// 选择菜单（二级）
			choiceTab(index) {
				this.stopAudio()
				this.popupShowFilter = !this.popupShowFilter
				if (this.popupShowFilter) {
					uni.pageScrollTo({
						selector: '#fiter' + index,
						duration: 300
					})
				}
				this.choiceIndex = index
			},
			// 选中筛选单选框
			choiceBox(value, index) {
				if (value == -1) {

				} else if (value == 1) {
					this.choiceWorTypeIndex = this.workTypeList[index].value
					this.choiceWorType = this.workTypeList[index].text
				} else if (value == 2) {
					this.choiceFlowStatusIndex = index
				} else if (value == 3) {
					this.choiceSalaryIndex = index
				}
			},
			// 选中我的需求
			choiceItem(index) {
				let orderNeed = this.orderNeedsList[index]
				let id = orderNeed.id
				if (orderNeed.isCheck) {
					this.orderNeedsList[index].isCheck = false
					for (let i = 0; i < this.orderNeedsList.length; i++) {
						if (this.orderNeedsChoiceIdList[i] == id) {
							this.$delete(this.orderNeedsChoiceList, i)
							this.$delete(this.orderNeedsChoiceIdList, i)
						}
					}
				} else {
					if (this.orderNeedsChoiceIdList.length == 0) {
						this.firstOrderNeeds = this.orderNeedsList[index]
					}
					this.orderNeedsList[index].isCheck = true
					this.orderNeedsChoiceList.push(this.orderNeedsList[index])
					this.orderNeedsChoiceIdList.push(this.orderNeedsList[index].id)
				}
				console.log("输出现在的选择：", this.orderNeedsChoiceIdList)
			},
			// 选中排序方式
			choiceOrderBy(index) {
				this.choiceOrderByIndex = index
				this.popupShowOrderBy = false
				this.refreshList()
			},
			// 刷新列表
			refreshList() {
				let index = this.current
				this.clearTabChoice()
				if (index == 0) {
					this.searchCondition.isPush = 1
				} else if (index == 1) {
					this.searchCondition.district = this.districtRange
					this.searchCondition.lat = this.lat || uni.getStorageSync("lat")
					this.searchCondition.lng = this.lng || uni.getStorageSync("lng")
				} else if (index == 2) {
					this.isMineOrderNeed = true
				}
				// else if (index == 2) {
				// 	this.searchCondition.dateRange = this.dateRange
				// }

				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {
				this.searchCondition.district = null
				this.searchCondition.lat = null
				this.searchCondition.lng = null
				this.searchCondition.dateRange = null
				this.searchCondition.isPush = null
				this.isMineOrderNeed = false
			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
				this.searchCondition.search = ""
				this.searchCondition.productName = ""
			},
			// 清空筛选条件（搜索框、选择器和单选框）
			cleanFilter() {
				this.searchText = ""
				this.searchStreet = ""
				this.choiceWorTypeIndex = 0
				this.choiceFlowStatusIndex = 0
				this.choiceSalaryIndex = 0
				this.choiceWorType = ""
				this.searchCondition.storeName = ""
				this.searchCondition.street = ""
				this.searchCondition.salaryLow = null
				this.searchCondition.salaryHigh = null

				this.cityList = uni.getStorageSync("citylist")
				this.cityName = ''
			},
			// 开始筛选
			startFilter() {
				this.orderNeedsList = []
				this.totalNow = 0
				this.searchCondition.current = 1
				this.searchCondition.search = this.searchText
				this.searchCondition.productName = this.choiceWorType
				if (this.searchParam) {
					this.searchCondition.productName = this.searchParam
				}
				this.searchCondition.flowStatus = this.flowStatusList[this.choiceFlowStatusIndex].value
				this.searchCondition.salaryLow = this.salaryList[this.choiceSalaryIndex].salaryLow
				this.searchCondition.salaryHigh = this.salaryList[this.choiceSalaryIndex].salaryHigh
				this.searchCondition.orderBy = this.orderByList[this.choiceOrderByIndex].value

				if (this.cityName) {
					this.searchCondition.street = this.cityName
				}
				console.log('查询地区', this.cityName)

				if (this.isMineOrderNeed) {
					this.searchCondition.isMine = 1
					this.searchCondition.agentId = uni.getStorageSync("employeeId") || 0
					this.searchCondition.channel = uni.getStorageSync("merchantCode") || ""
					if (this.choiceFlowStatusIndex == 0) {
						this.searchCondition.flowStatus = null
					}
					this.searchCondition.isSalary = null
					this.searchCondition.isStoreName = null
					this.searchCondition.productId = null
				} else {
					this.searchCondition.isMine = null
					this.searchCondition.agentId = null
					this.searchCondition.channel = null
					// this.searchCondition.isStoreName = 1
					this.searchCondition.isSalary = 1
				}

				if (this.isAdmin) {
					this.searchCondition.isPush = null
					if (this.searchText != '') {
						this.searchCondition.isSalary = null
					}
				} else {
					// 员工查看，则全部置为推荐（可面试的线索）
					this.searchCondition.isPush = 1
				}

				this.getList()
				this.popupShow = false
				this.popupShowFilter = false
			},
			// 清空筛选线索
			cleanFilterNeed() {
				this.orderNeedsChoiceList = []
				this.orderNeedsChoiceIdList = []
				this.isAllowChoice = false
				this.orderNeedsList.forEach(item => {
					item.isCheck = false
				})
				this.popupShowMine = false
				this.$refs.uNotify.success('重置成功！')
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			formatLongStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					let long = 60
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			// 格式化备注字段（隐藏隐私信息）
			formatRemarkStr(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					result = result.replace(/^([^s]*)元,/, " ")
				}
				return result
			},
			formatAddress(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
					}
				}
				return result
			},
			// 格式化期望薪资
			formatSalary(salary, salary1) {
				if (salary1 != null && salary1 != 0) {
					salary = salary1
				}

				// 如果工资字段值存在，则直接返回值
				if (salary != null && salary != 0) {
					return salary + "元/月"
				} else {
					return "工资面议"
				}
			},
			// 对请求参数中的工资范围进行格式化
			formatRange() {
				let salaryLow = this.searchCondition.salaryLow
				let salaryHigh = this.searchCondition.salaryHigh
				if (salaryLow == null || salaryLow == "") {
					this.searchCondition.salaryLow = null
				} else {
					this.searchCondition.salaryLow = parseInt(salaryLow)
				}
				if (salaryHigh == null || salaryHigh == "") {
					this.searchCondition.salaryHigh = null
				} else {
					this.searchCondition.salaryHigh = parseInt(salaryHigh)
				}
			},
			// 格式化线索状态
			formatFlowStatus(value) {
				let result = this.flowStatusList[value + 1].text || "暂无"
				return result
			},
			// 时间格式化
			formatDate(value) {
				if (!value) {
					return "/"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return MM + '-' + d
			},
			formatTipsBox(index) {
				let data = this.orderList[this.tipsBoxIndex]
				if (!data) {
					return
				}

				let sname = data.sname || ''
				sname = sname.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				let empName = data.realName || ''
				let result = '恭喜' + sname + ' ' + empName + ' 成交了'
				let salary = (data.salary != null && data.salary != 0) ? data.salary +
					'元' : ''
				result = result + salary + data.productName + '单'
				return result
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.shareImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			// 打开我的需求面板
			openMineNeed() {
				if (!this.isAllowChoice) {
					this.$refs.uNotify.success('勾选想要推荐的线索进行发布吧！')
					if (this.current != 2) {
						this.current = 2
						this.refreshList()
					}
				}
				console.log("xxx")
				this.popupShowMine = true
				this.isAllowChoice = true

				this.searchCondition.isPush = null
			},
			openWorkSearch() {
				uni.navigateTo({
					url: '/pages-mine/works/works-search'
				})
			},
			openWorksDetail(index) {
				if (this.isAllowChoice) {
					this.choiceItem(index)
					return
				}

				this.stopAudio()
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + this.orderNeedsList[index].id +
						"&flagShare=" +
						this.flagShare
				})
			},
			// 打开线索编辑
			openClewPage(index) {
				if (this.isMineOrderNeed) {
					uni.navigateTo({
						url: "/pages-work/operations/clew/clewPage?id=" + this.orderNeedsList[index].id
					})
				} else {
					uni.navigateTo({
						url: "/pages-other/store/index?id=" + this.orderNeedsList[index].storeId
					})
				}
			},
			selectCity(i) {
				this.choiceCityIndex = i
				this.cityName = this.cityList[i].name
				this.cityName = this.cityName == '全国' ? '' : this.cityName
				this.areaList = this.cityList[i].area
				let arr = this.cityList[i].area
				let arrnew = []
				arr.map(((item, i) => {
					arrnew.push(Object.assign({}, item, {
						disabled: false
					}))
				}))
				this.areaList = arrnew
			},
			selectArea(i) {
				if (i == -1) {
					this.areaId = 0
				} else {
					this.areaId = this.areaList[i].id
					this.cityName = this.areaList[i].name
				}
			},
			// 获取所有门店列表
			getAllStoreList() {
				this.http({
					url: 'getAllStoreList',
					method: 'POST',
					hideLoading: true,
					data: {
						isOpen: false
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList.push(res.data)
						}
					}
				})
			},
			// 获取员工信息
			getEmployee() {
				this.http({
					url: 'getEmployeeDtoById',
					method: 'GET',
					hideLoading: true,
					path: uni.getStorageSync("employeeId"),
					success: res => {
						if (res.code == 0) {
							this.employee = res.data
							this.lat = this.employee.lat
							this.lng = this.employee.lng
						}
					}
				})
			},
			openBaoMuGuanjia() {
				// #ifdef APP-PLUS
				plus.share.getServices(
					res => {
						let sweixin = null;
						for (let i in res) {
							if (res[i].id == 'weixin') {
								sweixin = res[i];
							}
						}
						//唤醒微信小程序
						if (sweixin) {
							sweixin.launchMiniProgram({
								id: 'gh_2939fe27ac86', //
								type: 0, //小程序版本  0-正式版； 1-测试版； 2-体验版。
								path: 'pages/index?tg=' + uni.getStorageSync(
									"employeeNo") //小程序的页面,用传的参数在小程序接值判断跳转指定页面
							});
						}
					}
				);

				// #endif

				// #ifdef MP-WEIXIN
				wx.navigateToMiniProgram({
					appId: 'wxc33730c9e09594f8', // pord
					path: 'pages/index?tg=' + uni.getStorageSync("employeeNo"),
					envVersion: 'release',
					success(res) {
						// 打开成功
					}
				})
				// #endif

				// #ifdef H5
				let param = {
					path: 'pages/index',
					scene: 'tg/' + uni.getStorageSync("employeeNo"),
					source: 'bmgj',
					title: '线索',
					img: null,
					type: 1
				}
				this.http({
					url: 'getEmployeePoster',
					method: 'POST',
					hideLoading: true,
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.shareImg = res.data
						this.popupShowOrder = true
					},
					fail: err => {
						console.log(res)
					}
				});
				// #endif
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.orderNeedsList[index].id
				this.http({
					url: "getOrderNeedsById",
					data: {
						orderNeedsId :id
					},
					method: 'get',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.logList = res.data.orderNeedsLogs
						} else {
							this.logList = []
						}
					}
				})
			},
			// 获取订单列表
			getOrderList() {
				this.http({
					url: 'getOrderNeedsPage',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						status: [2],
						orderBy: "ons.startTime DESC",
						current: 1,
						size: this.tipsBoxLimit
					},
					success: res => {
						this.orderList = res.data.records
						if (res.data.pages <= 1) {
							this.tipsBoxLimit = res.data.total
						}
					}
				})
			},
			// 获取线索列表
			getList() {
				this.formatRange()
				let data = this.searchCondition
				if (this.isShare) {
					this.$set(data, "isPush", null)
					this.$set(data, "dateRange", null)
					this.$set(data, "productId", null)
					this.$set(data, "isSalary", null)
					this.$set(data, "isStoreName", null)
					this.$set(data, "flowStatus", null)
					this.$set(data, "status", null)
				}
				console.log("输出查询条件：", JSON.stringify(data))
				this.http({
					url: 'getOrderNeedsPage',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							let data = res.data.records
							// 追加选择位
							if (this.isAdmin) {
								data.forEach(item => {
									this.$set(item, "isCheck", false)
								})
							}
							this.orderNeedsList = this.orderNeedsList.concat(data)
						}
					}
				})
			},
			// 添加线索分享日志
			addOrderNeedsShareLog() {
				this.http({
					url: 'addOrderNeedsShareLog',
					method: 'POST',
					hideLoading: true,
					data: {
						operatorId: uni.getStorageSync("employeeId") || 0,
						memberId: uni.getStorageSync("memberId") || null,
						typeDetail: this.flagShare,
						orderNeedsIdList: this.orderNeedsChoiceIdList
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						console.log(res.msg)
					}
				})
			},
			// 分享
			getWxShareImg() {
				let sname = this.firstOrderNeeds.sname || ''
				let productName = '高薪急招'
				let addressDeal = '优秀阿姨快来报名'
				let salary = ''
				let shareBack =
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_orderneed_img1.png'
				// 小羽佳家政xx店高薪业务急招员工
				// let title = sname + productName
				// 小羽佳家政急招！X条高薪业务发布
				let title = '小羽佳家政急招！' + this.orderNeedsChoiceIdList.length + '条高薪业务发布'
				let url = '/pages-mine/works/find-works?show=true&idList=' + JSON.stringify(this
						.orderNeedsChoiceIdList) +
					"&flagShare=1"

				// 文字大小
				const fontSize = 45
				// 文字间隔
				const fontInterval = 20
				// 文本初始x轴坐标
				let originX = 30
				// 文本初始y轴坐标
				let originY = 30

				let data = {
					textList: [{
						text: productName,
						fontSize: this.checkStr(this.firstOrderNeeds.productRemark) != '暂无' ?
							fontSize : fontSize + 10,
						isBold: true,
						x: 100,
						y: 120
					}, {
						text: addressDeal,
						fontSize: fontSize,
						isBold: true,
						x: 100,
						y: 190
					}, {
						text: salary,
						fontSize: this.checkStr(this.firstOrderNeeds.salaryRemark) != '暂无' ? fontSize :
							fontSize +
							10,
						isBold: true,
						x: 100,
						y: 250
					}],
					img: shareBack
				}
				if (this.orderNeedsChoiceIdList.length == 1) {
					productName = this.firstOrderNeeds.productName
					addressDeal = this.formatAddress(this.firstOrderNeeds.street) || this.firstOrderNeeds.street
					salary = this.formatSalary(this.firstOrderNeeds.salary, this.firstOrderNeeds.afterSalesFee)

					title = this.checkStr(this.firstOrderNeeds.productRemark) != '暂无' ? '' + this.firstOrderNeeds
						.productName +
						'-' + this.firstOrderNeeds.productRemark + '' : '' + this.firstOrderNeeds.productName + ''
					let time = this.firstOrderNeeds.workTime ? this.formatDate(this.firstOrderNeeds.workTime) + '上户' :
						'尽快上户'
					title = title + '|' + addressDeal + '|' + time
					url = '/pages-mine/works/works-detail?id=' + this.firstOrderNeeds.id + "&flagShare=1"

					// 添加备注
					productName = this.checkStr(this.firstOrderNeeds.productRemark) != '暂无' ? productName + '(' +
						this
						.firstOrderNeeds
						.productRemark +
						')' : productName
					salary = this.checkStr(this.firstOrderNeeds.salaryRemark) != '暂无' ? salary + '(' + this
						.firstOrderNeeds
						.salaryRemark + ')' : salary

					// 文字大小
					const fontSize = 45
					// 文字间隔
					const fontInterval = 15
					// 行间隔
					const rowInterval = 5
					// 文本初始x轴坐标
					let originX = 30
					// 文本初始y轴坐标
					let originY = 30
					// 工作地点高度
					let salaryRow = (salary.length > 10) || (salary.includes('面议') && salary.length > 7) ? 2 : 1

					let textList = [{
							"text": '',
							// "text": productName + '|' + addressDeal,
							"fontSize": fontSize,
							"isCenter": false,
							maxLineCount: 1,
							// "isBold": true,
							"x": originX,
							"y": originY
						},
						{
							"text": "薪资待遇：" + salary,
							"fontSize": fontSize + 5,
							"color": "0x1e1848",
							"isCenter": false,
							"isBold": true,
							"x": originX,
							// "y": originY + fontSize + fontInterval + rowInterval - 5,
							"y": originY
						},
					]
					originY = originY + (fontSize + fontInterval) * salaryRow + rowInterval + 5
					// 格式化后续文本（客户情况，工作内容，工作要求）
					let content = []
					let timeTitle = (this.firstOrderNeeds.productId == 66 || this.firstOrderNeeds.productId == 67) ?
						'预产日期：' : '上户日期：'
					time = this.firstOrderNeeds.workTime ? this.formatDate(this.firstOrderNeeds.workTime) : '尽快'
					content.push(timeTitle + time)
					content.push("客户情况：" + this.formatRemarkStr(this.firstOrderNeeds.remark))
					// content.push("工作内容：" + this.formatRemarkStr(this.firstOrderNeeds.workContent))
					content.push("工作要求：" + this.formatRemarkStr(this.firstOrderNeeds.workRequire))
					for (let i = 0; i < content.length; i++) {
						let long = 1
						if (i > 0 && content[i].length > 10) {
							long = 2
						}
						textList.push({
							text: content[i],
							fontSize: fontSize,
							isCenter: false,
							// isBold: true,
							maxLineCount: long,
							x: originX,
							y: originY
						})
						originY += (fontSize + fontInterval) * long + rowInterval
					}
					data = {
						textList: textList,
						maxWidth: 720,
						img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_orderneed_img1.png',
					}
				}
				this.$set(this.shareContent, "title", title)
				this.$set(this.shareContent, "path", url)

				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							let shareImg = res.data
							this.$set(this.shareContent, "imageUrl", shareImg)
							this.flagShare = 1
							this.addOrderNeedsShareLog()
							console.log("分享图片：", shareImg)
						} else {
							this.$refs.uNotify.error('分享失败！获取分享图片异常！' + res.msg)
						}
					}
				})
			},
			getCityInfo() {
				uni.request({
					url: 'https://ortherapi.xiaoyujia.com/store/storecity',
					method: 'GET',
					success: (res) => {
						let data = [{
							"cityid": -1,
							"name": "全国",
						}]
						this.cityList = data
						this.cityList = this.cityList.concat(res.data)
						uni.setStorageSync('citylist', this.cityList)
						this.selectCity(0)
					}
				});
			},
			// 分享
			onShareAppMessage(res) {
				return new Promise((resolve, reject) => {
					if (!this.isAdmin) {
						resolve({
							title: '高薪业务急招员工',
							path: '/pages-mine/works/find-works?show=true',
							mpId: 'wx8342ef8b403dec4e',
						})
					} else {
						if (this.orderNeedsChoiceIdList.length == 0) {
							this.$refs.uNotify.error('请至少选择一条要分享的线索！')
						} else {
							this.getWxShareImg()
							wx.showLoading({
								title: '正在获取分享内容...',
								icon: 'none'
							})

							setTimeout(() => {
								wx.hideLoading()
								resolve(this.shareContent)
							}, 1500)
						}
					}
				})
			},
			// 朋友圈
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			// 检查页面类型
			checkPageType() {
				if (this.isAdmin && !this.isShare) {
					uni.setNavigationBarTitle({
						title: "线索广场"
					})
					this.searchCondition.isPush = null

					this.menuList.push({
						index: 2,
						name: '我的',
					})
				}
			},
			playAudio(index) {
				if (this.showAudioIndex == index) {
					this.isShowAudio = !this.isShowAudio
					if (!this.isShowAudio) {
						uni.$emit('stop')
					}
				} else {
					this.showAudioIndex = index
					this.isShowAudio = true
				}
			},
			stopAudio() {
				this.isShowAudio = false
				uni.$emit('stop')
			},

		},
		onUnload() {
			uni.$emit('stop')
		},
		watch: {
			startLoadMore: {
				handler(newValue, oldVal) {
					console.log("滑动到底部加载更多...")
					this.searchCondition.current++
					this.getList()
				},
				deep: true
			},
			loadMore: {
				handler(newValue, oldVal) {
					console.log("滑动到底部加载更多...")
					if (this.isShare) {
						return
					}
					this.searchCondition.current++
					this.getList()
				},
				deep: true
			},
			tipsBoxPlayTime: {
				handler(newValue, oldVal) {
					let speed = this.tipsBoxInterval
					if (this.tipsBoxPlayTime > 1) {
						// 控制消息气泡轮播
						if (this.tipsBoxPlayTime % speed > 0) {
							if (this.tipsBoxPlayTime % speed == 1) {
								this.tipsBoxIndex++
								if (this.tipsBoxIndex == this.tipsBoxLimit) {
									this.tipsBoxIndex = 0
								}
							}
							this.tipsBoxShow = true
						} else if (this.tipsBoxPlayTime % speed == 0) {
							this.tipsBoxShow = false
						}
					}
				},
				deep: true
			},
		},
		onReachBottom() {
			if (this.showHeadLine) {
				this.loadMore++
			}
		},
		onLoad(options) {
			this.show = options.show || false
			this.isAdmin = options.isAdmin || false
			this.flagShare = options.flagShare || 0
			this.searchParam = options.search || ""
			this.isMineOrderNeed = options.isMine || false
			if (this.isMineOrderNeed) {
				this.choiceIndex = 3
			}

			// 测试时加上
			// this.isAdmin = true
			if (options.idList !== undefined) {
				this.idList = JSON.parse(options.idList)
				// this.idList = options.idList
				if (this.idList.length != 0) {
					this.isShare = true
					this.$set(this.searchCondition, "isPush", null)
					this.$set(this.searchCondition, "idList", this.idList)

					// this.choiceList[0].choiceTitle = "筛选"
					// this.choiceList[1].show = false
					// this.choiceList[2].show = false
				}
			}

			this.memberId = uni.getStorageSync('memberId') || 0
			this.employeeId = uni.getStorageSync('employeeId') || 0
			this.checkPageType()
			this.getEmployee()

			this.refreshList()
			this.getAllStoreList()
			this.getOrderList()
			this.getCityInfo()
			let time = setInterval(() => {
				this.tipsBoxPlayTime += 1
			}, 1000);
			if (!uni.getStorageSync('weChatCodeImgUrl')) {
				uni.showModal({
					title: '提示',
					content: '检测到暂未配置企业微信码，去配置？',
					success: function(res) {
						if (res.confirm) {
							this.weChatCodeFlag = true
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}.bind(this)
				});
			}
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/work-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
		width: 100%;
	}

	.top-tab {
		position: fixed;
		z-index: 999;
		top: 10rpx;
		left: 550rpx;
		width: 80%;
		height: 80rpx;

		text {
			display: block;
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}

	.top-tab2 {
		position: fixed;
		z-index: 999;
		top: 10rpx;
		left: 50rpx;
		width: 80%;
		height: 80rpx;

		text {
			display: block;
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}

	// 头部按钮
	.btn-top {
		width: 100%;
		height: 80rpx;

		text {
			display: block;
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			background-color: rgba(30, 24, 72, 0.7);
		}
	}

	// 需求栏目
	.need-tab {
		width: 100%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
		padding: 20rpx 0;
	}

	// 栏目头部
	.tab-head-bar {
		display: block;
		width: 100%;
		height: 80rpx;
	}

	.bar-title {
		display: bolck;
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		line-height: 80rpx;
		margin-left: 40rpx;
		color: #1e1848;
		font-weight: bold;
	}

	.bar-price {
		display: block;
		float: right;
		margin: 20rpx 40rpx 0 0;
		text-align: right;
		width: 400rpx;
		height: 40rpx;
		line-height: 40rpx;
		color: #ff4d4b;
		font-weight: bold;
		font-size: 32rpx;
	}

	// 栏目底部
	.tab-bottom {
		width: 100%;
		height: 120rpx;
		font-size: 36rpx;
		display: flex;
	}

	.bottom-img {
		width: 20%;
		height: 100rpx;

		img {
			display: block;
			margin: 15rpx auto;
			width: 90rpx;
			height: 90rpx;
			border-radius: 50%;
		}
	}

	.bottom-title {
		margin: 20rpx 0;
		width: 52%;
		height: 80rpx;
		display: flex;
		flex-direction: column;

		text {
			height: 40rpx;
			line-height: 40rpx;
		}
	}

	.bottom-button {
		width: 18%;
		margin: 30rpx 0;

		button {
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 20rpx;
			font-size: 32rpx;
			padding: 0 0;
		}
	}

	.log-list {
		width: 90%;
		height: auto;
		padding: 20rpx 5%;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.filter-popup {
		position: absolute;
		z-index: 888;
		width: 100%;
		top: 235rpx;
		background-color: #fff;
		border-radius: 0 0 40rpx 40rpx;
		border-bottom: 2rpx solid #dedede;
		// box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 按钮组
	.btn-group {
		width: 100%;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		padding: 40rpx 0;

		button {
			width: 40%;
			height: 80rpx;
			line-height: 80rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.choice-menu1 {
		display: flex;
		width: 70%;
		height: 40rpx;
		margin: 20rpx 0;
		padding-left: 50rpx;

		text {
			font-size: 32rpx;
		}
	}

	// 弹出-选择栏
	.popup-picker {
		position: absolute;
		top: 13.5vh;
		right: 22rpx;
		z-index: 999;
		width: 140rpx;
		height: auto;
		padding: 0 0 0 0;

		.picker-triangle {
			width: 0;
			height: 0;
			border: 15rpx solid transparent;
			border-bottom-color: #f4f4f5;
			margin-left: 65%;
		}

		.picker-tab {
			width: auto;
			mine-height: 60rpx;
			line-height: 60rpx;
			color: #1e1848;
			background-color: rgba(255, 255, 255, 0.98);
			border-radius: 15rpx;
			box-shadow: 0 4rpx 20rpx #dedede;
			padding: 0 20rpx;
		}
	}

	.btnStyle {
		color: #6989df;
		background-color: #fff;
	}

	.lineStyle {
		width: 10rpx;
		height: 50rpx;
		margin-left: 5rpx;
		border-radius: 10rpx;
		background-color: #6989df;
	}
</style>