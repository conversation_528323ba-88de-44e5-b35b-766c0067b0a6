<template>
	<view class="w10 h10 page">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 搜索框+筛选列表 -->
		<view class="navTop w10">
			<view class="w10 bac1e1848">
				<uni-search-bar radius="5" placeholder="请输入线索/订单编号/客户手机号" @confirm="search('1')" @cancel="search"
					cancel-text="搜索" v-model="getParam" />
			</view>
			<u-button v-if="storeType!=3" text="创建线索" @click="toBaoMuGuanjia" type="info" size="large">
			</u-button>
			<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17" v-if="roleId==66||roleId==1||roleId==95">
				<view style="text-align: right;padding-right: 20rpx;margin-left: 20rpx;">
					接收公域线索(<text style="color: red;">{{needsPrice}}</text>元/条)：
				</view>
				<view>
					<u-switch v-model="pushVal" :activeValue="1" :inactiveValue="0"
						@change="changeSwitch"></u-switch>
				</view>
			</view>
			<!-- 			<view class="bacf f16">
				<text v-if="integral">当前积分：{{integral||0}}</text>
			</view> -->
			<u-tabs @click="TabsClick" :list="emps" :current="tabscurrent" :scrollable="true" lineColor="#FF6344"
				lineWidth="40">
			</u-tabs>
			<HM-filterDropdown :filterData="filterData" :defaultSelected="defaultSelected" :updateMenuName="true"
				@confirm="confirm" dataFormat="Object"></HM-filterDropdown>
		</view>
		<view class="bacf f16 lh30" v-for="(item,i) in customers" :key="i" style="border: 2rpx solid #eee;">
			<u-swipe-action>
				<u-swipe-action-item :options="options1" @click="cancelNeeds(item)">
					<view class="swipe-action u-border-top u-border-bottom">
						<view class="swipe-action__content">
							<view @click="showCustomer(i)" @longpress="showDetail(i)">
								<view class="flac-row"
									style="padding: 20rpx 40rpx 0 8rpx;justify-content: space-between;letter-spacing: 4rpx;">
									<view class="flac-row">
										<u-icon name="play-right-fill"
											:color="(item.amount==99&&item.orderState!=99)?'red':''"
											style="margin-right: 15rpx;" v-if="!item.oldNeedsId"></u-icon>
										<u-icon name="error-circle-fill" style="margin-right: 15rpx;"
											v-if="item.oldNeedsId" color="red"></u-icon>
										线索编号:{{item.id}}
									</view>
									<u-tag :text="item.status==='0'?'已取消':item.statusMsg" @click="showCustomer(i)"
										type="error" plain plainFill></u-tag>
								</view>
								<view class="w85 mg-at">订单编号:{{item.billNo||'未生成订单编号'}}</view>
								<view class="w85 mg-at">客户电话:{{item.phone}}</view>
								<view class="flac-row" style="padding: 0rpx 40rpx 0 8rpx;justify-content: space-between;letter-spacing: 4rpx;">
									<view class="flac-row">
										<view style="margin-right: 50rpx;"></view>
										<!-- <u-icon  ></u-icon> -->
									服务类型:{{item.productName}}
									</view>
									<u-icon  @click="showCustomer(i)" v-if="item.ifDevelopNeeds===0" size="25" name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1746607392580系统 (1).png"></u-icon>
									<u-icon  @click="showCustomer(i)" v-if="item.ifDevelopNeeds===1" size="25" name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1746607293679开.png"></u-icon>
								</view>
								<!-- <view class="w85 mg-at">服务地址:{{item.cityName}}市-{{item.areaName}}</view> -->
								<view class="w85 mg-at">服务地址:{{item.street}}</view>
								<view class="w85 mg-at">服务时间:{{item.startTime}}</view>
							</view>
						</view>
					</view>
				</u-swipe-action-item>
			</u-swipe-action>
		</view>
		<!-- 打开小程序二维码 -->
		<u-modal :show="showModal" title="长按识别小程序" @confirm="showModal = false">
			<view>
				<u-image :src="imgCode" mode="aspectFit"></u-image>
			</view>
		</u-modal>

		<view class="bacf" style="position: fixed;bottom: 22%;right:34rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #1e1848 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;color:#f6cc70;
			background-color: #1e1848;line-height: 25rpx;font-size: 24rpx;" @click="openWorkOrder">
			<view>
				线索
			</view>
			<view>
				工单
			</view>
		</view>
		<view class="bacf" style="position: fixed;bottom: 15%;right:34rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #1e1848 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;color:#f6cc70;
			background-color: #1e1848;line-height: 25rpx;font-size: 24rpx;" @click="openOneBill">
			<view>
				线索
			</view>
			<view>
				合单
			</view>
		</view>

		<u-popup :show="popupFlag" @close="popupFlag = false">
			<view>
				<uni-section title="取消线索" type="line" padding style="height: calc(80vh - 80px);">
					<scroll-view scroll-y="true" class="scroll-Y">
						<view class="w95 mg-at  f16 lh80">
							<view class="w35 ">取消原因：</view>
							<u-tag :text="cancelReason" bgColor="#1e1848"
								@click="cancelReasonFlag= true,popupFlag=false" borderColor="#1e1848"></u-tag>
						</view>
						<view class="w95 mg-at  f16 lh80">
							<view class="w35 ">取消备注：</view>
							<textarea class="uni-input" type="textarea" v-model="cancelRemark" placeholder="请输入取消备注" />
						</view>
					</scroll-view>
				</uni-section>
			</view>
			<button style="width: 30%;margin-bottom: 40rpx;margin-left: 60%;" class="btnStyle"
				@click="confirmCancelNeeds">确认取消</button>
		</u-popup>
		<u-picker :show="cancelReasonFlag" @confirm="changeCancel" keyName="label" @cancel="cancelReasonFlag=false"
			:columns="columns"></u-picker>
	</view>
</template>

<script>
	import data from '../js/data5.js'; //筛选菜单数据
	import moment from 'moment'; //时间格式化
	moment.locale('zh-cn');
	export default {
		data() {
			return {
				scrollTop: 0,
				popupFlag: false,
				cancelReasonFlag: false,
				columns: [
					[{
							label: '客户只是咨询',
						},
						{
							label: '客户被同行先成交了',
						},
						{
							label: '客户预算有限，没阿姨接',
						},
						{
							label: '客户嫌中介费贵，不请',
						},
						{
							label: '客户没有看上合适的阿姨',
						},
						{
							label: '其他原因',
						},
					]
				],
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				showModal: false,
				imgCode: '',
				getParam: '',
				pushVal: null,
				defaultSelected: [],
				options1: [{
					text: '取消需求'
				}],
				filterData: [],
				integral: 0,
				storeType: uni.getStorageSync('storeType'),
				storeId: 0,
				tabscurrent: 0,
				roleId: uni.getStorageSync("roleId"),
				emps: [],
				listData: [],
				StrAgentId: '',
				startTime: '',
				needsPrice: 0,
				id: '',
				employeeId: '',
				endTime: '',
				agentId: null,
				loadMore: 0,
				catch: [],
				current: 1,
				cancelData: {},
				cancelReason: '点击选择取消原因',
				cancelRemark: '',
				flowStatus: null,
				orderBy: null,
				size: 10,
				customers: []
			}
		},
		onLoad: function(option) {
			if (!uni.getStorageSync('employeeId')) {
				uni.showToast({
					title: '登录失效，请重新登录',
					icon: 'none'
				})
				uni.setStorageSync('redirectUrl', '/pages-work/operation/clew/clewIndex');
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					})
				}, 2000)

			}
			this.getStoreClueSetting()
			this.catch = {
				"name": "全部",
				"value": "全部"
			}
			this.id = option.id
			if (option.employeeId) {
				this.employeeId = option.employeeId
			} else {
				this.employeeId = uni.getStorageSync("employeeId")
			}
			if (option.storeId) {
				this.storeId = option.storeId
			} else {
				this.storeId = uni.getStorageSync("storeId");
			}
			// this.getEmployeeIntegral()
			this.searchEmps();
			//定时器模拟ajax异步请求数据
			setTimeout(() => {
				this.filterData = data;
			}, 100);
			setTimeout(() => {
				//3秒后 修改defaultSelected
				this.changeSelected();
			}, 300);
		},
		mounted() {},
		onReachBottom() {
			// 滑动到底部刷新
			// console.log("滑动到底部加载更多...")
			this.loadMore++
		},
		watch: {
			loadMore: {
				handler(newValue, oldVal) {
					this.current++
					this.search()
					// this.getOrderNeedsPage()
				},
				deep: true
			}
		},
		methods: {
			changeSwitch(){
				uni.showModal({
					title: '提示',
					content: this.pushVal==1?'确认开启吗？开启后将自动扣除钱包余额!':"确认关闭吗？关闭后将不再接收线索！",
					success: function(res) {
						if (res.confirm) {
							this.http({
								url: 'changeStoreClueSetting',
								data: {
									state: this.pushVal,
									storeId: uni.getStorageSync("storeId")==1?2:uni.getStorageSync("storeId")
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								method: 'POST',
								hideLoading: true,
								success: res => {
									if (res.code == 0) {
										uni.showToast({
											title: "操作成功！",
											icon: 'none',
											duration: 2000
										})
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none',
											duration: 2000
										})
									}
								}
							})
				
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
						setTimeout(()=>{
							this.getStoreClueSetting()
						},2000)
					}.bind(this)
				});
			},
			getStoreClueSetting(){
				this.http({
					url: 'getStoreClueSetting',
					data: {
						storeId: uni.getStorageSync("storeId")==1?2:uni.getStorageSync("storeId")
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.pushVal = res.data.state
							this.needsPrice = res.data.price
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							})
						}
					}
				})
			},
			changeCancel(val) {
				this.cancelReasonFlag = false
				this.popupFlag = true
				this.cancelReason = val.value[0].label
			},
			confirmCancelNeeds() {
				if (this.cancelReason === '其他原因' && !this.cancelRemark) {
					return uni.showToast({
						title: '请输入取消备注!',
						icon: 'none',
						duration: 1500
					})
				}
				this.popupFlag = false
				uni.showModal({
					title: '提示',
					content: '确定取消此线索？取消后将短信通知客户!',
					success: function(res) {
						if (res.confirm) {
							this.http({
								url: 'cancelNeeds',
								data: {
									id: this.cancelData.id,
									cancelRemark: this.cancelRemark,
									cancelReason: this.cancelReason,
									agentId: uni.getStorageSync("employeeId")
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								method: 'POST',
								hideLoading: true,
								success: res => {
									if (res.code == 0) {
										uni.showToast({
											title: '取消成功',
											icon: 'none',
											duration: 2000
										})
										this.customers = []
										this.current = 1
										this.search()
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none',
											duration: 2000
										})
									}
								}
							})

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}.bind(this)
				});
			},
			cancelNeeds(item) {
				this.cancelData = item
				this.popupFlag = true
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开线索工单
			openWorkOrder() {
				uni.navigateTo({
					url: "/pages-mine/works/works-search"
				})
			},
			openOneBill() {
				uni.navigateTo({
					url: "/pages-work/operations/clew/oneBill"
				})
			},
			//修改选中项-示例
			changeSelected() {
				this.defaultSelected = [];
				this.$nextTick(() => {
					this.defaultSelected = [
						[0],
						[0],
						[0]
					];
				})
			},
			// getEmployeeIntegral() {
			// 	this.http({
			// 		url: 'getEmployeeIntegral',
			// 		method: 'GET',
			// 		hideLoading: true,
			// 		data: {
			// 			employeeId: uni.getStorageSync('employeeId')
			// 		},
			// 		success: res => {
			// 			if (res.code == 0) {
			// 				this.integral = res.data
			// 			}
			// 		}
			// 	})
			// },
			toBaoMuGuanjia() {

				// #ifdef APP-PLUS
				plus.share.getServices(
					res => {
						let sweixin = null;
						for (let i in res) {
							if (res[i].id == 'weixin') {
								sweixin = res[i];
							}
						}
						//唤醒微信小程序
						if (sweixin) {
							sweixin.launchMiniProgram({
								id: 'gh_2939fe27ac86', //
								type: 0, //小程序版本  0-正式版； 1-测试版； 2-体验版。
								path: 'pages-other/baomuType/BMindex' //小程序的页面,用传的参数在小程序接值判断跳转指定页面
							});
						}
					}
				);

				// #endif

				// #ifdef MP-WEIXIN
				wx.navigateToMiniProgram({
					appId: 'wx9272efff9f958cc0', // pord
					path: 'pages-other/baomuType/BMindex?tg=' + uni.getStorageSync("employeeNo"),
					envVersion: 'release',
					success(res) {
						// 打开成功
					}
				})
				// #endif

				// #ifdef H5

				let param = {
					"path": "pages-other/baomuType/BMindex",
					"scene": 'tg/' + uni.getStorageSync("employeeNo"),
					"source": "appminiprogram",
					"title": "保姆管家",
					"type": 1
				}
				this.http({
					url: 'getEmployeePoster',
					method: 'POST',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.imgCode = res.data
						this.showModal = true;
					},
					fail: err => {
						console.log(res)
					}
				});

				// #endif

			},
			showCustomer(index) {
				if (this.customers[index].status === '0') {
					return uni.showToast({
						title: '该线索已取消',
						icon: 'none',
						duration: 3000
					})
				}

				let c = this.customers[index];
				if (c.statusMsg == '开始接单') {
					this.http({
						url: 'updateOrderNeedState',
						data: {
							orderNeedsId: this.customers[index].id,
							flowStatus: 1,
							needStatus: 412,
							status: '3',
							employeeId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						hideLoading: true,
						success: res => {}
					})
				}
				uni.navigateTo({
					url: "/pages-work/operations/clew/clewPage?id=" + c.id

				})
			},
			showDetail(index) {
				let c = this.customers[index]
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + c.id
				})
			},
			searchEmps() {
				this.http({
					url: 'getEmpByStoreId',
					method: 'GET',
					hideLoading: true,
					data: {
						storeId: this.storeId
					},
					success: res => {
						if (res.code == 0) {
							if (this.roleId == 104 || this.roleId == 95 || this.roleId == 66 || this.roleId ==
								1 || this.roleId == 77 || this.roleId == 110 || this.roleId == 112) {
								this.listData = res.data
								for (var i = 0; i < res.data.length; i++) {
									this.StrAgentId += res.data[i].id + ","
								}
								this.StrAgentId = this.StrAgentId.substring(0, this.StrAgentId.length - 1);
								this.getEmpName(res.data)
							}
							this.search();
						}
					}
				});
			},
			getEmpName(item) {
				const list = item;
				this.emps = [];
				this.emps.push(this.catch)
				for (var i = 0; i < list.length; i++) {
					const li = list[i];
					const param = {
						name: li.realName,
						value: li.realName
					}
					this.emps.push(param);
				}
			},
			TabsClick(val) {
				this.current = 1
				this.customers = []
				this.StrAgentId = ''
				if (val.index == 0) {
					this.agentId = null
					this.searchEmps()
				} else {
					this.agentId = this.listData[val.index - 1].id
					this.search()
				}
			},
			search(val) {
				if (this.getParam || val) {
					this.customers = []
					this.current = 1
				}
				this.http({
					url: 'getOrderNeedsList',
					method: 'POST',
					hideLoading: true,
					data: {
						'storeId': this.storeId,
						'search': this.getParam,
						'size': this.size,
						'strAgentId': this.StrAgentId,
						'current': this.current,
						'userId': this.employeeId,
						'agentId': this.agentId,
						'id': this.id,
						'startTime': this.startTime,
						'endTime': this.endTime,
						'flowStatus': this.flowStatus,
						'orderBy': this.orderBy,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let addrReg = /(.{9})(.*)/; // 地址正则
							for (var i = 0; i < res.data.records.length; i++) {
								let data = res.data.records[i];
								if (data.street) {
									if (addrReg.test(data.street)) {
										let text1 = RegExp.$1;
										let text2 = RegExp.$2.replace(/./g, "*");
										data.street = text1 + text2;
									}
								}

								var reg = /^(\d{3})\d{4}(\d{4})$/;
								// data.phone = data.phone.replace(reg, "$1****$2");
								this.customers.push(data)
								if (data.street.length >= 13) {
									data.street = data.street.substring(0, 13)
								}
							}
							// this.customers = ;
						} else {
							let msg = res.msg;
							if (!msg) {
								msg = '系统错误';
							}
							this.$toast.toast(msg);
						}
					}
				})
			},
			//接收菜单结果
			confirm(e) {
				this.startTime = ''
				this.current = 1
				this.endTime = ''
				this.customers = []
				this.indexArr = e.index;
				this.valueArr = e.value;
				//将日期筛选下拉框的值转换成请求参数
				switch (this.indexArr[0][0]) {
					//本日
					case 1:
						this.startTime = moment().format('YYYY-MM-DD');
						this.endTime = moment().add(1, 'days').format('YYYY-MM-DD');
						break;
						//本周
					case 2:
						//获取本周开始日期
						this.startTime = moment().week(moment().week()).startOf('week').format('YYYY-MM-DD');
						//获取本周结束日期  并加一天
						this.endTime = moment().week(moment().week()).endOf('week').add(1, 'days').format('YYYY-MM-DD');
						break;
						//本月
					case 3:
						this.startTime = moment().month(moment().month()).startOf('month').format('YYYY-MM-DD');
						this.endTime = moment().month(moment().month()).endOf('month').add(1, 'days').format('YYYY-MM-DD');
						break;
						//上月
					case 4:
						this.startTime = moment().month(moment().month() - 1).startOf('month').format('YYYY-MM-DD');
						this.endTime = moment().month(moment().month() - 1).endOf('month').add(1, 'days').format(
							'YYYY-MM-DD');
						break;
					default:
						break;
				}
				//将状态筛选下拉框的值转换成请求参数
				if (this.indexArr[1][0] - 1 < 0) {
					this.flowStatus = null
				} else {
					this.flowStatus = this.indexArr[1][0] - 1
				}

				this.orderBy = this.filterData[2].submenu[this.indexArr[2][0]].value
				this.search()
			},
			// change(index) {
			// 	this.agentId = index.id;
			// },
		},
	}
</script>
<style lang="scss" scoped>
	.page {
		padding-bottom: 10rpx;
		min-height: 100vh;
		background-color: #f4f2f3 !important;
	}

	.navTop {
		position: sticky;
		top: 0;
		left: 0;
		z-index: 999;
	}

	.u-page {
		padding: 0;
	}

	.u-demo-block__title {
		padding: 10px 0 2px 15px;
	}

	.swipe-action {
		&__content {
			padding: 25rpx 0;

			&__text {
				font-size: 15px;
				color: $u-main-color;
				padding-left: 30rpx;
			}
		}
	}

	.orderList {
		width: 92%;
		margin: 30rpx auto;
		padding: 10rpx 0;
		border-radius: 20rpx;
	}

	.orderTitle {
		display: flex;
		justify-content: space-between;
		color: #0061b0;
	}

	.img {
		width: 90rpx;
		height: 90rpx;
		margin: auto 20rpx;
	}

	.order_con {
		display: flex;
		justify-content: space-between;
		border-bottom: 2rpx solid #ececec;
	}

	.account-info {
		width: 92%;
		margin: 100rpx auto;
		border-radius: 20rpx;
	}

	/deep/.u-tabs__wrapper__nav {
		background-color: #f4f3f2 !important;
	}

	// 弹出-选择栏
	.popup-picker {
		position: absolute;
		top: 13.5vh;
		right: 22rpx;
		z-index: 999;
		width: 140rpx;
		height: auto;
		padding: 0 0 0 0;

		.picker-triangle {
			width: 0;
			height: 0;
			border: 15rpx solid transparent;
			border-bottom-color: #f4f4f5;
			margin-left: 65%;
		}

		.picker-tab {
			width: auto;
			mine-height: 60rpx;
			line-height: 60rpx;
			color: #1e1848;
			background-color: rgba(255, 255, 255, 0.98);
			border-radius: 15rpx;
			box-shadow: 0 4rpx 20rpx #dedede;
			padding: 0 20rpx;
		}
	}
</style>