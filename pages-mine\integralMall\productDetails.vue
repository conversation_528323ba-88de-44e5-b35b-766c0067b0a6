<template>
	<view class="page">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>
		<view class="cardBox">
			<image class="imgBox" :src="productData.productPicture" mode="aspectFill"></image>
			<view class="w9 mg-at lh30">{{productData.productName || '商品名称'}}</view>
			<view class="w9 mg-at lh30 flac-row-b">
				<view class="f18 red fb">
					{{productData.exchangeIntegral || 8888}}
					<text class="f12 c0 fb4" style="margin-left: 10rpx;">积分</text>
				</view>
				<view class="f14 c9">{{productData.alreadyExchangeCount}}人已兑换</view>
			</view>
		</view>
		<view class="w8 boxStyle f14 lh25 t-indent2" style="padding: 30rpx;">
			<view class="">兑换规则：</view>
			<view v-html="productData.exchangeRules"></view>
		</view>
		<view v-if="productData.productIntroduce" class="w8 boxStyle f14 lh25 t-indent2" style="padding: 30rpx;">
			<view class="lh30">产品介绍：</view>
			<view class="lh35">{{productData.productName}}</view>
			<view v-html="productData.productIntroduce"></view>
		</view>
		<image v-if="productData.detailsPicture" style="width: 100%;" class="imgBox" :src="productData.detailsPicture" mode="widthFix"></image>
		<view style="background-color:cornsilk;position: fixed;bottom: 0;left: 0;z-index: 999;width: 100%;height: 150rpx;">
		<u-button  customStyle="width: 85%;margin: 50rpx auto;text-align: center;border-radius: 40rpx;line-height: 70rpx;color: black;background-color: #ffd852;" 
		@click="showModal=true" shape="circle"
		 :text="productData.inventory<=0?'已抢光':'兑换'" :disabled="productData.inventory<=0"></u-button>
		</view>
	<u-popup :show="showModal" title=""  @close="showModal = false" @confirm="showModal = false">
		<view style="display: flex;padding-top: 100rpx;">
			<u-image style="margin-left: 30rpx;" height="300rpx" width="300rpx" :src="productData.productPicture" mode="aspectFit"></u-image>
			<view style="font-weight: bold;margin-top: -80rpx;margin-left: -15rpx;">{{productData.productName}}</view>
			<view style="margin-top: 140rpx;font-size: 50rpx;margin-left: -80rpx;">{{productData.exchangeIntegral}}积分</view>
		</view>
		<view class="w9 mg-at boxStyle bacf radius10">
			
			<view v-if="productData.id!=14" class="w8 mg-at flac-row-b padding20-0"
				@click="goto('/pages-mine/integralMall/address?type=selectAddress')">
				<view class="">
					<view v-if="!userAddress.name">请选择地址</view>
					<view class="f14 fb" v-if="userAddress.name">{{userAddress.name}}&nbsp;&nbsp;&nbsp;&nbsp;{{userAddress.phone}}</view>
					<view class="f12 c9 lh30" v-if="userAddress.street">{{userAddress.street}}</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			
			<view v-if="productData.id==14"  class="w8 mg-at flac-row-b padding20-0">
				<view class="">
					<u-input placeholder="请输入车牌号" type="text" v-model="carCode" />
				</view>
			</view>
			
		</view>
		<u-button @click="exchangeProduct" customStyle="width: 85%;margin: 50rpx auto;text-align: center;border-radius: 40rpx;line-height: 70rpx;color: black;background-color: #ffd852;" shape="circle" text="兑换"></u-button>
	</u-popup>
		<u-modal @confirm="gotoDetails()" @cancel="modalShow = false" 
		:show="modalShow" title="兑换成功" :content="content"></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showModal: false,
				scrollTop: 0,
				carCode: '',
				content: "",
				modalShow: false,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				productData: {},
				userAddress: {},
				id: null,
			};
		},
		onLoad(e) {
			if(uni.getStorageSync('userAddress')){
				this.userAddress = JSON.parse(decodeURIComponent(uni.getStorageSync('userAddress')))
			}else {
				this.userAddress = {}
			}
			this.id = e.id
			this.getAllIntegralProduct()
		},
		onShow() {
			if(uni.getStorageSync('userAddress')){
				this.userAddress = JSON.parse(decodeURIComponent(uni.getStorageSync('userAddress')))
			}else {
				this.userAddress = {}
			}
		},
		methods:{
			gotoDetails(){
				this.modalShow=false
				uni.navigateTo({
					url: './exchangeLog'
				})
			},
			onPageScroll(e) {
					this.scrollTop = e.scrollTop;
				},
			getAllIntegralProduct() {
				this.http({
					url: 'getAllIntegralProduct',
					method: 'GET',
					data: {
						id: this.id
					},
					success: res => {
						if (res.code == 0&&res.data.records.length>0) {
							this.productData = res.data.records[0]
						} else {
							let msg = res.msg;
							if (!msg) {
								msg = '系统错误';
							}
							this.$toast.toast(msg);
						}
					}
				})
			},
			exchangeProduct(){
				if(!this.userAddress.name&&this.productData.id!=14){
					return uni.showToast({
					   icon: 'none',
					   title: '请选择或添加地址！'
					 })
				}
				if(this.productData.id==14&&!this.carCode){
					return uni.showToast({
					   icon: 'none',
					   title: '请输入车牌号！'
					 })
				}
				this.http({
          url: 'exchangeProduct',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync('memberId'),
						id: this.productData.id,
						memberAddId: this.userAddress.id,
						carCode: this.carCode
					},
					header: {
					  "content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.showModal = false
							this.content = "恭喜你兑换["+this.productData.productName+"],我们将在1~7个工作日根据您的收获地址发货。"
							 this.modalShow = true
							 setTimeout(()=>{
								 this.getAllIntegralProduct()
							 },1000)
						} else {
							return uni.showToast({
							   icon: 'none',
							   title: res.msg
							 })
						}
					},
				})
			},
			goto(url) {
				uni.navigateTo({
					url: url
				})
			},
			selectMemberAddress(){
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync('memberId')
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
							console.log('获取员工积分数量-成功！')
						} else {
							console.log('获取员工积分数量-返回失败！')
						}
					},
					fail: err => {
						console.log('获取员工积分数量-请求失败！' + res.code)
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;min-height: 100vh;
		margin: auto;
		padding: 50rpx 0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jfStore_bg.png') no-repeat;
		background-size: 100% 100%;
	}
	
	.cardBox {
		width: 90%;
		margin: 20rpx auto;
		border: 2rpx solid #ccc;
		border-radius: 50rpx;
		padding-bottom: 20rpx;
	}

	.imgBox {
		width: 100%;
		height: 400rpx;
		margin: auto;
		margin-bottom: 20rpx;
		border-top-left-radius: 45rpx;
		border-top-right-radius: 45rpx;
	}

	.boxStyle {
		margin: 30rpx auto;
		padding: 30rpx 0;
		border: 2rpx solid #ccc;
		border-radius: 50rpx;
		background-color: #fff9f2;
	}
	
	.btnStyle {
		width: 85%;
		margin: 50rpx auto;
		text-align: center;
		// border: 2rpx solid #ccc;
		border-radius: 40rpx;
		line-height: 70rpx;
		color: black;
		background-color: #ffd852;
	}
</style>