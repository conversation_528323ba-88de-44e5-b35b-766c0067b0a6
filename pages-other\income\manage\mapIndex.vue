<template>
	<view>
		<!-- longitude（类型为Number，没有默认值，表示中心经度）
      latitude（类型为Number，没有默认值，表示中心纬度）
      scale（类型为Number，默认值为16，缩放级别取值范围为5-18）
      markers（类型为Array数组，类型为数组即表示地图上可以有多个，没有默认值，表示标记点）
      show-location（类型为Boolean，表示显示带有方向的当前定位点）
      polygon polygon指定一系列坐标点，根据points坐标数据生成闭合多边形
      @markertap-表示点击标记点时触发，e.detail={markerId}
      @labeltap-表示点击label时触发，e.detail = {markerId}
      @callouttap-表示点击标记点对应的气泡时触发，e.detail = {markerId} -->
		<view class="map-container">
			<map style="width: 100%; height: 93.4vh;" :show-location='true' ref="map" id="map" :latitude="latitude"
				:longitude="longitude" :markers="markers" :circles="circles" :scale="scale" @callouttap='callouttap'>
				<cover-view class="cover-view" :style=''>
					<cover-view @click="refresh">
						<cover-image class="cover-image"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/refresh.png" />
						<cover-view>刷新</cover-view>
					</cover-view>
					<!-- <cover-view style="margin-top: 20rpx;" @click="onControltap">
            <cover-image class="cover-image" src="@/static/location.png" />
            <cover-view>定位</cover-view>
          </cover-view> -->
				</cover-view>
				<cover-view class="w10 mg-at bacf popupStyle" v-show="showPopup">
					<cover-view class="w9 mg-at flac-row-b">
						<cover-view class="f18 fb lh50">{{title}}</cover-view>
						<cover-image class="closeImg"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/close.png"
							@click="showPopup = false" />
					</cover-view>
					<cover-view class="w9 mg-at flac-row">
						<cover-view class="f16 fb lh35">小区名称：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{loupanName}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">住户数（户）：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{houseHold}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">客户数（位）：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{membernum}}</cover-view>
						<cover-image style="margin-left: 10rpx;width: 50rpx;" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/kh.png" mode="widthFix" @click="goPage"></cover-image>
					</cover-view>
					<cover-view class="w9 mg-at flac-row bacf">
						<cover-view class="f16 fb lh35">市占率：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{Math.round(membernum/houseHold * 10000) / 100.00 + "%"}}</cover-view>
					</cover-view>
					<cover-view class="w9 mg-at flac-row">
						<cover-view class="f16 fb lh35">房价(元)：</cover-view>
						<cover-view class="f16 t-indent2 lh35">{{price}}</cover-view>
					</cover-view>
				</cover-view>
			</map>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showPopup: false, //弹窗设置
				latitude: uni.getStorageSync('lat'), //纬度
				longitude: uni.getStorageSync('lng'), //经度
				title: '',
				loupanName: '',
				houseHold: '',
				membernum: '',
				houseId: '',
				price: '',
				scale: 16, //缩放级别
				dataList: [],
				markers: [{
					longitude: uni.getStorageSync('lng'),
					latitude: uni.getStorageSync('lat'),
					iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/xyjLogo.png', //显示的图标
					rotate: 0, // 旋转度数
					width: 35, //宽
					height: 30, //高
					callout: { //自定义标记点上方的气泡窗口 点击有效
						content: uni.getStorageSync('selectStoreName'), //文本
						color: '#fdd472', //文字颜色
						fontSize: 12, //文本大小
						borderRadius: 5, //边框圆角
						padding: '10', //文本边缘留白
						bgColor: '#1c1c75', //背景颜色
						display: 'ALWAYS', //‘BYCLICK’:点击显示; ‘ALWAYS’:常显
					},
				}],
				circles: [{
					longitude: uni.getStorageSync('lng'),
					latitude: uni.getStorageSync('lat'),
					fillColor: '#9db0de33',
					radius: uni.getStorageSync('radius') * 1000,
					strokeWidth: 1,
					color: '#00aaff'
				}]
			}
		},
		computed: {},
		onLoad() {
			this.getstoreScope()
			console.log(uni.getStorageSync('lng'))
			console.log(uni.getStorageSync('lat'))
		},
		methods: {
			getstoreScope() {
				uni.request({
					url: 'https://api2.xiaoyujia.com/activity/jdpost',
					data: {
						"url": 'https://dyapi.xiaoyujia.com/bscope',
						"storeid": uni.getStorageSync('selectStoreId'),
						"employeeid": uni.getStorageSync('employeeId'),
					},
					method: 'POST',
					success: (res) => {
						console.log(res)
						let arr = res.data.list
						console.log(arr, 'arr------------------')
						let newArry = []
						newArry = arr.map((item, index) => {
							return Object.assign({}, {
								'id': item.id,
								'lng': item.location.split(',')[0],
								'lat': item.location.split(',')[1],
								'loupanName': item.loupanName, //小区名称
								'orderNum': item.num, //小区前一天的订单数
								'houseHold': item.houseHold, //总户数
								"membernum": item.membernum, //客户数
								'price': item.price, //楼盘价
							})
						})
						console.log('newArry------------', newArry)
						this.dataList = newArry
						console.log(this.dataList, 'dataList-----------------')
						this.marker()
					}
				});
			},
			marker(i) { //标识
				let marker = []
				for (var i = 0; i < this.dataList.length; i++) {
					marker = marker.concat({
						id: i,
						longitude: this.dataList[i].lng,
						latitude: this.dataList[i].lat,
						iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/xyjLogo.png', //显示的图标
						rotate: 0, // 旋转度数
						width: 35, //宽
						height: 30, //高
						callout: { //自定义标记点上方的气泡窗口 点击有效  
							content: this.dataList[i].loupanName + '[' + this.dataList[i].orderNum + '单' +
								']', //文本
							color: '#ffffff', //文字颜色
							fontSize: 12, //文本大小
							borderRadius: 5, //边框圆角
							padding: '10', //文本边缘留白
							bgColor: '#406390', //背景颜色
							display: 'ALWAYS', //‘BYCLICK’:点击显示; ‘ALWAYS’:常显
						},
					})
				}
				this.markers = marker
				this.callouttap()
				console.log("marker-----------", this.markers)
			},
			refresh() {
				this.http({
					url: "getStoreData",
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					method: "GET",
					success: res => {
						if (res.code == 0) {
							this.latitude = res.data.lat
							this.longitude = res.data.lng
						}
					}
				})
			},
			//气泡点击事件
			callouttap(e) {
				console.log('callouttap', e)
				let index = parseInt(e.detail.markerId)
				this.title = this.dataList[index].loupanName + '[' + this.dataList[index].orderNum + '单' + ']'
				let obj = this.dataList[index]
				// console.log('xxxxxxxxxxxxxxxxxxx', this.dataList[index])
				this.loupanName = obj.loupanName
				this.houseHold = obj.houseHold
				this.membernum = obj.membernum
				this.price = obj.price
				this.houseId = obj.id
				this.showPopup = true
				console.log('你点击了气泡标签', index)
			},
			goPage(e) {
				uni.navigateTo({
					url: '/pages-work/operations/customer/customer?houseId=' + this.houseId + '&loupanName=' +this.loupanName
				})
				console.log(this.houseId)
			}
		}
	}
</script>

<style scoped lang="scss">
	.map-container {
		// margin-top: -40rpx;
		position: relative;
		overflow: hidden;
		// border-radius: 50rpx 50rpx 0 0;

		.cover-view {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			/* width: 80rpx;
			height: 160rpx; */
			padding: 20rpx;
			color: #4F575F;
			font-weight: 400;
			background-color: #fff;
			// background-size: 120rpx 120rpx;
			// background-position: center center;
			position: absolute;
			bottom: 30rpx;
			right: 30rpx;
			border-radius: 15rpx;
		}

		.cover-image {
			width: 40rpx;
			height: 40rpx;
			margin: 10rpx auto;
		}

		.popupStyle {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 999;
			padding: 40rpx 0;

			.closeImg {
				width: 60rpx;
				height: 60rpx;
			}
		}

	}
</style>