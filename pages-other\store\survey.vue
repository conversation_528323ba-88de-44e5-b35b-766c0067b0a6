<template>
	<view>
		<!-- 		<img :src="postImg" mode="widthFix" style="width: 100%;" /> -->

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="f18 fb lh30 text-c" style="padding: 10rpx 40rpx;" v-if="survey&&survey.surveyQuestionList.length">
			<view class="f24 lh50">
				{{survey.surveyTitle}}
			</view>
			<view class="">
				{{storeName}}
			</view>
		</view>

		<view v-if="!isFinishSurvey">
			<view v-if="survey&&survey.surveyQuestionList.length">
				<view class=" f18" style="padding: 20rpx 40rpx">
					{{survey.surveyContent}}
				</view>

				<view v-for="(item,index) in survey.surveyQuestionList" :key="index">
					<view class="tab-title">
						<text>{{item.questionTitle}}</text>
						<text style="color: #ff4d4b;" v-if="item.required==1">*</text>
					</view>
					<view class="f16" style="padding: 0 40rpx;">
						{{item.questionContent}}
					</view>
					<view v-if="item.answerType==0 ||item.answerType==1">
						<view class="tab-inputbox" v-if="index==0">
							<view class="tab-input"><input class="single-input"
									:type="item.answerType==1?'number':'text'" v-model="item.answerContent"
									placeholder="请填写您的回答" /></view>
						</view>
						<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;" v-else>
							<u--textarea class="multiline-input" confirmType="done"
								:type="item.answerType==1?'number':'text'" v-model="item.answerContent"
								placeholder="请填写您的回答" height="100"></u--textarea>
						</view>
					</view>
					<view v-if="item.answerType==2">
						<view class="flac-row">
							<view class="tab-checkbox">
								<view class="checkbox" :class="{activeBox: item.answerContent=='否'}">
									<text v-model="item.answerContent" @click="item.answerContent='否'">否</text>
								</view>
							</view>
							<view class="tab-checkbox">
								<view class="checkbox" :class="{activeBox: item.answerContent=='是'}">
									<text v-model="item.answerContent" @click="item.answerContent='是'">是</text>
								</view>
							</view>
						</view>
					</view>
					<view v-if="item.answerType==3" style="margin: 20rpx 40rpx;">
						<u-rate count="5" v-model="item.answerScore" active-color="#F1CB5A" inactive-color="#b2b2b2"
							size="24"></u-rate>
					</view>
					<view v-if="item.answerType==4" style="margin: 20rpx 40rpx;">
						<img :src="item.answerContent||imgUpload"
							@longpress="openImgPreview(item.answerContent||imgUpload)" @click="uploadImg(index)"
							mode="widthFix" style="width: 200rpx;height: auto;">
					</view>
				</view>
				<view style="width: 86%;padding: 20rpx 7% 200rpx 7%">
					<text style="display: block;font-size: 32rpx;color: #ff4d4b;">
						* 平台将对问卷内容进行保密，保证您的数据安全</text>
				</view>
			</view>

			<view class="filter-button" v-if="survey">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="clearSurvey()">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="insertSurveyRecord()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</view>


		<view v-if="isFinishSurvey">
			<u-empty text="本月已提交该门店问卷，感谢您的反馈！" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>

		<view v-if="!survey">
			<u-empty text="抱歉，该问卷不存在或已过期！" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 门店调查问卷id
				surveyId: 22,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/store_survey_post.png',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				isFinishSurvey: false,
				survey: null,
				storeId: null,
				storeName: ''
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			uploadImg(index) {
				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								this.$set(this.survey.surveyQuestionList[index], 'answerContent',
									result.data)
							}
						});
					}
				});
			},
			getSurveyById() {
				this.http({
					url: 'getSurveyById',
					method: 'GET',
					path: this.surveyId,
					success: res => {
						if (res.code == 0) {
							this.survey = res.data
						}
					}
				})
			},
			// 校验权限
			checkStoreSurveyRecordAuth() {
				this.http({
					url: "checkStoreSurveyRecordAuth",
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						surveyId: this.surveyId,
						memberId: uni.getStorageSync("memberId") || null,
						storeId: this.storeId || 0,
					},
					success: res => {
						if (res.code == 0) {
							this.isFinishSurvey = false
						} else {
							this.isFinishSurvey = true
						}
					},
				})
			},
			// 添加问卷调查记录
			insertSurveyRecord() {
				if (!this.storeId) {
					return this.$refs.uNotify.warning("未选择门店！")
				}

				let isBlank = false
				this.survey.surveyQuestionList.forEach(item => {
					if (item.required == 1) {
						if (item.answerType == 3) {
							if (!item.answerScore) {
								isBlank = true
							}
						} else if (!item.answerContent) {
							isBlank = true
						}
					}

				})
				if (isBlank) {
					return this.$refs.uNotify.warning("请将必填项填写完整！")
				} else {
					uni.showModal({
						title: '问卷已填写完成',
						content: '确认进行提交吗？',
						success: res => {
							if (res.confirm) {
								let list = this.survey.surveyQuestionList
								this.http({
									url: "insertSurveyRecord",
									method: 'POST',
									header: {
										"content-type": "application/json;charset=UTF-8"
									},
									data: {
										surveyId: this.survey.id,
										memberId: uni.getStorageSync("memberId") || null,
										employeeId: uni.getStorageSync("employeeId") || null,
										storeId: this.storeId || null,
										surveyAnswerRecordList: list
									},
									success: res => {
										if (res.code == 0) {
											this.$refs.uNotify.success("问卷内容提交成功！")
											this.isFinishSurvey = true
										} else {
											this.$refs.uNotify.warning(res.msg)
										}
									},
								})
							} else {

							}
						}
					});
				}
			},
			getStoreById() {
				this.http({
					url: 'getStoreById',
					method: 'GET',
					hideLoading: true,
					path: this.storeId,
					success: res => {
						if (res.code == 0) {
							this.storeName = res.data.storeName
						}
					}
				})
			},
			// 清空问卷调查回答
			clearSurvey() {
				this.survey.surveyQuestionList.forEach(item => {
					this.$set(item, 'answerContent', '')
					this.$set(item, 'answerScore', null)
				})
				this.$refs.uNotify.success("回答已清空！")
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '诚邀您填写调查问卷',
					path: '/pages-other/store/survey?storeId=' + this.storeId,
					mpId: 'wxd64d55d779f303d0'
				}
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', "/pages-other/store/survey?storeId=" + this.storeId)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				} else {
					this.checkStoreSurveyRecordAuth()
					this.getStoreById()
					this.getSurveyById()
				}
			},
		},
		watch: {
			scrollTop: {
				handler(newValue, oldVal) {

				},
				deep: true
			}
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.storeId = obj.id || null
				this.surveyId = obj.sId || null
			}
			this.storeId = options.storeId || this.storeId
			this.surveyId = options.surveyId || this.surveyId
			if (!this.storeId) {
				this.storeId = uni.getStorageSync('storeId')
			}
			this.checkLogin()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
</style>