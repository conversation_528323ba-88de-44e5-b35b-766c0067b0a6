<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 操作确认弹窗-加备注 -->
		<uni-popup ref="popupCheckDetail" type="dialog">
			<uni-popup-dialog type="success" cancelText="取消" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()">
				<view class="popupCheck-inputbox">
					<view class="flex-row">
						<text>{{checkText}}</text>
						<uni-icons type="plus" style="margin-left: 20rpx;" size="24" @click="popupShowReason=true">
						</uni-icons>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" type="text" v-model="workRemark" placeholder="输入暂存原因（选填）" />
						</view>
					</view>
				</view>

			</uni-popup-dialog>
		</uni-popup>

		<u-popup :show="popupShowFilter" mode="right" @close="popupShowFilter = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="可输入员工姓名、手机号"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">
							<view class="tab">
								<view class="tab-title">
									<text>所属门店</text>
								</view>
								<view class="tab-picker" @click="openPickerMine(0)">
									<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
									<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
									<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作类型</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in workTypeList" :key="index">
									<view class="checkbox" :class="{activeBox: index==choiceWorTypeIndex}">
										<text v-model="item.value" @click="choiceBox(2,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>接单状态</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in statusList" :key="index">
									<view class="checkbox"
										:class="{activeBox: item.value==statusList[choiceStatusIndex].value}">
										<text v-model="item.value" @click="choiceBox(1,index)">{{item.text}}</text>
									</view>
								</view>
							</view>
							<u-gap height="120"></u-gap>
						</scroll-view>
					</view>
				</view>

				<view class="filter-button w82">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="searchCondition.current=1;list=[];getList()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 备注弹框 -->
		<u-popup :show="popupShowRemark" mode="bottom" @close="popupShowRemark = false">
			<view class="filter-title">
				<text>员工备注</text>
			</view>

			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="workRemark"
							placeholder="员工工作备注" height="100" count></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 备注内容将不会展示给客户，仅开发人可编辑</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="workRemark=''">
						<text>清空备注</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="saveRemark()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 原因快捷选择弹框 -->
		<u-popup :show="popupShowReason" mode="bottom" @close="popupShowReason = false">
			<view class="filter-title">
				<text>选择原因</text>
			</view>

			<view class="filter-content" style="height: 1000rpx;">
				<view class="f16 t-indent" style="text-align: center;margin: 20rpx 0;">
					{{workRemark||'请选择暂存原因'}}
				</view>
				<view class="tab">
					<view class="tab-checkbox-col" v-for="(item,index) in reasonList" :key="index">
						<view class="checkbox">
							<text @click="choiceReason(index)">{{item.text}}</text>
						</view>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="workRemark=''">
						<text>清空原因</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="popupShowReason=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>员工日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{item.title||''}}</text>
					</view>
					<text v-if="formatLongStr(item.workContent)!='暂无'">{{formatLongStr(item.workContent)}}</text>
					<text>时间：{{item.creatTime}}</text>
					<text>操作人：{{item.crePerson||'-'}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 		<view class="flac-row-b w8 f16" style="height: 100rpx;margin: 10rpx auto;">
			<view class="flac-col-c" @click="openPickerMine(0)">
				<text class="fb">所属门店</text>
				<text>{{storeName||'未指定'}}</text>
			</view>
			<view class="flac-col-c">
				<text class="fb">经纪人</text>
				<text>{{agentName||'未指定'}}</text>
			</view>
			<view class="flac-col-c" @click="refreshList()">
				<button class="btnStyle">搜索</button>
			</view>
		</view> -->

		<!-- 搜索栏目 -->
		<view class="tab-menu">
			<view class="choice-menu">
				<view class="choice-item" v-for="(choiceList, index) in choiceList" :key="index"
					@click="choiceTab(index)">
					<text :class="{activeChoice: choiceIndex == index}"
						class="choice-title">{{choiceList.choiceTitle}}</text>
				</view>
				<view class="flac-row choice-filter" @click="popupShowFilter=true">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
						size="16"></u-icon>
					<text class="f18 t-indent">筛选</text>
				</view>
			</view>
		</view>

		<uni-swipe-action>
			<uni-swipe-action-item v-for="(item, index) in list" :key="index" :disabled="choiceIndex==2"
				:right-options="[]" @click="clickOption" @change="choiceBaomuIndex = index">
				<view class="baomu-tab flac-col" style="border-bottom: #f4f4f5 4rpx solid;">
					<view class="flac-row">
						<view class="tab-left">
							<img :src="item.headPortrait||blankImg" @click="openImgReview(index)">
						</view>
						<view class="tab-right">
							<view>
								<text class="tab-name">{{item.realName}}</text>
								<text>（简历：{{item.resumeScore||0}}分）</text>
								<view style="display: inline-block;">
									<view class="small-tag" v-if="hasLevel(item.levelId)">
										<img :src="authIcon" alt="">
										<text>{{formatLevel(item.levelId)}}</text>
									</view>
								</view>

								<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#F9AE3D"
									@click="openLog(index)">
								</uni-icons>
							</view>
							<view>
								<view class="tab-info" @click="openTab(4,index)">
									<text v-if="item.age!=null">{{item.age}}岁</text>
									<text>|&nbsp;{{formatStr(0,2,item.hometown)}}人</text>
									<text>|&nbsp;{{item.workYear!==null&&item.workYear!=="0"?item.workYear+"年以上":"暂无经验"}}</text>
									<text v-if="item.education">|&nbsp;{{formatEducation(item.education)}}</text>
								</view>
								<view @click="openTab(4,index)">
									<view class="tab-text">
										<text>工作类型：<text class="c0">{{item.workType||'暂无'}}</text></text>
									</view>
									<view class="tab-text"
										v-if="item.introducerName!=null||item.introducerMemberName!=null">
										<text>推荐人：<text
												class="c0">{{checkPeople(item.introducer,item.introducerName||item.introducerMemberName)}}</text></text>
									</view>
									<view class="tab-text">
										<text>上架：<text class="c0">{{item.putTime}}</text></text>
									</view>
								</view>
								<view class="tab-text" @click="openContractLog(item.id)">
									<text>上户状态：<text class="c0"
											:style="item.contractId?'color:#ff4d4b':'color:#000'">{{formatStatus(item)}}</text></text>
									<uni-icons type="eye" style="margin-left: 5rpx;display: inline-block;" size="18"
										color="#909399" v-if="choiceIndex==0||choiceIndex==1">
									</uni-icons>
								</view>
								<view @click="openTab(4,index)">
									<view class="tab-text">
										<text>门店：<text class="c0">{{item.storeName||'-'}}</text></text>
									</view>
								</view>
								<view class="tab-text1">
									<text style="display: inline-block;">备注：<text
											class="c0">{{formatLongStr(item.workRemark)}}</text>
									</text>
									<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
										color="#909399" @click="openRemark(index)">
									</uni-icons>
								</view>
							</view>
						</view>
					</view>

					<view class="flac-row w9 mg-at" style="height: 100rpx;">
						<view style="width: 50%;height: 40rpx;" @click="level(index)">
							<view class="button-right" style="background-color:#1e1848;color:#f6cc70">
								<text>暂存</text>
							</view>
						</view>
						<view style="width: 50%;height: 40rpx;" @click="openTab(0,index)">
							<view class="button-right" style="background-color:#1e1848;color:#f6cc70">
								<text>简历</text>
							</view>
						</view>
						<view style="width: 50%;height: 40rpx;" @click="openTab(1,index)">
							<view class="button-right" style="background-color:#1e1848;color:#f6cc70">
								<text>测评</text>
							</view>
						</view>
					</view>
				</view>
			</uni-swipe-action-item>
		</uni-swipe-action>

		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<!-- 悬浮按钮 -->
		<view style="position: fixed;right: 36rpx;bottom: 19%;">
			<img style="width: 80rpx;height: 80rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/icon_home.png"
				@click="goPage('./employee-appraisal')" />
		</view>

	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				authIcon: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/auth_icon.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,

				agentName: "",
				storeName: "",
				searchText: "",
				searchStoreId: null,
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				popupShow: false,
				popupShowLog: false,
				popupShowRemark: false,
				popupShowFilter: false,
				popupShowReason: false,
				choiceIndex: 0,
				choiceBaomuIndex: 0,
				choiceStatusIndex: 0,
				choiceWorTypeIndex: 0,
				choiceList: [{
					choiceTitle: "待测评",
					value: 0
				}, {
					choiceTitle: "已测评",
					value: 1
				}, {
					choiceTitle: "暂存",
					value: 2
				}],
				options: {
					option: [{
						text: '移除',
						style: {
							backgroundColor: '#1e1848',
						}
					}, ],
				},
				workTypeList: [{
					text: '',
					showText: "不限",
				}, {
					text: '住家',
					showText: "住家",
				}, {
					text: '单餐',
					showText: "单餐",
				}, {
					text: '月嫂',
					showText: "月嫂",
				}, {
					text: '钟点',
					showText: "钟点",
				}],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
				statusList: [{
						text: '不限',
						value: null
					}, {
						text: '找工作中',
						value: 0
					},
					{
						text: '已有工作',
						value: 1
					},
					{
						text: '暂不接单',
						value: 2
					}
				],
				educationList: [
					[{
							value: 0,
							label: '无',
						},
						{
							value: 1,
							label: '未填',
						},
						{
							value: 3,
							label: '小学',

						}, {
							value: 4,
							label: '中学',
						}, {
							value: 5,
							label: '高中',
						}, {
							value: 6,
							label: '大专',
						}, {
							value: 7,
							label: '本科及以上',
						}, {
							value: 8,
							label: '中专',
						}, {
							value: 9,
							label: '研究生',
						}
					]
				],
				searchEmployeeId: null,
				list: [],
				logList: [],
				storeList: [],
				searchCondition: {
					isQualified: 1,
					isExcellent: 1,
					isAppraisal: 1,
					resumeVersion: 0,
					levelId: null,
					storeId: null,
					current: 1,
					size: 10,
					orderBy: "putTime DESC"
				},
				workRemark: '',
				reasonList: [{
						text: '头像不合格',
					},
					{
						text: '健康资质不完善—缺少乙肝两对半报告',
					},
					{
						text: '健康资质不完善—九项体检报告不全',
					},
					{
						text: '体检项目不合格',
					},
					{
						text: '缺少岗位对应上岗证书',
					}
				]
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 跳转页面
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			openTab(index, index1) {
				this.choiceBaomuIndex = index1
				let resumeScore = this.list[index1].resumeScore
				let id = this.list[index1].id
				// 打开简历
				if (index == 0) {
					let url = "/pages-mine/resume/resume?baomuId=" + id
					uni.navigateTo({
						url: url
					})
				} else if (index == 1) {
					//  #ifdef H5
					this.$refs.uNotify.warning('请在小程序完成操作！')
					return
					// #endif 
					wx.navigateToMiniProgram({
						appId: 'wxfaa09acff97970be',
						path: 'pages-mine/auth/auth-skill?employeeId=' + id,
						envVersion: 'release',
						success(res) {

						}
					})

				} else if (index == 4) {
					let version = this.list[index1].resumeVersion || 0
					let url = version == 0 ? "/pages-mine/works/employee-detail?baomuId=" + id :
						"/pages-other/employee/employee-detail1?baomuId=" + id
					uni.navigateTo({
						url: url
					})
				}
			},
			// 打开备注
			openRemark(index) {
				this.choiceBaomuIndex = index
				this.workRemark = this.list[index].workRemark || ""
				this.popupShowRemark = true
			},
			level(index) {
				this.choiceBaomuIndex = index
				this.$refs.uNotify.warning("暂存后在待测评列表将不可见！")
				this.openCheckDetail(5, "暂存员工", "确定移动到暂存吗？（非下架，只是暂不进行测评）")
			},
			// 点击选项
			clickOption(e) {
				let choiceOption = e.index
				// 判断点击的按钮是什么
				if (this.choiceIndex == 0 || this.choiceIndex == 1) {
					this.workRemark = ''
					if (choiceOption == 0) {
						this.level()
					}
				}

			},
			// 选择原因
			choiceReason(index) {
				let text = this.reasonList[index].text
				if (this.workRemark) {
					text = '，' + text
				}
				this.workRemark += text
			},
			checkPeople(no, name) {
				if (!name && !no) {
					return "暂无"
				} else {
					return name + "（" + no + "）"
				}
			},
			// 判断是否已经测评
			hasLevel(level) {
				if (level != null) {
					if (level != 0) {
						return true
					}
				} else {
					return false
				}
			},
			// 格式化保姆等级
			formatLevel(level) {
				let result = ""
				if (level != null) {
					if (level > 1 && level < 6) {
						result = this.levelList[level - 1].text
					} else {
						result = "暂无"
					}
				}
				return result
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			formatLongStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			// 格式化学历信息
			formatEducation(education) {
				for (let item of this.educationList[0]) {
					if (item.value == education) {
						return item.label
					}
				}
				return "无学历"
			},
			formatStatus(item) {
				let str = item.contractId ? "已开单" : "未开单"
				let str1 = ""
				let statusList = ['-正在找工作', '-已有工作', '-暂不找工作']
				let statusIndex = item.status ? parseInt(item.status) : 0
				str1 = statusList[statusIndex]
				return str + str1
			},
			// 选择菜单
			choiceTab(index) {
				this.choiceIndex = index
				this.refreshList()
			},
			// 选中单选框
			choiceBox(value, index) {
				if (value == 1) {
					this.choiceStatusIndex = index
					this.searchCondition.status = this.statusList[index].value
				} else if (value == 2) {
					this.choiceWorTypeIndex = index
					this.searchCondition.workType = this.workTypeList[index].text
				}
			},
			// 清除筛选条件
			cleanFilter() {
				this.searchText = ''
				this.storeName = ''
				this.choiceStatusIndex = 0
				this.choiceWorTypeIndex = 0

				this.searchStoreId = null
				this.searchCondition.search = null
				this.searchCondition.storeId = null
				this.searchCondition.resumeVersion = null
				this.searchCondition.levelId = null
				this.searchCondition.status = null
			},
			// 刷新保姆列表
			refreshList() {
				let index = this.choiceIndex
				this.cleanFilter()
				this.searchCondition.current = 1
				this.list = []
				if (index == 0) {
					this.searchCondition.resumeVersion = 0
					this.searchCondition.needEvaluation = 1
				} else if (index == 1) {
					this.searchCondition.resumeVersion = 1
					this.searchCondition.needEvaluation = 1
				} else if (index == 2) {
					this.searchCondition.resumeVersion = 0
					// this.searchCondition.levelId = 1
					this.searchCondition.needEvaluation = 0
				}
				this.getList()
			},
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.popupShow = false
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.searchStoreId = this.storeList[index].id
					let storeName = this.storeList[index].storeName
					storeName = storeName.replace('小羽佳家政', '').replace('(', '')
						.replace(')', '').replace('·', '')
					this.storeName = storeName
				}
				this.showPickerMine = false
				this.popupShow = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 更新员工状态
			updateBaomuState() {
				let data = this.employee
				this.$set(data, "operatorName", uni.getStorageSync("employeeName") || "")
				this.http({
					url: 'updateBaomuState',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: data,
					success: res => {
						if (res.code == 0) {
							this.workRemark = ''
							this.$delete(this.list, this.choiceBaomuIndex)
							this.$refs.uNotify.success("已更新员工状态！")
						}
					}
				});
			},
			// 打开头像预览
			openImgReview(index) {
				this.headPortrait = this.list[index].headPortrait || this
					.blankImg
				let data = []
				data.push(this.headPortrait)
				uni.previewImage({
					urls: data,
					current: this.headPortrait
				})
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.list[index].id
				this.http({
					url: 'getBaomuWorkLog',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.logList = res.data
						} else {
							this.logList = []
						}
					}
				})
			},
			// 打开合同日志
			openContractLog(id) {
				if (id == null) {
					return
				}
				uni.navigateTo({
					url: "/pages-work/operations/contractLog?id=" + id
				})
			},
			saveRemark() {
				let id = this.list[this.choiceBaomuIndex].id
				let workRemark = this.workRemark
				if (workRemark == "" || workRemark == null) {
					this.$refs.uNotify.error("备注不能为空，请重新输入！")
					return
				}

				this.http({
					url: "updateBaomu",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: id,
						workRemark: workRemark,
					},
					success: res => {
						if (res.code == 0) {
							this.list[this.choiceBaomuIndex].workRemark = workRemark
							this.popupShowRemark = false
							this.addBaomuWorkLog(1, this.choiceBaomuIndex)
							this.$refs.uNotify.success("备注修改成功！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			addBaomuWorkLog(value, index) {
				let id = this.list[index].baomuId
				let workRemark = this.list[index].workRemark
				let name = uni.getStorageSync("employeeName") || uni.getStorageSync("memberName")
				let data = {}
				if (value == 0) {
					data = {
						employeeId: id,
						title: "资料查看",
						workContent: "员工详情资料被查看",
						crePerson: name,
						type: 1
					}
				} else if (value == 1) {
					data = {
						employeeId: id,
						title: "修改备注",
						workContent: "修改备注为：" + workRemark,
						crePerson: name,
						type: 6
					}
				}

				this.http({
					url: "addBaomuWorkLog",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/getAllStoreList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
						}
					}
				});
			},
			// 获取保姆列表
			getList() {
				this.searchCondition.storeId = this.searchStoreId || null
				this.searchCondition.search = this.searchText || ''
				this.$set(this.searchCondition, "baomuId", this.searchEmployeeId)
				this.searchEmployeeId = null
				this.http({
					url: 'getBaomuPage',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.list = this.list.concat(res.data.records)
							uni.setNavigationBarTitle({
								title: "优秀员工测评" + "（" + this.total + "）"
							})
							this.popupShowFilter = false
						} else {
							uni.setNavigationBarTitle({
								title: "优秀员工测评"
							})
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 打开确认框
			openCheckDetail(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheckDetail.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 5) {
					let workRemark = this.workRemark
					// if (workRemark == "") {
					// 	this.$refs.uNotify.error("请先补充移除测评原因！")
					// 	return
					// }
					// 移除员工
					this.employee = this.list[this.choiceBaomuIndex]
					this.employee.needEvaluation = 0
					// this.employee.workRemark = "移除测评原因：" + workRemark
					this.updateBaomuState()
				}
			},
		},
		watch: {
			scrollTop: {
				handler(newValue, oldVal) {

				},
				deep: true
			}
		},
		onReachBottom() {
			this.searchCondition.current++
			this.getList()
		},
		onLoad(options) {
			this.searchEmployeeId = options.searchEmployeeId || null
			this.getList()
			this.getStoreList()
		},
	}
</script>
<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
		width: 100%;
	}

	/deep/ .u-transition,
	.u-back-top {
		z-index: 999 !important;
	}

	// 保姆列表栏目
	.baomu-tab {
		width: 100%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 40rpx 0;
	}

	// 栏目左边
	.tab-left {
		width: 25%;
		height: 100%;

		img:nth-child(1) {
			display: block;
			width: 170rpx;
			height: 240rpx;
			border-radius: 15rpx;
			margin: 20rpx 0 0 12%;
		}

		img:nth-child(2) {
			display: block;
			width: 170rpx;
			height: 60rpx;
			margin: -60rpx auto 0rpx 22rpx;
		}
	}

	// 栏目右边
	.tab-right {
		float: right;
		display: flex;
		flex-direction: column;
		width: 69%;
		height: auto;
		padding: 0 3%;
	}

	.tab-name {
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		font-weight: bold;
		line-height: 80rpx;
	}

	.tab-info {
		text {
			font-size: 36rpx;
			line-height: 60rpx;
			padding: 0 5rpx;
		}
	}

	.tab-text {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}

	.tab-text1 {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
		}
	}

	.skills-content {
		width: 100%;
		height: auto;
	}

	// 弹窗输入区域
	.popupCheck-inputbox {
		display: flex;
		flex-direction: column;
		width: 100%;
		line-height: 60rpx;

		text {
			display: block;
			margin: -10rpx;
			text-align: center;
			font-size: 32rpx;
		}
	}

	.my-tag {
		float: left;
		padding: 0;
		width: auto;
		height: auto;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 10rpx 0rpx;
		padding: 0 10rpx;
	}

	.button-left,
	.button-right {
		width: 70%;
		height: 60rpx;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;

		color: #f6cc70;
		background-color: #1e1848;

		text {
			height: 60rpx;
			line-height: 60rpx;
			font-size: 32rpx;
		}
	}

	.img-delete {
		position: absolute;
		margin: -230rpx 0 0 140rpx;
	}

	.img-push {
		position: absolute;
		margin: -50rpx 0 0 140rpx;
	}

	.log-list {
		width: 90%;
		height: auto;
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.small-tag {
		display: flex;
		width: 120rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #fff;
		background-color: #f6cc70;
		border-radius: 10rpx;
		margin: 10rpx 0;

		img {
			display: block;
			width: 30rpx;
			height: 30rpx;
			margin: 7rpx 0 0 10rpx;
		}

		text {
			display: block;
			width: 60%;
			text-align: center;
		}
	}

	.tab-checkbox-col {
		display: flex;
		flex-direction: column;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		margin: 20rpx 10%;

		text {
			display: block;
			width: 100%;
			text-align: center;
			font-size: 32rpx;

			white-space: nowrap;
			/*强制span不换行*/
			overflow: hidden;
			/*超出宽度部分隐藏*/
			text-overflow: ellipsis;
			/*超出部分以点号代替*/
		}
	}
</style>