<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog :type="type1" cancelText="关闭" confirmText="确认" :title="checkTitle"
					:content="checkText" @confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>

		<!-- 工作状态选择器 -->
		<view>
			<u-picker :show="show" @cancel="show = false" :columns="workStateList" @confirm="confirmState"
				@change="changeHandler" keyName="label"></u-picker>
		</view>

		<!-- 个人中心-头部 -->
		<view class="mine-head">
			<view class="head-left">
				<img :src="headImg!==''?headImg:blankImg" @click="openAccount()">
				<button class="btn-signin" @click="openSignIn()">签到</button>
			</view>
			<view class="head-right">
				<view class="right-top">
					<text>{{ memberName!==''?memberName:"暂无用户名"}}</text>
					<view class="authentication">
						<view class="head-tag">
							<view class="my-tag" style="background-color:#1e1848;" @click="openIdentification()"
								v-if="isAuthenticated">
								已认证</view>
							<view class="my-tag" style="background-color:#dedede;color:#909399"
								@click="openIdentification()" v-if="!isAuthenticated">
								未认证</view>
						</view>
					</view>
					<view class="invitation-code">
						<text>邀请码 {{memberId}}</text>
					</view>

				</view>
				<view class="right-bottom">
					<text class="member-infor" style="border-right: #000000 2rpx solid;">{{ workSpace}}</text>
					<text class="member-infor">{{employeeProfession}}</text>
					<!-- <text>评分 {{memberScore}}</text> -->
				</view>
			</view>
			<view class="head-bottom">
				<view class="bottom-text">
					<text>{{jiaBiAmount}}</text>
					<text>我的积分</text>
					<button @click="openExchange()">兑换</button>
				</view>
				<view class="bottom-text">
					<text style="color: #EE130A;">￥ {{myAmount}}</text>
					<text>我的余额</text>
					<button @click="openReflect()">提现</button>
				</view>
			</view>
		</view>

		<!-- 个人中心-求职状态 -->
		<view class="resume-state">
			<text>工作状态</text>
			<text @click="show=true">{{workState}}&ensp;❯</text>
		</view>

		<!-- 个人中心-各项菜单 -->
		<view class="menu">
			<view class="menu-column" @click="openMenu(index)" v-for="(menuList,index) in menuList" :key="index">
				<img :src="menuList.listImage" />
				<text>{{menuList.listTitle}}</text>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout">
			<button @click="tryLogout()">退出登录</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'mine-old',
		data() {
			return {
				type: 'center',
				type1: 'success',
				msgType: "success",
				msgText: "",
				checkType: 0,
				checkTitle: "",
				checkText: "",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				headImg: '',
				memberId: 0,
				baomuId: null,
				account: uni.getStorageSync('account'),
				memberName: "",
				workSpace: "",
				workSpaceList: [],
				employeeProfession: "保姆",
				memberScore: "暂无",
				invitationCode: "555555",
				isAuthenticated: false,
				jiaBiAmount: 0,
				myAmount: 0,
				show: false,
				workState: "正在找工作",
				workStateIndex: "正在找工作",
				workStateList: [
					[{
						label: '正在找工作',
						id: 0
					}, {
						label: '已有工作',
						id: 1
					}]
				],
				menuList: [{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************我的简历.png',
						listTitle: '我的简历',
						listUrl: '/pages-mine/resume/resume'
					},
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************我的技能.png',
						listTitle: '我的技能',
						listUrl: '/pages-mine/resume/ability'
					},
					// {
					// 	listImage: 'https://codefun-proj-user-res-**********.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443817950029.png',
					// 	listTitle: '我的培训',
					// 	listUrl: '/pages-mine/train/train'
					// }, 
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266229980我的订单.png',
						listTitle: '我的订单',
						listUrl: '/pages-mine/orders/orders'
					}, {
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266267339我的合同.png',
						listTitle: '我的合同',
						listUrl: '/pages-mine/contract/contract',
					}, {
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266364346我的证书.png',
						listTitle: '我的证书',
						listUrl: '/pages-mine/certificate/certificate'
					},
					// {
					// 	listImage: 'https://codefun-proj-user-res-**********.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '工作手册',
					// 	listUrl: '/pages-mine/handbook/handbook'
					// }, 
					// {
					// 	listImage: 'https://codefun-proj-user-res-**********.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '帮助中心',
					// 	listUrl: '/pages-mine/help/help'
					// }, {
					// 	listImage: 'https://codefun-proj-user-res-**********.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443916814885.png',
					// 	listTitle: '建议反馈',
					// 	listUrl: '/pages-mine/feedback/feedback'
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-**********.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '小羽佳课堂',
					// 	listUrl: '/pages-mine/signIn/index'
					// }, 
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266396123邀请有礼.png',
						listTitle: '邀请有礼',
						listUrl: '/pages-mine/invitation/invitation'
					}, {
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664330942346我的积分.png',
						listTitle: '我的积分',
						listUrl: '/pages-mine/signIn/signin',
					}, {
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666158693768活动中心.png',
						listTitle: '活动中心',
						listUrl: ''
					}
				],
				// 员工详细信息
				employeeInfo: {
					employeeId: this.employeeId,
					zodiac: 0,
					workingState: 0,
					religion: 0,
					languagenum: null,
					educationnum: null,
					birthTime: null,
					idCard: null,
					hometown: null,
					nation: null,
					education: null,
					married: null,
					tomarried: null,
					language: null,
					workYear: null,
					family: null,
				},
				// 保姆信息
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null
				},
				baomuExpectedWork: {
					baomuId: this.baomuId,
					workModel: null,
					elderly: null,
					watchBaby: null,
					cookingSkills: null,
					otherSkills: null,
					salaryExpectation: null,
					siteId: null,
				},
			};
		},
		methods: {
			changeHandler(e) {
				const {
					index
				} = e
				this.workStateIndex = index
			},
			// 确认工作状态选择
			confirmState(e) {
				this.workState = this.workStateList[0][this.workStateIndex].label
				this.show = false
				if (this.workStateIndex == 0) {
					this.baomuInfo.status = "0"
				} else if (this.workStateIndex == 1) {
					this.baomuInfo.status = "1"
				}
				this.checkBaomuAndInit()
				let timer = setTimeout(() => {
					if (this.baomuId !== null) {
						this.updateBaomuInfo()
					}
				}, 400);
			},
			cancelState() {
				this.show = false
			},
			// 打开个人信息
			openAccount() {
				uni.navigateTo({
					url: '/pages-mine/resume/account'
				});
			},
			// 打开积分兑换页面
			openExchange() {
				uni.navigateTo({
					url: '/pages-mine/jiabi-store'
				});
			},
			// 打开提现页面
			openReflect() {
				this.$refs.uNotify.success("提现")
			},
			// 打开详细签到页面
			openSignIn() {
				uni.navigateTo({
					url: '/pages-mine/signIn/signin?memberId=' +
						this.memberId
				});
			},
			// 打开菜单项
			openMenu(index) {
				// 跳转到活动中心页面
				if (this.menuList[index].listTitle == "活动中心") {
					if (!this.account) {
						return uni.showToast({
							title: '登录信息为空，请重新登录'
						})
					}
					let param = {
						url: 'https://activity.xiaoyujia.com/boBing?&activityCode=********************************&account=' +
							this.account
					}
					let data = JSON.stringify(param);
					uni.navigateTo({
						url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
					})
				}
				uni.navigateTo({
					url: this.menuList[index].listUrl
				});
			},
			// 更新保姆信息
			updateBaomuInfo() {
				this.$set(this.baomuInfo, 'baomuId', this.baomuId)
				this.http({
					url: 'updateBaomuInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.baomuInfo,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("更新成功！")
						} else {
							this.$refs.uNotify.error("更新失败-返回错误！" + res.msg)
						}
					}
				})
			},
			// 获取保姆信息
			getBaomuInfo() {
				// 请求：获取保姆信息
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuInfoById',
						method: 'POST',
						data: {
							baomuId: this.baomuId
						},
						success: res => {
							if (res.code == 0) {
								this.baomuInfo = res.data
								this.workStateIndex = this.baomuInfo.status == "0" ? 0 : 1
								this.workState = this.workStateList[0][this.workStateIndex].label
								console.log("获取保姆信息-请求成功！")
							} else {
								console.log("获取保姆信息-返回错误！")
							}
						},
						fail: err => {
							console.log('获取保姆信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 获取保姆求职意向
			getBaomuExpectedWork() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuExpectedWork',
						method: 'POST',
						data: {
							"baomuId": this.baomuId
						},
						success: res => {
							if (res.code == 0) {
								this.baomuExpectedWork = res.data
							}
						},
					})
				}
			},
			// 格式化工作地点
			formatWorkSpace(data) {
				if (data == null || data == "") {
					this.workSpace = "厦门"
					return
				}
				for (let index in this.workSpaceList[0]) {
					if (this.workSpaceList[0][index].id == data) {
						this.workSpaceIndex = index
						this.workSpace = this.workSpaceList[0][index].name
					}
				}
			},
			getIntentionSite() {
				// 请求：获取所有工作地点
				this.http({
					url: 'getIntentionSite',
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.workSpaceList = res.data
							this.formatWorkSpace(this.baomuExpectedWork.siteId)
							console.log('获取所有工作地点-请求成功！')
						}
					},
				})
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				if (this.baomuId === null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId")
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync("baomuId", this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log("初始化的保姆ID为" + this.baomuId)
							} else {
								console.log("error", "通过会员ID获取保姆关联信息-返回错误！")
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 通过登录会员的手机号查询员工信息
			getEmployByPhone() {
				// 请求：通过登录会员的手机号查询员工信息
				if (this.employeeId == null) {
					this.http({
						url: 'getEmployeeByPhone',
						method: 'GET',
						path: this.account,
						success: res => {
							// 请求成功之后
							if (res.code == 0) {
								console.log('通过登录会员的手机号查询员工信息-成功！')
								this.employee = res.data
								this.employeeId = res.data.id
								this.headImg = res.data.headPortrait
								uni.setStorageSync('employeeId', this.employeeId)
								uni.setStorageSync('headPortrait', res.data.headPortrait)
								// 判断是否为行政员工
								if (this.employee.employeeType == 20) {

								}
							} else {
								console.log('通过登录会员的手机号查询员工信息-返回失败！')
							}
						},
					})
				}
			},
			// 获取员工详细信息
			getEmployeeInfo() {
				let url = "https://agentapi.xiaoyujia.com/employee/getBaomuInfo/" + this.baomuId
				// 请求：获取员工详细信息
				if (this.baomuId !== null) {
					uni.request({
						url: url,
						method: 'POST',
						success: res => {
							this.employeeInfo = res.data.data
							if (this.employeeInfo.idCard !== null) {
								this.isAuthenticated = true
								uni.setStorageSync("isAuthenticated", true)
							} else {
								uni.setStorageSync("isAuthenticated", false)
							}
						},
					})
				}
			},
			// 获取积分数量
			getJiaBiAmount() {
				// 请求：获取积分数量
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					data: {
						memberId: this.memberId
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
							console.log('获取员工积分数量-成功！')
						} else {
							console.log('获取员工积分数量-返回失败！')
						}
					},
					fail: err => {
						console.log('获取员工积分数量-请求失败！' + res.code)
					}
				})
			},
			// 退出登录
			tryLogout() {
				this.openCheck(0, "确定退出登录吗？", "下次需要重新登录哦～")
			},
			// 退出登录
			logout() {
				// 清除用户数据并返回到登录页
				this.$refs.uNotify.success("退出成功！")
				uni.clearStorage()
				let timer = setTimeout(() => {
					uni.redirectTo({
						url: "/pages-mine/login/login"
					})
				}, 500);
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				// Tpye的值可以控制，0：退出登录确认
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：退出登录确认
				if (this.checkType == 0) {
					// 退出登录
					this.logout()
				}
			},
			// 获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let employeeId = uni.getStorageSync('employeeId')
				let baomuId = uni.getStorageSync('baomuId')
				employeeId = employeeId == "" ? null : employeeId
				baomuId = baomuId == "" ? null : baomuId
				this.memberId = memberId
				this.baomuId = baomuId
				this.baomuInfo.baomuId = baomuId
				this.memberName = uni.getStorageSync('memberName')
				this.headImg = uni.getStorageSync("employeeHeadImg")
				console.log('获取会员Id：', memberId, '员工Id：', employeeId)
				if (baomuId == null) {
					this.checkBaomuAndInit()
					let timer = setTimeout(() => {
						this.initBaomuInfo()
					}, 200)
				} else {
					this.initBaomuInfo()
				}
			},
			initBaomuInfo() {
				this.getEmployByPhone()
				this.getEmployeeInfo()
				this.getBaomuInfo()
				this.getBaomuExpectedWork()
				this.getIntentionSite()
			},
			// 发送部分请求，初始化页面信息
			initInfo() {
				// 获取用户信息
				this.getMemberInfor()
				this.getJiaBiAmount()
			}
		},
		onLoad(options) {

		},
		mounted() {
			this.initInfo()
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #ffffff;
		margin-bottom: 250rpx;
	}

	.mine-head {
		width: 100%;
		height: 420rpx;
		background-color: #FFE102;
	}

	// 头部左边
	.head-left {
		float: left;
		width: 30%;
		height: 300rpx;
	}

	// 头像图片
	.head-left img {
		display: block;
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		margin: 20rpx auto 0 auto;
	}

	// 签到按钮
	.head-left button {
		margin: 20rpx auto;
		height: 50rpx;
		width: 140rpx;
		line-height: 50rpx;
		color: #ffffff;
		background-color: #1e1848;
		font-size: 28rpx;
	}

	// 头部右边
	.head-right {
		float: left;
		width: 70%;
		height: 300rpx;
		margin: 0 0 0 0;
	}

	.right-top {
		width: 100%;
		height: 100rpx;
		margin-top: 40rpx;
	}


	.right-top text {
		display: block;
		width: 100%;
		float: left;
		font-size: 36rpx;
	}

	// 认证区域
	.authentication {
		position: relative;
		display: flex;
		float: right;
		width: 40%;
		height: 60rpx;
		margin: 0rpx 40rpx 0rpx 0rpx;
	}

	.head-tag {
		display: block;
		padding: 0;
		float: left;
		width: 140rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
	}

	.my-tag {
		padding: 0;
		width: 120rpx;
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #ffffff;
	}

	.invitation-code {
		display: block;
		width: 50%;
		height: 40rpx;
	}

	.right-bottom {
		width: 100%;
		margin-top: 60rpx;
		font-size: 36rpx;
	}

	.right-bottom text:nth-child(2) {
		padding: 0 20rpx 0 20rpx;
	}

	.right-bottom text:nth-child(3) {
		float: left;
		display: block;
		width: 200rpx;
		margin-left: 20rpx;
	}

	// 头部下面
	.head-bottom {
		float: left;
		width: 100%;
		height: 120rpx;
	}

	.bottom-text {
		float: left;
		width: 49%;
		text-align: center;
		margin-top: 20rpx;
	}

	.bottom-text:first-child {
		border-right: #000000 2rpx solid;
	}

	.bottom-text text {
		display: block;
		text-align: center;
	}

	// 兑换按钮
	.bottom-text button {
		float: right;
		padding: 0 0;
		width: 80rpx;
		height: 30rpx;
		line-height: 30rpx;
		font-size: 24rpx;
		background-color: #F21177;
		color: #ffffff;
		text-align: center;
		margin-top: -36rpx;
		margin-right: 30rpx;
	}

	// 求职状态
	.resume-state {
		width: 100%;
		height: 140rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	// 求职文本
	.resume-state text {
		font-weight: 140rpx;
		line-height: 140rpx;

	}

	.resume-state text:first-child {
		display: block;
		float: left;
		margin-left: 30rpx;
		font-size: 36rpx;
	}

	.resume-state text:nth-child(2) {
		display: block;
		float: right;
		margin-right: 30rpx;
		font-size: 32rpx;
	}

	.member-infor {
		float: left;
		display: block;
		width: auto;

		padding-right: 20rpx;
	}

	// 菜单
	.menu {
		position: relative;
		height: 700rpx;
		border-radius: 20px;
		width: 90%;
		margin: 80rpx auto;
		padding-bottom: 0rpx;
		box-shadow: 0 0 20rpx #dedede;
		color: #1e1848;
	}

	// 菜单栏目
	.menu-column {
		float: left;
		height: 220rpx;
		width: 33%;
	}

	.menu-column img {
		display: block;
		width: 60rpx;
		height: 60rpx;
		margin: 60rpx auto 0 auto;
	}

	.menu-column text {
		display: block;
		margin: 30rpx 0 0 0;
		font-size: 36rpx;
		text-align: center;
	}

	.logout {
		margin-top: 100rpx;
	}

	.logout button {
		display: block;
		margin: 160rpx auto 200rpx auto;
		width: 40%;
		height: 80rpx;
		line-height: 80rpx;
		// background-color: #F21177;
		background-color: #1e1848;
		color: #ffffff;
		border-radius: 10rpx;
	}
</style>
