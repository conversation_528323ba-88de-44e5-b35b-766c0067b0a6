<template>
	<view style="padding-bottom: 10rpx;">
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="w9 mg-at boxStyle">
			<view class="flac-row fb lh40">服务项目<view class="red t-indent">{{tsLog.productName}}</view>
			</view>
			<view class="flac-row fb lh40" v-if="tsLog.typeDesc">投诉类型<view class="red t-indent">
					{{tsLog.typeDesc || '暂无投诉类型'}}
				</view>
			</view>
			<view class="flac-row fb lh40">
				投诉内容
			</view>
			<view class="red t-indent">
				{{tsLog.tsContent || tsLog.mcommContent || '暂无投诉内容'}}
			</view>
		</view>
		<view class="w9 mg-at boxStyle">
			<view class="flac-row fb lh40">申诉理由<view class="red f16 t-indent">*</view>
			</view>
			<view class="f12 c9" style="margin-bottom: 20rpx;">申诉理由，最少20个字。</view>
			<u--textarea v-model="tsLog.appealReason" placeholder="请输入内容" height="120" maxlength="300"
				count></u--textarea>
		</view>
		<view class="w9 mg-at boxStyle">
			<view class="flac-row fb lh40">品控核实<view class="red f16 t-indent"></view>
			</view>
			<view class="f12 c9" style="margin-bottom: 20rpx;">品控核实，最少20个字。</view>
			<u--textarea v-model="tsLog.appealVerify" placeholder="请输入内容" height="120" maxlength="300"
				count></u--textarea>
		</view>

		<view class="w9 mg-at boxStyle">
			<view class="flac-row fb lh40">上传证据
			</view>
			<uni-file-picker limit="1" v-model="fileList" title="上传图片证据，申诉更容易通过哦" @select="uploadImg"></uni-file-picker>
			<view class="f12 c9 lh40">上传证据图片可提高申诉成功率</view>
		</view>
		<button class="w8 btnStyle f15" @click="submit">提交申诉</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				fileList: [],

				billNo: null,
				tsLog: {}
			}

		},
		methods: {
			radioClick(name) {
				this.radios.map((item, i) => {
					item.checked = i === name ? true : false
				})
			},
			// 上传图片
			uploadImg(e) {
				const url = 'https://api2.xiaoyujia.com/system/imageUpload'
				const tempFilePaths = e.tempFilePaths
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {},
					dataType: 'json',
					success: res => {
						let result = JSON.parse(res.data)
						this.tsLog.appealFile = result.data

					}
				});
			},
			submit() {
				if (!this.tsLog.appealReason) {
					this.$refs.uNotify.error('申诉理由不能为空')
				} else {
					this.http({
						url: 'appealOrderTs',
						data: this.tsLog,
						method: 'POST',
						hideLoading: true,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.$refs.uNotify.success('申诉已提交！')
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					})
				}
			},
			// 获取投诉日志
			getOrderTsLog() {
				this.http({
					url: 'getOrderTsLog',
					data: {
						billNo: this.billNo
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.tsLog = res.data
							if (this.tsLog.appealFile) {
								let data = {
									name: this.tsLog.appealFile,
									extname: "png",
									url: this.tsLog.appealFile
								}
								this.fileList.push(data)
							}
						}
					}
				})
			},
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			popupCheck() {
				if (this.checkType == 0) {

				}
			},
		},
		onLoad(options) {
			this.billNo = options.billNo || null
			this.getOrderTsLog()
		}
	}
</script>

<style lang="scss" scoped>
	.boxStyle {
		width: 90%;
		margin: auto;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #eee;
	}

	.tagStyle {
		display: flex;
		flex-wrap: nowrap;
		align-items: center;
		width: auto;
		margin: 10rpx;
		display: inline-block;

	}

	/deep/.u-tag__text--medium {
		line-height: 26px;
	}

	.btnStyle {
		margin: 40rpx auto;
	}
</style>