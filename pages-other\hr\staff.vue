<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 选择器 -->
		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>员工日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{item.operationName||'-'}}</text>
					</view>
					<text>时间：{{item.creatDate}}</text>
					<text>操作人：{{item.operatorName||'系统'}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<!-- 操作确认弹窗-加备注 -->
		<uni-popup ref="popupCheckDetail" type="dialog">
			<uni-popup-dialog type="success" cancelText="取消" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()">
				<view class="popupCheck-inputbox">
					<text>{{checkText}}</text>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" type="text" v-model="trialStaff.workRemark"
								placeholder="请输入移除/删除/下架原因" />
						</view>
					</view>
				</view>

			</uni-popup-dialog>
		</uni-popup>

		<!-- 筛选弹窗 -->
		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="员工姓名、手机号、工号等"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">
							<view class="tab">
								<view class="tab-title">
									<text>筛选模式</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in searchModeList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.value==choiceSearchModeIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(-1,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>排序方式</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in orderByList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceOrderByIndex}">
										<text v-model="item.value" @click="choiceBox(8,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>入驻时间</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in timeList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.index==choiceCreTimeIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(0,index)">{{tabList.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>所属门店</text>
								</view>
								<view class="tab-picker" @click="openPickerMine(0)">
									<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
									<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
									<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作类别</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in employeeTypeList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.index==choiceEmployeeTypeIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(1,index)">{{tabList.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作性质</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in workingPropertyList" :key="index">
									<view class="checkbox"
										:class="{activeBox: tabList.index==choiceWorkingPropertyIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(2,index)">{{tabList.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>流程状态</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in processIdList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.index==choiceProcessIdIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(3,index)">{{tabList.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>学历情况</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in educationList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.index==choiceEducationIndex}">
										<text v-model="tabList.text" @click="choiceBox(4,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>婚姻状态</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in marriedList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.index==choiceMarriedIndex}">
										<text v-model="tabList.text" @click="choiceBox(5,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>年龄</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in ageList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.value==choiceAgeIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(6,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作年限</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index) in workYearList" :key="index">
									<view class="checkbox" :class="{activeBox: tabList.value==choiceWorkYearIndex}">
										<text v-model="tabList.value"
											@click="choiceBox(7,index)">{{tabList.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>鉴定等级</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in levelList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceLevelIndex}">
										<text v-model="item.value" @click="choiceBox(9,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<u-gap height="120"></u-gap>
						</scroll-view>

					</view>
				</view>

				<view class="filter-button w82">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="startFilter()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 头部菜单-一级 -->
		<view class="tab-menu" style="margin: 0 40rpx;">
			<u-sticky>
				<u-tabs :list="menuList" @click="choiceMenu" :current="current" lineWidth="22" lineHeight="8"
					:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
			        color: '#1e1848',
			        fontWeight: 'bold',
			        transform: 'scale(1.2)'
			    }" :inactiveStyle="{
			        color: '#333',
			        transform: 'scale(1.05)'
			    }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
				</u-tabs>
			</u-sticky>
		</view>

		<!-- 头部菜单-二级 -->
		<view class="w95 flac-row-b" style="margin: 10rpx 0;">
			<view class="choice-menu" style="margin: 20rpx 0;padding: 0 5rpx;">
				<view class="choice-item" v-for="(choiceList, index) in choiceList" :key="index" v-if="choiceList.show"
					@click="choiceTab(index)">
					<text :class="{activeChoice: choiceIndex == index}"
						class="choice-title">{{choiceList.choiceTitle}}</text>
				</view>
			</view>
			<view class="flac-row" @click="popupShow=true">
				<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
					size="15"></u-icon>
				<text class="f16 t-indent">筛选</text>
			</view>
		</view>

		<!-- 员工列表 -->
		<uni-swipe-action>
			<uni-swipe-action-item v-for="(item, index) in list" :key="index" :disabled="choiceIndex==0||choiceIndex==4"
				:right-options="choiceIndex==2?options.option:(choiceIndex==3?options.option1:options.option0)"
				@click="clickOption" @change="clickOptionChange(index)">
				<view class="baomu-tab" style="border-bottom: #f4f4f5 4rpx solid;">
					<view class="tab-left">
						<view class="tab-left-img">
							<img :src="item.headPortrait||blankImg" @click="openImgReview(index)">
						</view>
						<view class="tab-left-btn" v-if="choiceIndex==2">
							<u-tag text="员工上架" @click="openTab(3,index)" color="#FFFFFF" bgColor="#1e1848" plain
								shape="circle" size="mini" borderColor="#1e1848"
								v-if="item.examinationState==1"></u-tag>
						</view>
						<view class="tab-left-btn" v-if="choiceIndex==2">
							<u-tag text="上架催审" @click="urgeListing(index)" color="#FFFFFF" bgColor="#1e1848" plain
								shape="circle" size="mini" borderColor="#1e1848" v-if="item.state!=1"></u-tag>
						</view>
					</view>
					<view class="tab-right">
						<view>
							<view style="width: 100%;height: 100rpx;line-height: 100rpx;display: flex;">
								<text style="font-weight: bold;font-size: 40rpx;">{{item.name}}</text>
								<text v-if="item.post">（{{item.post}}）</text>
								<view class="small-tag" v-if="(item.staffLevel||0)>0">
									<img :src="authIcon" alt="">
									<text>{{levelList[item.staffLevel].text}}</text>
								</view>
								<img :src="item.employeeType==10?employeeTypeList[1].img:employeeTypeList[2].img" alt=""
									style="width: 80rpx;height: 80rpx;margin: 10rpx 10rpx;" v-else />
								<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#1e1848"
									@click="openLog(index)" />
							</view>
							<view class="tab-info" @click="openTab(0,index)">
								<text>{{formatStr(0,2,item.hometown)}}人</text>
								<text v-if="item.sex!='隐藏'">|&nbsp;{{item.sex}}</text>
								<text v-if="item.age!=null&&item.age!=1">|&nbsp;{{item.age}}岁</text>
								<text>|&nbsp;{{item.workYear!==null&&item.workYear!=="0"?"工作"+item.workYear+"年" :"无经验"}}</text>
								<text v-if="checkStr(item.education)!='暂无'">|&nbsp;{{item.education}}</text>
								<text v-if="checkStr(item.married)!='暂无'">|&nbsp;{{item.married}}</text>
							</view>

							<view @click="openTab(0,index)">
								<view class="tab-text">
									<text>工号：<text class="c0">{{item.no||'-'}}</text></text>
								</view>
								<view class="tab-text">
									<text>入驻：<text class="c0">{{item.creatDate}}</text></text>
								</view>
								<view class="tab-text" v-if="item.storeName!=null">
									<text>门店：{{item.storeName}}</text>
								</view>
								<view class="tab-text" v-if="checkStr(item.recruitChannel)!='暂无'">
									<text>渠道：<text class="c0">{{checkStr(item.recruitChannel)}}</text></text>
								</view>
								<view class="tab-text" v-if="choiceIndex==1&&checkStr(item.workState)!='暂无'">
									<text>求职状态：<text class="c0">{{checkStr(item.workState)}}</text></text>
								</view>
								<view class="tab-text" v-if="item.introducerName!=null">
									<text>推荐人：<text
											class="c0">{{checkPeople(item.introducerNo,item.introducerName)}}</text></text>
								</view>

								<!-- 						<view class="tab-text">
								<text>员工状态：{{stateList[item.state].text}}</text>
							</view> -->
								<view class="tab-text" v-if="item.workingProperty!=0">
									<text>工作性质：<text
											class="c0">{{workingPropertyList[item.workingProperty].showText}}</text></text>
								</view>
								<view class="tab-text" v-if="checkSalary(item.salary)!='暂无'">
									<text>期望薪资：<text class="c0">{{checkSalary(item.salary)}}</text></text>
								</view>
								<view class="tab-text" v-if="item.departName!=null">
									<text>部门：<text class="c0">{{item.departName}}</text></text>
								</view>
								<view class="tab-text" v-if="item.areaName!=null">
									<text>区域：<text class="c0">{{item.areaName}}</text></text>
								</view>


								<view class="tab-text" v-if="choiceIndex==2||choiceIndex==3">
									<text>流程：<text class="c0">{{processIdList[item.processId].showText}}</text></text>
								</view>


								<view class="tab-text" v-if="choiceIndex==4">
								</view>
								<view class="tab-text" v-if="choiceIndex==5">
									<text>鉴定人：<text
											class="c0">{{checkPeople(item.updaterNo,item.updaterName)}}</text></text>
								</view>
							</view>
						</view>


						<view style="display: flex; flex-direction: row;width: 100%;height: 100rpx;"
							v-if="(choiceIndex==2||choiceIndex==3)&&!canPut">
							<view style="width: 50%;height: 60rpx;" @click="openTab(1,index)">
								<view class="button-left">
									<text>鉴定</text>
								</view>
							</view>
							<view style="width: 50%;height: 60rpx;" @click="openTab(2,index)">
								<view class="button-right">
									<text>流程</text>
								</view>
							</view>
						</view>


					</view>
				</view>
			</uni-swipe-action-item>
		</uni-swipe-action>

		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />


		<!-- 年龄段 工作时间 工作类型筛选 -->

	</view>
</template>

<script>
	export default {
		name: 'findWorks',
		props: {
			startLoadMore: {
				type: Number
			}
		},
		data() {
			return {
				// 可设置
				// 推荐人提醒-订阅消息模板
				templateId: "ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI",
				// 是否可以直接上架
				canPut: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				scrollLeft: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				options: {
					option0: [{
						text: '流转',
						style: {
							backgroundColor: '#19be6b',
						}
					}],
					option: [{
							text: '移除',
							style: {
								backgroundColor: '#1e1848',
							}
						},
						{
							text: '删除',
							style: {
								backgroundColor: '#909399',
							}
						},
						{
							text: '流转',
							style: {
								backgroundColor: '#19be6b',
							}
						}
					],
					option1: [{
							text: '下架',
							style: {
								backgroundColor: '#1e1848',
							}
						},
						{
							text: '删除',
							style: {
								backgroundColor: '#909399',
							}
						},
						{
							text: '流转',
							style: {
								backgroundColor: '#19be6b',
							}
						}
					],
				},
				// 员工信息
				employee: {
					id: null,
					workRemark: ""
				},
				trialStaff: {
					id: null,
					workRemark: ""
				},
				logList: [],
				roleId: uni.getStorageSync("roleId") || 0,
				popupShow: false,
				popupShowLog: false,
				searchText: "",

				storeName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				choiceSearchModeIndex: 0,
				choiceOrderByIndex: 0,
				choiceCreTimeIndex: 0,
				choiceEmployeeTypeIndex: 1,
				choiceWorkingPropertyIndex: 0,
				choiceProcessIdIndex: 0,
				choiceEducationIndex: 0,
				choiceMarriedIndex: 0,
				choiceAgeIndex: 0,
				choiceWorkYearIndex: 0,
				choiceLevelIndex: 0,

				choiceWorType: "",
				memberId: null,
				baomuId: null,

				// 初始栏目
				choiceIndex: 2,
				choiceIndexOld: 2,
				choiceItemIndex: 0,
				current: 0,

				education: "",
				total: 0,
				totalNow: 0,
				headPortrait: "",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/hr/default.jpg",
				blankHeadPortrait: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/hr/default.jpg",
				authIcon: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/auth_icon.png",
				list: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [{
					index: 0,
					name: '全部',
					value: null
				}],
				choiceList: [{
					choiceTitle: "待绑定",
					value: 0,
					show: false
				}, {
					choiceTitle: "待完善",
					value: 1,
					show: false
				}, {
					choiceTitle: "待鉴定",
					value: 2,
					show: true
				}, {
					choiceTitle: "已上架",
					value: 3,
					show: true
				}],
				searchModeList: [{
					text: '按分类',
					value: 0
				}, {
					text: '全局',
					value: 1
				}],
				stateList: [{
						index: 0,
						text: '待完善',
						value: null
					}, {
						index: 1,
						text: '上架',
						value: 1
					},
					{
						index: 2,
						text: '下架',
						value: 2
					},
					{
						index: 3,
						text: '离职',
						value: 3
					},
					{
						index: 4,
						text: '未知',
						value: 4
					}
				],
				statusList: [{
						text: '不限',
						value: null
					}, {
						text: '找工作中',
						value: 0
					},
					{
						text: '已有工作',
						value: 1
					}
				],
				timeList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: 0
				}, {
					index: 1,
					text: '',
					showText: "今天",
					value: 1
				}, {
					index: 2,
					text: '',
					showText: "三天内",
					value: 3
				}, {
					index: 3,
					text: '',
					showText: "一周内",
					value: 7
				}, {
					index: 4,
					text: '',
					showText: "一个月内",
					value: 30
				}, {
					index: 5,
					text: '',
					showText: "三个月内",
					value: 90
				}],
				workingPropertyList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null,
				}, {
					index: 1,
					text: '',
					showText: "全职",
					value: 1,
				}, {
					index: 2,
					text: '',
					showText: "兼职",
					value: 2,
				}, {
					index: 3,
					text: '',
					showText: "行政",
					value: 0,
				}],
				employeeTypeList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null,
					img: ""
				}, {
					index: 1,
					text: '',
					showText: "一线",
					value: 10,
					img: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/hr/frontline.jpg"
				}, {
					index: 2,
					text: '',
					showText: "行政",
					value: 20,
					img: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/hr/administration.jpg"
				}],
				processIdList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null
				}, {
					index: 1,
					text: '',
					showText: "资料",
					value: 1
				}, {
					index: 2,
					text: '',
					showText: "面试",
					value: 2
				}, {
					index: 3,
					text: '',
					showText: "培训",
					value: 3
				}, {
					index: 4,
					text: '',
					showText: "保险",
					value: 4
				}, {
					index: 5,
					text: '',
					showText: "跟单",
					value: 5
				}, {
					index: 6,
					text: '',
					showText: "考试",
					value: 6
				}, {
					index: 7,
					text: '',
					showText: "入职",
					value: 7
				}, {
					index: 8,
					text: '',
					showText: "上架",
					value: 8
				}],
				// 年龄段
				ageList: [{
					value: 0,
					text: '不限',
					ageLow: null,
					ageHigh: null,
				}, {
					value: 1,
					text: '18-20岁',
					ageLow: 18,
					ageHigh: 20,
				}, {
					value: 2,
					text: '21-30岁',
					ageLow: 21,
					ageHigh: 30,
				}, {
					value: 3,
					text: '31-40岁',
					ageLow: 31,
					ageHigh: 40,
				}, {
					value: 4,
					text: '41-50岁',
					ageLow: 41,
					ageHigh: 50,
				}, {
					value: 5,
					text: '51-60岁',
					ageLow: 51,
					ageHigh: 60,
				}, {
					value: 6,
					text: '60岁以上',
					ageLow: 61,
					ageHigh: 200,
				}],
				orderByList: [{
						index: 0,
						value: "creatDate DESC",
						showText: "默认",
					}, {
						index: 1,
						value: "entryTime DESC,creatDate DESC",
						showText: "入驻时间",
					},
					{
						index: 2,
						value: "creatDate DESC",
						showText: "上架时间",
					}
				],
				workYearList: [{
					value: 0,
					text: '不限',
					workYearLow: null,
					workYearHigh: null,
				}, {
					value: 1,
					text: '无经验',
					workYearLow: 0,
					workYearHigh: 0,
				}, {
					value: 2,
					text: '1-3年',
					workYearLow: 1,
					workYearHigh: 2,
				}, {
					value: 3,
					text: '3-5年',
					workYearLow: 3,
					workYearHigh: 5,
				}, {
					value: 4,
					text: '5-10年',
					workYearLow: 6,
					workYearHigh: 10,
				}, {
					value: 5,
					text: '10-20年',
					workYearLow: 11,
					workYearHigh: 20,
				}, {
					value: 6,
					text: '20年以上',
					workYearLow: 21,
					workYearHigh: 200,
				}],
				educationList: [{
						value: null,
						text: "不限",
						index: 0
					},
					{
						value: '无',
						text: "无",
						index: 1
					},
					{
						value: '小学',
						text: "小学",
						index: 2

					}, {
						value: '中学',
						text: "中学",
						index: 3
					}, {
						value: '高中',
						text: "高中",
						index: 4
					}, {
						value: '中专',
						text: "中专",
						index: 5
					}, {
						value: '大专',
						text: "大专",
						index: 6
					}, {
						value: '本科及以上',
						text: "本科及以上",
						index: 7
					}, {
						value: '研究生',
						text: "研究生",
						index: 8
					}
				],
				// 婚姻情况
				marriedList: [{
					value: null,
					text: '不限',
					index: 0
				}, {
					value: '未婚',
					text: '未婚',
					index: 1
				}, {
					value: '已婚',
					text: '已婚',
					index: 2
				}, {
					value: '离异',
					text: '离异',
					index: 3
				}],
				levelList: [{
					value: null,
					text: "不限",
				}, {
					value: 1,
					text: "一星",
				}, {
					value: 2,
					text: "二星",
				}, {
					value: 3,
					text: "三星",
				}, {
					value: 4,
					text: "四星",
				}, {
					value: 5,
					text: "五星",
				}],
				// 查询条件
				searchCondition: {
					search: "",
					state: null,
					storeId: null,
					workingProperty: null,
					startTime: null,
					endTime: null,
					creTimeRange: null,
					processId: null,
					processIdRange: null,
					education: null,
					married: null,
					ageLow: null,
					ageHigh: null,
					workYearLow: null,
					workYearHigh: null,
					staffLevel: null,
					employeeType: 10,
					isBaomu: 0,
					isAppraisal: 0,
					orderBy: "t.creatDate DESC",
					current: 1,
					size: 10
				},
			}
		},
		methods: {
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openTab(index, index1) {
				this.choiceItemIndex = index1
				let id = this.list[index1].trialId
				if (index == 0) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-resume?id=' + id + "&isAdmin=true"
					})
				} else if (index == 1) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-appraisal?id=' + id
					})
				} else if (index == 2) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-process?id=' + id + "&isAdmin=true"
					})
				} else if (index == 3) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-grounding?id=' + id
					})
				}
			},
			// 选择菜单（一级）
			choiceMenu(e) {
				this.current = e.index
				// 重置筛选
				this.choiceIndex = 2
				this.choiceSearchModeIndex = 0
				this.refreshList()
			},
			// 选择菜单（二级）
			choiceTab(index) {
				this.choiceIndex = index
				// 重置筛选模式
				this.choiceSearchModeIndex = 0
				this.refreshList()
			},
			// 选中单选框
			choiceBox(value, index) {
				if (value == -1) {
					this.choiceSearchModeIndex = this.searchModeList[index].value
					// 记录选择全局筛选之前的分类
					if (index == 0) {
						this.choiceIndex = this.choiceIndexOld
						this.choiceTab(this.choiceIndex)
					} else if (index == 1) {
						this.choiceIndexOld = this.choiceIndex
						this.choiceIndex = 4

						this.searchCondition.state = null
						this.searchCondition.isAppraisal = null
						this.searchCondition.processIdRange = null
					}
				} else if (value == 0) {
					this.choiceCreTimeIndex = index
					this.searchCondition.creTimeRange = this.timeList[index].value
				} else if (value == 1) {
					this.choiceEmployeeTypeIndex = index
					this.searchCondition.employeeType = this.employeeTypeList[index].value
				} else if (value == 2) {
					this.choiceWorkingPropertyIndex = index
					this.searchCondition.workingProperty = this.workingPropertyList[index].value
				} else if (value == 3) {
					this.choiceProcessIdIndex = index
					this.searchCondition.processId = this.processIdList[index].value
				} else if (value == 4) {
					this.choiceEducationIndex = index
					this.searchCondition.education = this.educationList[index].value
				} else if (value == 5) {
					this.choiceMarriedIndex = index
					this.searchCondition.married = this.marriedList[index].value
				} else if (value == 6) {
					this.choiceAgeIndex = index
				} else if (value == 7) {
					this.choiceWorkYearIndex = index
				} else if (value == 8) {
					this.choiceOrderByIndex = index
					this.searchCondition.orderBy = this.orderByList[index].value
				} else if (value == 9) {
					this.choiceLevelIndex = index
				}
			},
			// 刷新保姆列表
			refreshList() {
				let index = this.choiceIndex
				this.clearTabChoice()
				// 默认检索参数
				if (index == 0) {

				} else if (index == 1) {
					this.searchCondition.isAppraisal = 0
				} else if (index == 2) {
					this.searchCondition.isAppraisal = 0
				} else if (index == 3) {
					this.searchCondition.isAppraisal = 1
				}
				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {

			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
			},
			// 清空筛选条件（搜索框和单选框）
			cleanFilter() {
				this.searchText = ""
				this.searchCondition.startTime = null
				this.searchCondition.endTime = null
				this.choiceCreTimeIndex = 0
				this.choiceEmployeeTypeIndex = 1
				this.choiceWorkingPropertyIndex = 0
				this.choiceProcessIdIndex = 0
				this.choiceEducationIndex = 0
				this.choiceMarriedIndex = 0
				this.choiceAgeIndex = 0
				this.choiceWorkYearIndex = 0
				this.choiceOrderByIndex = 0
				this.choiceLevelIndex = 0

				this.searchCondition.creTimeRange = null

				this.storeName = ""
				this.searchCondition.storeId = null

				// 默认筛选条件
				if (this.choiceIndex == 0) {

				} else if (this.choiceIndex == 1) {

				} else if (this.choiceIndex == 2) {

				} else if (this.choiceIndex == 3) {

				}
			},
			// 开始筛选
			startFilter() {
				this.list = []
				this.total = 0
				this.searchCondition.current = 1
				this.searchCondition.search = this.searchText
				this.searchCondition.post = this.menuList[this.current].value

				this.searchCondition.orderBy = this.orderByList[this.choiceOrderByIndex].value
				this.searchCondition.creTimeRange = this.timeList[this.choiceCreTimeIndex].value
				this.searchCondition.employeeType = this.employeeTypeList[this.choiceEmployeeTypeIndex].value
				this.searchCondition.workingProperty = this.workingPropertyList[this.choiceWorkingPropertyIndex].value
				this.searchCondition.processId = this.processIdList[this.choiceProcessIdIndex].value
				this.searchCondition.education = this.educationList[this.choiceEducationIndex].value
				this.searchCondition.married = this.marriedList[this.choiceMarriedIndex].value
				this.searchCondition.ageLow = this.ageList[this.choiceAgeIndex].ageLow
				this.searchCondition.ageHigh = this.ageList[this.choiceAgeIndex].ageHigh
				this.searchCondition.workYearLow = this.workYearList[this.choiceWorkYearIndex].workYearLow
				this.searchCondition.workYearHigh = this.workYearList[this.choiceWorkYearIndex].workYearHigh
				this.searchCondition.staffLevel = this.levelList[this.choiceLevelIndex].value

				if (this.choiceIndex == 3) {

				}
				// 全局筛选模式
				if (this.choiceIndex == 4) {
					this.searchCondition.state = null
					this.searchCondition.isAppraisal = null
				}

				// 排序规则调整（默认排序下才生效）
				if (this.choiceOrderByIndex == 0) {
					if (this.choiceIndex == 0) {
						this.searchCondition.orderBy = "creatDate DESC"
					} else {
						this.searchCondition.orderBy = "creatDate DESC,entryTime DESC"
					}
				}

				this.getList()
				this.popupShow = false
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.list[index].trialId
				this.http({
					url: 'listTrialStaffLog',
					method: 'POST',
					hideLoading: true,
					data: {
						trialId: id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.logList = res.data
						} else {
							this.logList = []
						}
					}
				})
			},
			// 打开头像预览
			openImgReview(index) {
				this.headPortrait = this.checkStr(this.list[index].headPortrait) != "暂无" ? this.list[index]
					.headPortrait : this.blankImg

				let data = []
				data.push(this.headPortrait)
				uni.previewImage({
					urls: data,
					current: this.headPortrait
				})
			},
			// 侧滑选项改变
			clickOptionChange(index) {
				this.choiceItemIndex = index
			},
			// 点击选项
			clickOption(e) {
				let choiceOption = e.index
				// 判断点击的按钮是什么
				if (this.choiceIndex == 0) {

				} else if (this.choiceIndex == 1) {

				} else if (this.choiceIndex == 2) {
					if (choiceOption == 0) {
						this.$refs.uNotify.error("移除后在鉴定列表将不可见！")
						this.openCheckDetail(5, "移除员工", "确定从鉴定列表中移除吗？")
					} else if (choiceOption == 1) {
						this.$refs.uNotify.error("删除后员工将无法鉴定和上架，请谨慎操作！")
						this.openCheckDetail(4, "删除员工", "确定删除该员工吗？")
					} else if (choiceOption == 2) {
						this.openCheck(8, "流转员工", "流转的员工必须是保姆/月嫂/育儿嫂等工种，流转后可在【保姆】列表查看，确定流转该员工吗？")
					}
				} else if (this.choiceIndex == 3) {
					if (choiceOption == 0) {
						this.openCheckDetail(3, "下架员工", "确定下架该员工吗？")
					} else if (choiceOption == 1) {
						this.openCheckDetail(4, "删除员工", "确定删除该员工吗？")
					} else if (choiceOption == 2) {
						this.openCheck(8, "流转员工", "流转的员工必须是保姆/月嫂等工种，确定流转该员工吗？")
					}
				}

			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			checkSalary(str) {
				if (str == null || str == "" || str == 0) {
					return "暂无"
				} else {
					return str + ""
				}
			},
			checkPeople(no, name) {
				if (no == null || name == null) {
					return "暂无"
				} else {
					return name + "（" + no + "）"
				}
			},
			// 检验当前员工在哪个二级菜单栏目
			checkTab(index) {
				let i = 0
				let state = this.list[index].state
				let processId = this.list[index].processId
				if (state != 1) {
					i = 1
				} else if (state == 1) {
					i = 3
				}
				return this.choiceList[i].choiceTitle
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			// 对请求参数中的简历分范围进行格式化
			formatRange() {

			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '.' + MM + '.' + d
			},
			unqualified(index) {
				this.choiceItemIndex = index
				this.openCheck(7, "头像确定不合格吗？", "将扣除简历分，重置保姆为待完善状态，并通知推荐人！")
			},
			// 更新员工
			updateTrialStaff() {
				this.$set(this.trialStaff, "trialId", this.list[this.choiceItemIndex].trialId)
				this.$set(this.trialStaff, "operatorId", uni.getStorageSync("employeeId") || 0)
				this.http({
					url: 'updateTrialStaff',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.trialStaff,
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("已更新员工！")
						}
					}
				});
			},
			// 员工流转
			staffToEmployee() {
				let data = {
					trialId: this.list[this.choiceItemIndex].trialId,
					employeeId: this.list[this.choiceItemIndex].employeeId || 0,
					operatorId: uni.getStorageSync("employeeId") || 0,
					operatorName: uni.getStorageSync("employeeName") || ""
				}
				this.http({
					url: 'staffToEmployee',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("员工流转成功！请至【保姆】栏目查看！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 更新员工状态
			updateStaffState() {
				this.$set(this.trialStaff, "trialId", this.list[this.choiceItemIndex].trialId)
				this.$set(this.trialStaff, "operatorId", uni.getStorageSync("employeeId") || 0)
				this.http({
					url: 'updateStaffState',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.trialStaff,
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("已更新员工状态！")
						}
					}
				});
			},
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.popupShow = false
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.searchCondition.storeId = this.storeList[index].id
					this.storeName = this.storeList[index].storeName
				}
				this.showPickerMine = false
				this.popupShow = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 催审
			urgeListing(index) {
				uni.showModal({
					title: '员工上架催审',
					content: '是否催审？将会给门店鉴定师发送消息！',
					success: res => {
						if (res.confirm) {
							let item = this.list[index]
							this.http({
								outsideUrl: "https://api.xiaoyujia.com/acn/urgeListing",
								method: 'POST',
								header: {
									'content-type': "application/json;charset=UTF-8"
								},
								data: {
									employeeId: item.employeeId,
									trialId: item.id,
									type: 1,
									storeId: item.storeId,
									operator: uni.getStorageSync('employeeName') || uni.getStorageSync(
										'memberName') || (
										'会员：' +
										uni.getStorageSync('account')),
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('催审成功！请等待鉴定师操作！')
										this.$set(this.list[index], 'hideUrge', 1)
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								}
							})
						}
					}
				});
			},
			// 获取工种字典内容
			getWorkType() {
				this.http({
					url: "getWorkType",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							// 格式化一级菜单
							let workTypeList = res.data
							for (let i = 0; i < workTypeList.length; i++) {
								let str = workTypeList[i].typeName.substring(0, 2)
								let data = {
									index: i + 1,
									name: str,
									value: str
								}
								this.menuList.push(data)
							}
						}
					}
				})
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					url: 'getAllStoreList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
						}
					}
				});
			},
			// 获取保姆列表
			getList() {
				this.formatRange()
				this.http({
					url: 'getTrialStaffPage',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.list = this.list.concat(res.data.records)
							uni.setNavigationBarTitle({
								title: "找员工" + "（" + this.total + "）"
							})
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 打开确认框
			openCheckDetail(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheckDetail.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.list[this.choiceItemIndex].baomuId
					})
				} else if (this.checkType == 1) {
					uni.navigateTo({
						url: "/pages-other/employee/workSkill?baomuId=" + this.list[this.choiceItemIndex].id
					})
				} else if (this.checkType == 2) {
					// 上架员工
					// this.employee = this.list[this.choiceItemIndex]
					// this.employee.state = 1
					// this.employee.putTime = Number(new Date())
					// this.updateBaomuState()
				} else if (this.checkType == 3) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充下架原因！")
						return
					}
					// 下架员工
					this.trialStaff.state = 2
					this.trialStaff.workRemark = "下架原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateStaffState()
				} else if (this.checkType == 4) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充删除原因！")
						return
					}
					// 删除员工
					this.trialStaff.state = 4
					this.trialStaff.workRemark = "删除原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateStaffState()
				} else if (this.checkType == 5) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充移除原因！")
						return
					}
					// 移除员工
					this.trialStaff.staffLevel = -1
					this.trialStaff.workRemark = "移除原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateTrialStaff()
				} else if (this.checkType == 6) {

				} else if (this.checkType == 7) {

				} else if (this.checkType == 8) {
					this.staffToEmployee()
				}
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast("您还未进行登录哦，登录后可体验更多功能！")
					uni.setStorageSync('redirectUrl', '/pages-other/hr/staff')
					return false
				} else {
					let no = uni.getStorageSync("employeeNo") || 0
					this.searchCondition.introducer = no
					this.getWorkType()
					this.getList()
					this.getStoreList()
					return true
				}
			}
		},
		watch: {
			startLoadMore: {
				handler(newValue, oldVal) {
					console.log("滑动到底部加载更多...")
					this.searchCondition.current++
					this.getList()
				},
				deep: true
			}
		},
		onReachBottom() {
			console.log("滑动到底部加载更多...")
			this.searchCondition.current++
			this.getList()
		},
		onShow() {
			// this.list = []
			// this.getList()
		},
		onLoad() {
			let memberId = uni.getStorageSync('memberId')
			this.memberId = memberId
			if (this.roleId == 110 || this.roleId == 112) {
				this.canPut = true
			}
			this.checkLogin()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";

	.choice-menu {
		width: 60%;
	}

	page {
		height: auto;
		background-color: #ffffff;
	}

	/deep/ .u-transition,
	.u-back-top {
		z-index: 999 !important;
	}

	// 保姆列表栏目
	.baomu-tab {
		width: 100%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 40rpx 0;
		display: flex;
		flex-direction: row;
	}

	// 栏目左边
	.tab-left {
		float: left;
		width: 25%;
		height: 100%;
	}

	.tab-left-img {
		img:nth-child(1) {
			display: block;
			width: 170rpx;
			height: 240rpx;
			border-radius: 15rpx;
			margin: 20rpx 0 0 12%;
		}

		img:nth-child(2) {
			display: block;
			width: 170rpx;
			height: 60rpx;
			margin: -60rpx auto 0rpx 22rpx;
		}
	}

	.tab-left-btn {
		padding: 20rpx 10rpx;
		margin: 0 15%;
		width: 70%;
	}

	// 栏目右边
	.tab-right {
		float: right;
		display: flex;
		flex-direction: column;
		width: 69%;
		height: auto;
		padding: 0 3%;
	}

	.tab-name {
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		font-weight: bold;
		line-height: 80rpx;
	}

	.tab-info {
		text {
			font-size: 36rpx;
			line-height: 60rpx;
			padding: 0 5rpx;
		}
	}

	.tab-text {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}

	.skills-content {
		width: 100%;
		height: auto;
	}

	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 95%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 弹窗输入区域
	.popupCheck-inputbox {
		display: flex;
		flex-direction: column;
		width: 100%;
		line-height: 60rpx;

		text {
			display: block;
			margin: -10rpx;
			text-align: center;
			font-size: 32rpx;
		}
	}

	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}

	.my-tag {
		float: left;
		padding: 0;
		width: auto;
		height: auto;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 10rpx 0rpx;
		padding: 0 10rpx;
	}

	.button-left,
	.button-right {
		width: 70%;
		height: 60rpx;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		color: #f6cc70;
		background-color: #1e1848;

		text {
			height: 60rpx;
			line-height: 60rpx;
			font-size: 32rpx;
		}
	}

	.img-delete {
		position: absolute;
		margin: -230rpx 0 0 140rpx;
	}

	.log-list {
		width: 100%;
		height: auto;
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.small-tag {
		display: flex;
		width: 120rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #fff;
		background-color: #f6cc70;
		border-radius: 10rpx;
		margin: 30rpx 0;

		img {
			display: block;
			width: 30rpx;
			height: 30rpx;
			margin: 7rpx 0 0 10rpx;
		}

		text {
			display: block;
			width: 60%;
			text-align: center;
		}
	}
</style>