<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 须知提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="t-indent fb f18 lh40 mg-at">
				积分规则
			</view>
			<view style="height: 400rpx;">
				<view class="f16 lh30" v-for="(item,index) in ruleList" :key="index" style="margin: 0 40rpx;">
					{{item}}
				</view>
			</view>
		</u-popup>

		<view class="w9 mg-at text-r f14" style="margin-top: 20rpx;" @click="popupShow=true">规则</view>
		<u-sticky bgColor="#fff">
			<u-tabs :list="tabslist" :scrollable="false" lineWidth="35" lineHeight="2" lineColor="#10027c" :activeStyle="{
			        color: '#10027c',
			    }" itemStyle="padding: 10rpx" @click="clickTabs" />
		</u-sticky>

		<view class="w85 flac-row-b" style="margin: 40rpx auto;" v-for="(item,index) in list" :key="index"
			v-if="showItem(index)">
			<view class="w7 flac-row">
				<view class="f14 lh20" style="margin-left: 40rpx;">
					<view class="fb">{{item.changeTypeName}}</view>
					<view class="f12 c9">{{item.createTime}}</view>
				</view>
			</view>
			<view :class="item.changeValue > 0 ? 'red' : 'green'">
				{{item.changeValue > 0 ? '+' : ''}}{{item.changeValue}}
			</view>
		</view>
		<u-empty v-if="!list.length" text="暂无记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />
	</view>
</template>

<script>
	var allArr = [],
		outlayArr = [],
		incomeArr = []
	export default {
		data() {
			return {
				popupShow: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				ruleList: [
					"1.开发种子客户/种子员工可获得财富值！",
					"2.财富值后续可用于兑换奖励！",
				],
				choiceIndex: 0,
				memberId: uni.getStorageSync('memberId') || null,
				employeeId: uni.getStorageSync('employeeId') || null,
				tabslist: [{
					name: '全部',
					type: null,
				}, {
					name: '收入',
					type: 1,
				}, {
					name: '支出',
					type: 3,
				}],
				list: []
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			showItem(index) {
				let changeValue = this.list[index].changeValue
				let result = false
				if (this.choiceIndex == 0) {
					result = true
				} else if (this.choiceIndex == 1 && changeValue > 0) {
					result = true
				} else if (this.choiceIndex == 2 && changeValue < 0) {
					result = true
				}
				return result
			},
			clickTabs(e) {
				this.choiceIndex = e.index
			},
			// 获取积分数量
			listUnionMemberWealthRecord() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/member/listUnionMemberWealthRecord",
					method: 'POST',
					data: {
						unionMerchantId: parseInt(this.unionMerchantId)
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						}
					},
				})
			},
		},
		onLoad(options) {
			this.unionMerchantId = options.id || 0
			this.listUnionMemberWealthRecord()
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-tabs {
		width: 70%;
		margin: auto;
	}

	/deep/ .u-tabs__wrapper__nav__item__text {
		font-size: 14px;
	}
</style>