<template>
	<view class="bg-style">
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>
		<view v-if="certType<3">
			<img :src="certImg" mode="widthFix" style="width: 100%;" @click="openImgPreview(certImg)" />
			<view class="btn-big" @click="saveToPhone">
				<button>保存证书</button>
			</view>
		</view>

		<view v-else>
			<view class="back-img flac-col">
				<view class="head-tab1 fb">
					<img :src="dom.headImg||blankImg" class="" @click="openImgPreview(dom.headImg||blankImg)" />
					<text class="f20">{{dom.storeId?dom.storeName:dom.realName}}</text>
				</view>
				<view class="head-tab2 flac-row-c f14">
					<view class="flac-row mg-at" v-for="(item,index) in tabList" :key="index" @click="oepnTab(index)"
						v-if="item.typeList.includes(certType)">
						<uni-icons :type="item.icon" size="20"></uni-icons>
						<text>{{item.name}}</text>
					</view>
				</view>
			</view>
			<u-gap height="10"></u-gap>

			<view class="content-tab flac-col f16 lh40">
				<view class="flac-row">
					<text class="fb f18"> {{levelList[level-1]}}{{certType==3?'门店':certType==4?'导师':'员工'}}</text>
					<uni-rate :value="level" readonly="true" style="margin-left: 15rpx;" />
				</view>
				<view>
					<text style="color:#909399">资质颁发时间：</text>
					{{formatDate(dom.creTime,0)}}
				</view>
			</view>

			<view class="content-tab flac-col f16 lh40">
				<view class="flac-row">
					<text class="fb f18">其他信息</text>
				</view>
				<view>
					<text style="color:#909399">资质编号：</text>
					{{dom.certCode}}
				</view>
				<view v-if="serviceField">
					<text style="color:#909399">服务领域：</text>
					{{serviceField}}
				</view>
				<view v-if="settleInTime">
					<text style="color:#909399">入驻时间：</text>
					{{formatDate(settleInTime,0)}}
				</view>
				<view v-if="certType==3">
					<text style="color:#909399">门店地址：</text>
					{{store.cityName}}{{store.areaName}}
				</view>
				<view v-if="certType==4||certType==5">
					<text style="color:#909399">个人简介：</text>
					{{introduce|| '暂无'}}
				</view>
			</view>
		</view>

		<view class="w9 flac-col text-c mg-at" style="color: #909399;margin: 100rpx auto;">
			<view class="">
				本页面的信息仅为门店/员工的基本资质展示，星级、服务领域信息为平台依据门店/员工填写信息的展示，仅供参考使用，不视为平台对该门店/员工的推荐或保证。
			</view>
			<view class="">
				联系电话：<text style="color: #00aaff;">0592-5178888</text>
			</view>
		</view>

		<u-gap height="100"></u-gap>

		<view class="btn-bottom-fixed" @click="openChat">
			<button>免费咨询</button>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 证书类型（0：普通证书 1：课程结业证书 2：联盟认证证书 3：门店资质 4：讲师资质 5：技能资质）
				certType: 0,
				// 开放下载
				allowDownload: false,

				headImgHeight: [470, 375, 455],
				titleHeight: [718, 654, 650],
				titleHeight1: [810, 780, 860],
				dateHeight: [1200, 1060, 1140],
				sealHeight: [1120, 980, 1060],

				sealImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/sealImg.png',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/cert_search_head.png',
				certImg: '',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				storeId: uni.getStorageSync("storeId") || null,
				certId: null,
				dom: null,
				store: null,
				employee: null,
				courseTeacher: null,
				// 星级
				level: 3,
				settleInTime: '',
				serviceField: '',
				introduce: '',
				levelList: ['一星', '二星', '三星', '四星', '五星'],
				tabList: [{
						name: '已入驻平台',
						icon: 'checkbox',
						type: 0,
						typeList: [3, 4, 5]
					},
					{
						name: '缴纳保证金',
						icon: 'calendar',
						type: 0,
						typeList: [3]
					},
					{
						name: '实名认证',
						icon: 'staff',
						type: 0,
						typeList: [4, 5]
					},
					{
						name: '认证资质',
						icon: 'wallet',
						type: 1,
						typeList: [3, 4, 5]
					}
				]
			}
		},
		methods: {
			oepnTab(index) {
				let item = this.tabList[index]
				// 尝试下载
				if (item.type == 1) {
					this.saveToPhone()
				}
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开聊一聊
			openChat() {
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfcd10f238c56ecf31f' //客服地址链接
					},
					corpId: 'wx8342ef8b403dec4e', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				let param = {
					url: 'https://work.weixin.qq.com/kfid/kfcd10f238c56ecf31f'
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
			formatDate(value, value1) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				// return this.$moment().format('YYYY-MM-DD')
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				if (value1 == 0) {
					return y + '.' + MM + '.' + d
				} else {
					return y + '年' + MM + '月' + d + '日'
				}
			},
			formatStoreName(name) {
				if (!name) {
					return ''
				}
				name = name.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				return name
			},
			formatArray(str) {
				if (!str) {
					return ''
				}
				if (!str.includes(',')) {
					return str
				}
				let result = ''
				let array = str.split(',')
				array.forEach(item => {
					result += item + ' | '
				})
				if (result.length > 2) {
					result = result.substring(0, result.length - 2)
				}
				return result
			},
			// 保存到手机
			saveToPhone() {
				if (this.certType == 3) {
					if (this.dom.storeId != this.storeId) {
						return this.$refs.uNotify.warning('非门店员工无法进行下载!')
					}
				} else if (this.certType == 4 || this.certType == 5) {
					if (this.dom.employeeId != this.employeeId) {
						return this.$refs.uNotify.warning('仅资质拥有者本人可下载!')
					}
				}
				this.openImgPreview(this.certImg)
				uni.downloadFile({
					url: this.certImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								this.$refs.uNotify.success('图片保存成功!')
							},
							fail: (err) => {
								this.$refs.uNotify.error('图片保存失败!')
							}
						})
					}
				})
			},
			// 获取证书图片
			getWxShareImg() {
				if (this.dom.certImg) {
					return
				}
				let title = this.dom.storeId ? this.formatStoreName(this.dom.storeName) : this.dom.realName
				let titleIndex = this.dom.certType - 3
				let idCard = this.dom.certType == 5 ? this.dom.idcard : ''
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					hideLoading: true,
					data: {
						textList: [{
								"text": title,
								"fontSize": 35,
								"isCenter": true,
								"isBold": true,
								"color": "0xe0ad40",
								"x": 0,
								"y": this.titleHeight[titleIndex]
							},
							{
								"text": this.dom.certCode,
								"fontSize": 30,
								"isCenter": true,
								"color": "0xe0ad40",
								"x": 0,
								"y": this.titleHeight1[titleIndex]
							},
							{
								"text": idCard,
								"fontSize": 25,
								"isCenter": false,
								"color": "0xe0ad40",
								"x": 330,
								"y": 705
							}, {
								"text": '发证日期：' + this.formatDate(this.dom.creTime, 1),
								"fontSize": 25,
								"isCenter": false,
								"color": this.dom.certType == 3 ? '0xf5d0a3' : '0x1d364b',
								"x": 360,
								"y": this.dateHeight[titleIndex]
							}
						],
						imgList: [{
								url: this.dom.headImg || this.blankImg,
								width: 170,
								height: 170,
								roundCorner: 170,
								x: 290,
								y: this.headImgHeight[titleIndex]
							},
							{
								url: this.sealImg || this.blankImg,
								width: 140,
								height: 140,
								roundCorner: 140,
								x: 400,
								y: this.sealHeight[titleIndex]
							}
						],
						img: this.dom.certPreview,
						source: 'xyjacn',
						type: 1,
						maxWidth: 750
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.certImg = res.data
							this.updateUnionCertRecord()
						}
					}
				})
			},
			updateUnionCertRecord() {
				if (this.dom.certImg) {
					return
				}
				this.$set(this.dom, 'certImg', this.certImg)
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/updateUnionCertRecord',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.dom,
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 获取证书详情
			getUnionCertRecordById() {
				if (!this.certId) {
					return
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/getUnionCertRecordById',
					method: 'GET',
					hideLoading: true,
					path: this.certId,
					success: res => {
						if (res.code == 0) {
							this.dom = res.data
							this.certType = res.data.certType || 0
							this.certImg = res.data.certImg || ''
							this.getWxShareImg()
							if (this.certType == 3) {
								this.getStore()
							} else if (this.certType == 4 || this.certType == 5) {
								this.getEmployeeDtoById()
							}
						}
					}
				})
			},
			getStore() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/system/getStoreById',
					method: 'GET',
					hideLoading: true,
					path: this.dom.storeId,
					success: res => {
						if (res.code == 0) {
							this.store = res.data
							this.settleInTime = this.store.createTime || this.settleInTime
							this.level = this.store.storeLevel || this.level
							this.serviceField = '招商 | 招生 | 三嫂单 | 保姆单'
						}
					}
				})
			},
			getTeacher() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/course/getCourseTeacherByEmployeeId',
					method: 'GET',
					hideLoading: true,
					path: this.dom.employeeId || 0,
					success: res => {
						if (res.code == 0) {
							this.courseTeacher = res.data
							this.settleInTime = this.courseTeacher.createTime
							this.level = this.courseTeacher.teacherLevel || this.level
							this.introduce = this.courseTeacher.teacherIntroduction || ''
						} else {
							this.settleInTime = this.dom.creTime || this.settleInTime
						}
					}
				})
			},
			getEmployeeDtoById() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/getEmployeeDtoById',
					method: 'GET',
					hideLoading: true,
					path: this.dom.employeeId,
					success: res => {
						if (res.code == 0) {
							this.employee = res.data
							this.settleInTime = this.employee.createDate || this.settleInTime
							this.serviceField = this.formatArray(this.employee.workType) || '家政行业'
							this.introduce = this.employee.introduce || this.introduce
						}
						if (this.certType == 4) {
							this.getTeacher()
						}
					}
				})

			},
		},
		onLoad(options) {
			this.certId = options.id || null
			this.getUnionCertRecordById()
		},
	}
</script>
<style lang="scss">
	.bg-style {
		width: 100%;
		min-height: 100vh;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/cert_search_head.png') no-repeat center;
		background-size: 100% 100%;
	}

	.btn-big {
		button {
			margin: 40rpx auto;
			width: 40%;
			height: 70rpx;
			line-height: 70rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.back-img {
		img {
			display: block;
			width: 100%;
			height: 450rpx;
		}
	}

	.head-tab1 {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 80%;
		margin: 70rpx auto;

		img {
			display: block;
			width: 160rpx;
			height: 160rpx;
			border-radius: 50%;
		}

		text {
			margin-top: 40rpx;
			text-align: center;
		}
	}

	.head-tab2 {
		position: absolute;
		width: 80%;
		top: 340rpx;
		left: 10%;
		color: #909399;
	}

	.content-tab {
		width: 84%;
		margin: 40rpx 4%;
		padding: 10rpx 4%;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #fff;
		background-color: #fff;
	}

	// 底部固定按钮
	.btn-bottom-fixed {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 20rpx 5%;
			width: 90%;
			height: 80rpx;
			line-height: 80rpx;
			color: #fdd472;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>