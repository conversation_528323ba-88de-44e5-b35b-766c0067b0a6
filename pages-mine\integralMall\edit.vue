<template>
	<view>
		
		<view class="content">
			<view class="row">
				<view class="nominal">
					姓名
				</view>
				<view class="input">
					<input placeholder="请输入姓名" type="text" v-model="addressInfo.name" />
				</view>
			</view>
			<view class="row">
				<view class="nominal">
					电话号码
				</view>
				<view class="input">
					<input placeholder="请输入电话号码" type="text" v-model="addressInfo.phone" />
				</view>
			</view>
			<view class="row">
				<view class="nominal">
					所在地区
				</view>
				<view class="input" @click="toggleMaskLocation">
					{{addressByPcrs}}
				</view>

			</view>
			<view class="row">
				<view class="nominal">
					所在地址
				</view>
				<view class="input">
					<view @click="onChangeDetail()">{{addressInfo.street || '点我进行定位区域选择'}}</view>
					<!-- <u--input @change="onChangeDetail" v-model="addressInfo.street" placeholder="选择你需要的服务地址"
						suffixIcon="map-fill" suffixIconStyle="color: #909399" border="none"></u--input> -->
				</view>
			</view>
			<view class="row">
				<view class="nominal">
					详细地址
				</view>
				<view class="input">
					<textarea v-model="streetinfo" auto-height placeholder="请输入你的详细地址"></textarea>
				</view>
			</view>

			<view class="row">
				<view class="nominal">
					住宅面积
				</view>
				<view class="remarkStyle">
					<view style="width: 50%;height: 70rpx;" v-for="(item,index) in areas" :key="index">
						<uni-tag
							custom-style=" display:inline-block;width:220rpx;height:43rpx;line-height:43rpx;text-align:center;font-weight:500;overflow: hidden;text-overflow: ellipsis;"
							type="warning" :inverted="item.flag" :text="item.text" @click="changeTag(index)" />
					</view>
				</view>
			</view>

			<view class="row">
				<view class="nominal">
					设置默认地址
				</view>
				<view class="input switch">
					<switch color="#f9ae3d" :checked="addressInfo.isDefault" @change="isDefaultChange" />
				</view>
			</view>
		</view>
		<view class="save" @click="save">
			<view class="btn">
				保存地址
			</view>
		</view>
		<gk-city :headtitle="headtitle" :provincedata="provincedata" :data="selfData" mode="cityPicker" ref="cityPicker"
			@funcvalue="getpickerParentValue" :pickerSize="2"></gk-city>

		<u-popup :show="showPopup" @close="closepopup" :closeOnClickOverlay="false">
			<u-list height="400px">
				<u-list-item class="searchAddress" v-for="(item,index) in searchResult" :key="index" v-show="showSearchResult">
					<u-cell :title="item.name" :value="item.address" :label="item.district" style="background: #ddd"
						@click="changeAddress(item)" />
				</u-list-item>
			</u-list>

		</u-popup>
	</view>
</template>

<script>
	import provinceData from '../common/js/city.data.js';
	// import uniLocation from 'uni-location';
	export default {
		data() {
			return {
				addressList: [],
				streetinfo: '',
				addressInfo: {
					memberId: '',
					id: '',
					name: '',
					phone: '',
					street: '',
					city: '',
					area: '',
					cityId: '',
					areaId: '',
					lng: '',
					lat: '',
					isDefault: false,
					remark: ''
				},
				areas: [{
					text: '80m²以内',
					flag: true
				}, {
					text: '80m²',
					flag: true
				}, {
					text: '80-99m²',
					flag: true
				}, {
					text: '100-119m²',
					flag: true
				}, {
					text: '120-139m²',
					flag: true
				}, {
					text: '140-159m²',
					flag: true
				}, {
					text: '160m²以上',
					flag: true
				}],
				showSearchResult: false,
				searchResult: [],
				showPopup: false,
				provincedata: [{
					text: '请选择',
					value: ''
				}],
				selfData: null,
				addressByPcrs: "请选择所在地",
				headtitle: "请选择所在地",
				editType: 'edit',
			};
		},
		methods: {
			changeTag(index) {
				//先把所有的flag全部变为true,把选中的变为false即可
				const newArr = this.areas.map(item => {
					return {
						...item,
						flag: true
					};
				});
				this.areas = newArr;
				this.areas[index].flag = !this.areas[index].flag;
				this.$set(this.areas, index, this.areas[index])
				let re = this.areas[index].text;
				this.addressInfo.remark = re;

			},
			closepopup() {
				this.showPopup = false;
			},
			changeAddress(val) {
				this.showSearchResult = false;
				this.showPopup = false;
				console.log(val.district)
				if (val.location.length > 0) {
					let locationArr = val.location.split(",");
					this.addressInfo.lng = locationArr[0];
					this.addressInfo.lat = locationArr[1];
				}
				this.addressInfo.street = val.name;
				this.searchCityInfo = val.district
			},
			async geocoder(coder) {
				await this.http({
					outsideUrl: 'https://api.xiaoyujia.com/openapi/getAddressBylat',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {'lat':coder.latitude,'lng':coder.longitude},
					success: res => {
						if (res.code === 0) {
							let city = res.data.city;
							let district = res.data.district;
							console.log(city + '-----' + district)
							if (district) {
								if (!district.includes(this.addressInfo.area)) {
									console.log(district + '----' + this.addressInfo.area)
									this.addressInfo.street = ''
									this.streetinfo = ''
									return uni.showToast({
										title: '当前地图区域不在' + this.addressInfo.area + '，请重新选择',
										duration: 3000,
										icon: 'none'
									})
								}
							
							} else {
								if (!city.includes(this.addressInfo.city)) {
									this.addressInfo.street = ''
									this.streetinfo = ''
									return uni.showToast({
										title: '当前地图城市不在' + this.addressInfo.city + '，请重新选择',
										duration: 3000,
										icon: 'none'
									})
								}
							}
							this.addressInfo.street = coder.name
							this.addressInfo.lng = coder.longitude
							this.addressInfo.lat = coder.latitude
						} else {
							uni.showToast({
								title:res.msg,
								icon:'none'
							})
						}
					},
					fail: err => {
						console.log(res)
					}
				})
			},
			onChangeDetail() {
				if (!this.addressInfo.cityId || !this.addressInfo.areaId) {
					return uni.showToast({
						title: '请先选择所在地区'
					})
				}
				uni.chooseLocation({
					success: res => {
						const json = JSON.stringify(res);
						console.log('位置：' + json);
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						if (res.name == null || res.name == '' || res.address == null || res.address == '') {
							return this.$toast.toast('请先选择好地址后再点击确认');
						}
						this.geocoder(res);
					},
					fail: res => {
						// console.info('fail: ' + JSON.parse(res));
						console.info(res);
						console.info(JSON.parse(JSON.stringify(res)));
					}
				});

				// let ref = this;
				// if (val) {
				// 	uni.request({
				// 		method: "get", //type可以为post也可以为get
				// 		url: "https://restapi.amap.com/v3/assistant/inputtips?key=d9fd5809c0198941162d063fe9431861&keywords=" +
				// 			val + "&type=&location=&city=" + this.addressInfo.city + "&datatype=poi",
				// 		data: {
				// 			citylimit: true
				// 		}, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
				// 		dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
				// 		success: function(data) {
				// 			//有数据在展示，否则不展示框
				// 			if (data.data.tips.length > 0) {
				// 				ref.searchResult = [];
				// 				ref.showPopup = true;
				// 				ref.showSearchResult = true;
				// 				data.data.tips.forEach(
				// 					function(item, index) {
				// 						if (item.address.length >= 12) {
				// 							item.address = item.address.substring(0, 12);
				// 						}
				// 						ref.searchResult.push({
				// 							name: item.name,
				// 							address: item.address,
				// 							location: item.location,
				// 							district: item.district
				// 						})
				// 					})
				// 			}
				// 		},
				// 		error: function(data) {
				// 			// alert("出现了错误！");
				// 		}
				// 	})
				// 	console.log(this.searchResult)
				// }

			},
			getpickerParentValue(data) {
				this.provincedata = data;
				this.addressByPcrs = data[0].text + " " + data[1].text + " ";
				// console.log(data[0])
				// console.log(data[0].text + "----" + data[0].value)
				// console.log(data[1].text + "----" + data[1].value)
				this.addressInfo.city = data[0].text;
				this.addressInfo.area = data[1].text;
				this.addressInfo.areaId = data[1].value;
				this.addressInfo.cityId = data[0].value;
			},
			getcityArea() {
				let ref = this;
				uni.request({
					type: "get", //type可以为post也可以为get
					url: "https://appapi.xiaoyujia.com/api/other/cityarea",
					data: {}, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中

					success: function(data) {
						data.data.Data.forEach(v => {
							v.text = v.Name + "市"
							v.value = v.Id
							v.children = v.Areas
							v.Areas.forEach(t => {
								t.text = t.Name
								t.value = t.Id
							})

						})

						ref.selfData = data.data.Data
						// console.log(ref.selfData)

					},
					error: function(data) {
						console.log("出现错误")
					}
				})
			},
			toggleMaskLocation() {
				this.$nextTick(() => {
					this.$refs["cityPicker"].show();
				})
			},
			onCancel(e) {
				console.log(e)
			},
			isDefaultChange(e) {
				this.addressInfo.isDefault = e.detail.value;
			},
			save() {
				console.log(JSON.stringify(this.addressInfo))
				let data = this.addressInfo;

				if (!data.name) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					});
					return;
				}
				if (!data.phone) {
					uni.showToast({
						title: '请输入电话号码',
						icon: 'none'
					});
					return;
				}
				if (!data.cityId) {
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none'
					});
					return;
				}
				if (!data.street) {
					uni.showToast({
						title: '请输入详细地址',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '正在提交'
				})
				if (this.streetinfo) {
					this.addressInfo.street = this.addressInfo.street + '|' + this.streetinfo;
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/member/saveMemberAddr',
					method: 'POST',
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code === 0) {
							uni.hideLoading();
							uni.showToast({
								title: '保存地址成功'
							});
							uni.navigateBack();
						} else {
							uni.hideLoading();
							uni.showToast({
								title: res.data,
								icon: 'error'
							});
							return;
						}
						console.log(res)
					},
					fail: err => {
						console.log(res)
					}
				})


			}
		},
		onLoad(e) {
			//获取传递过来的参数
			this.getcityArea()
			this.editType = e.type;
			if (e.type == 'edit') {
				uni.getStorage({
					key: 'editAddress',
					success: (e) => {
						this.addressInfo = e.data;
						this.streetinfo = e.data.streetinfo
						this.addressByPcrs = e.data.city + e.data.area;
						console.log(e.data)
					}
				})
			} else {
				this.addressInfo.phone = uni.getStorageSync('account');
				
			}
			console.log(e.memberId)
			this.addressInfo.memberId = e.memberId
		},
	};
</script>
<style lang="scss">
	.remarkStyle {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		width: 100%;
		margin: 40rpx auto;
	}

	.save {
		view {
			display: flex;
		}

		position: fixed;
		bottom: 0;
		width: 100%;
		height: 120upx;
		display: flex;
		justify-content: center;
		align-items: center;

		.btn {
			box-shadow: 0upx 5upx 10upx rgba(0, 0, 0, 0.4);
			width: 70%;
			height: 80upx;
			border-radius: 80upx;
			background-color: #f9ae3d;
			color: #fff;
			justify-content: center;
			align-items: center;

			.icon {
				height: 80upx;
				color: #fff;
				font-size: 30upx;
				justify-content: center;
				align-items: center;
			}

			font-size: 30upx;
		}
	}

	.content {
		display: flex;
		flex-wrap: wrap;

		view {
			display: flex;
		}

		.row {
			width: 94%;

			margin: 0 3%;
			border-top: solid 1upx #eee;

			.nominal {
				width: 30%;
				height: 120upx;
				font-weight: 200;
				font-size: 30upx;
				align-items: center;
			}

			.input {
				width: 70%;
				padding: 20upx 0;
				align-items: center;
				font-size: 30upx;

				&.switch {
					justify-content: flex-end;
				}

				.textarea {
					margin: 20upx 0;
					min-height: 120upx;
				}
			}

			.del {
				width: 100%;
				height: 100upx;
				justify-content: center;
				align-items: center;
				font-size: 36upx;
				color: white;
				background-color: #f9ae3d;
				border-bottom: solid 1upx #eee;
			}
		}
	}
</style>