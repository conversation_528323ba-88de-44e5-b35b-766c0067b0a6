<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view>
			<u-sticky style="padding-bottom: 20rpx;">
				<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="22" lineHeight="8"
					:lineColor="`url(${lineBg}) 100% 100%`" :scrollable="true" :activeStyle="{
					color: '#fff',
					fontWeight: 'bold',
					transform: 'scale(1.1)'
				}" :inactiveStyle="{
					color: '#fff',
					fontSize: '16px'
				}" itemStyle="padding: 0 30rpx; height: 100rpx;">
				</u-tabs>
			</u-sticky>
		</view>

		<view class="w85 bacf radius15" style="margin: 20rpx auto;padding: 20rpx 40rpx;" v-for="(item,index) in list"
			:key="index" @click="openDetail(index)" @longpress="tryDelete(index)">
			<view class="f18 fb lh50 flac-row" :class="{ 'c9' : (item.informState || 0) == 1 }">
				<uni-icons style="margin: 0 8rpx 0 0;" type="smallcircle-filled" size="12" v-if="item.informState == 0"
					color="#19be6b"></uni-icons>
				<text>{{item.informTitle}}</text>
				<uni-tag text="待办" v-if="item.informCollect==1" type="error" style="margin:-10rpx 0 0 10rpx;"></uni-tag>
			</view>
			<view class="f16 c3 textCon lh25">{{item.informContent}}</view>
			<view class="flac-row-b lh40">
				<view class="f15 c6 flac-row">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-rz.png"
						size="18" />
					<view class="t-indent">{{item.informTypeName}} {{item.creTime}}</view>
				</view>
				<view class="c2988d5 f16 fb">{{item.senderName || "管理员"}}</view>
			</view>
		</view>

		<u-empty v-if="list.length == 0" text="暂无消息通知" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="bacf" style="position: fixed;bottom: 20%;right:37rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #f4f4f5 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;" @click="goCollect">
			<uni-icons type="notification-filled" :size="20" color="#f6cc70" v-if="showCollect"></uni-icons>
			<uni-icons type="notification" :size="20" color="#1e1848" v-else></uni-icons>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'inform',
		props: {
			refresh: {
				type: Boolean
			},
			loadMore: {
				type: Number
			}
		},
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || 0,

				showCollect: false,
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile2.png',
				choiceIndex: 0,
				choiceItemIndex: 0,
				menuList: [{
					"name": "全部"
				}, {
					"name": "公告",
				}, {
					"name": "政策",
				}, {
					"name": "奖励",
				}, {
					"name": "惩罚",
				}],
				searchCondition: {
					memberId: uni.getStorageSync("memberId") || 0,
					employeeId: uni.getStorageSync("employeeId") || 0,
					informGroupingList: null,
					orderBy: "u.creTime DESC,u.informState ASC",
					informCollect: null,
					informState: null,
					current: 1,
					authId: uni.getStorageSync('authId') || 0,
					size: 10
				},
				total: 0,
				list: [],
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 点击切换菜单（一级）
			choiceMenu(e) {
				this.choiceIndex = e.index
				this.refreshList()
			},
			// 刷新列表
			refreshList() {
				let index = this.choiceIndex
				this.searchCondition.informGrouping = this.menuList[index].id
				this.list = []
				this.startFilter()
			},
			// 开始筛选
			startFilter() {
				this.list = []
				this.total = 0
				this.searchCondition.current = 1

				this.searchCondition.informCollect = this.showCollect ? 1 : null
				this.getList()
			},
			tryDelete(index) {
				this.choiceItemIndex = index
				this.openCheck(0, "确定删除这条通知吗", "删除后该通知将不可见！")
			},
			deleteUnionInform() {
				let id = this.list[this.choiceItemIndex].id
				this.http({
					url: 'deleteUnionInform',
					method: 'POST',
					hideLoading: true,
					data: {
						id: id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("删除消息通知成功！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			getList() {
				this.http({
					url: 'getUnionInformPage',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.list = this.list.concat(res.data.records)
						}
					}
				})
			},
			getUnionInformMenu() {
				this.http({
					url: 'getUnionInformMenu',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: uni.getStorageSync("memberId") || 0,
						employeeId: uni.getStorageSync("employeeId") || 0,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.menuList = res.data
							this.menuList.forEach(item => {
								let data = {}
								this.$set(data, "value", item.unreadCount)
								this.$set(item, "badge", data)
								if (item.name.includes('全部')) {
									this.$emit('menuChange', item.unreadCount)
								}
							})
						} else {
							this.menuList = []
						}
					}
				})
			},
			goCollect() {
				let tips = ["已显示我的待办", "已显示全部消息"]
				this.showCollect = !this.showCollect
				let text = this.showCollect ? tips[0] : tips[1]
				this.$refs.uNotify.success(text)
				this.refreshList()
			},
			openDetail(index) {
				let id = this.list[index].id
				let state = this.list[index].informState
				if (state == 0) {
					this.list[index].informState = 1
				}

				uni.navigateTo({
					url: '/pages-mine/inform/inform-detail?id=' + id
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.deleteUnionInform()
				}
			},
			async refreshPage() {
				this.list = []
				await this.getUnionInformMenu()
				this.getList()
			}
		},
		watch: {
			refresh: {
				handler(newValue, oldVal) {
					this.getUnionInformMenu()
				},
				deep: true
			},
			loadMore: {
				handler(newValue, oldVal) {
					this.searchCondition.current++
					this.getList()
				},
				deep: true
			}
		},
		onShow() {
			this.refreshPage()
		},
		mounted() {
			this.refreshPage()
		},
		onLoad(options) {
			if (options.showCollect) {
				this.showCollect = true
			}

			if (options.state) {
				this.searchCondition.informState = options.state
			}
		}

	}
</script>

<style lang="scss" scoped>
	page {
		height: auto;
		background-color: #f4f4f5;
	}

	.textCon {
		text-overflow: ellipsis;
		display: -webkit-box;
		/* 限制在一个块元素显示的文本的行数（第几行裁剪） */
		-webkit-line-clamp: 2;
		/* 设置或检索伸缩盒对象的子元素的排列方式 */
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.c2988d5 {
		color: #2988d5;
	}

	/deep/.u-sticky {
		color: #fff;
		background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
		box-shadow: 0rpx 12rpx 13rpx 0rpx rgba(38, 36, 128, 0.47);
	}
</style>