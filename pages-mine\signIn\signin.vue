<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 人脸识别相机 -->
		<cameraPage :showCameraPage="showCameraPage" @submitCameraPhoto="submitCameraPhoto"
			@closeCameraPage="closeCameraPage" />

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 签到弹窗 -->
		<uni-popup ref="popupSignIn" background-color="#fff" class="popup">
			<view>
				<img :src="signInImg" class="signin-img" mode="widthFix" />
				<text class="signin-text">已连续签到 {{signInDays}} 天</text>
				<button class="signin-btn" plain @click="startSignIn()" v-if="!isSignInToday">
					{{signInBtnText}}</button>
				<button class="signin-btn1" plain v-if="isSignInToday">
					{{afterSignInBtnText}}</button>
			</view>
		</uni-popup>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 签到分享 -->
		<u-popup :show="popupShare" mode="bottom" @close="popupShare = false">
			<view class="filter-title">
				<text>门店签到分享</text>
			</view>
			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab">
					<view class="tab">
						<view class="tab-title">
							<text>签到门店</text>
						</view>
						<view class="tab-picker" @click="openPickerMine(0,1)">
							<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
							<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</view>

					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 500;color: #909399;">
							* 生成签到分享码，扫码即可在指定门店签到</text>
					</view>

					<view class="tab-title" @click="openRecord()">
						<text style="font-size: 32rpx;text-align: right;font-weight: 500;color: #ff4d4b;">
							* 点此查看门店签到记录</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="popupShare = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="openShare(1)">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<uni-popup ref="popupShareImg" background-color="#fff" class="popup">
			<view>
				<img :src="shareImg" class="signin-img" mode="widthFix" />
			</view>
		</uni-popup>

		<view v-if="!showCameraPage">
			<view class="store-btn">
				<uni-icons type="scan" size="24" color="#1e1848" style="margin-left: 30rpx;"
					@click="openShare(0)"></uni-icons>
				<button @click="openPage(0)">积分商城</button>
				<button v-if="isStoreSignin" @click="changeSignType()">线上签到</button>
				<button v-if="isStoreSignin&&isArriveStoreSignin" @click="openPage(1)">到店考试</button>
			</view>

			<view class="tab" v-if="!isStoreSignin">
				<view class="tab-head">
					<text>线上签到</text>
					<text>参与签到获取积分，即可兑换好礼！</text>
				</view>
			</view>

			<view class="tab" v-if="isStoreSignin">
				<view class="tab-head">
					<text>门店签到</text>
					<text @click="choiceNearStore()">附近：{{storeNameNear}}</text>
					<uni-icons @click="choiceNearStore()" style="margin-left: 5rpx;" type="help" size="20"></uni-icons>
				</view>
				<!-- 			<view class="tab-head-tips">
				<text v-if="isStoreSignin&&storeSigninGrant">*同一门店7天内只能签到一次哦</text>
			</view> -->

				<view class="tab-picker" @click="openPickerMine(0,0)">
					<text class="picker-text">{{ storeName }}</text>
					<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
				</view>
			</view>


			<view>
				<button class="signin-btn-center" :disabled="isSignInToday||!isLogin"
					@click="startSignIn()">{{formatSignInButton()}}</button>
				<text class="signin-title">已连续签到 {{signInDays}} 天</text>
				<text class="signin-title">积分余额 {{jiaBiAmount}} 个</text>
			</view>

			<!-- 签到记录列表 -->
			<view class="tab" style="margin-top: 60rpx;">
				<view class="tab-head">
					<text>{{jiaBiDetailTitle}}</text>
				</view>
			</view>
			<view class="w85 flac-row-b" style="margin: 20rpx auto;" v-for="(item,index) in jiaBiDetailList"
				:key="index">
				<view class="w7 flac-row">
					<view class="f14 lh20">
						<view class="fb">{{formatChangeType(index)}}</view>
						<view class="f12 c9">{{item.createTime}}</view>
					</view>
				</view>
				<view :class="item.changeAmount > 0 ? 'red' : 'green'">
					{{item.changeAmount > 0 ? '+' : ''}}{{item.changeAmount}}
				</view>
			</view>
			<u-empty v-if="jiaBiDetailList.length==0" text="暂无记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>
	</view>
</template>

<script>
	import cameraPage from "@/pages-mine/common/components/cameraPage.vue"
	export default {
		components: {
			cameraPage
		},
		data() {
			return {
				// 参数设置部分
				// 最近门店检索距离范围
				district: 5,
				// 是否开启门店签到（默认只在周日开启）
				isStoreSignin: false,
				// 是否开启到店签到（默认只在周日开启）
				isArriveStoreSignin: false,
				// 签到渠道（0：未知 1：家姐联盟  2：家姐课堂）
				channel: 1,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				signInImgShare: '',
				signInImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662429908187signin_img.png',
				// 普通签到
				signInImgList: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670503803signInImg.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670514687signInImg_1.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670525986signInImg_2.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670535590signInImg_3.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670544715signInImg_4.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670553561signInImg_5.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670561453signInImg_6.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671670570155signInImg_7.png'
				],
				// 累计签到积分加倍
				signInImgList1: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614886118signInImg.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614901988signInImg_1.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614913474signInImg_2.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614934031signInImg_3.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614944921signInImg_4.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614954600signInImg_5.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614965196signInImg_6.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671614974178signInImg_7.png'
				],
				shareImg: '',
				shareSigninImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_signin.png',

				storeSigninGrant: false,
				isLogin: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				popupShow: false,
				popupShare: false,
				popupShareImg: false,
				successMsg: '恭喜您，签到成功！',

				signInBtnText: '签到领积分',
				afterSignInBtnText: '今天已经签到过啦',
				isPopupToday: false,

				storeName: "",
				pickerIndex: -1,
				openPickerMineValue: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				isSignInToday: false,
				signInDays: 0,
				jiaBiAmount: 0,
				memberId: uni.getStorageSync('memberId') || 0,
				jiaBiDetailTitle: '积分明细',
				storeIdParam: -1,
				storeId: -1,
				storeIdNear: -1,
				storeName: '',
				storeNameNear: '正在尝试定位...',
				storeIndex: -1,
				defaultIndex: [0],
				jiaBiDetailList: [],
				storeList: [],
				// 查询条件
				searchCondition: {
					memberId: uni.getStorageSync("memberId") || 0,
					current: 1,
					size: 20,
				},
				showCameraPage: false,
				headImg: ''
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开选择器
			openPickerMine(value, index) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.openPickerMineValue = index
				this.showPickerMine = true
				if (index == 1) {
					this.popupShare = false
				}
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.storeIndex = index
					this.storeId = this.storeList[index].id
					// this.storeIdParam = this.storeList[index].id
					this.storeName = this.storeList[index].storeName
				}
				this.showPickerMine = false
				if (this.openPickerMineValue == 1) {
					this.popupShare = true
				}
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 打开签到记录
			openRecord() {
				uni.navigateTo({
					url: '/pages-mine/signIn/signin-record'
				})
			},
			// 签到按钮文案变更
			formatSignInButton() {
				if (this.isSignInToday) {
					return "已签"
				} else {
					if (this.isArriveStoreSignin && this.isStoreSignin) {
						return "到店"
					}
				}
				return "签到"
			},
			// 格式化积分类型
			formatChangeType(index) {
				let type = this.jiaBiDetailList[index].changeType
				let changeTypeName = this.jiaBiDetailList[index].changeTypeName
				switch (type) {
					case 0:
						if (this.jiaBiDetailList[index].storeId == -1) {
							return "线上签到"
						} else {
							let storeName = this.jiaBiDetailList[index].storeName
							let result = "-" + storeName
							result = storeName != null ? result : ''
							return "门店签到" + result
						}
					default:
						return changeTypeName
				}
			},
			// 切换签到状态
			changeSignType() {
				this.isStoreSignin = false
				this.storeId = -1
			},
			// 打开页面
			openPage(index) {
				if (index == 0) {
					uni.navigateTo({
						url: "/pages-mine/integralMall/productIndex"
						// url: "/pages-mine/signIn/jiabi-store"
					})
				} else if (index == 1) {
					uni.navigateTo({
						url: "/pages-mine/exam/exam-center"
					})
				}
			},
			openShare(index) {
				if (index == 0) {
					this.popupShare = true
					this.getAllStoreList()
				} else if (index == 1) {
					this.popupShare = false
					this.getShareImg()
				}
			},
			// 积分数量格式化
			formatJiaBi(amount) {
				if (amount > 0) {
					return "+" + amount
				} else {
					return "" + amount
				}
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.shareImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			getShareImg() {
				let id = this.storeIdParam
				if (this.storeIndex != -1) {
					id = this.storeList[this.storeIndex].id
				}
				let scene = "id/" + id
				let param = {
					source: "aygj",
					path: "pages-mine/signIn/signin",
					type: 1,
					scene: scene,
					title: "小羽佳-家姐联盟",
					backImg: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/QR_img/QR_%E4%BD%B3%E5%B8%81%E7%AD%BE%E5%88%B0%20.png'
				}
				this.http({
					url: 'getEmployeePoster',
					method: 'POST',
					hideLoading: true,
					data: param,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.shareImg = res.data
							this.$refs.popupShareImg.open()
							this.saveToPhone()
							console.log("获取分享图片-成功！")
						} else {
							console.log("获取分享图片-返回失败！")
						}
					},
					fail: err => {
						console.log("获取分享图片-请求失败！" + res)
					}
				})
			},
			closeCameraPage(flag) {
				console.log("关闭人脸相机！")
				this.showCameraPage = flag
			},
			// 提交照片
			submitCameraPhoto(tempFilePaths) {
				const url = 'https://api2.xiaoyujia.com/system/imageUpload'
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						let result = JSON.parse(res.data)
						this.headImg = result.data
						this.showCameraPage = false
						this.startStoreSignIn(this.storeId)
					}
				});
			},
			getLocation() {
				//#ifdef  MP-WEIXIN || APP-PLUS || H5
				let that = this
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					success: res => {
						console.log("定位调用成功")
						console.log('---', res)
						let {
							latitude,
							longitude
						} = res
						let x = longitude
						let y = latitude
						let x_pi = (3.14159265358979324 * 3000.0) / 180.0
						let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi)
						let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi)
						let lng = z * Math.cos(theta) + 0.0065
						let lat = z * Math.sin(theta) + 0.006
						console.log("（高精度）当前的纬度：", lat, "当前的经度", lng)
						uni.setStorageSync("lat", lat)
						uni.setStorageSync("lng", lng)
						that.getNearStore()
					},
					fail: err => {
						// console.log('获取当前位置失败！：' + JSON.stringify(err))
						that.storeNameNear = "暂无门店（可线上签到）"
					},
				})
				// #endif

				//#ifdef  H5
				// let url = "https://apis.map.qq.com/ws/location/v1/ip";
				// this.$jsonp(url, {
				// 		key: 'RNRBZ-ZMZ6R-BSVWC-WHRJI-SACIT-VLBVI',
				// 		output: 'jsonp'
				// 	})
				// 	.then(res => {
				// 		// 纬度和经度
				// 		let lat = res.result.location.lat
				// 		let lng = res.result.location.lng
				// 		console.log("（低精度）当前的纬度：", lat, "当前的经度", lng)
				// 		uni.setStorageSync("lat", lat)
				// 		uni.setStorageSync("lng", lng)
				// 		this.getNearStore()
				// 	})
				// 	.catch(err => {
				// 		console.log(err);
				// 		this.storeNameNear = "暂无门店（可线上签到）"
				// 	});
				// #endif
			},
			// 检查门店签到权限
			checkStoreSignIn() {
				this.http({
					url: 'checkStoreSignIn',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.memberId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.storeSigninGrant = true
								this.isStoreSignin = true
								// 获取所有直营门店数据以及定位附近门店
								this.getAllStoreList()
							} else {
								// this.isStoreSignin = false
								this.storeId = -1
							}
						}
					}
				})
			},
			// 检查是否开启到店签到
			checkArriveSignIn() {
				this.http({
					url: 'checkArriveSignIn',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.isArriveStoreSignin = true
							}
						}
					}
				})
			},
			// 获取所有门店列表
			getAllStoreList() {
				// 判断是否为周日，是则引导用户进行到店签到
				// let nowDate = new Date().getDay()
				// if (nowDate == 0) {
				// 	// this.isStoreSignin = true
				// 	this.isArriveStoreSignin = true
				// }
				this.checkArriveSignIn()

				if (this.isStoreSignin || this.popupShare) {
					this.http({
						url: 'getAllStoreList',
						method: 'POST',
						hideLoading: true,
						data: {
							isOpen: false
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.storeList = res.data
								this.checkStoreIdParam()
								if (this.popupShare) {
									let myStoreId = uni.getStorageSync("storeId") || 0
									for (let i = 0; i < this.storeList.length; i++) {
										if (myStoreId == this.storeList[i].id) {
											this.storeName = this.storeList[i].storeName
											this.storeIndex = i
											this.pickerIndex = i
											break
										}
									}
								} else {
									// 获取定位并获得最近门店数据
									this.getLocation()
								}
							}
						}
					})
				}
			},
			// 计算连续签到获得的积分数量
			getSignInJiabi() {
				let changeAmount = 10
				// switch (this.signInDays % 7 + 1) {
				// 	case 3:
				// 		changeAmount *= 2
				// 		break
				// 	case 5:
				// 		changeAmount *= 3
				// 		break
				// 	case 7:
				// 		changeAmount *= 4
				// 		break
				// }
				return changeAmount
			},
			// 签到领积分逻辑部分
			startSignIn() {
				this.$refs.popupSignIn.close()
				if (this.isSignInToday) {
					return this.$refs.uNotify.error("今天已经签到过啦！")
				}
				let storeId = -1
				let changeAmount = this.getSignInJiabi()
				if (this.storeIndex != -1 && this.isStoreSignin) {
					storeId = this.storeId
					changeAmount = 15
					if (this.storeName == "") {
						this.openCheck(1, "确定进行线上签到吗", "附近暂未定位到门店（请检查手机定位开关或手动选择门店）否则将进行线上签到")
						return
					}
				} else {
					// 线上签到提醒
					if (this.storeSigninGrant && this.isStoreSignin && this.storeIdParam == -1) {
						this.openCheck(1, "确定进行线上签到吗", "附近暂未定位到门店（请检查手机定位开关或手动选择门店）否则将进行线上签到")
						return
					}
				}

				if (!this.isStoreSignin) {
					this.openCheck(2, "确定进行线上签到吗", "")
					return
				}

				let tips = '当前已定位【' + this.storeNameNear + '】'
				if (this.storeIdParam != -1) {
					tips = '当前已通过【签到码】定位到【' + this.storeName + '】'
				}
				if (this.storeIdNear != this.storeId) {
					let text = this.storeIdParam != -1 ? '签到码' : '定位'
					tips = '选择的门店与' + text + '门店不一致，确定在【' + this.storeName + '】进行签到吗'
				}

				this.openCheck(2, "确定进行签到吗？", tips)
			},
			startStoreSignIn(storeId) {
				// if (!this.headImg) {
				// 	// #ifdef  MP-WEIXIN
				// 	this.showCameraPage = true
				// 	return this.$refs.uNotify.warning("请先拍照上传人脸头像进行验证！")
				// 	// #endif
				// }

				let data = {
					employeeId: uni.getStorageSync("employeeId"),
					memberId: uni.getStorageSync("memberId"),
					channel: this.channel,
					storeId: storeId,
					type: 0,
					// headImg: this.headImg
				}
				// 到店签到
				if (this.isArriveStoreSignin) {
					this.$set(data, "type", 1)
				}
				// 扫码签到
				if (this.storeIdParam != -1) {
					this.$set(data, "storeId", this.storeIdParam)
					this.$set(data, "type", 2)
				}
				this.http({
					url: 'startStoreSignIn',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							// 签到成功，弹窗提醒
							// this.$refs.uNotify.success("恭喜您，签到成功！积分+" + changeAmount)
							this.$refs.uNotify.success("恭喜您，签到成功！已获得积分！")

							// 更新签到数据
							this.isSignInToday = true

							this.signInDays = this.signInDays + 1
							// 更新积分数量
							this.getJiaBiAmount()
							// this.jiaBiAmount = this.jiaBiAmount + changeAmount
							console.log('签到领积分-成功！')
						} else {
							console.log('签到领积分-返回失败！')
							if (res.data == "3") {
								this.openCheck(0, "同一门店7天内只能签到一次！", "请选择其它门店或切换为线上签到！")
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					},
					fail: err => {
						console.log('签到领积分-请求失败！' + res.code)
					}
				})
			},
			choiceNearStore() {
				// this.storeId = this.storeIdNear
				// this.formatNearStore()
				let lng = uni.getStorageSync("lng") || '-'
				let lat = uni.getStorageSync("lat") || '-'
				this.openCheck(-1, "定位不到当前门店？", "当前位置，经度：" + lng + "纬度：" + lat + '（请截图该页面，并联系技术校准位置）')
			},
			// 格式化最近门店信息到选择框
			formatNearStore() {
				let storeId = this.storeIdNear
				for (let i = 0; i < this.storeList.length; i++) {
					if (storeId == this.storeList[i].id) {
						this.storeIndex = i
						this.pickerIndex = i
						this.storeName = this.storeList[i].storeName
						return
					}
				}

				// 测试时加上
				// this.storeId = -1
				// this.storeIdNear = -1
				// this.storeNameNear = "暂无门店（可线上签到）"
				// uni.setStorageSync("nearStoreId", -1)
			},
			// 获取最近的自营门店 
			getNearStore() {
				if (this.storeIdParam != -1) {
					return
				}

				let lng = parseFloat(uni.getStorageSync("lng"))
				let lat = parseFloat(uni.getStorageSync("lat"))
				console.log("（传给后端接口的定位）当前的纬度：", lat, "当前的经度", lng)
				this.http({
					url: 'getNearStore',
					method: 'POST',
					hideLoading: true,
					data: {
						lng: lng,
						lat: lat,
						// lng: 118.1087093713055,
						// lat: 24.495751822545675,
						district: this.district,
						memberId: this.memberId,
						name: uni.getStorageSync('employeeName') || uni.getStorageSync('memberName') || '匿名用户',
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeId = res.data.storeId
							this.storeIdNear = this.storeId
							this.storeNameNear = res.data.storeName
							this.storeName = res.data.storeName
							console.log("获取最近门店成功！", "id：", this.storeId, "门店名：", this.storeName)
							this.formatNearStore()
							uni.setStorageSync("nearStoreId", storeId)
						} else {
							this.storeId = -1
							this.storeIdNear = -1
							this.storeNameNear = "暂无门店（可线上签到）"
							uni.setStorageSync("nearStoreId", -1)
							this.$refs.uNotify.warning('附近暂无可签到门店哦～')
						}
					},
				})
			},
			checkStoreIdParam() {
				console.log("检查是否传入签到门店参数！")
				if (this.storeIdParam != -1 && this.isStoreSignin) {
					let storeId = this.storeIdParam
					this.storeId = storeId
					this.storeIdNear = storeId
					this.formatNearStore()
				}
			},
			// 获取员工积分明细
			getJiaBiDetail(value) {
				this.http({
					url: 'getJiaBiDetailPage',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							if (value == 1) {
								this.jiaBiDetailList = []
							}
							this.jiaBiDetailList = this.jiaBiDetailList.concat(res.data.records)
							console.log('获取员工积分明细-成功！')
						} else {
							console.log('获取员工积分明细-返回失败！')
						}
					},
					fail: err => {
						console.log('获取员工积分明细-请求失败！' + res.code)
					}
				})
			},
			// 获取积分数量
			getJiaBiAmount() {
				// 请求：获取积分数量
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
							console.log('获取员工积分数量-成功！')
						} else {
							console.log('获取员工积分数量-返回失败！')
						}
					},
					fail: err => {
						console.log('获取员工积分数量-请求失败！' + res.code)
					}
				})
			},
			// 获取签到天数
			getSignInDays() {
				// 请求：获取签到天数
				this.http({
					url: 'getSignInDays',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.memberId
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.signInDays = res.data
							this.signInImg = this.signInImgList[this.signInDays % 7]
							console.log('获取签到天数-成功！')
						} else {
							console.log('获取签到天数-返回失败！')
						}
					},
					fail: err => {
						console.log('获取签到天数-请求失败！' + res.code)
					}
				})
			},
			// 判断今天是否签到
			isSignIn() {
				this.http({
					url: 'isSignInToday',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId
					},
					success: res => {
						if (res.code == 0) {
							this.isSignInToday = res.data
							if (!this.isSignInToday) {
								this.$refs.popupSignIn.open()
							}
							console.log('检查是否签到-成功！')
						} else {
							console.log('检查是否签到-返回失败！')
						}
					},
					fail: err => {
						console.log('检查是否签到-请求失败！' + res.code)
					}
				})
			},
			// 分享
			onShareAppMessage(res) {
				let url = '/pages-mine/signIn/signin'
				if (this.storeIdParam != -1) {
					url += '?scene=id/' + this.storeIdParam
				}
				return {
					title: '点一下，一起领积分吧~',
					path: url,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: this.shareSigninImg
				}
			},
			onShareTimeline(res) {
				return {
					title: '点一下，一起领积分吧~',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.changeSignType()
				} else if (this.checkType == 1) {
					this.changeSignType()
				} else if (this.checkType == 2) {
					this.startStoreSignIn(this.storeId)
				}
			},
			checkLogin() {
				if (uni.getStorageSync('memberId')) {
					this.isLogin = true
				} else {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					let url = '/pages-mine/signIn/signin'
					if (this.storeIdParam != -1) {
						url += '?storeIdParam=' + this.storeIdParam
					}
					uni.setStorageSync('redirectUrl', url)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				}
			},
		},
		// 监听页面数据变化，重新渲染组件
		watch: {
			// 签到后重新获取积分明细
			isSignInToday: {
				handler(newValue, oldVal) {
					// 延时更新数据
					let timer = setTimeout(() => {
						this.searchCondition.current = 1
						this.getJiaBiDetail(1)
					}, 200);
				},
				deep: true
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.getJiaBiDetail(0)
		},
		// 获取上个页面的数据
		onLoad(options) {
			this.checkLogin()
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.storeIdParam = obj.id || -1
				console.log("传入的门店id为：", this.storeIdParam)
			}
			this.storeIdParam = options.storeIdParam || this.storeIdParam
			this.memberId = options.memberId || this.memberId || -1
		},
		mounted() {
			// 检查门店签到权限
			this.checkStoreSignIn()
			// 检查是否签到
			this.isSignIn()
			// 获取签到天数
			this.getSignInDays()
			// 获取员工积分消费
			this.getJiaBiDetail(0)
			// 获取员工积分数量
			this.getJiaBiAmount()
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		// 自适应页面高度
		height: auto;
		background-color: #ffffff;
	}

	.demo-uni-col {
		height: 90px;
	}

	.head-tab {
		height: 100%;
		// background-color: #d3dce6;
	}

	.tab {
		overflow: auto;
		height: auto;
		padding-bottom: 0rpx;
	}

	// 栏目头部
	.tab-head {
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;
	}

	.tab-head {
		text {
			display: block;
			float: left;
			line-height: 100rpx;
			height: 10%;
		}

		text:first-child {
			font-size: 40rpx;
			padding: 0 0 0 30rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			font-size: 32rpx;
			color: #909399;
			margin-left: 20rpx;
		}

	}

	.tab-head-tips {
		padding: 0 0 0 30rpx;

		text {
			display: block;
			line-height: 50rpx;
			color: #909399;
			font-size: 28rpx;
		}
	}

	// 选择输入框
	.tab-picker {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;

	}

	.picker-text {
		float: left;
		display: block;
		width: 80%;
		height: 100rpx;
		line-height: 100rpx;
		padding-left: 40rpx;
		color: #87878c;
		font-size: 32rpx;
	}

	// 小箭头
	.picker-text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 36rpx;
		padding-right: 40rpx;
		color: #88888c;
	}


	// 商城按钮
	.store-btn {
		width: 100%;
		height: 50rpx;
		margin-top: 20rpx;

		button {
			padding: 0;
			float: right;
			width: 180rpx;
			line-height: 50rpx;
			font-size: 32rpx;
			margin-right: 20rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 40rpx;
		}
	}

	// 签到按钮
	.signin-btn-center {
		position: relative;
		width: 200rpx;
		margin: 40rpx auto 0 auto;
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		line-height: 160rpx;
		color: #ffffff;
		background-color: #F21177;
	}

	// 签到天数文本
	.signin-title {
		display: block;
		margin: 40rpx auto 0 auto;
		width: 100%;
		font-size: 40rpx;
		text-align: center;
	}

	// 签到列表
	.signin-list {
		width: 100%;
		margin: 40rpx 0 0 0;
	}

	/deep/ .uni-popup__wrapper {
		border-radius: 50rpx;
	}

	// 弹窗图片样式
	.signin-img {
		display: block;
		width: 600rpx;
		height: auto;
	}

	// 签到天数文字
	.signin-text {
		position: absolute;
		top: 16%;
		left: 0rpx;
		z-index: 1;
		width: 100%;
		text-align: center;
		font-size: 42rpx;
		color: #ffffff;
	}

	// 签到按钮
	.signin-btn,
	.signin-btn1 {
		position: absolute;
		bottom: 6%;
		left: 5%;
		z-index: 1;
		width: 90%;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 60rpx;
		font-weight: bold;
		font-size: 44rpx;
		color: #fe5e2f;
		// background-color: #ffc83c;
		background: linear-gradient(to right, #fffb76, #ff9e0a);
	}

	.signin-btn1 {
		font-weight: 100;
		color: #000000;
		background: #dedede;
	}

	button[plain] {
		border: 0
	}
</style>