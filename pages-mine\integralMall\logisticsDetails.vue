<template>
	<view class="page">
		<view class="w9 mg-at flac-row-b" style="margin: 50rpx auto 30rpx;">
			<image class="w4" style="border-radius: 10rpx;" :src="dataInfo.productPicture" mode="widthFix"></image>
			<view class="w10 c3 lh25" style="margin-left: 30rpx;">
				<view class="c0 fb lh35">{{dataInfo.productName}}</view>
				<view class=" f14">订单号：{{dataInfo.billNo}}</view>
				<view class="f12">兑换时间：{{dataInfo.createTime}}</view>
			</view>
			<image class="w2" style="transform: rotate(25deg);"
				:src="dataInfo.ifDeliveryFlag == 2 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/icon_wff.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/icon_yff.png' "
				mode="widthFix"></image>
		</view>
		<view class="boxStyle">
			<view class="" v-if="steps.length >0">
				<view class="f14 fb">{{dataInfo.expressType}}</view>
				<view class="f12 flac-row-b lh35">
					<view class=""> 物流单号：{{dataInfo.expressNo}}</view>
					<view class=""><text @click="copy(dataInfo.expressNo)">复制</text>
					<!-- <text style="margin: auto 10rpx;">|</text><text @click="callPhone">打电话</text> -->
							</view>
				</view>
				<uni-steps :options="steps" activeColor="#ffc724" deactiveColor="#999" :active="active"
					direction="column" />
			</view>
			<view v-else class="flex-col-c">
				<image class="w3" src="https://ceres.zkthink.com/static/img/bgnull.png" mode="widthFix"></image>
				<view class="f14 c9 lh35">你还没有物流信息哦～</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dataInfo: {},
				active: 1,
				steps: [],
			};
		},
		onLoad(e) {
			if(!e.productData){
					 uni.showToast({
						title: '未获取到兑换信息！',
						icon: 'none'
					})
			}
			this.dataInfo = JSON.parse(decodeURIComponent(e.productData))
			if(this.dataInfo.expressNo){
			this.getLogisticsData()
			}
		},
		methods: {
			getLogisticsData(){
				this.http({
					outsideUrl: 'https://shopapi.xiaoyujia.com/order/getDilevery?express='+this.dataInfo.expressType
					+'&deliverFormid='+this.dataInfo.expressNo,
					header: {
						'Authorization': "Bearer xyjxyj",
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 200) {
							for (var i = 0; i < res.data.length; i++) {
								let obj ={
									title: res.data[i].reason,
									desc: res.data[i].time
								}
								this.steps.push(obj)
							}
							console.log(this.steps);
						} else {
							return uni.showToast({
								title: '获取物流信息失败!',
								icon: 'none'
							})
						}
					}
				})
			},
			//复制物流单号
			copy(e) {
				uni.setClipboardData({
					data: e, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: '复制成功'
						})
					}
				});
			},
			// 拨打电话
			callPhone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.boxStyle {
		width: 80%;
		margin: 40rpx auto;
		border-radius: 30rpx;
		padding: 30rpx 40rpx;
		border: 2rpx solid #ccc;
	}
</style>