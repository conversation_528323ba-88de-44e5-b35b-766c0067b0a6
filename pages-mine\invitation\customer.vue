<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>
		<u-gap height="80"></u-gap>

		<view class="flac-col-c" v-if="customer">
			<view class="fb f20 lh40">
				{{customer.title}}
			</view>
			<view class="f18">
				{{customer.content}}
			</view>
			<view class="btn-big" style="width: 300rpx;" @click="openChat">
				<button>开始咨询</button>
			</view>
			<view class="btn-big" style="width: 300rpx;" @click="openResume" v-if="type!=2">
				<button style="margin-top: -80rpx;">立即入驻</button>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 客服类型
				type: 0,

				menuList: [{
					title: '三嫂入职咨询',
					content: '咨询保姆月嫂育儿嫂入驻相关问题',
					url: 'https://work.weixin.qq.com/kfid/kfc286c63c19f27e461',
				}, {
					title: '一线员工入职咨询',
					content: '咨询保洁搬家维修入驻相关问题',
					url: 'https://work.weixin.qq.com/kfid/kfc49151a5f48007bbd',
				}, {
					title: '课程培训咨询',
					content: '咨询平台课程保姆培训相关问题',
					url: 'https://work.weixin.qq.com/kfid/kfc9ebc096e72986a00',
				}],
				customer: null

			}
		},
		methods: {
			// 分享到好友
			onShareAppMessage(res) {
				return {
					title: '咨询客服，解决你的问题吧！',
					path: "/pages-mine/invitation/customer?type=" + this.type,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_setting.png',
				}
			},
			openResume() {
				uni.navigateTo({
					url: "/pages-mine/resume/resume-simplify"
				})
			},
			openChat() {
				let url = this.customer.url
				// #ifdef MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: url //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				let param = {
					url: url
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.type = obj.t || this.type
			}
			this.type = options.type || this.type
			this.customer = this.menuList[this.type]
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/button.scss";
</style>