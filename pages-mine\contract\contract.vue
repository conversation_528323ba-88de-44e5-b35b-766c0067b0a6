<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 栏目菜单 -->
		<view class="swiper-menu">
			<u-sticky>
				<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="22" lineHeight="8"
					:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
                color: '#1e1848',
                fontWeight: 'bold',
                transform: 'scale(1.1)'
            }" :inactiveStyle="{
                color: '#333',
                transform: 'scale(1.05)'
            }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
				</u-tabs>
			</u-sticky>
		</view>

		<!-- 合同信息 -->
		<view class="swiper-tab" v-for="(item, index) in changeList" :key="index">
			<view class="swiper-head">
				<text class="swiper-title" @click="copyText(item.no)">合同号：{{item.no||'-'}}</text>
				<text class="swiper-tips">{{getContractStatus(item.status)}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left">
					<img :src="item.productImg||contractImg" alt="">
				</view>
				<view class="content-right">
					<view class="content-title">
						<text>{{item.productName||item.serviceType}}</text>
					</view>
					<view class="content-text">
						<text>{{item.serviceContent}}</text>
					</view>
					<view class="content-text">
						<text>甲方：{{item.memberName}}</text>
					</view>
					<view class="content-text">
						<text>经纪人：{{item.agentName}}</text>
					</view>
					<view class="content-text">
						<text>工资：￥{{item.servicePay}}</text>
					</view>
					<view class="content-text">
						<text>时间：{{item.serviceStarDate}}-{{item.serviceEndDate}}</text>
					</view>
					<view class="content-text">
						<text>地址：{{item.memberAdress}}</text>
					</view>
					<u-button customStyle="width:32%;float:right" text="立即签约" color="#1e1848"
						v-if="!item.employeeSignDate&&item.isSupply!==1" @click="showTip(item)">
					</u-button>
					<u-button customStyle="width:32%;float:right" text="查看合同" color="#1e1848"
						v-if="item.employeeSignDate" @click="showContract(item)">
					</u-button>
					<view style="width: 32%; float: right;" v-if="item.signState == 1&&item.isSupply!==1">
						<u-button plain text="再次续约" color="#1e1848" @click="showPrompts(index)">
						</u-button>
					</view>
					<u-gap height="60"></u-gap>
				</view>
			</view>
		</view>
		
		<u-modal :show="showFadadaModal" title="查看合同" @confirm="showFadadaModal = false">
			<view class="slot-content" v-if="actionDom">
				<u-button text="在线查看法大大合同" color="#1e1848" @click="lookContract(actionDom)"></u-button>
				<u-gap height="20"></u-gap>
				<u-button text="下载法大大合同文件" color="#1e1848" @click="downloadContract(actionDom.no)"></u-button>
				<u-gap height="20"></u-gap>
				<u-button text="下载腾讯电子签合同" color="#1e1848" @click="getTencentContract(actionDom.no)"></u-button>
			</view>
		</u-modal>
		
		<u-empty v-if="changeList.length <= 0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />


		<u-popup :show="showTips" mode="center" closeIconPos="top-right" @close="showTips = false"
			:closeOnClickOverlay="false" customStyle="z-index: 9999 !important;width: 95%;height: 500rpx;"
			style="display: flex;">

			<view style="margin-top: 40rpx;">
				<text style="color: red;font-size: 20px;">
					{{signTips}}
				</text>
				<uni-card title="基础卡片">
					<template v-slot:title>
						<uni-list>
							<uni-list-item>
								<template v-slot:body>
									<view style="display: flex; justify-content: space-between;width: 100% !important;">
										<view style="font-size: 16px;color: #222;margin: 0 auto;">
											合同编号：{{ showList.no }}
										</view>
									</view>
								</template>
							</uni-list-item>
						</uni-list>
					</template>
					<view style="display: flex;">
						<view style="margin: 0 auto;">
							<view style="padding: 5px 0px;color: #000;font-size: 16px;">
								订单号：{{showList.billNo}}
							</view>
						</view>
					</view>
				</uni-card>
				<view style="display: flex;padding-bottom: 10px;justify-content: center;margin-top: 35px;">
					<view style="width: 48%;">
						<u-button plain text="取消签约" color="#1e1848" @click="showTips=false"></u-button>
					</view>
					<view style="width: 1%;"></view>
					<view style="width: 48%;" v-show="signStatus == 0">
						<u-button text="前往认证" color="#1e1848" @click="goCertification(),showTips=false"></u-button>
					</view>
					<view style="width: 1%;"></view>
					<view style="width: 48%;" v-show="signStatus == 1">
						<u-button text="前往签约" color="#1e1848" @click="goSign(),showTips=false">
						</u-button>
					</view>
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				choiceIndex: 0,
				showFadadaModal: false,
				memberId: uni.getStorageSync('memberId') || 0,
				employeeId: uni.getStorageSync('employeeId') || 0,
				headImg: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				contractImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				changeList: [],
				contractList: [],
				actionDom: {},
				menuList: [{
						name: '全部',
					},
					{
						name: '待签约',
					},
					{
						name: '已签约',
					}
				],
				showList: [],
				signUrl: '',
				signTips: '',
				signNo: '',
				signStatus: 0,
				showTips: false,
			}
		},
		methods: {
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('合同号复制成功!')
					}
				})
			},
			lookContract(item) {
				let no = item.no
				let contractType = item.contractType
				let contractId = item.id
						
								if (contractType == null) {
									return uni.showToast({
										title: '合同状态错误',
										icon: 'error'
									})
								}
								let urlPrefix = 'https://agent.xiaoyujia.com/upbaomu/';
								const type = Number(contractType);
								if (type === 0 || type === 1 || type === 6) {
									urlPrefix = urlPrefix + 'baomucontractInfo/' + contractId + '?flag=0'
								} else if (type === 2) {
									urlPrefix = urlPrefix + 'baomucontractEscrowInfo/' + contractId + '?flag=0'
								} else if (type === 4) {
									urlPrefix = urlPrefix + 'baomucontractInstallmentInfo/' + contractId + '?flag=0'
								}
								
								let param = {
									url: urlPrefix
								}
								let data = JSON.stringify(param);
								uni.navigateTo({
									url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
								})
			
			},
			getTencentContract(no) {
				this.http({
					url: 'showSignContractByTencent',
					method: 'GET',
					hideLoading: true,
					data: {
						no: no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							// let param = {
							// 	url: res.data.url
							// }
							// let data = JSON.stringify(param)
							// uni.navigateTo({
							// 	url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
							// })
							uni.downloadFile({
							  url: res.data.url,
							  success: function (res) {
							    var filePath = res.tempFilePath;
							    uni.openDocument({
							      filePath: filePath,
							      showMenu: true,
							      success: function (res) {
							        console.log('打开文档成功');
							      }
							    });
							  }
							});
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			downloadContract(no) {
				this.http({
					url: 'downloadPdfUrl',
					method: 'GET',
					data: {
						contractId: no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('将链接复制到浏览器打开下载')
							this.copy(res.data)
						} else {
							this.$refs.uNotify.error(res.msg)
						}
			
					}
			
				})
			},
			copy(info) {
				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif
			
				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
			showContract(contract) {
				this.showFadadaModal = true
				this.actionDom = contract
				// this.http({
				// 	url: 'viewContractPdfUrl',
				// 	method: 'GET',
				// 	hideLoading: true,
				// 	data: {
				// 		contractId: contract.no
				// 	},
				// 	header: {
				// 		"content-type": "application/json;charset=UTF-8"
				// 	},
				// 	success: res => {

				// 		if (res.code == 0) {
				// 			let param = {
				// 				url: res.data
				// 			}
				// 			let data = JSON.stringify(param);

				// 			//#ifdef MP-WEIXIN
				// 			wx.navigateToMiniProgram({
				// 				appId: 'wxc33730c9e09594f8', // pord
				// 				// appId: 'wx855361a721050c6b', // test
				// 				path: 'pages/webview/web?param=' + encodeURIComponent(data),
				// 				envVersion: 'release',
				// 				success(res) {
				// 					// 打开成功
				// 				}
				// 			})
				// 			//#endif

				// 			//#ifdef H5
				// 			uni.navigateTo({
				// 				url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				// 			})
				// 			//#endif
				// 		} else {
				// 			this.$refs.uNotify.error(res.msg)
				// 		}
				// 	}
				// })
			},
			// 格式化时间
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				var dt = new Date(value)
				let year = dt.getFullYear()
				let month = (dt.getMonth() + 1).toString().padStart(2, '0')
				let date = dt.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${date}`
			},
			// 点击切换菜单
			choiceMenu(e) {
				this.changeList = []
				console.log("切换菜单栏：", e.index)
				this.choiceIndex = e.index
				this.contractList.forEach(v => {
					if (e.index == 0) {
						this.changeList = this.contractList
					} else if (e.index == 1) {
						v.status >= 4 && this.changeList.push(v)
					} else if (e.index == 2) {
						v.status < 4 && this.changeList.push(v)
					}
				})
			},
			// 获取员工合同列表
			getMemberContract() {
				this.http({
					url: 'getContractByPeopleId',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.employeeId
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							let data = res.data
							data.forEach(item => {
								if (item.productImg && item.productImg.includes(',')) {
									let array = item.productImg.split(',')
									item.productImg = array[0]
								}
							})
							this.contractList = data
							this.changeList = data
						} else {
							this.contractList = []
						}
					},
				})
			},
			getContractStatus(status) {
				status = Number(status);
				switch (status) {
					case 0:
						return '暂存中';
					case 1:
						return '生效中';
					case 2:
						return '已完成';
					case 3:
						return '补签单';
					case 4:
						return '未续签(已过期)';
					case 5:
						return '合同终止';
					case 99:
						return '作废单';
					case 100:
						return '暂存下户';
					default:
						break;
				}
			},
			showTip(item) {
				console.log(item)
				this.showList = {}
				this.showList = item;
				this.certification(item.no)
			},
			certification(contractNo) {
				this.signUrl = ''
				this.signTips = ''
				this.signNo = ''
				const param = {
					contractNo: contractNo,
					type: 1 //1员工 2客户
				}
				this.http({
					url: 'registeredAndCertification',
					method: 'GET',
					hideLoading: true,
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.code == 0) {
							switch (res.msg) {
								case "成功获取实名认证地址":
									// console.log("认证url", res.data)
									this.signUrl = res.data
									this.signTips = '未实名认证，请点击下面按钮前往认证'
									this.signNo = contractNo
									this.signStatus = 0
									break;
								case "成功获取签署地址":
									// console.log("签约url", res.data)
									this.signUrl = res.data
									this.signTips = '已实名认证，请点击下面按钮前往签约'
									this.signNo = contractNo
									this.signStatus = 1
									break;
							}
							this.showTips = true
							console.log(this.signTips)
							console.log(this.signStatus)
						} else {
							let msg = res.msg;
							this.signTips = res.msg
							uni.showToast({
								title: msg
							})
						}
					}
				})
			},
			goCertification() {
				//#ifdef MP-WEIXIN
				wx.navigateToMiniProgram({
					appId: 'wxa1439f77c6d06a15', // pord
					// appId: 'wx855361a721050c6b', // test
					path: 'pages/result-loading/result-loading?verifyUrl=' + encodeURIComponent(this.signUrl),
					envVersion: 'release',
					success(res) {
						// 打开成功
					}
				})
				//#endif

				//#ifdef H5 || APP-PLUS
				return uni.showToast({
					title: '请从小程序打开该页面在认证',
					icon: 'none',
					duration: 5000
				})
				//#endif
			},
			goSign() {
				wx.navigateToMiniProgram({
					appId: 'wxa1439f77c6d06a15',
					path: 'pages/h5sign/h5sign',
					envVersion: 'release',
					extraData: {
						verifyUrl: this.signUrl
					},
					success(res) {
						// 打开成功
					}
				})
			},
		},
		onLoad() {
			this.getMemberContract()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>