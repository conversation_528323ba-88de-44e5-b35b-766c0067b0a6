<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="w9 mg-at f18 fb lh40 flac-row">
			<view class="w5" style="color: #00aaff;text-decoration:underline;">
				<text v-if="reviewId" @click="goPage('/pages-other/excitation/review-detail?id='+reviewId)">图表预览</text>
			</view>
			<view class="text-r w5">
				<text @click="openEx">填写示例</text>/
				<text @click="openRecord">填写记录</text>
			</view>
		</view>

		<view class="w9 mg-at f15" style="color: #ff4d4b;">
			填写后可在右上角【填写记录】中查看并进行修改
		</view>

		<view class="w9 mg-at">
			<u--form :model="dom" labelPosition="top" labelWidth="330" :labelStyle="labelStyle">
				<u-form-item label="流程一句话" prop="flowContent" required>
					<view class="flac-col f16">
						<view class="" v-if="flowContent" style="color:#ff4d4b;">
							填入历史记录
							<uni-icons type="cloud-download" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#ff4d4b" @click="copyOld(0)">
							</uni-icons>
						</view>
						<u-textarea placeholder="填写你的流程一句话" v-model="dom.flowContent" style="width: 640rpx;" />
					</view>
				</u-form-item>

				<u-form-item label="每日目标" prop="dailyGoal" required>
					<u-input placeholder="你的每日目标，**万/**个等" v-model="dom.dailyGoal" />
				</u-form-item>
				<u-form-item label="日目标达成率" prop="dailyGoalRate" required>
					<view class="flac-row">
						<u-input placeholder="今日目标达成率（只填数字即可)" v-model="dom.dailyGoalRate" type="digit"
							style="width: 600rpx;margin-right: 20rpx;" />%
					</view>
				</u-form-item>
				<u-form-item label="每周目标" prop="weeklyGoal">
					<u-input placeholder="你的每周目标，**万/**个等" v-model="dom.weeklyGoal" />
				</u-form-item>
				<u-form-item label="周目标达成率" prop="weeklyGoalRate">
					<view class="flac-row">
						<u-input placeholder="本周的目标达成率（只填数字即可)" v-model="dom.weeklyGoalRate" type="digit"
							style="width: 600rpx;margin-right: 20rpx;" />%
					</view>
				</u-form-item>
				<u-form-item label="团队目标" prop="teamGoal" required>
					<u-input placeholder="团队月度目标，**万/**个等" v-model="dom.teamGoal" />
				</u-form-item>

				<u-form-item label="左手动作" prop="leftHand" required>
					<u-input placeholder="如私域运营/客户接待等" v-model="dom.leftHand" :maxlength="maxlength" />
				</u-form-item>
				<u-form-item label="左手工作项" prop="dailyThing" required>
					<view class="flac-col f16">
						<view class="" v-if="dailyThingOld" style="color:#ff4d4b;">
							填入昨日动作
							<uni-icons type="cloud-download" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#ff4d4b" @click="copyOld(1)">
							</uni-icons>
						</view>
						<view class="flac-row" style="margin: 20rpx 0;" v-for="(item,index) in textList" :key="index"
							v-if="index<pointNum">
							{{index+1}}、<u-input placeholder="请填写左手工作项" v-model="textList[index]" style="width: 600rpx;"
								:maxlength="maxlength" @change="changeInput" />
						</view>
						<view class="flac-row " v-if="pointNum<maxPointNum" @click="addPoint(0)">
							添加一点
							<uni-icons type="plus" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399">
							</uni-icons>
						</view>
					</view>
				</u-form-item>
				<u-form-item label="做对的工作项" prop="dailyImprovement" required>
					<view class="flac-col">
						<view class="flac-row" style="margin: 20rpx 0;" v-for="(item,index) in textList1" :key="index"
							v-if="index<pointNum1">
							{{index+1}}、<u-input placeholder="请填写做对的动作" v-model="textList1[index]"
								style="width: 600rpx;" />
						</view>
						<view class="flac-row f16" v-if="pointNum1<maxPointNum" @click="addPoint(1)">
							添加一点
							<uni-icons type="plus" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399">
							</uni-icons>
						</view>
					</view>
				</u-form-item>
				<u-form-item label="可以帮助你的人" prop="collaborator">
					<u-input placeholder="过程中可以帮助你的人（具体人名）" v-model="dom.collaborator" />
				</u-form-item>
				<u-form-item label="是否自荐标杆">
					<view class="w10 mg-at flac-row f16">
						<view class="flac-row" style="margin: 20rpx 20rpx;" @click="self">
							<img :src="dom.selfRecommend==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
								style=" width: 50rpx;height: 50rpx;" />
							<text style="margin: 20rpx 0;">是</text>
						</view>
						<view class="flac-row" style="margin: 20rpx 20rpx;" @click="dom.selfRecommend=0">
							<img :src="dom.selfRecommend==0?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
								style=" width: 50rpx;height: 50rpx;" />
							<text style="margin: 20rpx 0;">否</text>
						</view>
					</view>
				</u-form-item>
				<u-form-item label="图片展示" v-if="dom.selfRecommend==1">
				</u-form-item>
			</u--form>
			<shmily-drag-image v-if="dom.selfRecommend==1" v-model="imgList" keyName="imgUrl" class="w10 mg-at"
				:addImage="uploadImg" :delImage="delImage" @delDragImg="delDragImg"></shmily-drag-image>
		</view>

		<view class="btn-bottom-fixed" @click="pageType==0?save():update()">
			<button style="background-color:#1e1848;">
				{{pageType==0?'发布复盘':'保存修改'}}
			</button>
		</view>
		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 页面类型（0：发布 1：修改）
				pageType: 0,
				// 分点数量 
				pointNum: 3,
				// 分点数量
				pointNum1: 3,
				// 最大分点数量
				maxPointNum: 9,
				// 最大长度
				maxlength: 150,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				labelStyle: {
					'font-weight': 'bold',
					'font-size': '32rpx',
					'letter-spacing': '5rpx',
				},
				dom: {
					memberId: null,
					employeeId: null,
					flowContent: '',
					dailyGoal: '',
					dailyGoalRate: null,
					dailyThing: '',
					dailyImprovement: '',
					selfRecommend: 0,
					monthlyResult: '',
				},

				showPopup: false,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_head_portrait.png",
				exampleImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/example_review.png',
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				reviewId: null,
				flowContent: '',
				dailyGoal: '',
				weeklyGoal: '',
				teamGoal: '',
				leftHand: '',
				collaborator: '',
				dailyThingOld: '',
				imgList: [],
				textList: ['', '', '', '', '', '', '', '', '', ''],
				textList1: ['', '', '', '', '', '', '', '', '', ''],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 复制旧的
			copyOld(value) {
				let list = [{
					title: '确定填入流程一句话吗？',
					content: '流程一句话、目标等确立后可定为长期目标执行，填入后将覆盖当前内容'
				}, {
					title: '确定填入昨日的左手工作项吗？',
					content: '左手工作项若确认无误，可每日重复做，填入后将覆盖当前内容'
				}]
				uni.showModal({
					title: list[value].title,
					content: list[value].content,
					success: res => {
						if (res.confirm) {
							if (value == 0) {
								this.dom.flowContent = this.flowContent
								this.dom.dailyGoal = this.dailyGoal
								this.dom.weeklyGoal = this.weeklyGoal
								this.dom.teamGoal = this.teamGoal
								this.dom.leftHand = this.leftHand
								this.dom.collaborator = this.collaborator
							} else if (value == 1) {
								this.pointNum = 0
								this.pointNum = this.textToList(this.dailyThingOld, this.textList)
							}
						}
					}
				})

			},
			// 添加分点
			addPoint(value) {
				if (value == 0 && this.pointNum < this.maxPointNum) {
					this.pointNum++
					return
				} else if (value == 1 && this.pointNum1 < this.maxPointNum) {
					this.pointNum1++
					return
				}
				this.$refs.uNotify.warning('最多只能添加' + this.maxPointNum + '条哦！')
			},
			changeInput(e) {
				if (e.length >= this.maxlength) {
					return this.$refs.uNotify.warning('输入长度过长（单点限制' + this.maxlength + '个字）可以尝试分点哦！')
				}
			},
			// 文本转化为列表
			textToList(text, list) {
				let count = 0
				if (!text) {
					return 3
				}
				if (!text.includes('|')) {
					list[0] = text
					count++
				} else {
					let array = text.split('|')
					for (let i = 0; i < array.length && i < this.maxPointNum; i++) {
						list[i] = array[i]
						count++
					}

				}
				return count
			},
			// 列表转化为文本
			listToText(list, pointNumName) {
				let count = 0
				let text = ''
				for (let i = 0; i < this[pointNumName]; i++) {
					if (list[i]) {
						if (count != 0) {
							text += '|'
						}
						text += list[i]
						count++
					}
				}
				this[pointNumName] = count || 3
				return text
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			openEx() {
				let data = []
				data.push(this.exampleImg)
				uni.previewImage({
					urls: data,
					current: this.exampleImg
				})
			},
			// 自荐
			self() {
				this.dom.selfRecommend = 1
				this.$refs.uNotify.warning("自荐后将会自动发布一条表彰帖，并参与今日的标杆投票哦！")
			},
			// 打开记录
			openRecord() {
				uni.navigateTo({
					url: '/pages-other/excitation/review-list?showMine=1'
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			// 上传图片
			uploadImg() {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					count: 9,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						for (let i = 0; i < tempFilePaths.length; i++) {
							let item = tempFilePaths[i]
							uni.uploadFile({
								url: url,
								filePath: item,
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: res => {
									let result = JSON.parse(res.data)
									let imgUrl = result.data
									let data = {
										'id': 0,
										'imgUrl': imgUrl
									}
									this.imgList.push(data)
								}
							});
						}
					}
				});
			},
			delDragImg(index) {
				this.choiceImgIndex = index
			},
			// 删除个人照片
			delImage(done) {
				uni.showModal({
					content: '确定删除该照片吗，删除后不可恢复！?',
					success: res => {
						if (res.confirm) {
							this.delete(done)
						}
					}
				})
			},
			// 删除照片-旧方法
			delete(done) {
				let id = this.imgList[this.choiceImgIndex].id
				if (id == 0) {
					this.$delete(this.imgList, this.choiceImgIndex)
					this.$refs.uNotify.success("照片删除成功！")
					return
				}

				this.http({
					url: 'deleteExcellentEmployeeImg',
					method: 'GET',
					path: id,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("照片删除成功！")
							done()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			//输入校验
			checkInput() {
				if (!uni.getStorageSync('employeeId')) {
					this.$refs.uNotify.warning('非员工账号，无法提交每日复盘！')
					return false
				}

				// 格式化填入的分点项
				this.dom.dailyThing = this.listToText(this.textList, 'pointNum')
				this.dom.dailyImprovement = this.listToText(this.textList1, 'pointNum1')

				if (!this.dom.flowContent) {
					this.$refs.uNotify.warning('请填写流程一句话！')
					return false
				}

				if (!this.dom.dailyGoal) {
					this.$refs.uNotify.warning('请填写每日目标！')
					return false
				}
				if (this.dom.dailyGoalRate == null) {
					this.$refs.uNotify.warning('请填写目标达成率！')
					return false
				}
				if (!this.dom.teamGoal) {
					this.$refs.uNotify.warning('请填写团队目标！')
					return false
				}

				if (!this.dom.leftHand) {
					this.$refs.uNotify.warning('请填写左手动作！')
					return false
				}
				if (!this.dom.dailyThing) {
					this.$refs.uNotify.warning('请填写左手工作项！')
					return false
				}
				if (!this.dom.dailyImprovement) {
					this.$refs.uNotify.warning('请填写做对的动作！')
					return false
				}

				// if (this.dom.leftHand.length > 10) {
				// 	this.$refs.uNotify.warning('左手动作不能超过10个字！')
				// 	return false
				// }
				return true
			},
			// 添加优秀员工
			save() {
				if (!this.checkInput()) {
					return
				}
				uni.showModal({
					title: '确定发布每日复盘吗？',
					content: '发布后帖子将设为公开状态，管理员可见！',
					success: res => {
						if (res.confirm) {
							this.$set(this.dom, 'memberId', this.memberId)
							this.$set(this.dom, 'employeeId', this.employeeId)
							this.http({
								outsideUrl: 'https://api.xiaoyujia.com/content/insertDailyReview',
								method: 'POST',
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								data: this.dom,
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('每日复盘发布成功！')
										this.pageType = 1
										this.dom = res.data
										this.reviewId = this.dom.id
										this.updateExcellentEmployeeImgList()
										// 保存后自动返回上一级
										setTimeout(() => {
											uni.navigateBack()
										}, 2000);
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				});
			},
			// 更新
			update() {
				if (!this.checkInput()) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/updateDailyReview',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.dom,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('修改成功！')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 更新图片
			updateExcellentEmployeeImgList() {
				if (!this.imgList.length) {
					return
				}
				this.imgList.forEach(item => {
					this.$set(item, 'dailyReviewId', this.reviewId)
				})
				this.http({
					url: 'updateExcellentEmployeeImgList',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.imgList,
					success: res => {
						if (res.code == 0 && this.dom.selfRecommend == 1) {
							this.syncDailyReviewImg()
						}
					},
				})
			},
			// 同步每日复盘图片
			syncDailyReviewImg() {
				if (!this.reviewId) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/syncDailyReviewImg',
					method: 'GET',
					hideLoading: true,
					path: this.reviewId || 0,
					success: res => {
						if (res.code == 0) {}
					},
				})
			},
			// 获取每日复盘列表
			pageDailyReview() {
				if (this.reviewId) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/pageDailyReview',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						memberId: this.memberId,
						orderBy: "t.createTime DESC",
						current: 1,
						size: 10
					},
					success: res => {
						if (res.code == 0) {
							let data = res.data.records[0]
							this.flowContent = data.flowContent || ''
							this.dailyGoal = data.dailyGoal || ''
							this.weeklyGoal = data.weeklyGoal || ''
							this.teamGoal = data.teamGoal || ''
							this.leftHand = data.leftHand || ''
							this.collaborator = data.collaborator || ''
							let old = data.dailyThing || ''
							if (old.includes('|')) {
								this.dailyThingOld = old
							}
						}
					},
				})
			},
			// 获取每日复盘信息
			getDailyReviewById() {
				if (!this.reviewId) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/getDailyReviewById',
					method: 'GET',
					hideLoading: true,
					path: this.reviewId || 0,
					success: res => {
						if (res.code == 0) {
							this.dom = res.data
							this.pageType = 1
							this.pointNum = this.textToList(this.dom.dailyThing, this.textList)
							this.pointNum1 = this.textToList(this.dom.dailyImprovement, this.textList1)
						}
					},
				})
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先去登录吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/excitation/review')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else if (!uni.getStorageSync('employeeId')) {
					this.$refs.uNotify.warning("您登录的账号非员工账号哦，请勿尝试填写，否者无法提交！请重新登录或联系技术处理！")
					return true
				} else {
					return true
				}
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				let img = this.defaultPost
				return {
					title: '每日复盘，提升自我！',
					path: '/pages-other/excitation/review',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: img
				}
			},
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.reviewId = obj.t || this.reviewId
			}
			this.reviewId = options.id || this.reviewId
			this.getDailyReviewById()
			this.pageDailyReview()
			setTimeout(() => {
				this.checkLogin()
			}, 4000);
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.btn-commit {
		margin-left: 20rpx;
		text-align: center;
		width: 175rpx;
		height: 50rpx;
		line-height: 50rpx;
		color: #ffffff;
		background-color: #1e1848;
		border-radius: 30rpx;
	}
</style>