<template>
	<view class="w10 page">
		<view class="bacf">
			<u-collapse accordion :value="1">
				<!-- 基本信息 -->
				<u-collapse-item name="1" title="基本信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">订单编号:</view>
							<view class="" style="margin: auto 30rpx;">{{isSplit==1?baseInfo.billNo:orderData.billNo}}
							</view>
							<u-icon name="file-text" @click="copyValue(isSplit==1?baseInfo.billNo:orderData.billNo)"></u-icon>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">服务项目:</view>
							<view class="" style="margin: auto 30rpx;">
								{{isSplit==1?baseInfo.productName:orderData.productName}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">订单状态:</view>
							<view class="" style="margin: auto 30rpx;">
								{{isSplit==1?baseInfo.orderStateName:orderData.orderState}}
							</view>
						</view>
					</view>

					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="isSplit==0">
						<view class="" style="display: flex;">
							<view class="c0">订单标签:</view>
							<view class="" style="margin: auto 30rpx;">
								{{orderData.lblId||'无'}}
							</view>
						</view>
					</view>

					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">会员名称:</view>
							<view class="" style="margin: auto 30rpx;">
								{{isSplit==1?baseInfo.name || '' :orderData.name || ''}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">会员号码:</view>
							<view class="" style="margin: auto 30rpx;">{{isSplit==1?baseInfo.bindTel:orderData.bindTel}}
							</view>
							<u-icon name="phone"
								@click="callPhone(isSplit==1?baseInfo.bindTel:orderData.bindTel)"></u-icon>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">会员账号:</view>
							<view class="" style="margin: auto 30rpx;">{{isSplit==1?baseInfo.account:orderData.account}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="c0">订单金额:</view>
						<view class="red" style="margin: auto 30rpx;">
							{{ '¥' + (isSplit==1?baseInfo.realTotalAmount:orderData.realTotalAmount)}}
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="!isSplit==1">
						<view class="c0">服务时间:</view>
						<view class="red" style="margin: auto 30rpx;">{{orderData.startTime}}</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="!isSplit==1">
						<view class="c0" style="width: 27%;">服务地址:</view>
						<view class="w75 red" style="margin: 0 0rpx;width: 73%;">
							<u-text :text="orderData.serviceAddress" lines="6"></u-text>
						</view>
						<u-icon name="file-text" @click="copyValue(orderData.serviceAddress)"></u-icon>
					</view>
					<view class="f16 w9 mg-at lh32" v-if="isSplit==1">
						<view class="" style="display: flex;">
							<view class="c0">服务地址:</view>

							<u-text :text="baseInfo.street" lines="6"></u-text>
							<u-icon name="file-text" @click="copyValue(baseInfo.street)"></u-icon>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="!isSplit==1 && orderData.serviceName">
						<view class="c0">服务人员:</view>
						<view class="red" style="margin: auto 30rpx;">{{orderData.serviceName}}({{orderData.serviceNo}})
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="!isSplit==1 && orderData.tel">
						<view class="c0">联系电话:</view>
						<view class="red" style="margin: auto 30rpx;">{{orderData.tel}}</view>
						<u-icon name="phone" @click="callPhone(orderData.tel)"></u-icon>
					</view>
				</u-collapse-item>
				<!-- 开发信息 -->
				<u-collapse-item v-if="isSplit" name="2" title="开发信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">开单人:</view>
							<view class="" style="margin: auto 20rpx;">{{baseInfo.agentName}}({{baseInfo.agentNo}})
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">开发人工号:</view>
							<view class="" style="margin: auto 20rpx;">{{baseInfo.channel}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">预约时间:</view>
							<view class="" style="margin: auto 20rpx;">{{baseInfo.startTime}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="c0">发布服务内容(仅阿姨、客户可见):</view>
						<u-textarea placeholder="请输入内容" v-model="baseInfo.serviceRemark" maxlength="250"></u-textarea>
						<view class="flac-row" style="margin: 10rpx auto;">
							<u-button text="更改保存" color="#1e1848" customStyle="color:#f6cc70"
								@click="updateServiceRemark"></u-button>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="c0">订单备注(客户要求):</view>
						<u-textarea v-model="baseInfo.remark" placeholder="请输入内容" maxlength="250"></u-textarea>
						<view class="flac-row" style="margin: 10rpx auto;">
							<u-button text="模板输入" color="#1e1848" plain
								@click="showRemark=true,remarkDom.type=baseInfo.productName,remarkDom.addr=baseInfo.street">
							</u-button>
							<u-button text="更改保存" color="#1e1848" customStyle="color:#f6cc70"
								@click="updateRemark"></u-button>
						</view>
					</view>
				</u-collapse-item>
				<!-- 费用信息 -->
				<u-collapse-item name="3" title="费用信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">订单金额:</view>
							<view class="red" style="margin: auto 30rpx;">
								{{'¥' + (isSplit==1?baseInfo.realTotalAmount:orderData.realTotalAmount )}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">优惠金额:</view>
							<view class="red" style="margin: auto 30rpx;">
								{{'¥' + (isSplit==1?baseInfo.syhqamount:orderData.preferentialAmount )}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">已支付金额:</view>
							<view class="red" style="margin: auto 30rpx;">
								{{'¥' + (isSplit==1?baseInfo.amount:orderData.amount )}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">可退款金额:</view>
							<view class="red" style="margin: auto 30rpx;">
								{{'¥' + (isSplit==1?baseInfo.qamount:orderData.qaMount )}}
							</view>
						</view>
					</view>
				</u-collapse-item>
				<!-- 业务状态 -->
				<u-collapse-item v-if="isSplit" name="4" title="业务状态">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">业务状态:</view>
							<view class="" style="margin: auto 30rpx;">{{returnYwState(Number(baseInfo.ywStatus))}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">接单人:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.jdPerpson || ''}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">指定服务员工:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.serviceNo || ''}} </view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">可拆单数:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.totalSplitNum}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">剩余可拆单数:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.totalSplitNum - baseInfo.splitNum}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">合同开始日期:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.RealStartTime}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">合同结束日期:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.RealEndTime}}</view>
						</view>
					</view>
				</u-collapse-item>
				<!-- 合同绑定信息 -->
				<u-collapse-item v-if="isSplit" name="5" title="合同绑定">
					<u-icon name="play-right-fill" size="20" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">合同编号:</view>
							<view class="" style="margin: auto 30rpx;">{{contractInfo.no}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">服务开始时间:</view>
							<view class="" style="margin: auto 30rpx;">{{contractInfo.serviceStarDate}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">服务结束时间:</view>
							<view class="" style="margin: auto 30rpx;">{{contractInfo.serviceEndDate}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">接单员工:</view>
							<view class="" style="margin: auto 30rpx;">{{contractInfo.employeeName}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">员工工号:</view>
							<view class="" style="margin: auto 30rpx;">{{contractInfo.employeeNo}}</view>
						</view>
					</view>
					<u-button text="合同信息传入订单业务信息" color="#1e1848" customStyle="color:#f6cc70"
						:disabled="baseInfo.orderState>30" @click="setYw"
						v-if="contractInfo.employeeNo && contractInfo.employeeName && contractInfo.serviceEndDate && contractInfo.serviceStarDate && contractInfo.no">
					</u-button>
					<u-button text="查看法大大合同" color="#1e1848" customStyle="color:#f6cc70" @click="showFadada">
					</u-button>
				</u-collapse-item>
				<!-- 拆单信息 -->
				<u-collapse-item v-if="isSplit" name="6" title="拆单信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">拆单人:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.splitUserName || ''}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">拆单时间:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.SplitTime || ''}}</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">拆单数量:</view>
							<view class="" style="margin: auto 30rpx;">{{baseInfo.splitNum}}</view>
						</view>
					</view>

					<view class="f16 w9 mg-at lh32">
						<view class="c0">拆单内容:</view>
						<view class="splitCon" v-for="(item,i) in splitCon" :key="i">
							<view class="">拆单编号:<text class="c6" style="margin: auto 30rpx;">{{item.billNo}}</text>
							</view>
							<view class="">金额:<text class="c6"
									style="margin: auto 30rpx;">{{item.realTotalAmount}}</text></view>
						</view>
					</view>
				</u-collapse-item>
				<!-- 评价信息 -->
				<u-collapse-item name="7" v-if="!isGroup&&!isSplit" title="评价信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="w95 mg-at" v-if="commentData">
						<view class="flac-row-b">
							<view class="flac-row lh40">
								<u-avatar :src="commentData.memberHeadImg||blankHeadImg" size="30" shape="circle" />
								<view class="t-indent fb">{{commentData.memberName || '匿名用户'}}</view>
							</view>
							<view class="f12 c9">{{commentData.productName || ''}} |
								{{commentData.creatDate || '暂无创建时间'}}
							</view>
						</view>
						<view class="flac-row" style="margin: 20rpx auto;">
							<view class="iconStyle flac-row" v-if="commentData.evaluateGrade == 2">
								<u-icon :name="commentData.icon || 'thumb-up-fill'" color="#8f755d" shape="circle"
									size="15" />
								<view class="f12">{{commentData.tagName || '认为可推荐'}}</view>
							</view>
						</view>
						<view>
							<view>
								{{commentData.evaluate || '暂无评价内容'}}
							</view>
							<view class="imgBox">
								<image class="imgStyle" v-for="(item,index) in commentData.serviceAcceptanceFormImgList"
									:key="index" :src="item.imgUrl" mode="widthFix" @click="openImgPreview(index)">
								</image>
							</view>
						</view>
					</view>

					<view v-else>
						<u-empty text="暂无评价" icon="http://cdn.uviewui.com/uview/empty/data.png" />
					</view>

					<u-gap height="20"></u-gap>
				</u-collapse-item>
				<!-- 投诉信息 -->
				<u-collapse-item v-if="!isGroup&&!isSplit" name="7" title="投诉信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="w95 mg-at" v-if="tsLog">
						<view class="f16 w9 mg-at lh32">
							<view class="" style="display: flex;">
								<view class="c0 w3">投诉内容:</view>
								<view class="w7" style="margin: 0 0rpx;">
									<text>{{tsLog.tsContent || tsLog.mcommContent || '暂无投诉内容'}}</text>
								</view>
							</view>
						</view>
						<view class="f16 w9 mg-at lh32">
							<view class="" style="display: flex;">
								<view class="c0">投诉时间:</view>
								<view class="" style="margin: auto 30rpx;">{{tsLog.createTime || '暂无时间'}}
								</view>
							</view>
						</view>
					</view>

					<view v-else>
						<u-empty text="暂无投诉" icon="http://cdn.uviewui.com/uview/empty/data.png" />
						<u-gap height="20"></u-gap>
					</view>
				</u-collapse-item>
				<!-- 投诉信息 -->
				<u-collapse-item name="7" title="投保信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view v-if="!orderData.insuranceManager|| orderData.insuranceManager.length == 0">
						暂无投保信息(可能订单还未开始服务)
					</view>
					<view v-else>
						<view v-for="(item,index) in orderData.insuranceManager" :key="index">
							<view class="f16 w9 mg-at lh32">
								<u-gap height="10"></u-gap>
								<view class="" style="display: flex;">
									<view class="c0 w3">投保员工:</view>
									<view class="w7" style="margin: 0 0rpx;">
										<text>{{item.employeeName}}</text>
									</view>
								</view>
								<view class="" style="display: flex;">
									<view class="c0 w3">投保单号:</view>
									<view class="w7" style="margin: 0 0rpx;">
										<text>{{item.insuranceNo}}</text>
									</view>
								</view>
							</view>
							<view class="f16 w9 mg-at lh32">
								<view class="" style="display: flex;">
									<view class="c0 w3">投保状态:</view>
									<view class="w7" style="margin: 0 0rpx;">
										<text>{{item.statusDetail}}</text>
									</view>
								</view>
							</view>

							<view class="f16 w9 mg-at lh32">
								<view class="" style="display: flex;">
									<view class="c0 w3">投保详情:</view>
									<view class="w7" style="margin: 0 0rpx;">
										<u-text @click="copy(item.policyUrl)" text="点击复制投保链接" color="red"></u-text>
									</view>
								</view>
							</view>
						</view>

					</view>
					<view class="w9" style="display: flex;margin: 30rpx auto;" v-if="storeType === 3">
						<u-button @click="insuranceTo(3)" text="按单投保" color="#1e1848"
							customStyle="width:30%;color:#f6cc70" v-if="!isSplit"></u-button>
						<u-button @click="insuranceTo(1)" text="按月投保" color="#1e1848"
							customStyle="width:30%;color:#f6cc70" v-if="isSplit"></u-button>
						<u-button @click="insuranceTo(2)" text="按年投保" color="#1e1848"
							customStyle="width:30%;color:#f6cc70" v-if="isSplit"></u-button>
					</view>


				</u-collapse-item>
				
				<!-- 薪酬信息 -->
				<u-collapse-item v-if="!isSplit" name="9" title="薪酬信息">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view v-for="(item,index) in abcServiceCompensationList" :key="index">
						<view class="w9 mg-at">
							<uni-table ref="table"  border stripe emptyText="暂无更多数据">
								<uni-tr>
									<uni-th class="thStyle" align="center" >员工</uni-th>
									<uni-th class="thStyle" align="center">订单号</uni-th>
									<uni-th class="thStyle" align="center">金额（元）</uni-th>
									<uni-th class="thStyle" align="center">计提金额（元）</uni-th>
									<uni-th class="thStyle" align="center">员工薪酬（元）</uni-th>
								</uni-tr>
									<uni-td align="center">{{ item.serviceName+'('+item.serviceNo+')' }}</uni-td>
									<uni-td align="center">{{ item.billItemNo||"" }}</uni-td>
									<uni-td align="center">{{ item.orderWage||0.00 }}</uni-td>
									<uni-td align="center">{{ item.getWage||0.00 }}</uni-td>
									<uni-td align="center"><u--input placeholder="请输入员工薪酬" type="number" border="surround" v-model="item.realWage"/></uni-td>
							</uni-table>
						</view>
					</view>
					<u-button v-if="storeType==2" size="circle" text="重置薪酬" color="#1e1848" customStyle="color:#f6cc70"
						@click="resetPay"></u-button>
				</u-collapse-item>

				<!-- 操作日志 -->
				<u-collapse-item name="8" title="操作日志">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<!-- <view class="logCon w10 mg-at f16 c0 lh32" v-for="(item,i) in operationLog" :key="i">
						<view class="fb">{{item.title}}</view>
						<view class="">内容:<text class="c6" style="margin: auto 30rpx;">{{item.message}}</text></view>
						<view class="">操作时间:<text class="c6" style="margin: auto 30rpx;">{{item.createTime}}</text>
						</view>
						<view class="">操作人:<text class="c6" style="margin: auto 30rpx;">{{item.operator}}</text></view>
					</view> -->
					<view v-for="(item,i) in operationLog" :key="i">
						<uni-card :title="item.title" :border="false">
							<view class="">内容:<text class="c6" style="margin: auto 30rpx;">{{item.message}}</text>
							</view>
							<view class="">操作时间:<text class="c6" style="margin: auto 30rpx;">{{item.createTime}}</text>
							</view>
							<view class="">操作人:<text class="c6" style="margin: auto 30rpx;">{{item.operator}}</text>
							</view>
						</uni-card>
					</view>
				</u-collapse-item>
				
			</u-collapse>
		</view>
		<view class="w10 bacf" style="display: flex;position:fixed;bottom:0;left:0">
				<u-button text="更多操作" color="#1e1848" customStyle="height:100rpx;color:#f6cc70"
					@click="moreOperate=true"></u-button>
		</view>
		<!-- 订单改价---弹窗 -->
		<u-modal :show="showModal1" :showCancelButton="true" @confirm="confirm1" @cancel="showModal1 = false">
			<view class="f18">
				<view class="fb lh50">订单编号:<text style="margin-left: 20rpx;">{{billNo}}</text></view>
				<u-divider text="订单改价"></u-divider>
				<!-- <u-number-box v-model="changeAmount" @change="valChange" integer></u-number-box> -->
				<u-input v-model="changeAmount" placeholder="请输入订单金额" type="number"></u-input>
				<view class="lh40 text-c">订单金额：<text class="blue">{{ '¥' + baseInfo.realTotalAmount}}</text></view>
				<view class="lh40 text-c">改后金额：<text class="red">{{ '¥' + changeAmount}}</text></view>
			</view>
		</u-modal>
		<!-- 打开收款码 -->
		<u-modal :show="showModal2" title="付款码" @confirm="showModal2 = false">
			<view v-if="imgCode">
				<u-image :src="imgCode" mode="aspectFit"></u-image>
			</view>
			<view v-else>
				<u-text :text="text"></u-text>
			</view>
		</u-modal>
		<u-popup :show="showRemark" mode="bottom" @close="showRemark = false">
			<u-alert type="warning" description="警告：点击“更改保存”后，原有备注信息将会被覆盖。 "></u-alert>
			<u-form labelPosition="left" labelWidth="auto">
				<u-form-item label="【服务类型】" prop="remarkDom.type">
					<u-input v-model="remarkDom.type" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="【工作地址】" prop="remarkDom.addr">
					<u-input v-model="remarkDom.addr" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="【工作内容】" prop="remarkDom.con">
					<u-input v-model="remarkDom.con" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="【工作要求】" prop="remarkDom.yq">
					<u-input v-model="remarkDom.yq" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="【薪资标准】" prop="remarkDom.xz">
					<u-input v-model="remarkDom.xz" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="【联系电话】" prop="remarkDom.phone">
					<u-input v-model="remarkDom.phone" border="bottom"></u-input>
				</u-form-item>
			</u-form>
			<u-row>
				<u-col span="6">
					<u-button text="取消" @click="showRemark = false" color="#1e1848" plain></u-button>
				</u-col>
				<u-col span="6">
					<u-button text="更改保存" color="#1e1848" customStyle="color:#f6cc70"
						@click="updateRemarkDom"></u-button>
				</u-col>
			</u-row>
		</u-popup>
		<u-modal :show="showInsuranceModal" title="付款码" @confirm="showInsuranceModal = false">
			<view class="slot-content">
				<u-image :src="insurancePayUrl" mode="aspectFit"></u-image>
				<u-button @click="saveImg" color="#1e1848" text="保存付款码"></u-button>
			</view>
		</u-modal>
		<u-popup :show="yuezfFlag" @close="yuezfFlag = false">
			<view>
				<uni-section title="余额支付" type="line" padding>
					<scroll-view scroll-y="true" class="scroll-Y" style="height: 41vh">
						<u-empty text="暂无优惠券!" width="120" textSize="18" v-if="!couponList.length"
							customStyle="padding-top:200rpx">
						</u-empty>
						<radio-group @change="radioChange">
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in couponList" :key="i">
								<view>
									<radio :value="item.value" :checked="i === current" />
								</view>
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="lh36">面值：{{item.valueDesc}}</view>
										<view class="lh36">优惠券名称：{{item.condition}}</view>
										<view class="lh36">有效期起始时间：{{item.startATime}}</view>
										<view class="lh36">有效期结束时间：{{item.endATime}}</view>
									</view>
								</view>
							</view>
						</radio-group>
					</scroll-view>
				</uni-section>
			</view>
			<view style="margin-left: 45rpx;">
				<view style="display: flex;">
					<view class="fb f15" style="padding-bottom: 15rpx;">
						订单金额：￥{{isSplit==1?baseInfo.realTotalAmount:orderData.realTotalAmount}}</view>
					<view class="fb f15" style="padding-bottom: 15rpx;margin-left: 20%">抵扣金额：￥{{dikoMoney}}</view>
				</view>
				<view style="display: flex;">
					<view class="fb f15" style="color: red;padding-bottom: 15rpx;">应付金额：￥{{yfMoney-dikoMoney-amount}}
					</view>
					<view class="fb f15" style="padding-bottom: 15rpx;margin-left: 20%">账户余额：￥{{memberAmount}}</view>
				</view>
			</view>
			<u-button color="#1e1848" v-if="" text="确认支付" shape="circle" @click="confirmYeZf()"
				customStyle="width: 60%;height: 70rpx;margin:40rpx auto ;"></u-button>
		</u-popup>

		<u-modal :show="showModal" title="编辑管家备注" :showCancelButton="true" @confirm="editConfirm1"
			@cancel="showModal = false">
			<view class="slot-content" style="display: flex; align-items: center;">
				<u--input v-model="billRemark" placeholder="请输入留言" border="bottom" inputAlign="center" clearable>
				</u--input>
			</view>
		</u-modal>
		<u-popup :show="moreOperate" @close="moreOperate = false" mode="right">
			<u-button text="收款码"  color="#1e1848" customStyle="height:100rpx;color:#f6cc70;width: 500rpx;"
				v-if="!isGroup||isSplit" @click="showModal2=true,moreOperate=false" style="margin-bottom: 20rpx;margin-top: 150rpx;"></u-button>
			<u-button text="余额支付" color="#1e1848" style="margin-bottom: 20rpx;margin-top: 20rpx;"
				v-if="!isGroup||isSplit" customStyle="height:100rpx;color:#f6cc70" @click="yuezf"></u-button>
			<u-button text="编辑管家备注" color="#1e1848" style="margin-bottom: 20rpx;margin-top: 20rpx;"
				customStyle="height:100rpx;color:#f6cc70" @click="edit"></u-button>
			<u-button text="拆单" color="#1e1848" customStyle="height:100rpx;color:#f6cc70" throttleTime="2000"
				@click="splitOrder" v-show="baseInfo.isGroup&&isSplit" style="margin-bottom: 20rpx;margin-top: 20rpx;"></u-button>
				<u-button v-if="isSplit" text="订单改价" color="#1e1848" customStyle="height:100rpx;color:#f6cc70"
					@click="showModal1=true,moreOperate = false" style="margin-bottom: 20rpx;margin-top: 20rpx;"></u-button>
			<u-button v-if="!isGroup&&!isSplit" text="订单改时" color="#1e1848" customStyle="height:100rpx;color:#f6cc70"
				@click="showTime=true,moreOperate = false" style="margin-bottom: 20rpx;margin-top: 20rpx;"></u-button>
			<u-button v-if="!isGroup&&!isSplit&&orderData.orderState=='服务结束'" text="一键结算" color="#1e1848" customStyle="height:100rpx;color:#f6cc70"
				@click="oneClickSettlement" style="margin-bottom: 20rpx;margin-top: 20rpx;"></u-button>
		</u-popup>
		
		<hTimeAlert title="改时时间" subhead="列表只显示可预约时间段" dayStartIntTime="0" rangeDay="7" :no="orderData.billNo"
			:isShow="showTime" :maskHide="maskHide" :rangeType="rangeType" :closeBtn="closeBtn"
			@closeAlert="handelClose"></hTimeAlert>
			
	</view>
</template>

<script>
	import hTimeAlert from '@/pages-work/components/h-time-alert/h-time-alert.vue';
	export default {
		components: {
			hTimeAlert
		},
		data() {
			return {
				storeType: uni.getStorageSync('storeType'),
				moreOperate: false,
				showTime: false,
				showInsuranceModal: false,
				maskHide: true, //预约时间打开组件
				rangeType: false, //预约时间打开组件
				closeBtn: true, //预约时间打开组件
				rangeType: false, //预约时间打开组件
				storeType: uni.getStorageSync('storeType'),
				text: '',
				insurancePayUrl: '',
				isGroup: '',
				showRemark: false,
				dikoMoney: 0.00,
				showModal: false,
				memberAmount: 0.00,
				changeAmount: 0,
				couponList: [],
				abcServiceCompensationList: [],
				billRemark: '',
				isSplit: null,
				current: 0,
				memberId: null,
				yuezfFlag: false,
				yfMoney: 0.00,
				amount: 0.00,
				billNo: '',
				showModal1: false,
				orderData: {},
				groupBillNo: '',
				showModal2: false,
				baseInfo: {},
				contractInfo: {},
				splitInfo: {},
				splitCon: [],
				operationLog: [],
				imgCode: '',
				remarkDom: {
					type: null,
					addr: null,
					con: null,
					yq: null,
					xz: null,
					phone: uni.getStorageSync('account') + ' ' + uni.getStorageSync('employeeName'),
				},
				commentData: null,
				tsLog: null,
				timeParam: {},
				blankHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				imglist: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-bzyw.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-ygsj.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
				]
			};
		},
		onLoad(option) {
			if (!option.billNo) {
				return uni.showToast({
					title: '订单号错误',
					duration: 5000,
					icon: 'error'
				})
			}
			this.billNo = option.billNo;
			this.ifIsSplit()
			this.getServiceAcceptanceForm()
			this.getOrderTsLog()
		},
		methods: {
			copyValue(val) {
				uni.setClipboardData({
					data: val,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			oneClickSettlement(){
				this.moreOperate= false
				uni.showModal({
					title: '提示',
					content: '确认执行此操作吗？',
					success: res => {
						if (res.confirm) {
							this.http({
								url: 'oneClickSettlement',
								method: 'POST',
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								data: {
									employeeId: uni.getStorageSync('employeeId'),
									billNo: this.billNo,
								},
								success: res => {
									 uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}
							})
						}
					}
				});
			},
			resetPay(){
				uni.showModal({
					title: '提示',
					content: '确定重置薪酬吗？',
					success: res => {
						if (res.confirm) {
							for (var i = 0; i < this.abcServiceCompensationList.length; i++) {
								if(!
								(!isNaN(parseFloat(this.abcServiceCompensationList[i].realWage)) 
								&& isFinite(this.abcServiceCompensationList[i].realWage)
								&&parseFloat(this.abcServiceCompensationList[i].realWage) >= 0)
									){
									return uni.showToast({
										title: '请输入正确的员工薪酬！',
										icon: 'none'
									})
								}
							}
							this.http({
								url: 'resetPay',
								method: 'POST',
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								data: {
									employeeId: uni.getStorageSync('employeeId'),
									orderWagesList: this.abcServiceCompensationList
								},
								success: res => {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
									setTimeout(()=>{
										this.getGroupOrderData()
									},2000)
								}
							})
							
						} else if (res.cancel) {
							// this.showTime = false;
						}
					}
				});
			},
			getAbcServiceCompensation(){
				this.http({
					url: 'getAbcServiceCompensation',
					method: 'GET',
					data: {
						billNo: this.billNo
					},
					success: res => {
						if (res.code == 0) {
							this.abcServiceCompensationList = res.data
						}
					}
				})
			},
			handelClose(data) {
				if (!data) {
					return this.showTime = false;
				}
				this.timeParam.startTime = data.date + " 00:00:00";
				this.timeParam.startTimeSpan = data.start;
				this.timeParam.endTimeSpan = data.end;
				this.timeParam.billNo = this.orderData.billNo;
				uni.showModal({
					title: '提示',
					content: '是否改时到' + data.all,
					success: res => {
						if (res.confirm) {
							this.updateTime();
						} else if (res.cancel) {
							this.showTime = false;
						}
					}
				});
			},
			updateTime() {
				this.http({
					url: 'generateAddCarToken',
					data: {
						id: uni.getStorageSync("employeeId"),
						no: uni.getStorageSync("employeeNo"),
						remark: uni.getStorageSync("employeeToken")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {
						if (res.code == 0 && res.data) {
							uni.setStorageSync("employeeToken", res.data)
							this.http({
								outsideUrl: 'https://appapi.xiaoyujia.com/api/customer/UpdateServiceTime',
								data: this.timeParam,
								header: {
									"content-type": "application/json;charset=UTF-8",
									'Authorization': "Basic " + res.data,
								},
								method: 'POST',
								success: res => {
									if (res.Meta.State == 200) {
										this.showTime = false;
										uni.showToast({
											title: '操作成功',
											icon: 'none'
										})
										setTimeout(() => {
											this.saveUpdateOrderServiceTimeLog()
											this.ifIsSplit()
											this.getOrderTsLog()
											this.getGroupOrderData()
										}, 1500)
									} else {
										uni.showToast({
											title: res.Meta.Msg,
											icon: 'none'
										})
									}
			
								}
							})
						} else {
							uni.showToast({
								title: '改时失败！系统异常！',
								icon: 'none'
							})
						}
					}
				})
			},
			saveUpdateOrderServiceTimeLog(){
				this.http({
					url: 'saveUpdateOrderServiceTimeLog',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						employeeId: uni.getStorageSync('employeeId'),
						billNo: this.billNo,
						updateType: 2,
						startTime: this.timeParam.startTime
					},
					success: res => {
					}
				})
			},
			// 打开弹窗进行编辑
			edit() {
				this.showModal = true
				this.moreOperate = false
			},
			// 弹窗确认按钮--点击确认后输入框内容赋值给item.other
			editConfirm1() {
				this.http({
					url: 'updateAgentRemarkByBillNo',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						billNo: this.billNo,
						agentRemark: this.billRemark
					},
					success: res => {
						uni.showToast({
							title: '更新成功'
						})
						this.agentRemark = '';
						this.showModal = false;

					}
				})

			},
			confirmYeZf() {
				// if(this.yfMoney-this.dikoMoney>this.memberAmount){
				// 	return uni.showToast({
				// 		title: '余额不足！',
				// 		duration: 2000,
				// 		icon: 'error'
				// 	})
				// }
				let realMoney = this.yfMoney - this.dikoMoney
				let billNo = ""
				let Cid = this.current
				if (this.isSplit == 0) {
					billNo = this.orderData.billNo
					if (this.couponList.length > 0) {
						Cid = this.couponList[this.current].id
					}
				} else {
					billNo = this.baseInfo.billNo
				}
				this.http({
					outsideUrl: 'https://inside.xiaoyujia.com/api/order/addpay?BillNo=' +
						billNo + "&Fvalue=" + realMoney + "&empId=" + uni.getStorageSync('employeeId') +
						"&Cid=" + Cid,
					method: "POST",
					success: res => {
						if (res.Meta.State == 200) {
							uni.showToast({
								title: '支付成功!',
								icon: 'none',
								duration: 1000,
							})
							setTimeout(() => {
								this.yuezfFlag = false
								this.ifIsSplit()
								this.getOrderTsLog()
								this.getGroupOrderData()
							}, 1500)
						} else {
							uni.showToast({
								title: res.Meta.Msg,
								icon: 'none'
							})
						}
					}
				})
				console.log("zzzz", this.current);
			},
			radioChange(evt) {
				for (let i = 0; i < this.couponList.length; i++) {
					if (this.couponList[i].value === evt.detail.value) {
						this.current = i;
						this.dikoMoney = this.couponList[i].valueDesc.split("元")[0]
						break;
					}
				}
			},
			yuezf() {
				this.moreOperate = false
				//如果是标准单获取优惠券
				if (this.isSplit == 0) {
					this.getCouponByProduct()
				}
				//获取会员余额
				this.getMemberAmount()
				this.yuezfFlag = true
			},
			getMemberAmount() {
				this.http({
					url: 'getMemberAmount',
					method: 'get',
					data: {
						id: this.memberId
					},
					success: res => {
						if (res.code == 0) {
							this.memberAmount = res.data.amount
						} else {
							uni.showToast({
								title: '获取用户余额失败!',
								icon: 'none',
							})
						}
					}
				})
			},
			getCouponByProduct() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/getCouponByProduct?memberId=' + this.orderData
						.id +
						'&productId=' + this.orderData.productId + '&storeId=' + this.orderData.storeId,
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.couponList = res.data
							if (res.data.length > 0) {
								this.dikoMoney = res.data[0].value
							}
							for (var i = 0; i < this.couponList.length; i++) {
								this.couponList[i].value = i.toString()
							}
						} else {
							uni.showToast({
								title: '获取优惠券错误!',
								icon: 'none',
							})
						}
					}
				})
			},
			saveImg() {
				uni.downloadFile({ //下载文件资源到本地,返回文件的本地临时路径
					url: this.insurancePayUrl, //网络图片路径
					success: (res) => {
						var imageUrl = res.tempFilePath; //临时文件路径
						uni.saveImageToPhotosAlbum({ //保存图片到系统相册
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			goInsurance(type) {
				this.http({
					url: 'orderBuRePayOtherPay',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						billNo: this.billNo,
						type: type
					},
					success: res => {
						if (res.code === 0) {
							this.insurancePayUrl = res.data;
							this.showInsuranceModal = true;
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			insuranceTo(type) {
				let text = "";
				if (type == 3) {
					text = "保单生效时间为半小时后,确定上保后请及时支付保单,超过5分钟无法支付即视为拒保"
				} else {
					text = "保单开始生效时间为第二天凌晨开始,请在今天内完成支付即可生效,否则视为拒保"
				}
				uni.showModal({
					title: "注意",
					content: text,
					confirmText: '我已知晓',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.goInsurance(type);
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})

			},
			callPhone(val) {
				uni.makePhoneCall({
					phoneNumber: val, //电话号码
					success: function(e) {},
					fail: function(e) {}
				});
			},
			ifIsSplit() {
				this.http({
					url: 'ifIsSplit',
					method: 'GET',
					data: {
						billNo: this.billNo
					},
					success: res => {
						if (res.code == 0) {
							this.isSplit = res.data.isSplit
							this.isGroup = res.data.isGroup
							//判断是集团单还是标准单
							this.getOrderAmountCode();
							if (this.isSplit) {
								this.getorderInfo();
								this.getorderSplitInfo();
								this.getOrderOperation();
							}else{
								this.getAbcServiceCompensation()
							}
							this.getGroupOrderData()

						} else {
							uni.showToast({
								title: '系统错误!',
								icon: 'none'
							})
						}
					}
				})
			},
			// 获取订单服务评价
			getServiceAcceptanceForm() {
				this.http({
					url: 'getServiceAcceptanceForm',
					method: 'POST',
					data: {
						orderNo: this.billNo
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.commentData = res.data
						}
					}
				})
			},
			getGroupOrderData() {
				this.http({
					url: 'getGroupOrderData',
					data: {
						billNo: this.billNo
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							if (this.isSplit == 0) {
								this.orderData = res.data.vo
								this.operationLog = res.data.list
							}
							this.groupBillNo = res.data.vo.groupBillNo
							this.memberId = res.data.vo.id
							this.amount = res.data.vo.amount
							this.yfMoney = res.data.vo.realTotalAmount
						} else {
							uni.showToast({
								title: '系统错误!',
								icon: 'none'
							})
						}
					}
				})
			},
			getOrderAmountCode() {
				this.http({
					url: 'getOrderPayCode',
					method: 'GET',
					data: {
						billNo: this.groupBillNo?this.groupBillNo:this.billNo
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.imgCode = res.data;
						} else {
							this.text = res.msg;
						}

					}

				})
			},
			setYw() {
				let ywDom = {
					ywStatus: 3,
					jdPerpson: this.contractInfo.employeeName,
					serviceNo: this.contractInfo.employeeNo,
					totalSplitNum: 1,
					RealStartTime: this.contractInfo.serviceStarDate,
					RealEndTime: this.contractInfo.serviceEndDate,
					billNo: this.billNo,
					operator: uni.getStorageSync('employeeName'),
					operatorId: uni.getStorageSync('employeeId'),
				};
				this.http({
					url: 'contractToOrder',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: ywDom,
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '更新成功',
								})
							}, 1500)
							this.getorderInfo();
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			updateRemarkDom() {
				let remark = '';
				if (this.remarkDom.type) {
					remark += "【订单类型】" + this.remarkDom.type + "\n"
				}
				if (this.remarkDom.addr) {
					remark += "【工作地址】" + this.remarkDom.addr + "\n"
				}
				if (this.remarkDom.con) {
					remark += "【工作内容】" + this.remarkDom.con + "\n"
				}
				if (this.remarkDom.yq) {
					remark += "【工作要求】" + this.remarkDom.yq + "\n"
				}
				if (this.remarkDom.xz) {
					remark += "【薪资标准】" + this.remarkDom.xz + "\n"
				}
				if (this.remarkDom.phone) {
					remark += "【联系电话】" + this.remarkDom.phone + "\n"
				}
				this.baseInfo.remark = remark;
				this.showRemark = false;
				this.updateRemark()
			},
			updateServiceRemark() {
				this.http({
					url: 'updateServiceRemark',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.baseInfo,
					success: res => {
						setTimeout(() => {
							uni.showToast({
								title: '更新成功',
							})
						}, 1500)
						this.getorderInfo();
					}
				})
			},
			updateRemark() {
				this.http({
					url: 'updateOrderRemark',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.baseInfo,
					success: res => {
						setTimeout(() => {
							uni.showToast({
								title: '更新成功',
							})
						}, 1500)
						this.getorderInfo();
					}
				})
			},
			getOrderOperation() {
				this.http({
					url: 'getOrderOperationLogList',
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						billNo: this.billNo
					},
					success: res => {
						this.operationLog = res.data;
					}
				})
			},
			getorderSplitInfo() {
				this.http({
					url: 'agentOrderInfo',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						sourceBillNo: this.billNo
					},
					success: res => {
						this.splitCon = res.data;
					}
				})
			},
			returnYwState(value) {
				switch (value) {
					case 1:
						return "咨询";
					case 2:
						return "已试用";
					case 3:
						return "已签单";
					case 4:
						return "已接单";
					case 5:
						return "已面试";
					default:
						return "未知";
				}
			},
			getOrderTsLog() {
				this.http({
					url: 'getOrderTsLog',
					data: {
						billNo: this.billNo
					},
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.tsLog = res.data
						}
					}
				})
			},
			getorderInfo() {
				this.http({
					url: 'agentOrderInfo',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						billNo: this.billNo
					},
					success: res => {
						this.baseInfo = res.data[0];
						// this.yfMoney = res.data.realTotalAmount
						this.getContractInfo();
					}
				})
			},
			getContractInfo() {
				this.http({
					url: 'getContractByOrderId',
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						orderId: this.baseInfo.id
					},
					success: res => {
						if (res.data) {
							this.contractInfo = res.data;
						}

					}
				})
			},
			valChange(e) {
				console.log('当前值为: ' + e.value)
			},
			showFadada() {
				this.http({
					url: 'viewContractPdfUrl',
					method: 'GET',
					data: {
						contractId: this.contractInfo.no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let info = res.data;
							uni.showModal({
								content: info, //模板中提示的内容
								confirmText: '复制链接',
								success: (res) => { //点击复制内容的后调函数
									if (res.confirm) {
										let result
										// #ifndef H5
										//uni.setClipboardData方法就是讲内容复制到粘贴板
										uni.setClipboardData({
											data: info, //要被复制的内容
											success: () => { //复制成功的回调函数
												uni.showToast({ //提示
													title: '复制成功'
												})
											}
										});
										// #endif

										// #ifdef H5
										let textarea = document.createElement("textarea")
										textarea.value = info
										textarea.readOnly = "readOnly"
										document.body.appendChild(textarea)
										textarea.select() // 选中文本内容
										textarea.setSelectionRange(0, info.length)
										uni.showToast({ //提示
											title: '复制成功'
										})
										result = document.execCommand("copy")
										textarea.remove()
										// #endif
									} else {
										console.log('取消')
									}
								}
							});
						} else {
							uni.showToast({
								title: res.msg
							})
						}

					}

				})
			},
			// 弹窗确认按钮--改价
			confirm1() {
				if (!this.changeAmount) {
					return uni.showToast({
						title: '请输入金额',
						icon: 'none'
					})
				}
				let orderDto = {
					billNo: this.billNo,
					realTotalAmount: this.changeAmount,
					oldRealTotalAmount: this.baseInfo.realTotalAmount,
					operator: uni.getStorageSync('employeeName'),
					operatorId: uni.getStorageSync("employeeId"),
				};
				this.http({
					url: 'changeRealTotalAmount',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: orderDto,
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '修改成功'
								})
							}, 1500)
							this.getorderInfo();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}

					}
				})
				this.showModal1 = false
			},
			splitOrder() {
				this.moreOperate = false
				if (this.baseInfo.totalSplitNum == null || ((this.baseInfo.totalSplitNum - this.baseInfo.splitNum) < 1)) {
					return uni.showToast({
						title: '没有拆单次数',
						icon: 'none'
					})
				}
				if (this.baseInfo.realTotalAmount - this.baseInfo.amount > 0) {
					return uni.showToast({
						title: '该订单还未付款',
						icon: 'none'
					})
				}
				let splitOrder = {
					billNo: this.billNo
				};
				this.http({
					url: 'splitOrder',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: splitOrder,
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '拆单成功'
								})
							}, 1500)
							this.getorderInfo();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}

					}
				})
			},
			// 打开图片浏览
			openImgPreview(index) {
				let imgs = this.commentData.serviceAcceptanceFormImgList.map(item => {
					return item.imgUrl
				})
				uni.previewImage({
					urls: imgs,
					current: index
				})
			},
			copy(info) {

				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif

				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
		},
	}
</script>

<style lang="scss">
	.page {
		min-height: 100vh;
		background-color: #f4f2f3 !important;
	}

	.main {
		display: flex;
		justify-content: center;
		align-items: center;
		border-top: 2rpx solid #eee;
		border-bottom: 2rpx solid #eee;
	}

	.part1-title {
		display: flex;
		align-items: center;
		// margin: 10rpx auto;
	}

	.splitCon {
		background-color: #f4f3f2;
		text-indent: 0.5rem;
		border-bottom: 2rpx solid #fff;
	}

	.logCon {
		background: #f4f3f2;
		text-indent: 2rem;
		border-bottom: 2rpx solid #fff;
		padding: 20rpx 0;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
	}

	/deep/ .u-button {
		border-radius: 0 !important;
	}

	.iconStyle {
		background-color: #f3efeb;
		color: #8f755d;
		padding: 0 10rpx;
		border-radius: 4rpx;
	}

	.textStyle {
		margin: 20rpx auto;
	}

	.imgBox {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.imgStyle {
		width: 30%;
		margin: 10rpx;
		border-radius: 10rpx;
	}
</style>