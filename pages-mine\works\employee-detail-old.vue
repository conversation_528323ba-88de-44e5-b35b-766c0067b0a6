<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 海报分享 -->
		<u-popup :show="popupSharePost" mode="center" @close="popupSharePost = false">
			<view @click="saveToPhone()">
				<img :src="postShareImg" class="post-img" mode="widthFix" />
			</view>
		</u-popup>

		<!-- 分享方式选择 -->
		<u-popup :show="popupShareWay" mode="bottom" @close="popupShareWay = false">
			<view class="filter-title">
				<text>简历分享</text>
			</view>
			<view style="display: flex;flex-direction: column;">
				<view class="btn-share">
					<button plain="true" @click="openShare(0)" open-type="share">链接分享</button>
				</view>
				<view class="btn-share">
					<button plain="true" @click="openShare(1)">生成海报</button>
				</view>
			</view>
			<view class="btn-bottom-center" @click="popupShareWay = false">
				<button>取消</button>
			</view>
		</u-popup>

		<!-- 头像与照片 -->
		<!-- <img class="imgBg" :src="baomu.headPortrait||blankImg" @click="openImgPreview(0,baomu.headPortrait,0)"
			v-if="!photeArray.length" mode="widthFix" />
		<view class="wrap" v-if="photeArray.length">
			<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
				:duration="duration" :current="current">
				<swiper-item v-for="(item,index) in photeArray" :key="index" @click="openImgPreview1(index,photeArray)">
					<img :src="item" class="imgBg" mode="widthFix" />
				</swiper-item>
			</swiper>
		</view>
		<view class="w9 mg-at flac-row imgBox" v-if="photeArray.length">
			<img v-for="(item,index) in photeArray" :key="index" :src="item" @click="changeImg(index)"
				v-if="index<maxShowPhoto" />
			<view class="BtnBox flac-col" @click="openImgPreview1(maxShowPhoto,photeArray)"
				v-if="photeArray.length>maxShowPhoto">
				<text>更多</text>
				<text>({{photeArray.length-maxShowPhoto}})</text>
			</view>
		</view> -->

		<!-- 基本信息 -->
		<view class="boxStyle">
			<view class="infoBox">
				<view class="w9 flac-row" style="margin: 0 5%;">
					<view style="width: 25%;">
						<img style="display: block;width: 155rpx;height: 180rpx;border-radius: 20rpx;"
							:src="baomu.headPortrait||blankImg" @click="openImgPreview(0,baomu.headPortrait,0)" />
					</view>
					<view class="flex-col" style="width: 75%">
						<view class="flac-row" @click="saveText(0)">
							<text class="f20 fb">{{formatRealName(baomu.realName,employeeInfo.sex)}}</text>
							<text class="tagRed">
								{{baomu.workYear&&baomu.workYear!=0?baomu.workYear+"年经验":"暂无经验"}}
							</text>
							<text class="tagRed">
								{{formatSite(0)}}
							</text>
						</view>
						<view class="flac-row" style="margin: 10px 0;">
							<text class="tagIcon" v-if="hasLevel(baomu.levelId)"
								@click="openReport">{{formatLevel(baomu.levelId|| 0)}}
							</text>
							<text style="color:#909399" @click="saveText(1)">{{employee.no}}</text>
						</view>
						<view class="flac-row fb">
							<view v-if="baomuInfo.zodiac" style="margin-right: 15rpx;">属{{baomuInfo.zodiac}}</view>
							<view v-if="baomu.age" style="margin-right: 15rpx;">{{baomu.age?(baomu.age+'岁'):''}}</view>
							<view v-if="baomuInfo.constellation" style="margin-right: 15rpx;">
								{{baomuInfo.constellation||''}}
							</view>
							<view v-if="employeeInfo.nation" style="margin-right: 15rpx;">
								{{formatNation(employeeInfo.nation||'')}}
							</view>
							<view v-if="baomuDetail.education" style="margin-right: 15rpx;">
								{{baomuDetail.education||''}}
							</view>
						</view>
					</view>
				</view>
				<img class="w9 mg-at"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/line.png"
					mode="widthFix" />
				<view class="w9 mg-at flac-row-a">
					<view class="tagBlue f12" v-for="(item,index) in advantagesList" :key="index"
						v-if="item.isCheck==1">{{item.text}}</view>
				</view>
			</view>
			<view class="w9 mg-at">
				<!-- 求职意向 -->
				<!-- 	<view class="w9 f18 fb lh50">求职意向</view>
				<view class="bacf radius5" style="padding: 10px;">
					<view class="" style="align-items: unset;">
						<view class="flac-row lh30">
							<img style="width: 12px;margin-left:5px;"
								src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon21.png"
								mode="widthFix" />
							<view class="t-indent fb">工作类型</view>
						</view>
						<view class="w85 mg-at c6" style="margin: 5px auto 10px;">{{baomu.workType||'暂无'}}</view>
					</view>
					<view class="" style="align-items: unset;">
						<view class="flac-row lh30">
							<img style="width: 12px;margin-left:5px;"
								src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon21.png"
								mode="widthFix" />
							<view class="t-indent fb">工作内容</view>
						</view>
						<view class="w85 mg-at c6" style="margin: 5px auto 10px;">{{baomuInfo.serverContent||'暂无'}}
						</view>
					</view>
					<view class="" style="align-items: unset;">
						<view class="flac-row lh30">
							<img style="width: 12px;margin-left:5px;"
								src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon21.png"
								mode="widthFix" />
							<view class="t-indent fb">工作地点</view>
						</view>
						<view class="w85 mg-at c6" style="margin: 5px auto 10px;">
							{{baomuDetail.siteName||'本地'}}{{ baomuExpectedWork.expectedAddress?'/'+baomuExpectedWork.expectedAddress:'' }}</text>
						</view>
					</view>
				</view> -->


				<view class="w9 f18 fb lh50" v-if="introduce||baomu.introduceRemark">自我介绍</view>
				<view class="bacf radius5" style="padding: 10px;">
					<view class="w9 mg-atv flac-row"
						style="background-color: #eaeaf4;align-items: unset;padding: 15px;border-radius: 5px;"
						v-if="introduce||baomu.introduceRemark">
						<img style="width: 35rpx;height: 30rpx;margin-right: 10px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon1.png" />
						<view class="">
							<view v-if="introduce">
								<text class="fb" v-if="baomu.introduceRemark">自我介绍：</text>
								<text>{{introduce||''}}</text>
							</view>
							<view v-if="baomu.introduceRemark">
								<text class="fb">推荐理由：</text>
								<text>{{baomu.introduceRemark||''}}</text>
							</view>
						</view>
					</view>

					<scroll-view class="w10 mg-at" :scroll-x="true" v-if="photeArray.length">
						<view class="flac-row">
							<view class="flex-col-c" style="margin: 40rpx 20rpx 40rpx 0rpx;;"
								v-for="(item,index) in photeArray" :key="index"
								@click="openImgPreview1(index,photeArray)">
								<img style="width: 180rpx;height: 180rpx;border-radius: 10rpx;" :src="item" />
							</view>
						</view>
					</scroll-view>
				</view>

				<view class="w9 f18 fb lh50" v-if="baomuInfo.languages||baomuInfo.cooking||highlightSkills">基本信息</view>
				<view class="bacf radius5" style="padding: 10px;"
					v-if="baomuInfo.languages||baomuInfo.cooking||highlightSkills">
					<u-empty v-if="!baomuInfo.languages&&!baomuInfo.cooking&&!highlightSkills" text="暂未填写"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
					<view class="lh35 f15 fb" v-if="baomuDetail.married">
						<text>婚姻情况:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{baomuDetail.married}}
						</text>
					</view>
					<view class="lh35 f15 fb" v-if="(familyMemberText&&familyMemberText!='无')||employeeInfo.family">
						<text>家庭情况:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{employeeInfo.family||familyMemberText}}
						</text>
					</view>
					<view class="lh35 f15 fb" v-if="baomuInfo.languages">
						<text>语言能力:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{baomuInfo.languages}}
						</text>
					</view>
					<view class="lh35 f15 fb" v-if="formatSite(1)">
						<text>现住地址:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{formatSite(1)}}
						</text>
					</view>
					<!-- 			<view class="flac-row lh35 f15 fb" v-if="employeeInfo.height">身高:<view class="fb7 c6 t-indent">
							{{employeeInfo.height}}cm
						</view>
					</view>
					<view class="flac-row lh35 f15 fb" v-if="employeeInfo.weight">体重:<view class="fb7 c6 t-indent">
							{{employeeInfo.weight}}kg
						</view>
					</view> -->

					<!-- <view class="lh35 f15 fb" v-if="baomuInfo.cooking">
						<text>烹饪能力:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{baomuInfo.cooking}}
						</text>
					</view>
									<view class=" lh35 f15 fb" v-if="baomuInfo.serverContent">
						<text>擅长工作:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{baomuInfo.serverContent}}
						</text>
					</view>
					<view class="lh35 f15 fb" v-if="highlightSkills">
						<text>突出技能:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{highlightSkills}}
						</text>
					</view> -->
				</view>

				<!-- <view class="w9 f18 fb lh50">家庭情况</view>
				<view class="bacf radius5" style="padding: 10px;">
					<view class="lh35 f15 fb" v-if="formatSite(1)">
						<text>住址:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{formatSite(1)}}
						</text>
					</view>
					<view class="flac-row lh35 f15 fb" v-if="baomuDetail.married">婚姻:<view class="fb7 c6 t-indent">
							{{baomuDetail.married}}
						</view>
					</view>
					<view class="f16 fb lh30" v-if="(familyMemberText&&familyMemberText!='无')||employeeInfo.family">
					</view>
					<view class="flac-row lh35 f15 fb" v-if="familyMemberText&&familyMemberText!='无'">成员:
						<view class="fb7 c6 t-indent">
							{{familyMemberText}}
						</view>
					</view>
					<view class="lh35 f15 fb" v-if="employeeInfo.family">
						<text>具体情况:</text>
						<text class="fb7 c6 t-indent" style="margin-left: 15rpx;">
							{{employeeInfo.family}}
						</text>
					</view>
				</view> -->

				<!-- 亲友推荐 -->
				<view class="w9 f18 fb lh50" v-if="recommendList.length">亲友推荐</view>
				<view class="bacf radius5" style="padding: 10px;" v-if="recommendList.length">
					<view class="flac-row" style="align-items: unset;" v-for="(item,index) in recommendList"
						:key="index" v-if="index<maxEx||showAllEx">
						<view class="flex-col-c lh30">
							<uni-icons type="smallcircle-filled" color="#5254aa" size="12"></uni-icons>
							<view class="lineY"></view>
						</view>
						<view class="w10">
							<view class="flac-row lh30">
								<view class="t-indent fb">{{familyList[item.recommender]}}推荐</view>
							</view>
							<view class="w88 mg-at c6" style="margin: 5px auto 15px;">{{item.recommendContent}}</view>
						</view>
					</view>
					<view class="w8 mg-at c6 flac-row" @click="showAllEx = !showAllEx"
						v-if="(recommendList.length||0)>maxEx">
						{{showAllEx?'收起推荐':'查看更多'}}
						<img style="width: 12px;margin-left:5px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icondown.png"
							mode="widthFix"></img>
					</view>
				</view>

				<!-- 工作经历 -->
				<view class="w9 f18 fb lh50" v-if="baomuWorkExperienceList.length">工作经历</view>
				<view class="bacf radius5" style="padding: 10px;" v-if="baomuWorkExperienceList.length">
					<view class="flac-row" style="align-items: unset;" v-for="(item,index) in baomuWorkExperienceList"
						:key="index" v-if="index<maxEx||showAllEx">
						<view class="flex-col-c lh30">
							<uni-icons type="smallcircle-filled" color="#5254aa" size="12"></uni-icons>
							<view class="lineY"></view>
						</view>
						<view class="w10">
							<view class="flac-row lh30">
								<view class="t-indent fb">{{formatExWorkType(item.workType)}}</view>
								<view class="t-indent2" v-if="item.startWorkTime">
									{{formatDate(item.startWorkTime)}}-{{formatDate(item.endWorkTime)}}
								</view>
							</view>
							<view class="w88 mg-at c6" style="margin: 5px auto 15px;">{{item.workContent}}</view>
						</view>
					</view>
					<view class="w8 mg-at c6 flac-row" @click="showAllEx = !showAllEx"
						v-if="(baomuWorkExperienceList.length||0)>maxEx">
						{{showAllEx?'收起经历':'查看更多'}}
						<img style="width: 12px;margin-left:5px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icondown.png"
							mode="widthFix"></img>
					</view>
					<!-- 					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png"
						v-if="!baomuWorkExperienceList.length" /> -->
				</view>

				<!-- 接单履历 -->
				<!-- 	<view class="w9 f18 fb lh50" v-if="employeeContractList.length">平台接单履历</view>
				<view class="bacf radius5" style="padding: 10px;" v-if="employeeContractList.length">
					<view class="flac-row" style="align-items: unset;" v-for="(item,index) in employeeContractList"
						:key="index" v-if="index<maxEx||showAllEx">
						<view class="flex-col-c lh30">
							<uni-icons type="smallcircle-filled" color="#5254aa" size="12"></uni-icons>
							<view class="lineY"></view>
						</view>
						<view class="w10">
							<view class="flac-row-b lh30">
								<view class="flac-row">
									<view class="t-indent fb ">{{item.productName}}</view>
									<view class="t-indent2">
										{{formatDate(item.serviceStarDate)}}
									</view>
								</view>
								<view>
									单号：{{formatBillNo(item.billNo)}}
								</view>
							</view>
							<view class="w88 mg-at c6" style="margin: 5px auto 15px;">{{formatContractContent(index)}}
							</view>
						</view>
					</view>
					<view class="w8 mg-at c6 flac-row" @click="showAllEx = !showAllEx"
						v-if="(employeeContractList.length||0)>maxEx">
						{{showAllEx?'收起经历':'查看更多'}}
						<img style="width: 12px;margin-left:5px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icondown.png"
							mode="widthFix"></img>
					</view>
					<view class="tips lh20">
						<uni-icons type="info" size="20" color="#E3693C"></uni-icons>
						温馨提醒：阿姨履约平台发单历史记录，不作为阿姨能力评估标准，该记录受阿姨入驻平台时间、个人接单意愿影响，接单率越高，阿姨对平台认可度越高。
					</view>
				</view> -->

				<!-- 培训经历 -->
				<view class="w9 f18 fb lh50" v-if="trainList.length">培训经历</view>
				<view class="bacf radius5" style="padding: 10px;" v-if="trainList.length">
					<view class="flac-row" style="align-items: unset;" v-for="(item,index) in trainList" :key="index"
						v-if="index<maxEx||showAllEx">
						<view class="flex-col-c lh30">
							<uni-icons type="smallcircle-filled" color="#5254aa" size="12"></uni-icons>
							<view class="lineY"></view>
						</view>
						<view class="w10">
							<view class="flac-row lh30">
								<view class="t-indent fb">{{item.type==0?'打卡':'培训'}}</view>
								<view class="t-indent2" v-if="item.date">
									{{formatDate(item.date)}}
								</view>
							</view>
							<view class="w88 mg-at c6" style="margin: 5px auto 15px;">
								<text>{{formatTrain(index)}}</text>
								<text :style="item.level=='良好'?'color: #F9AE3D;':'color: #19be6b;'"
									style="margin-left: 10rpx;">{{item.level || ''}}</text>
							</view>
						</view>
					</view>
					<view class="w8 mg-at c6 flac-row" @click="showAllEx = !showAllEx"
						v-if="(trainList.length||0)>maxEx">
						{{showAllEx?'收起经历':'查看更多'}}
						<img style="width: 12px;margin-left:5px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icondown.png"
							mode="widthFix"></img>
					</view>
				</view>

				<!-- 服务能力 -->
				<view class="w9 f18 fb lh50" v-if="employeeSkill.length">服务能力</view>
				<view class="bacf radius5" style="padding: 10px;" v-if="employeeSkill.length">
					<view class="" style="align-items: unset;" v-for="(item,index) in workCodeList" :key="index"
						v-if="item.count!=0 && employeeSkill.length!=0&&item.key!='Q'">
						<view class="flac-row lh30">
							<img style="width: 12px;margin-left:5px;"
								src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon21.png"
								mode="widthFix" />
							<view class="t-indent fb">{{item.name}}</view>
						</view>
						<view class="w85 mg-at c6" style="margin: 5px auto 10px;">
							<!-- <text v-for="(item1, index1) in employeeSkill" :key="index1"
								v-if="item1.workCode==item.key&&item1.remark">{{item1.remark}}，
							</text> -->
							<text>{{formatSkill(item)}}</text>
						</view>
					</view>
				</view>


				<!-- 				<view class="w9 f18 fb lh50">客户评价</view>
				<view class="bacf radius5" style="padding: 10px;">
					<view class="flac-row-b" style="flex-wrap: wrap;">
						<view class="tagBlue text-c f12" style="margin: 5px;border-radius: 10px;"
							v-for="(item,i) in appraiselist" :key="i">{{item}}</view>
					</view>
				</view> -->

				<!-- 证件信息 -->
				<view class="w9 f18 fb lh50" v-if="uploadCertCount != 0">相关证件</view>
				<view class="bacf radius5 flac-row-b" style="align-items: unset;padding: 0 10px;">
					<scroll-view class="w9 mg-at" id='domSticky' :scroll-x="true" v-if="uploadCertCount != 0">
						<view class="flac-row">
							<view class="flex-col-c" style="margin: 40rpx 20rpx;position: relative;"
								v-for="(item,index) in otherCertificateList" :key="index" v-if="item.isUpload"
								@click="openEncryptionCert(item)">
								<image style="width: 250rpx;" mode="widthFix" :src="item.iconImg" />
								<view class="f12 text-c" style="width: 250rpx;position: absolute;bottom: 8%;left:0">
									{{item.tabTitle}}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 服务保障 -->
				<view class="w9 f18 fb lh50">服务保障</view>
				<view class="bacf radius5 flac-row-b" style="align-items: unset;padding: 0 10px;">
					<view class="w10" style="padding: 10px 0;">
						<view class="" style="align-items: unset;">
							<view class="flac-row lh30">
								<image style="width: 15px;margin-left:5px;"
									src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon31.png"
									mode="widthFix"></image>
								<view class="t-indent fb">中国人寿保险承保</view>
							</view>
							<view class="w85 mg-at">
								<view class="">1、人身意外险 最高赔付50万元</view>
								<view class="">2、家政责任险 最高赔付1万元</view>
							</view>
						</view>
						<view class="" style="align-items: unset;">
							<view class="flac-row lh50">
								<image style="width: 15px;margin-left:5px;"
									src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon31.png"
									mode="widthFix"></image>
								<view class="t-indent fb">健康认证核实</view>
							</view>
						</view>
					</view>
					<image class="w4"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagicon41.png"
						mode="widthFix"></image>
				</view>

				<!-- 服务流程 -->
				<view class="w9 f18 fb lh50">服务流程</view>
				<view class="bacf radius5 text-c" style="padding: 10px;">
					<image class="w9"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img8.png"
						mode="widthFix"></image>
				</view>

				<!-- 聊一聊 -->
				<view class="w9 flac-row-b" style="margin: 20px auto;">
					<image style="width: 45px;margin-right: 10px;"
						:src="isInterested?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icon71_fill.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icon71.png'"
						mode="widthFix" @click="interested"></image>
					<view class="w10 mg-at flac-row-c btn" @click="openChat()">
						<image style="width: 20px;"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/icon81.png"
							mode="widthFix"></image>
						<view class="t-indent">聊一聊</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 分享按钮 -->
		<view style="position: fixed;right: 36rpx;bottom: 19%;">
			<img style="width: 80rpx;height: 80rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/iconshare.png"
				@click="popupShareWay=true" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imglist: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img11.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img21.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img31.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img41.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img51.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/img61.png',
				],
				taglist: ['干活利索', '待人亲切', '手脚勤快', '细心认真', '喜欢学习'],
				worklist: [{
					title: '保姆',
					date: '2023.08.28-2024.03.17',
					content: '在恒大御龙天峰当白班育儿嫂，带九个月女宝'
				}, {
					title: '保姆',
					date: '2023.08.28-2024.03.17',
					content: '在恒大御龙天峰当白班育儿嫂，带九个月女宝'
				}, {
					title: '保姆',
					date: '2023.08.28-2024.03.17',
					content: '在恒大御龙天峰当白班育儿嫂，带九个月女宝'
				}],
				servelist: [{
					title: '做饭',
					content: '掌握菜肴，家常菜'
				}, {
					title: '做家务',
					content: '卫生打扫清洁，会使用双面擦，厨房卫生整理，家务卫生。'
				}, {
					title: '洗涤',
					content: '手洗衣服，分类机洗'
				}, {
					title: '照顾小孩',
					content: '协助幼儿用餐，抚触，教小孩认字，幼儿护理，接送小孩，帮助小孩洗澡，育婴师相关技能'
				}],
				appraiselist: ['责任心强（2）', '相处愉快（2）', '服务态度好（2）', '责任心强（2）', '经验丰富（2）', '服务态度好（2）'],


				// 设置项
				// 展示所有照片
				showAllPhoto: false,
				showAllPhoto1: false,
				// 展示所有工作经验
				showAllEx: false,
				// 最多展示照片数
				maxShowPhoto: 6,
				// 最多展示工作经验数
				maxEx: 2,
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				choiceIndex: 0,

				recommendedList: [], //推荐阿姨列表
				indicatorDots: false,
				autoplay: true,
				interval: 4500,
				duration: 1000,
				show: false,
				showPopup: false,
				popupShareWay: false,
				popupSharePost: false,
				needId: 0,
				roleId: uni.getStorageSync("roleId") || 0,
				isRole: false,
				isAdmin: false,
				isAccordAuth: false,
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				baomuId: null,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankHeadPortrait: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_head_portrait.png",
				baomuDetail: {},
				employee: {},
				employeeInfo: {},
				baomuInfo: {},
				baomuExpectedWork: {
					workModel: null,
					elderly: null,
					watchBaby: null,
					cookingSkills: null,
					otherSkills: null,
					salaryExpectation: null,
					siteId: null
				},
				baomuWorkExperienceList: [],
				abilityTabList: [],
				abilityCount: 0,
				baomu: {
					realName: ''
				},
				level: 0,
				levelName: '',
				updaterNo: '',
				updaterName: '',
				updateTime: '',
				salary: '',
				employeeSkill: [],
				familyMemberList: [{
					value: 0,
					text: '无',
				}, {
					value: 1,
					text: '父母',
				}, {
					value: 2,
					text: '小孩',
				}, {
					value: 3,
					text: '配偶',
				}],
				familyMemberText: '',
				workTypeList: [{
						urgentType: '保姆',
						value: 10
					},
					{
						urgentType: '月嫂',
						value: 20
					},
					{
						urgentType: '育儿嫂',
						value: 30
					},
					{
						urgentType: '护工',
						value: 40
					},
					{
						urgentType: '保洁师',

						value: 50
					},
					{
						urgentType: '搬家师',
						value: 60
					},
					{
						urgentType: '清洗师',
						value: 70
					},
					{
						urgentType: '维修师',
						value: 80
					},
					{
						urgentType: '疏通师',
						value: 90
					},
					{
						urgentType: '其他',
						value: 100
					}
				],
				workCodeList: [{
					key: 'Q',
					name: "突出技能",
					count: 0
				}, {
					key: 'B',
					name: "做饭",
					count: 0
				}, {
					key: 'C',
					name: "做家务",
					count: 0
				}, {
					key: 'A',
					name: "洗涤",
					count: 0
				}, {
					key: 'D',
					name: "照顾小孩",
					count: 0
				}, {
					key: 'E',
					name: "照顾老人",
					count: 0
				}, {
					key: 'F',
					name: "宠物照料",
					count: 0
				}, {
					key: 'O',
					name: "其他",
					count: 0
				}],
				siteFlagList: [{
					index: 0,
					value: null,
					showText: "不限",
					show: true
				}, {
					index: 1,
					value: -1,
					showText: "本地",
					show: true
				}, {
					index: 2,
					value: 1,
					showText: "省内",
					show: false
				}, {
					index: 3,
					value: 2,
					showText: "省外",
					show: false
				}, {
					index: 4,
					value: 3,
					showText: "全国",
					show: true
				}, {
					index: 5,
					value: -1,
					showText: "非全国",
					show: false
				}],
				scrollTop: 0,
				scrollLeft: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#fff',
					},
					customStyle: {
						backgroundColor: '#5354A5',
					}
				},
				searchParam: [
					// "保姆",
					"保姆能力",
					"住家保姆能力",
					"不住家保姆能力",
					"单餐保姆能力",
					"保姆其它技能",
					"月嫂能力",
					"育儿嫂能力",
					"护工能力",
					"保洁能力",
					"搬家能力",
					"维修清洗能力",
					"疏通能力",
					"陪读师能力",
					"陪诊师能力",
					"整理师能力",
					"装修师能力",
					"豪宅管家能力",
				],
				serverContentList: [{
						text: "带宝宝（带睡)",
						showText: "宝宝带睡",
						isCheck: 0
					},
					{
						text: "带宝宝（不带睡)",
						showText: "宝宝不带睡",
						isCheck: 0
					},
					{
						text: "照顾产妇",
						showText: "照顾产妇",
						isCheck: 0
					},
					{
						text: "看护病人",
						showText: "看护病人",
						isCheck: 0
					},
					{
						text: "看护老人",
						showText: "看护老人",
						isCheck: 0
					},
					{
						text: "做饭",
						showText: "做饭",
						isCheck: 0
					},
					{
						text: "纯做饭",
						showText: "纯做饭",
						isCheck: 0
					},
					{
						text: "做卫生",
						showText: "做卫生",
						isCheck: 0
					},
					{
						text: "纯做卫生",
						showText: "纯做卫生",
						isCheck: 0
					}
				],
				uploadCertCount: 0,
				introduce: '',
				advantagesList: [],
				certificateList: [],
				otherCertificateList: [{
						tabTitle: '身份证',
						tabTips: '',
						validity: null,
						value: 1,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert.png',
						id: null,
						imgIdList: [],
						imgList: []
					},
					// {
					// 	tabTitle: '身份证背面',
					// 	tabTips: '',
					// 	validity: null,
					// 	value: 2,
					// 	isUpload: false,
					// 	img: '',
					// 	iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert.png',
					// },
					{
						tabTitle: '健康证',
						tabTips: '',
						validity: null,
						value: 3,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '体检表',
						tabTips: '',
						validity: null,
						value: 8,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '技能证书',
						tabTips: '',
						validity: null,
						value: 23,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '月嫂证',
						tabTips: '',
						validity: null,
						value: 4,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '育婴师证',
						tabTips: '',
						validity: null,
						value: 6,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '催乳师证',
						tabTips: '',
						validity: null,
						value: 7,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '护工证',
						tabTips: '',
						validity: null,
						value: 9,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '家政员证',
						tabTips: '',
						validity: null,
						value: 10,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '毕业证',
						tabTips: '',
						validity: null,
						value: 20,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '教师证',
						tabTips: '',
						validity: null,
						value: 21,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}, {
						tabTitle: '驾驶证',
						tabTips: '',
						validity: null,
						value: 22,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '赛事证书',
						tabTips: '',
						validity: null,
						value: 24,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					},
					{
						tabTitle: '其它证件',
						tabTips: '',
						validity: null,
						value: 99,
						isUpload: false,
						img: '',
						iconImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/cert1.png',
						imgIdList: [],
						imgList: []
					}
				],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
				certificateType: 25,
				photeList: [],
				photeList1: [],
				photeArray: [],
				photeArray1: [],
				evaluateList: [],
				trainList: [],
				employeeContractList: [],
				recommendList: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				tabsList: [{
					name: '介绍'
				}, {
					name: '履历'
				}, {
					name: '技能'
				}, {
					name: '培训'
				}, {
					name: '推荐'
				}],
				safeList: [{
					safeTitle: '中国人寿保险承保',
					textList: ['人身意外险 最高赔付50万元', '家政责任险 最高赔付1万元']
				}, {
					safeTitle: '健康认证核实',
				}],
				shareType: 0,
				postShareImg: '',
				shareImg: '',
				shareImg1: '',
				shareContent: {
					title: '',
					path: '',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: ''
				},
				shareErr: true,
				isLogin: false,
				pages: 0,
				searchCondition: {
					workType: '',
					shimingState: 1,
					state: 1,
					isAppraisal: 1,
					isPush: 1,
					orderBy: "putTime DESC,createDate DESC",
					current: 1,
					size: 4
				},
				isInterested: false,
				interestedList: [], // 感兴趣列表
				current: 0,
				familyList: ['配偶', '子女', '父母', '朋友', '亲戚', '雇主'],
				highlightSkills: ''
			}
		},
		methods: {
			changeImg(index) {
				this.current = index
			},
			// 选择菜单
			choiceMenu(e) {
				this.choiceIndex = e.index
				this.getDomData(this.choiceIndex)
			},
			getDomData(e) {
				uni.pageScrollTo({
					selector: '#dom' + (e + 1), // string 选择器
					duration: 500,
				})
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			saveText(value) {
				let text = ''
				let name = ''
				let nameData = ['员工姓名', '员工工号', '联系方式', '现居地址', '分享链接']
				switch (value) {
					case 0:
						text = this.employee.realName
						break
					case 1:
						text = this.employee.no
						break
					case 2:
						text = this.employee.phone
						break
					case 3:
						text = this.baomu.address
						break
					case 4:
						text = 'https://jiajie.xiaoyujia.com/pages-mine/works/employee-detail?baomuId=' + this.baomuId
						break
				}
				name = nameData[value]
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success(name + '复制成功!')
					}
				})
			},
			// 检查员工权限
			checkEmployeePower() {
				let result = false
				// 聊一聊可拨打员工经纪人电话
				let roleList = [1, 42, 66, 77, 95, 106]
				roleList.forEach(item => {
					if (item == this.roleId) {
						result = true
					}
				})
				this.isRole = result

				// 校验员工是否符合管理权限
				this.http({
					url: "checkEmployeePower",
					data: {
						id: uni.getStorageSync("employeeId"),
						baomuId: this.baomuId
					},
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.isAdmin = true
								this.checkIsAccordAuth()
							}
						}
					}
				})
			},
			// 校验是否符合技能鉴定条件
			checkIsAccordAuth() {
				this.http({
					url: "checkIsAccordAuth",
					path: this.baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.isAccordAuth = true
							} else {
								this.isAccordAuth = false
							}
						}
					}
				})
			},
			// 打开头像预览
			openImgPreview(value, url, index) {
				let data = []
				data.push(url)
				// 非经纪人不可查看证件图片
				if (value == 1) {
					// 全都不可见
					// this.$refs.uNotify.error('暂无查看权限！')
					// return

					// 不可查看身份证
					// if (index == 0) {
					// 	this.$refs.uNotify.error('暂无查看权限！')
					// 	return
					// }

					if (!this.isRole) {
						this.$refs.uNotify.warning('经纪人才可以查看证件图片哦！')
						return
					}

					for (let item of this.certificateList) {
						let img = item.certificateImg
						let type = item.certificateType
						if (img != url && type != null && type != 25 && type != 98 && type != 99) {
							if (type != 1 && type != 2) {
								data.push(img)
							} else if (index == 0) {
								data.push(img)
							}
						}
					}
				} else if (value == 2) {
					for (let i = 0; i < this.photeList.length; i++) {
						if (i == index) {
							continue
						}
						data.push(this.photeList[i].certificateImg)
					}
				}

				uni.previewImage({
					urls: data,
					current: url
				})
			},
			openImgPreview1(index, list) {
				let url = list[index]
				uni.previewImage({
					urls: list,
					current: url
				})
			},
			showPreviewImage(url, urls) {
				uni.previewImage({
					urls: urls,
					current: url
				})
				this.$refs.uNotify.warning('只有经纪人才可查看证件图片哦，请遵守平台隐私协议，勿对外泄漏员工信息！')
			},
			// 打开加密证件图
			openEncryptionCert(item) {
				let idList = item.imgIdList
				if (item.imgList.length) {
					this.showPreviewImage(item.imgList[0], item.imgList)
					return
				}
				this.http({
					url: "getEncryptionCert",
					data: {
						employeeId: uni.getStorageSync("employeeId"),
						idList: idList
					},
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$set(item, 'imgList', res.data)
							this.showPreviewImage(res.data[0], res.data)
						}
					}
				})
			},
			// 打开详情
			openDetail(index) {
				uni.navigateTo({
					url: "/pages-mine/works/employee-detail?baomuId=" + this.recommendedList[index].baomuId
				})
			},
			// 打开等级报告
			openReport() {
				uni.navigateTo({
					url: "/pages-mine/auth/level-report?baomuId=" + this.baomuId
				})
			},
			openChat() {
				if (!this.isLogin) {
					return this.reLogin()
				}

				if (this.isRole) {
					console.log("开始店长聊一聊")
					let text = "将会联系专属经纪人"
					if (this.baomu.introducerName != null) {
						text = "将会联系专属经纪人：" + this.baomu.introducerName
					}
					this.openCheck(0, "确定拨打电话吗", text)
				} else {
					// #ifdef  MP-WEIXIN
					console.log("开始微信聊一聊")
					wx.openCustomerServiceChat({
						extInfo: {
							url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741' //客服地址链接
						},
						corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
						success(res) {
							console.log(res, 1)
						},
						fail(res) {
							console.log(res, 2)
						},
					})
					// #endif
					// #ifdef  APP-PLUS || H5
					console.log("开始H5聊一聊")
					let param = {
						url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741'
					}
					let data = JSON.stringify(param)
					uni.navigateTo({
						url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
					})
					// #endif
				}
			},
			// 打开管理页面
			openAdminPage(index) {
				if (index == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.baomuId
					})

				} else if (index == 1) {
					this.checkIsAccordAuth()

					let timer = setTimeout(() => {
						if (!this.isAccordAuth) {
							this.openCheck(-1, "员工暂不符合鉴定条件", "请帮助Ta完善简历吧！")
							return
						}
						uni.navigateTo({
							url: "/pages-other/employee/workSkill?baomuId=" + this.baomuId
						})
					}, 600);
				}
			},
			openShareTips() {
				this.openCheck(-1, "敏感信息分享后自动隐藏", "客户将无法查阅诸如员工真实姓名、联系方式、最低薪资、备注和证件等敏感信息，也无法查看任何操作按钮")
			},
			// 获取开发人手机号
			getIntroducerPhone() {
				let that = this
				this.http({
					url: "getIntroducerPhone",
					path: this.baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							let phone = res.data
							console.log("开发人的隐私号为：" + phone)
							// 打开拨号键盘
							uni.makePhoneCall({
								phoneNumber: phone,
								success: function(e) {
									that.addBaomuWorkLog()
								},
								fail: function(e) {
									console.log(e)
								}
							});
						}
					}
				})
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			checkSalary(str) {
				if (str == null || str == "" || str == 0) {
					return "暂无"
				} else {
					return str + "元"
				}
			},
			// 格式化订单号
			formatBillNo(billNo) {
				if (billNo.length < 12) {
					return '****'
				}

				let result = billNo.substring(0, 4) + '****' + billNo.substring(8, 12)
				return result
			},
			// 格式化合同内容
			formatContractContent(index) {
				let item = this.employeeContractList[index]
				let address = item.address ? '在' + item.address.substring(0, 6) + '，' : ''
				let result = '通过家姐联盟平台，' + address +
					'完成了' + item.productName + '订单。' + (item
						.workRequire || '')
				return result
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (!str) {
					return ''
				}
				let result = str.substring(index, index1)
				return result
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '.' + MM + '.' + d
			},
			formatRealName(name, sex) {
				let xin = name.substring(0, 1)
				let data = ['', '师傅', '阿姨', '', '']
				let call = data[sex]
				if (call == '') {
					call = '阿姨'
				}
				return xin + call
			},
			// 格式化民族
			formatNation(nation) {
				if (!nation.includes('族')) {
					nation += '族'
				}
				return nation
			},
			// 格式化证件数据，校验已上传的证件类型
			formatCertificate() {
				for (let item of this.certificateList) {
					let certificateType = item.certificateType
					for (let item1 of this.otherCertificateList) {
						if (item1.value == certificateType) {
							item1.isUpload = true
							item1.id = item.id
							item1.validity = item.validity
							this.$set(item1, 'img', item.certificateImg)
							item1.imgIdList.push(item.id)
							this.uploadCertCount++
						}
					}
				}
			},
			// 格式化服务内容到标签
			formatServerContent(serverContent) {
				let resultList = []
				if (serverContent) {
					for (let item of this.serverContentList) {
						if (serverContent.includes(item.text)) {
							resultList.push(item.showText)
						}
					}
				}
				return resultList
			},
			// 格式化家庭成员信息
			formatFamily() {
				this.familyMemberText = ''
				let familyMember = this.employeeInfo.familyMember || ''
				for (let item of this.familyMemberList) {
					if (familyMember.includes(item.value.toString())) {
						this.familyMemberText += (this.familyMemberText ? ',' + item.text : item.text)
					}
				}
			},
			// 格式化技能项
			formatSkill(item) {
				let str = ''
				this.employeeSkill.forEach(item1 => {
					if (item1.workCode == item.key && item1.remark) {
						str += item1.remark + '，'
					}
				})
				if (str.length) {
					return str.substring(0, str.length - 1) + '。'
				}
				return str
			},
			// 判断是否已经提审
			hasLevel(level) {
				if (level) {
					if (level != 0) {
						return true
					}
				} else {
					return false
				}
			},
			// 格式化保姆等级
			formatLevel(level) {
				let result = ""
				if (level) {
					if (level > 1 && level < 6) {
						result = this.levelList[level - 1].text
					} else {
						result = "暂无"
					}
				}
				return result
			},
			formatSite(value) {
				const maxSize = 16
				let result
				let hometown = this.baomu.hometown || ''
				if (value == 0) {
					let reg =
						/(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)?(?<city>[^市]+自治州|.*?市|.*?市辖区|.*?区|.*?地区|.*?行政单位|.*?盟|.*?县|)?(?<village>.*)/
					let match = hometown.match(reg)
					if (match) {
						let {
							province,
							city
						} = match.groups
						result = province || ''
						result += city && city.length > 2 ? city : ''
						result = result.replace('省', '').replace('市', '').replace('区', '').replace('县', '').replace('镇',
							'')
					} else {
						result = this.formatStr(0, 2, this.baomu.hometown) + ''
					}
				} else {
					// result = '' + this.formatStr(0, 6, this.baomu.address)
					// 显示完整住址
					result = this.baomu.address
				}
				let length = result ? result.length : 0
				result = length > maxSize ? result.substring(0, maxSize) : result
				return result
			},
			formatSiteFlag() {
				let item = this.baomu
				let result = ''
				result = this.siteFlagList[(item.siteFlag || 0) + 1].showText
				if (result == '本地' && item.address) {
					result = this.formatAddress(item.address)
				}
				return result
			},
			// 格式化地址
			formatAddress(str, value) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
					}
				}
				return result
			},
			// 格式化工作类型
			formatWorkType() {
				let workType = this.baomu.workType || ''
				let workTypeList = ['不住家', '住家', '单餐', '育儿嫂', '月嫂', '护工', '陪读师', '管家', '钟点晚餐', '钟点保洁']
				let type = ''
				// 格式化工种
				for (let item of workTypeList) {
					if (workType.includes(item)) {
						type = item
						break
					}
				}
				return type
			},
			// 格式化工作类型
			formatExWorkType(workType) {
				let name = ''
				this.workTypeList.forEach(item => {
					if (item.value == workType) {
						name = item.urgentType
					}
				})
				return name
			},
			// 格式化性格优势
			formatAdvantages(introduce) {
				let choiceCount = 0
				let advantagesIndex = 0
				if (introduce) {
					for (let item of this.advantagesList) {
						if (introduce.includes(item.text) && choiceCount < 4) {
							item.isCheck = 1
							advantagesIndex = advantagesIndex + item.text.length + 1
							choiceCount = choiceCount + 1
						}
					}
					this.introduce = introduce.substr(advantagesIndex)
				}
			},
			// 格式化技能（计算各个类别的数量）
			formatEmployeeSkill() {
				for (let item of this.employeeSkill) {
					for (let item1 of this.workCodeList) {
						if (item.workCode == item1.key) {
							item1.count++
							if (item1.key == 'Q' && item.remark) {
								this.highlightSkills += this.highlightSkills.length ? ',' + item.remark : item.remark
							}
						}

					}
				}
			},
			formatBody() {
				let weight = this.employeeInfo.weight
				let height = this.employeeInfo.height
				if (!weight || !height) {
					return ''
				}
				return ' | 身高:' + height + ' | 体重:' + weight
			},
			// 格式化培训记录
			formatTrain(index) {
				let result = ""
				let date = this.formatDate(this.trainList[index].date)
				let name = this.trainList[index].name
				let type = this.trainList[index].type || 0
				result = "参加「" + name + '」'
				// if (type == 0) {
				// 	result += '打卡'
				// } else if (type == 1) {
				// 	result += '培训'
				// }
				return result
			},
			addBaomuWorkLog() {
				console.log("插入电联开发人日志！")
				this.http({
					url: "addBaomuWorkLog",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomu.id,
						title: "电联",
						workContent: "电话联系开发人",
						crePerson: uni.getStorageSync("employeeName"),
						type: 2
					},
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 获取性格优势字典内容
			getDictionaryByText() {
				this.http({
					url: "getDictionaryByText",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: [
						"性格优势"
					],
					success: res => {
						if (res.code == 0) {
							this.advantagesList = res.data
							this.formatAdvantages(this.baomuInfo.introduce)
						} else {}
					}
				})
			},
			// 获取保姆详细信息
			getBaomuDetail() {
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							let baomuDetail = res.data
							this.baomuDetail = baomuDetail
							this.employee = baomuDetail.employee
							this.employeeInfo = baomuDetail.employeeInfo
							this.baomuInfo = baomuDetail.baomuInfo
							if (baomuDetail.baomuExpectedWork != null) {
								this.baomuExpectedWork = baomuDetail.baomuExpectedWork
							}
							this.baomuWorkExperienceList = baomuDetail.baomuWorkExperience
							this.getDictionaryByText()
							this.formatFamily()
							this.getMyPhotoList(0)
							this.getMyPhotoList(1)
						} else {}
					},
				})
			},
			// 获取个人照片列表
			getMyPhotoList(value) {
				let type = value == 0 ? 25 : 98
				this.http({
					url: 'getCertificateByCertType',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId,
						certificateType: type
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.photeList = res.data
								this.photeArray.push(this.employee.headPortrait)
								this.photeList.forEach(item => {
									this.photeArray.push(item.certificateImg)
								})
								if (this.photeArray.length > 1) {
									this.$delete(this.photeArray, 0)
								}
							} else if (value == 1) {
								this.photeList1 = res.data
								this.photeList1.forEach(item => {
									this.photeArray1.push(item.certificateImg)
								})
							}
						}
					}
				})
			},
			// 获取员工已经上传的证件
			getCertificateByEmployeeId() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getCertByEmployeeId',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								this.certificateList = res.data
								this.formatCertificate()
							} else {
								this.certificateList = []
							}
						},
					});
				}
			},
			// 获取员工培训记录
			getTrainList() {
				this.http({
					url: 'getTrainList',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.trainList = res.data
						} else {
							console.log(+res.msg)
						}
					}
				})
			},
			// 点击感兴趣
			interested() {
				if (this.isInterested) {
					this.$refs.uNotify.warning('已经添加过感兴趣啦！')
					return
				}
				this.openCheck(1, '对该员工很感兴趣', '确定添加为感兴趣吗？将通知该员工经纪人！')
			},
			listInterested() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/order/listInterested',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId || null,
						type: 1,
						interestedEmployeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.interestedList = res.data
							this.isInterested = true
						}
					}
				})
			},
			insertInterested() {
				if (!this.memberId) {
					this.$refs.uNotify.warning('登录后体验更多功能！')
					return
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/order/insertInterested',
					method: 'POST',
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId || null,
						type: 1,
						interestedEmployeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('已添加至感兴趣！')
							this.isInterested = true
						}
					}
				})
			},
			// 获取签单记录
			listEmployeeContract() {
				this.http({
					url: 'listEmployeeContract',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							this.employeeContractList = res.data
						}
					}
				})
			},
			// 获取亲友推荐
			listEmployeeRecommend() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/listEmployeeRecommend',
					method: 'GET',
					path: this.baomuId || 0,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.recommendList = res.data
						}
					},
				});
			},
			// 获取员工技能鉴定记录
			getEmployeeSkill() {
				this.http({
					url: 'getEmployeeSkill',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.employeeSkill = res.data.employeeSkill
							this.formatEmployeeSkill()
							this.levelName = res.data.levelName
							this.salary = res.data.salary
							this.updaterNo = res.data.updaterNo
							this.updaterName = res.data.updaterName
							this.updateTime = res.data.updateTime

						} else {
							console.log("鉴定记录获取失败！员工之前还未鉴定过！")
						}
					}
				})
			},
			// 获取保姆的技能字典内容
			getBaomuAbility() {
				this.http({
					url: "getBaomuDictionaryById",
					path: this.baomuId || 0,
					hideLoading: true,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.searchParam,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.abilityTabList = res.data
							for (let item of this.abilityTabList) {
								if (item.isCheck == 1) {
									this.abilityCount = 1
									return
								}
							}
						}
					}
				})
			},
			getBaomu(value) {
				let data = {}
				if (value == 0) {
					data = {
						baomuId: this.baomuId,
						current: 1,
						size: 10
					}
				} else {
					data = this.searchCondition
					this.$set(data, "storeId", this.baomu.storeId)
				}
				this.http({
					url: 'getBaomuPage',
					method: 'POST',
					data: data,
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.baomu = res.data.records[0]
								this.searchCondition.workType = this.formatWorkType()
								this.getBaomu(1)
							} else {
								this.recommendedList = this.recommendedList.concat(res.data.records)
								this.pages = res.data.pages
							}
						}
					}
				})
			},
			// 选择分享方式
			openShare(value) {
				this.popupShareWay = false
				this.shareType = value

				this.shareContent.title = this.getShareTitle()
				this.shareContent.path = '/pages-mine/works/employee-detail?baomuId=' + this.baomuId
				if (value == 1) {
					this.openPostShare()
				}

				// #ifdef H5
				if (value == 0) {
					this.saveText(4)
				}
				// #endif
			},
			// 分享海报
			async openPostShare() {
				await this.getWxShareImg()
				this.$refs.uNotify.success("点击海报即可保存！")
				this.popupSharePost = true
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.postShareImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			getWxShareImg() {
				let value = this.shareType
				let name = this.formatRealName(this.baomu.realName, this.employeeInfo.sex || 0)
				let headImg = this.baomu.headPortrait || this.blankHeadPortrait
				let workYear = this.baomu.workYear != null && this.baomu.workYear != 0 ? this.baomu.workYear +
					"年经验" :
					"暂无经验"
				let hometown = this.formatStr(0, 2, this.baomu.hometown) + this.formatStr(3, 5, this.baomu
					.hometown)
				let age = this.baomu.age ? this.baomu.age + '岁' : ''
				let title = hometown + " " + age + " " +
					workYear
				let introducer = this.baomuInfo.introduce || '暂无自我介绍'

				let shareBack =
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/employee_post.png'
				let data = {}

				// 分享类型0：图文分享 1：海报分享 2：文本分享
				if (value == 0) {

				} else if (value == 1) {
					if (this.postShareImg) {
						return
					}

					// 文字大小
					const fontSize = 28
					// 文字间隔
					const fontInterval = 20
					// 行间隔
					const rowInterval = 20
					// 最大文本宽度
					const maxWidth = 360
					// 文本初始x轴坐标
					let originX = 78
					// 文本初始y轴坐标
					let originY = 814

					let textList = [{
							"text": name,
							"fontSize": fontSize,
							"isCenter": false,
							"color": "0x1e1848",
							"isBold": true,
							"x": originX,
							"y": originY
						}, {
							"text": title,
							"fontSize": fontSize,
							"isCenter": false,
							"maxLineCount": 1,
							"color": "0x1e1848",
							"isBold": true,
							"x": originX,
							"y": originY + fontSize + fontInterval + rowInterval
						},
						{
							"text": introducer,
							"fontSize": fontSize,
							"isCenter": false,
							"color": "0x1e1848",
							"isBold": true,
							"x": originX,
							"y": originY + (fontSize + fontInterval + rowInterval) * 2
						},
					]

					data = {
						textList: textList,
						// 二维码
						qrCodeStyle: {
							width: 150,
							height: 150,
							x: 423,
							y: 808
						},
						// 图片合成
						imgList: [{
							url: headImg,
							width: 500,
							height: 720,
							x: 50,
							y: 50
						}],
						maxWidth: maxWidth,
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/employee_post.png",
						path: "pages-mine/works/employee-detail",
						scene: "id/" + this.baomuId,
						source: "xyjacn",
						type: 1
					}
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							let shareImg = res.data
							if (value == 0) {
								// #ifdef H5
								this.postShareImg = shareImg
								// #endif
							} else if (value == 1) {
								this.postShareImg = shareImg
							}
							this.shareErr = false
						} else {
							this.$refs.uNotify.error('分享失败！获取分享图片异常！' + res.msg)
							this.shareErr = true
						}
					}
				})
			},
			// 获取分享标题
			getShareTitle() {
				let title = ''
				let realName = this.formatRealName(this.baomu.realName, this.employeeInfo.sex || 0)
				let workType = this.formatWorkType()
				// 暂时隐藏工作类型
				workType = ''
				let age = this.baomu.age != null ? this.baomu.age + '岁，' : ''
				let hometown = this.formatStr(0, 6, this.baomu.hometown) + '人，'
				let zodiac = '属' + (this.baomuInfo.zodiac || '') + '，'
				let ex = this.baomu.workYear != null && this.baomu.workYear != 0 ? this.baomu.workYear +
					"年经验，" : "暂无经验，"

				title = '【' + workType + realName + '】' + age + hometown + zodiac + ex + '点击查看详情'
				console.log("分享文案", title)
				return title
			},
			// 分享
			onShareAppMessage(res) {
				return new Promise((resolve, reject) => {
					this.shareContent.title = this.getShareTitle()
					if (this.shareContent.imageUrl || this.shareType == 0) {
						this.shareContent.imageUrl = this.baomu.headPortrait
						resolve(this.shareContent)
					} else {
						this.getWxShareImg()
						wx.showLoading({
							title: '正在获取分享内容...',
							icon: 'none'
						})

						setTimeout(() => {
							wx.hideLoading()
							if (!this.shareErr) {
								resolve(this.shareContent)
							} else {
								this.$refs.uNotify.error('分享失败！获取分享图片异常！请重试！')
							}
						}, 1600)
					}
				})
			},
			onShareTimeline(res) {
				let title = this.getShareTitle()
				return {
					title: title,
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			reLogin() {
				this.$toast.toast('您还未进行登录哦，先去登录吧！')
				uni.setStorageSync('redirectUrl', '/pages-mine/works/employee-detail?baomuId=' + this.baomuId)
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					});
				}, 2000);
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.getIntroducerPhone()
				} else if (this.checkType == 1) {
					this.insertInterested()
				}
			},
		},
		onLoad(options) {
			this.memberId = uni.getStorageSync('memberId') || null
			this.isLogin = this.memberId ? true : false
			if (options.baomuId !== undefined) {
				this.baomuId = parseInt(options.baomuId)
				this.show = true
			}

			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.baomuId = obj.id || 0
				this.show = true
			}
			this.checkEmployeePower()
			this.getBaomu(0)
			this.getBaomuDetail()
			this.getCertificateByEmployeeId()
			this.getBaomuAbility()
			this.getEmployeeSkill()
			this.getTrainList()
			this.listEmployeeContract()
			this.listEmployeeRecommend()
			this.listInterested()
			uni.pageScrollTo({
				selector: 'domSticky', // string 选择器
				duration: 500,
			})
		},
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #F2F5F7 !important;
		padding: 0;
	}

	.imgBg {
		display: block;
		width: 100%;
		max-height: 75vh;
		// height: auto;
	}

	.imgBox {
		position: absolute;
		top: 43%;
		right: 5%;

		img {
			display: block;
			width: 90rpx;
			height: 90rpx;
			margin: 0 6rpx;
			border-radius: 20rpx;
		}
	}

	.BtnBox {
		width: 90rpx;
		height: 90rpx;
		line-height: 40rpx;
		padding: 5rpx 0;
		margin: auto 3px;
		border-radius: 5px;
		font-size: 26rpx;
		color: #fff;
		background-color: #47476b;
		text-align: center;
	}

	.boxStyle {
		width: 100%;
		position: absolute;
		// 要显示头部的照片时候就加上top
		// top: 51%;
		top: 0%;
		left: 0;
	}

	.infoBox {
		border-top-right-radius: 10px;
		border-top-left-radius: 10px;
		background-color: #fff;
		align-items: unset;
		padding: 20px 0;
	}

	.tagIcon {
		display: block;
		white-space: nowrap;
		width: 60rpx;
		margin-right: 10rpx;
		padding: 0 20rpx 0 50rpx;
		color: #4E50B0;
		font-weight: bold;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/baomuJL/tagBg.png') no-repeat;
	}

	.tagRed {
		margin: auto 10rpx;
		padding: 0 10rpx;
		color: #EC1313;
		background-color: #f7eceb;
		border-radius: 10rpx;
	}

	.tagBlue {
		margin: 10rpx auto;
		padding: 0 10rpx;
		color: #4E50B0;
		background-color: #ededf6;
		border-radius: 10rpx;
	}

	.lineY {
		width: 2rpx;
		height: 100%;
		background-color: #ccc;
	}

	.btn {
		color: #fff;
		background-color: #2D2FA1;
		border-radius: 40px;
		line-height: 40px;
	}

	.filter-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	.btn-share {
		button {
			border: none;
		}
	}

	button[plain] {
		border: 0
	}

	.btn-bottom-center {
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 30rpx 5%;
			width: 90%;
			height: 70rpx;
			line-height: 70rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 32rpx;
		}
	}

	.post-img {
		display: block;
		width: 600rpx;
		margin: auto;
		height: auto;
	}

	// 头部轮播区域
	.wrap {
		width: 100%;
		height: 60vh;
		margin: auto;
	}

	.swiper {
		width: 100%;
		height: 100%;
		margin: auto;
		background: transparent;
		border: none;
	}

	.tips {
		margin-top: 10rpx;
		color: #E3693C;
		border: 1rpx dashed #E3693C;
		padding: 10rpx;
		border-radius: 10rpx;
	}
</style>