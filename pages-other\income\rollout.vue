<template>
	<view>
		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="w95 mg-at f14 c9 " style="color: red;" v-if="!bzjFlag&&current==0">提示：单次提现金额最低1千元
		</view>
		<view class="w95 mg-at f14 c9 " style="color: red;" v-if="bzjFlag&&current==0">
			提示：保证金低于3000元时，提现功能仅每月1-10号开放，且提现订单不包含当月</view>
		<!--    <view class="w95 mg-at f14 c9 " style="color: red;">提示：提现操作只在每月三号开放！如三号未提现，系统将自动提现！</view> -->
		<view class="w95 mg-at f14 c9 lh40">余额转出将汇入以下银行卡，如信息有误解绑后重新绑定</view>
		<view class="bacf f16 fb" style="padding: 20rpx 40rpx;">
			<view class="lh40">到账银行卡</view>
			<view class="lh40">
				<text class="c9" style="margin-right: 40rpx;">姓名</text>
				<text class="">{{infoData.accountName || ''}}</text>
			</view>
			<view class="lh40">
				<text class="c9" style="margin-right: 40rpx;">银行卡号</text>
				<text class="">{{infoData.bankCode ||''}}</text>
			</view>
			<view class="lh40" v-if="infoData.bankName">
				<text class="c9" style="margin-right: 40rpx;">所属银行</text>
				<text class="">{{infoData.bankName ||''}}</text>
			</view>
			<view class="lh40">
				<text class="c9" style="margin-right: 40rpx;">开户支行</text>
				<text class="">{{infoData.accountOpenBank ||''}}</text>
			</view>
		</view>
		<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button"
			activeColor="#1e1848"></uni-segmented-control>
		<view class="content">
			<view v-show="current === 0">
				<view class="bacf f16 lh40" style="padding: 20rpx 40rpx;margin-top: 40rpx;">
					<view style="display: flex;">
						<view class="fb" @click="popupFlag= true">无票转出金额</view>
						<uni-tag text="查看无票订单" color="#1e1848" style="margin-left: 35%" plain plainFill
							@click="popupFlag= true" borderColor="#1e1848" />
					</view>
					<view class="flac-row" @click="popupFlag= true">
						<text class="f30 fb4">¥</text>
						<u--input placeholder="点击选择要转出的无票订单" disabled border="bottom" fontSize="16px"
							v-model="noVoteMoney" />
					</view>
					<view class="f14 fb">无票可转出金额：{{noVoteSum}}元，<text class="red" @click="clcikAll">全部转出</text></view>
					<view style="display: flex;padding-top: 20rpx;">
						<view class="fb" @click="noVotePopupFlag= true">含票转出金额</view>
						<uni-tag text="查看含票订单" color="#1e1848" style="margin-left: 35%" plain plainFill
							@click="noVotePopupFlag= true" borderColor="#1e1848" />
					</view>
					<view class="flac-row" @click="noVotePopupFlag= true">
						<text class="f30 fb4">¥</text>
						<u--input placeholder="点击选择要转出的含票订单" disabled border="bottom" fontSize="16px"
							v-model="haveVoteMoney" />
					</view>
					<view class="f14 fb">含票可转出金额：{{haveVoteSum}}元</view>
				</view>
			</view>
			<view v-show="current === 1">
				<view class="bacf f16 lh40" style="padding: 20rpx 40rpx;margin-top: 40rpx;">
					<view style="display: flex;">
						<view class="fb" @click="recruitPopupFlag= true">转出金额</view>
						<uni-tag text="扣减返还订单" color="#1e1848" style="margin-left: 35%" plain plainFill
							@click="recruitPopupFlag= true" borderColor="#1e1848" />
					</view>
					<view class="flac-row" @click="recruitPopupFlag= true">
						<text class="f30 fb4">¥</text>
						<u--input placeholder="0.00" disabled border="bottom" fontSize="16px"
							v-model="recruitMoney" />
					</view>
					<view class="f14 fb">扣减返还可转出金额：{{recruitSum}}元，<text class="red"
							@click="recruitClcikAll">全部转出</text></view>
				</view>
			</view>
			<view v-show="current === 2">
				<view class="bacf f16 lh40" style="padding: 20rpx 40rpx;margin-top: 40rpx;">
					<view style="display: flex;">
						<view class="fb" @click="rewardPopupFlag= true">转出金额</view>
						<uni-tag text="查看奖励明细" color="#1e1848" style="margin-left: 35%" plain plainFill
							@click="rewardPopupFlag= true" borderColor="#1e1848" />
					</view>
					<view class="flac-row" @click="rewardPopupFlag= true">
						<text class="f30 fb4">¥</text>
						<u--input placeholder="0.00" disabled border="bottom" fontSize="16px"
							v-model="rewardMoney" />
					</view>
					<view class="f14 fb">其他奖励可转出金额：{{rewardMoney}}元</view>
				</view>
			</view>
		</view>
		<u-button :disabled="txFlag" text="确认转出，请等待财务审核打款" shape="circle" color="#1e1848" throttleTime="2000"
			customStyle="width:90%;margin:50rpx auto;letter-spacing: 2rpx;color:#f6cc70" @click="rollout"></u-button>
			
			<u-popup :show="rewardPopupFlag" @close="rewardPopupFlag = false">
				<view>
					<uni-section title="其他奖励明细" type="line" padding style="height: calc(80vh - 80px);">
						</u-search>
						<u-empty text="暂无其他奖励!" width="120" textSize="18" v-if="!rewardList.length"
							customStyle="padding-top:-30rpx">
						</u-empty>
						<scroll-view scroll-y="true" class="scroll-Y">
								<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
									v-for="(item,i) in rewardList" :key="i">
									<view class="f15 flac-row">
										<view style="margin: auto 20rpx;">
											<view class="lh36">平台使用费：￥{{item.platformFee}}</view>
											<view class="lh36">累计金额：￥{{item.orderAmount}}</view>
											<view class="lh36">累计实付：￥{{item.realOrderAmount}}</view>
											<view class="lh36">返还比率：{{item.ratio*100}}%</view>
											<view class="lh36">返还金额：￥{{item.amount}}</view>
											<view class="lh36">奖励类型：{{item.rewardType==1?'勤牛奖':'未知'}}</view>
											<view class="lh36">年度：{{item.year}}年</view>
										</view>
									</view>
								</view>
						</scroll-view>
					</uni-section>
				</view>
			</u-popup>
			

		<u-popup :show="recruitPopupFlag" @close="recruitPopupFlag = false">
			<view>
				<uni-section title="招工扣减返还订单" type="line" padding style="height: calc(80vh - 80px);">
					</u-search>
					<u-empty text="暂无招工扣减返还订单!" width="120" textSize="18" v-if="!recruitReturnList.length"
						customStyle="padding-top:-30rpx">
					</u-empty>
					<scroll-view scroll-y="true" class="scroll-Y">
						<checkbox-group @change="recruitChangeCheckbox">
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in recruitReturnList" :key="i">
								<view class="checkBox">
									<checkbox :value="item.id" :checked="recruitCheckedArr.includes(item.id)"
										:class="{'checked':recruitCheckedArr.includes(item.id)}"></checkbox>
								</view>
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="flac-row" style="justify-content: space-between;">
											<view class="">服务项目：{{item.productName}}</view>
										</view>
										<view class="lh36">反还时间：{{item.createTime}}</view>
										<view class="lh36">订单编号：{{item.billNo}}</view>
										<view class="lh36">订单金额：{{item.amount}}</view>
										<view class="lh36">扣减金额：{{item.recruitDeductions}}</view>
										<view class="lh36">反还金额：{{item.commission}}</view>
										<view class="lh36">员工名称：{{item.employeeName}}</view>
										<view class="lh36">员工工号：{{item.employeeNo}}</view>
										<view class="lh36">上户天数：{{item.validDay}}</view>
									</view>
								</view>
							</view>
						</checkbox-group>
					</scroll-view>
				</uni-section>
			</view>
		</u-popup>

		<u-popup :show="popupFlag" @close="popupFlag = false">
			<view>
				<uni-section title="无票可提现订单" type="line" padding style="height: calc(90vh - 80px);">
					</u-search>
					<u-empty text="暂无可提现订单!" width="120" textSize="18" v-if="!noVoteList.length"
						customStyle="padding-top:200rpx">
					</u-empty>
					<scroll-view scroll-y="true" class="scroll-Y">
						<checkbox-group @change="changeCheckbox">
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in noVoteList" :key="i">
								<view class="checkBox">
									<checkbox :disabled="item.orderDisabled" :value="item.orderId"
										:checked="checkedArr.includes(item.orderId)"
										:class="{'checked':checkedArr.includes(item.orderId)}"></checkbox>
								</view>
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="flac-row" style="justify-content: space-between;">
											<view class="">服务项目：{{item.productName}}</view>
										</view>
										<view class="lh36">交易时间：{{item.paySettlementTime}}</view>
										<view class="lh36">订单编号：{{item.billNo}}</view>
										<view class="lh36">可提金额：{{item.amount}}</view>
										<view class="lh36">实收金额：{{item.orderNetReceipts}}</view>
										<view class="lh36">金额说明：{{item.remark}}</view>
									</view>
								</view>
							</view>
						</checkbox-group>
					</scroll-view>
				</uni-section>
			</view>
		</u-popup>
		<u-popup :show="noVotePopupFlag" @close="noVotePopupFlag = false">
			<view>
				<uni-section title="含票可提现订单" type="line" padding style="height: calc(90vh - 90px);">
					</u-search>
					   <u-empty text="暂无可提现订单!" width="120" textSize="18" v-if="!haveVoteList.length" customStyle="padding-top:200rpx">
	      </u-empty>
					<scroll-view scroll-y="true" class="scroll-Y" v-if="haveVoteList.length">
						<view class="lh36" style="margin-left: 30rpx;">提示：勾选订单后请上传订单所属发票
							
							<view style="display: flex;margin-top: 20rpx;">
								<u-tag text="查看开票规则" style="width: 25%;" plain size="mini"
									@click="goToInvoiceInfo" type="warning"></u-tag>
								<u-tag text="查看上传文件发票流程" style="width: 40%;margin-left: 25%" plain size="mini"
									@click="fileFpFlag = true" type="warning"></u-tag>
							</view>

							<view style="padding-top: 30rpx;color: red;">上传图片发票（最多5张）</view>
							<view @click="uploadFp(1)">
								<u-upload maxCount="5" :fileList="fileList1" @afterRead="afterRead" @delete="deletePic"
									name="1" multiple></u-upload>
							</view>
						</view>

						<view class="lh36" style="margin-left: 30rpx;">
							<view style="padding-top: 30rpx;color: red;">上传文件发票（微信聊天记录文件）</view>
							<view @click="uploadFp(2)">
								<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="2"
									multiple accept="all">
								</u-upload>
							</view>
						</view>
						<checkbox-group @change="voteChangeCheckbox">
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in haveVoteList" :key="i">
								<view class="checkBox">
									<checkbox :value="item.orderId" :checked="voteCheckedArr.includes(item.orderId)"
										:class="{'checked':voteCheckedArr.includes(item.orderId)}"></checkbox>
								</view>
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="flac-row" style="justify-content: space-between;">
											<view class="">服务项目：{{item.productName}}</view>
										</view>
										<view class="lh36">交易时间：{{item.paySettlementTime}}</view>
										<view class="lh36">订单编号：{{item.billNo}}</view>
										<view class="lh36">实收金额：{{item.orderNetReceipts}}</view>
										<view class="lh36">可提金额：{{item.amount}}</view>
										<view class="lh36">金额说明：{{item.remark}}</view>
									</view>
								</view>
							</view>
						</checkbox-group>
					</scroll-view>
				</uni-section>
			</view>
		</u-popup>
		

		<u-popup :show="fileFpFlag" @close="fileFpFlag = false">
			<uni-section title="上传文件发票流程" type="line" padding></uni-section>
			<scroll-view scroll-y="true" class="scroll-Y" style="height:80vh;">
				<image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17049403084996f664d65e8a80333625822030950adf.png"
					class="w10" mode="widthFix"></image>
				<image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17049404379905197a70f5346b3d359e87cea1fd3e67.png"
					class="w10" mode="widthFix"></image>
				<image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1704940552454dce6cba6c218f7856d7ab5e505802fb.png"
					class="w10" mode="widthFix"></image>
				<image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1704940660655260eb199b54356884f5a25ec0e60472.png"
					class="w10" mode="widthFix"></image>
			</scroll-view>
		</u-popup>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化 
	export default {
		data() {
			return {
				popupFlag: false,
				fileFpFlag: false,
				txFlag: true,
				bzjFlag: true,
				noVotePopupFlag: false,
				recruitPopupFlag: false,
				rewardPopupFlag: false,
				infoData: {},
				noVoteMoney: '',
				recruitMoney: '',
				rewardMoney: '',
				voteImgUrl: '',
				items: ['订单收入', '招工扣减返还'],
				current: 0,
				haveVoteMoney: '',
				checkType: 0,
				fileList1: [],
				fileList2: [],
				checkedArr: [], //复选框选中的值
				voteCheckedArr: [], //复选框选中的值
				recruitCheckedArr: [], //复选框选中的值
				noVoteList: [],
				recruitReturnList: [],
				rewardList: [],
				invoiceDataList: [],
				haveVoteList: [],
				checkTitle: "",
				checkText: "",
				uploadType: null,
				noVoteSum: 0.00,
				recruitSum: 0.00,
				haveVoteSum: 0.00,
			};
		},
		onLoad() {
			this.getCanWithdrawalOrder()
			this.getStoreData()
		},
		methods: {
			onClickItem(e) {
				if (e.currentIndex == 0) {
					this.getCanWithdrawalOrder()
				} else if(e.currentIndex == 1){
					this.getCanWithdrawalRecruitReturn()
				}else {
					this.getStoreReward()
				}
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
			},
			uploadFp(val) {
				this.uploadType = val
			},
			// 删除图片
			deletePic(event) {
				this.event = event
				this.noVotePopupFlag = false
				this.openCheck(1, "确认删除该发票图片吗？")
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					const invoiceResult = await this.checkStoreWithdrawalInvoice(obj.data)
					if(invoiceResult==1){
						this.fileList1 = []
						this.fileList2 = []
						this.invoiceDataList = []
						this.voteImgUrl = ''
						return
					}
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					let listFile = this.fileList1.concat(this.fileList2)
					// if (this.uploadType == 1) {
					// 	// this.fileList2 = []
					// 	listFile = this.fileList1
					// } else {
					// 	// this.fileList1 = []
					// 	listFile = this.fileList2
					// }
					let imgs = '';
					if (listFile.length > 0) {
						for (var j = 0; j < listFile.length; j++) {
							imgs += listFile[j].url + ","
						}
					}
					if (imgs) {
						imgs = imgs.substring(0, imgs.length - 1)
					}
					this.voteImgUrl = imgs
					fileListLen++
				}
			},
			checkStoreWithdrawalInvoice(url){
				return new Promise((resolve, reject) => {
					this.http({
						url: 'checkStoreWithdrawalInvoice',
						method: 'GET',
						data: {
							invoiceUrl: url
						},
						success: res => {
							if (res.code == 0) {
								this.invoiceDataList.push(res.data)
							} else {
								uni.showToast({
									icon: 'none',
									title: res.msg
								})
							}
							resolve(res.code)
						}
					})
				})
			},
			uploadFilePromise(url) {
				if (this.uploadType == 1) {
					return new Promise((resolve, reject) => {
						let a = uni.uploadFile({
							url: 'https://api2.xiaoyujia.com/system/imageUpload',
							filePath: url,
							name: 'file',
							formData: {},
							success: (res) => {
								setTimeout(() => {
									resolve(res.data)
								}, 1000)
							}
						});
					})
				} else {
					return new Promise((resolve, reject) => {
						let a = uni.uploadFile({
							url: 'https://api2.xiaoyujia.com/system/uploadFile',
							filePath: url,
							name: 'file',
							success: (res) => {
								setTimeout(() => {
									resolve(res.data)
								}, 1000)
							}
						});
					})
				}
				
			},
			recruitChangeCheckbox(e) {
				this.recruitCheckedArr = e.detail.value;
				let count = null;
				for (var i = 0; i < this.recruitCheckedArr.length; i++) {
					for (var j = 0; j < this.recruitReturnList.length; j++) {
						if (this.recruitCheckedArr[i] == this.recruitReturnList[j].id) {
							count += this.recruitReturnList[j].commission
						}
					}
				}
				if (!count) {
					this.recruitMoney = count
				} else {
					this.recruitMoney = count.toFixed(2)
				}
			},
			changeCheckbox(e) {
				this.checkedArr = e.detail.value;
				let count = null;
				for (var i = 0; i < this.checkedArr.length; i++) {
					for (var j = 0; j < this.noVoteList.length; j++) {
						if (this.checkedArr[i] == this.noVoteList[j].orderId) {
							count += this.noVoteList[j].amount
						}
					}
				}
				if (!count) {
					this.noVoteMoney = count
				} else {
					this.noVoteMoney = count.toFixed(2)
				}
			},
			voteChangeCheckbox(e) {
				this.voteCheckedArr = e.detail.value;
				let count = null;
				for (var i = 0; i < this.voteCheckedArr.length; i++) {
					for (var j = 0; j < this.haveVoteList.length; j++) {
						if (this.voteCheckedArr[i] == this.haveVoteList[j].orderId) {
							count += this.haveVoteList[j].amount
						}
					}
				}
				if (!count) {
					this.haveVoteMoney = count
				} else {
					this.haveVoteMoney = count.toFixed(2)
				}
			},
			getStoreReward() {
				this.rewardList = []
				this.rewardMoney = 0.00
				this.http({
					url: 'getStoreReward',
					method: 'GET',
					data: {
						storeId: uni.getStorageSync("selectStoreId")
					},
					success: res => {
						if (res.code == 0) {
							this.rewardList = res.data
							let sumMoney = 0.00
							for (var i = 0; i < this.rewardList.length; i++) {
								sumMoney+= this.rewardList[i].amount
							}
							this.rewardMoney = sumMoney
						} else {
							uni.showToast({
								icon: 'none',
								title: '获取其他奖励信息失败!'
							})
						}
					}
				})
			},
			getCanWithdrawalRecruitReturn() {
				this.recruitReturnList = []
				this.recruitCheckedArr = []
				this.recruitMoney = 0.00
				this.recruitSum = 0.00
				this.http({
					url: 'getCanWithdrawalRecruitReturn',
					method: 'GET',
					data: {
						storeId: uni.getStorageSync("selectStoreId")
					},
					success: res => {
						if (res.code == 0) {
							this.recruitReturnList = res.data.workCommissions
							for (var i = 0; i < this.recruitReturnList.length; i++) {
								this.recruitReturnList[i].id = this.recruitReturnList[i].id.toString()
							}
							this.recruitSum = res.data.recruitSumMoney
						} else {
							uni.showToast({
								icon: 'none',
								title: '获取可提现的招工反还失败!'
							})
						}
					}
				})
			},
			getCanWithdrawalOrder() {
				this.noVoteList = []
				this.haveVoteList = []
				this.checkedArr = []
				this.voteCheckedArr = []
				this.voteImgUrl = ''
				this.noVoteMoney = 0.00
				this.haveVoteMoney = 0.00
				this.noVoteSum = 0.00
				this.haveVoteSum = 0.00
				this.http({
					url: 'getCanWithdrawalOrder',
					method: 'GET',
					data: {
						storeId: uni.getStorageSync("selectStoreId")
					},
					success: res => {
						if (res.code == 0) {
							this.noVoteList = res.data.noVoteList
							this.haveVoteList = res.data.haveVoteList
							this.noVoteSum = res.data.noVoteSum
							this.haveVoteSum = res.data.haveVoteSum
						} else {
							uni.showToast({
								icon: 'none',
								title: '获取可提现订单信息失败!'
							})
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					//判断是订单转出还是招工
					if (this.current == 0) {
						if ((!this.noVoteMoney || this.noVoteMoney == 0) && (!this.haveVoteMoney || this.haveVoteMoney ==
								0)) {
							return uni.showToast({
								icon: 'none',
								title: '转出金额不能小于等于零元!'
							})
						} else if (this.noVoteMoney > this.noVoteSum || this.haveVoteMoney > this.haveVoteSum) {
							return uni.showToast({
								icon: 'none',
								title: '转出金额不能大于可转出余额!'
							})
						} else if ((this.noVoteMoney + this.haveVoteMoney) < 1000) {
							return uni.showToast({
								icon: 'none',
								title: '单次转出金额不能小于1千元!'
							})
						} else if (this.voteCheckedArr.length > 0 && !this.voteImgUrl) {
							return uni.showToast({
								icon: 'none',
								title: '请上传含票转出所需的发票图片!'
							})
						} else {
							this.http({
								url: "withdrawalApplyOf",
								method: 'POST',
								data: {
									storeId: uni.getStorageSync('selectStoreId'),
									employeeId: uni.getStorageSync("employeeId"),
									orderIdList: this.checkedArr,
									voteOrderIdList: this.voteCheckedArr,
									voteImgUrl: this.voteImgUrl,
									withdrawalMoney: this.noVoteMoney,
									voteWithdrawalMoney: this.haveVoteMoney,
									invoiceDataList: this.invoiceDataList
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									setTimeout(() => {
										uni.showToast({
											title: res.msg,
											icon: 'none',
											duration: 2500
										})
									}, 30)
									if (res.code == 0) {
										this.noVoteMoney = null
										this.haveVoteMoney = null
										this.getCanWithdrawalOrder()
									}
								}
							})
						}
					} else if(this.current == 1){
						this.http({
							url: "withdrawalRecruitApplyOf",
							method: 'POST',
							data: {
								storeId: uni.getStorageSync('selectStoreId'),
								employeeId: uni.getStorageSync("employeeId"),
								commissionIdList: this.recruitCheckedArr,
								withdrawalMoney: this.recruitMoney,
							},
							header: {
								"content-type": "application/json;charset=UTF-8"
							},
							success: res => {
								setTimeout(() => {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}, 30)
								if (res.code == 0) {
									this.recruitMoney = null
									this.getCanWithdrawalRecruitReturn()
								}
							}
						})
					}else{
						this.http({
							url: "withdrawalReward",
							method: 'POST',
							data: {
								storeId: uni.getStorageSync('selectStoreId'),
								employeeId: uni.getStorageSync("employeeId"),
								withdrawalMoney: this.rewardMoney,
							},
							header: {
								"content-type": "application/json;charset=UTF-8"
							},
							success: res => {
								setTimeout(() => {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}, 30)
								if (res.code == 0) {
									this.rewardMoney = null
									this.rewardList()
								}
							}
						})
					}
				}
				if (this.checkType == 1) {
					this.fileList1 = []
					this.fileList2 = []
					this.invoiceDataList = []
					this.voteImgUrl = ''
				}
			},
			getStoreData() {
				this.http({
					url: "getStoreData",
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					method: "GET",
					success: res => {
						if (res.code == 0) {
							this.infoData = res.data
							let currentDate = new Date();
							let day = currentDate.getDate();
							if (res.data.amount >= 3000) {
								this.bzjFlag = false
							}
							if (res.data.amount >= 3000 || day <= 10) {
								this.txFlag = false
							}
							if(res.data.ifOpenSpecial){
								this.items.push("其他奖励")
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '获取门店信息失败!'
							})
						}
					}
				})
			},
			recruitClcikAll() {
				if (!this.recruitSum) {
					uni.showToast({
						icon: 'none',
						title: '当前余额为0'
					})
				} else {
					let sumMoeny = 0.00
					for (var i = 0; i < this.recruitReturnList.length; i++) {
						let ss = (sumMoeny * 100 + this.recruitReturnList[i].commission * 100) / 100
						sumMoeny = (sumMoeny * 100 + this.recruitReturnList[i].commission * 100) / 100
						this.recruitCheckedArr.push(this.recruitReturnList[i].id)
					}
					this.recruitMoney = sumMoeny.toFixed(3)
				}
			},
			clcikAll() {
				if (!this.noVoteSum) {
					uni.showToast({
						icon: 'none',
						title: '当前余额为0'
					})
				} else {
					let sumMoeny = 0.00
					for (var i = 0; i < this.noVoteList.length; i++) {
						let ss = (sumMoeny * 100 + this.noVoteList[i].amount * 100) / 100
						if (!this.bzjFlag && ss > 20000) {
							uni.showToast({
								icon: 'none',
								title: '单次最大转出2万元!'
							})
							break;
						}
						if (this.bzjFlag && !this.noVoteList[i].orderDisabled) {
							sumMoeny = (sumMoeny * 100 + this.noVoteList[i].amount * 100) / 100
							this.checkedArr.push(this.noVoteList[i].orderId)
						}
						if (!this.bzjFlag) {
							sumMoeny = (sumMoeny * 100 + this.noVoteList[i].amount * 100) / 100
							this.checkedArr.push(this.noVoteList[i].orderId)
						}
					}
					this.noVoteMoney = sumMoeny.toFixed(2)
				}
			},
			rollout() {
				this.openCheck(0, "是否确认提交转出申请?")
			},
			// 跳转到发票信息页面
			goToInvoiceInfo() {
				this.noVotePopupFlag = false
				uni.navigateTo({
					url: '/pages-other/income/invoiceEg?from=rollout'
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5;
	}

	.scroll-Y {
		height: 1100rpx;
	}
</style>