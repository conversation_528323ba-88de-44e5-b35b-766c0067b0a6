<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 图片裁切 -->
		<l-clipper v-if="showClipper" @success="clipperSuccess" @cancel="showClipper = false" :width="500" :height="500"
			:min-width="300" :min-height="300" :scale-ratio="6" />

		<!-- 头部 用户信息 -->
		<view class="user-info">
			<view class="info-item">
				<text class="title-big">会员信息</text>
			</view>

			<view class="info-item">
				<text class="title">头像</text>
				<view class="head-img" @longpress="openImgPreview(member.headImg)">
					<img :src="member.headImg!==''?member.headImg:blankImg" @click="uploadHeadPortrait()">
				</view>
			</view>

			<view class="info-item">
				<text class="title">姓名</text>
				<!-- <text class="content">{{ memberName!==''?memberName:"暂无用户名"}}</text> -->
				<view class="content">
					<input class="single-input" type="text" v-model="member.name" placeholder="请输入用户名" />
				</view>
			</view>

			<view class="info-item disable-text">
				<text class="title" @click="openTips">编号</text>
				<view class="content" @click="saveText(memberId)">
					{{memberId}}
				</view>
			</view>

			<view v-if="employeeId">
				<view class="info-item" @click="openTips">
					<text class="title-big">员工信息</text>
				</view>

				<view class="info-item disable-text">
					<text class="title" @click="openTips">姓名</text>
					<view class="content" @click="saveText(employeeName)">
						{{employeeName}}
					</view>
				</view>

				<view class="info-item disable-text">
					<text class="title" @click="openTips">工号</text>
					<view class="content" @click="saveText(employeeNo)">
						{{employeeNo}}
					</view>
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="btn-big"><button @click="trySave()">保存更改</button></view>
		</view>

		<view class="user-info">
			<view class="info-item flac-col" style="height: auto;">
				<text class="title-big">绑定微信公众号</text>
				<text class="title-tips" style="line-height: 30rpx;">绑定后可接收订单通知、服务提醒等消息</text>
			</view>
			<view class="wechat-bind-card">
				<view class="wechat-bind-status">
					<image class="wechat-bind-icon"
						:src="isBindMember?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/bindmember_success.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/bindmember_error.png'"
						mode="aspectFill" />
					<view class="wechat-bind-texts">
						<text class="wechat-bind-title">{{isBindMember?'微信公众号已绑定':'微信公众号未绑定'}}</text>
						<text class="wechat-bind-desc">{{isBindMember?'可':'无法'}}接收订单通知、服务提醒等消息</text>
					</view>
				</view>
				<button class="wechat-btn-test" @click="sendTestMsg" v-if="isBindMember">发送测试消息</button>
				<img v-if="bindMemberCode&&!isBindMember" :src="bindMemberCode" class="mg-at"
					@click="openImgPreview(bindMemberCode)"
					style="width: 200rpx;height: 200rpx;margin-bottom: 40rpx;" />
				<button class="wechat-btn-unbind" style="color: #1e1848;border: 2rpx solid #1e1848;"
					v-if="!isBindMember" @click="getBindCode()">绑定公众号</button>
				<button class="wechat-btn-unbind" v-else @click="liftBindMember()">解绑公众号</button>
			</view>
			<u-gap height="20"></u-gap>
		</view>


		<view class="user-info">
			<view class="info-item">
				<text class="title-big">其他功能</text>
			</view>
			<!-- 			<view class="info-item">
				<text class="title">暂无</text>
			</view> -->

			<view class="list-column" v-for="(mineList, index) in mineList" :key="index" v-if="mineList.show"
				@click="openMineDetail(mineList.listUrl)" @longpress="pressMineDetail(mineList.listUrl)">
				<image :src="mineList.listImage"></image>
				<text class="column-text">{{mineList.listTitle}}</text>
				<text class="column-text-right">&ensp;❯</text>
			</view>
		</view>


		<!-- 
		<text @click="goview()"
			style="text-align:center;position: fixed;width: 100%;bottom: 100rpx;font-size: 28rpx;">版本信息
		</text> -->
		<u-gap height="120"></u-gap>
	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				// 可设置
				// 是否开启图片裁剪
				isOpenClipper: true,
				// 绑定公众号
				isBindMember: false,
				bindMemberCode: '',
				// 自动刷新时间
				refreshTime: 5,
				pageTime: 0,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				showClipper: false,
				memberId: uni.getStorageSync('memberId') || null,
				employeeId: uni.getStorageSync('employeeId') || null,
				memberName: uni.getStorageSync('memberName') || "",
				employeeName: uni.getStorageSync('employeeName') || "",
				employeeNo: uni.getStorageSync('employeeNo') || "",
				headImg: uni.getStorageSync("memberHeadImg"),
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				settingImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664265540790settings.png",
				member: {
					id: uni.getStorageSync("memberId"),
					name: uni.getStorageSync("memberName"),
					headImg: uni.getStorageSync("memberHeadImg"),
				},
				mineList: [
					// {
					// 	listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266309079我的考试.png',
					// 	listTitle: '考试中心',
					// 	listUrl: '/pages/mine/exam/exam-center'
					// },
					// {
					// 	listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266364346我的证书.png',
					// 	listTitle: '我的证书',
					// 	listUrl: '/pages/mine/mine-detail/my-certificate'
					// },
					// {
					// 	listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664266345045我的优惠券.png',
					// 	listTitle: '我的优惠券',
					// 	listUrl: '/pages/mine/mine-detail/my-discount'
					// },
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zs.png',
						listTitle: '我的简历',
						listUrl: '/pages-mine/resume/resume',
						show: true,
					},{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zs.png',
						listTitle: '员工手册签署',
						listUrl: '/pages-other/other/employeeHandbookSign',
						show: true,
					}
				]
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			// 上传头像
			uploadHeadPortrait() {
				if (this.isOpenClipper) {
					this.showClipper = true
					return
				}

				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos'
							},
							dataType: 'json',
							success: res => {
								// 本地临时地址
								this.headImg = tempFilePaths[0]
								// 上传到服务器后的地址
								let result = JSON.parse(res.data)
								this.member.headImg = result.data
							}
						});
					}
				});
			},
			// 图片裁剪成功
			async clipperSuccess(e) {
				this.showClipper = false
				let path = []
				// #ifdef H5
				await base64ToPath(e.url).then(res => {
					path.push(res)
				})
				// #endif

				// #ifdef MP-WEIXIN || APP-PLUS
				path.push(e.url)
				// #endif

				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				const tempFilePaths = path
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						// route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						this.headImg = tempFilePaths[0]
						let result = JSON.parse(res.data)
						this.member.headImg = result.data
					},
					fail: res => {
						this.$refs.uNotify.error("上传失败！" + res.msg)
					}
				});
			},
			// 打开个人信息
			openAccount() {
				uni.navigateTo({
					url: '/pages-mine/resume/account'
				});
			},
			// 打开对应的详细页面
			openMineDetail(url) {
				console.log(url)

				// 跳转到行政员工简历
				if (url.includes("pages-mine/resume/resume")) {
					let isBaomu = uni.getStorageSync("isBaomu") || false
					if (!isBaomu) {
						uni.navigateTo({
							url: "/pages-other/hr/staff-resume?employeeId=" + uni.getStorageSync("employeeId")
						})
						return
					}
				}
				uni.navigateTo({
					url: url
				});
			},
			// 复制文本
			saveText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('复制成功!')
					}
				})
			},
			openTips() {
				this.$refs.uNotify.warning('暂不支持修改!')
			},
			// 长按打开对应的详细页面
			pressMineDetail(url) {
				console.log(url)
				// 跳转到行政员工简历
				// if (url.includes("pages-mine/resume/resume")) {
				// 	uni.navigateTo({
				// 		url: url
				// 	});
				// }
			},
			goview() {
				let detailUrl = ""
				this.http({
					method: 'POST',
					url: "getCourseById",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					data: {
						id: 11
					},
					dataType: "json",
					success: res => {
						if (res.code == 0) {
							detailUrl = res.data.courseDetailUrl
							console.log("文档链接：" + detailUrl)
						}
					}
				})


				// #ifdef  APP-PLUS || H5
				let url = "https://jiajie.xiaoyujia.com/pages-mine/index/update-log?flag=1"
				let param = {
					url: url
				}
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(JSON.stringify(param))}`
				})
				// #endif

				// #ifdef  MP-WEIXIN
				console.log('开始跳转半屏小程序！')
				// wx.navigateToMiniProgram({
				// 	appId: 'wxd45c635d754dbf59',
				// 	path: 'pages/detail/detail.html?url=' + encodeURIComponent(detailUrl)
				// })

				wx.openEmbeddedMiniProgram({
					appId: 'wxd45c635d754dbf59',
					// path: 'pages/detail/detail.html?url=' + detailUrl
					path: 'pages/detail/detail.html?url=' + encodeURIComponent(detailUrl)
				})
				// #endif
			},
			trySave() {
				this.openCheck(0, "确定保存吗？", "更改将立即生效！")
			},
			save() {
				this.http({
					url: 'updateMemberById',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: this.member,
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync("memberName", this.member.name)
							uni.setStorageSync("memberHeadImg", this.member.headImg)
							this.$refs.uNotify.success('保存成功！')
						} else {
							this.$refs.uNotify.error('更新员工信息失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 检查员工状态
			checkState() {
				if (uni.getStorageSync("isInvited") !== null) {
					if (uni.getStorageSync("isInvited") == true) {
						return true
					}
				}
				let employeeState = uni.getStorageSync("employeeState")
				if (employeeState != -1 && employeeState != 2) {
					return true
				} else {
					return false
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：保存设置
				if (this.checkType == 0) {
					this.save()
				}
			},
			sendTestMsg() {
				this.http({
					url: 'sendTemplateMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						"source": "xyjacnoa",
						"memberId": this.memberId,
						"employeeId": null,
						"employeeNo": null,
						"templateId": "_Z3GjateHsdspdvnnOlvwhoSIwS5QmlIRzZ0cccjoW8",
						"data": {
							"character_string1": {
								"value": "0000"
							},
							"thing3": {
								"value": "测试消息"
							},
							"thing5": {
								"value": "厦门市小羽佳家政"
							}
						}
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('公众号测试消息已发送！')
						} else {
							this.$refs.uNotify.warning(res.msg)
						}
					}
				})
			},
			getBindCode() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/openapi/createQRCodeImg',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						source: 'xyjAcnOa',
						codeType: 2,
						sceneStr: 'bindMember:' + this.memberId,
						sceneId: null,
						expireSeconds: '',
						sousceneStrrce: '',
					},
					success: res => {
						if (res.code == 0) {
							this.bindMemberCode = res.data
							// #ifdef MP-WEIXIN
							this.$refs.uNotify.success('请长按二维码，扫码绑定！')
							// #endif
							// #ifndef MP-WEIXIN
							this.$refs.uNotify.success('请点击二维码，使用微信扫码绑定！')
							// #endif
							setTimeout(() => {
								this.openImgPreview(this.bindMemberCode)
							}, 1000);
						} else {
							this.$refs.uNotify.warning(res.msg)
						}
					}
				});
			},
			// 绑定会员公众号
			liftBindMember() {
				uni.showModal({
					title: '确定解绑公众号吗？',
					content: '解绑后将无法接收订单通知、服务提醒等消息',
					success: (res) => {
						if (res.confirm) {
							this.http({
								outsideUrl: 'https://api.xiaoyujia.com/member/bindMember',
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								method: 'POST',
								data: {
									memberId: this.memberId,
									source: 'xyjAcnOa',
									liftBind: true
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('已解绑公众号！')
										this.isBindMember = false
									} else {
										this.$refs.uNotify.warning(res.msg)
									}
								}
							});
						}
					}
				});
			},
			checkMemberBind() {
				if (this.isBindMember) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/member/checkMemberBind',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						source: 'xyjAcnOa'
					},
					success: res => {
						if (res.code == 0) {
							this.isBindMember = true
						} else {
							this.isBindMember = false
						}
					}
				});
			},
			// 分享到好友
			onShareAppMessage(res) {
				return {
					title: '家姐联盟-个人设置',
					path: "/pages-mine/mine-detail/setting",
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
		watch: {
			pageTime: {
				handler(newValue, oldVal) {
					if (this.pageTime % this.refreshTime == 0) {
						// 校验是否绑定
						this.checkMemberBind()
					}
				},
				deep: true
			}
		},
		onShow() {
			this.checkMemberBind()
		},
		mounted() {
			if (!this.memberId) {
				this.$refs.uNotify.warning('请先登录哦！')
				uni.setStorageSync('redirectUrl', "/pages-mine/mine-detail/setting")
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					});
				}, 2000);
			}
			setInterval(() => {
				this.pageTime += 1
			}, 1000);
			this.checkMemberBind()
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.user-info {
		width: 100%;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin-bottom: 20rpx;
	}

	// 用户信息每个栏目
	.info-item {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
	}

	.info-item text {
		margin-left: 40rpx;
	}

	.title-big {
		font-size: 40rpx;
		font-weight: bold;
	}

	.title {
		font-size: 36rpx;
	}

	.content {
		float: right;
		margin-right: 40rpx;
		font-size: 36rpx;

		input {
			line-height: 100rpx;
			height: 100rpx;
			text-align: right;
			font-size: 36rpx;
		}
	}

	.head-img {
		float: right;
		width: 100rpx;
		height: 100rpx;
		margin-right: 40rpx;
	}

	.head-img img {
		display: block;
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.list-column {
		width: 100%;
		height: 120rpx;
	}

	.list-column image {
		float: left;
		margin: 35rpx 0 0 40rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.column-text {
		line-height: 120rpx;
		margin: 0 0 0 20rpx;
		font-size: 32rpx;
	}

	.column-text-right {
		float: right;
		margin-right: 40rpx;
		line-height: 120rpx;
		color: #909399;
	}

	// 大号按钮
	.btn-big {
		padding-bottom: 40rpx;

		button {
			margin: 20rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.disable-text {
		color: rgba(0, 0, 0, 0.5);
	}

	.wechat-bind-card {
		background: #fff;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 32rpx 32rpx 32rpx 32rpx;
		padding: 40rpx 32rpx 40rpx 32rpx;
		display: flex;
		flex-direction: column;
		align-items: stretch;
	}

	.wechat-bind-status {
		display: flex;
		align-items: center;
		margin: 0 auto 32rpx auto;
	}

	.wechat-bind-icon {
		width: 80rpx;
		height: 80rpx;
		margin-right: 24rpx;
	}

	.wechat-bind-texts {
		display: flex;
		flex-direction: column;
	}

	.wechat-bind-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #222;
	}

	.wechat-bind-desc {
		font-size: 28rpx;
		color: #888;
		margin-top: 8rpx;
	}

	.wechat-btn-test {
		background: #1e1848;
		color: #f6cc70;
		font-size: 32rpx;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		height: 80rpx;
		line-height: 80rpx;
		width: 70%;
	}

	.wechat-btn-unbind {
		background: #fff;
		color: #ff4d4f;
		border: 2rpx solid #ff4d4f;
		font-size: 32rpx;
		border-radius: 16rpx;
		height: 80rpx;
		line-height: 80rpx;
		width: 70%;
	}
</style>