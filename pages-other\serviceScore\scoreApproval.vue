<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 规则弹窗提示 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="t-indent fb f18 lh40 mg-at">
				加分审批
			</view>
			<view style="height: 400rpx;">
				<view class="f16 lh30" v-for="(item,index) in ruleList" :key="index" style="margin: 0 40rpx;">
					{{item}}
				</view>
			</view>
		</u-popup>

		<!-- 审批记录 -->
		<view v-if="pageType == 0">
			<view class="w9 mg-at text-r f16 fb" style="margin: 20rpx 20rpx;" @click="popupShow=true">规则</view>
			<u-sticky bgColor="#fff">
				<u-tabs :list="tabslist" :scrollable="false" lineWidth="35" lineHeight="2" lineColor="#10027c"
					:activeStyle="{
			        color: '#10027c',
			    }" itemStyle="padding: 10rpx" @click="clickTabs" :current="choiceIndex" />
			</u-sticky>

			<view class="w85 flac-row-b" style="margin: 40rpx auto;" v-for="(item,index) in approvalList" :key="index"
				v-if="approvalList.length" @click="openDetail(index)">
				<view class="w7 flac-row">
					<view class="f14 lh20">
						<view class="fb">{{item.ruleTitle}}</view>
						<view v-if="item.approvalRemark">备注：{{item.approvalRemark||''}}</view>
						<view class="f12 c9">{{item.createTime}}</view>
					</view>
				</view>
				<view :class="item.approvalState ==0?'c9' :item.approvalState ==1 ? 'red' : 'green'">
					{{item.approvalState==0?'待审批':item.approvalState==1?'未通过':'已通过'}}
				</view>
			</view>
			<u-empty v-if="!approvalList.length" text="暂无记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>

		<!-- 审批提交 -->
		<view class="resume-tab" v-if="pageType !=0">
			<view class="w9 mg-at">
				<u--form :model="approval" labelPosition="top" labelWidth="330" :labelStyle="labelStyle">
					<u-form-item label="提交者" prop="approvalContent" v-if="pageType==3">
						<text class="fb f16" style="color: #ff4d4b;">{{approval.employeeName||'匿名用户'}}</text>
						<text class="f16">（{{approval.storeName||'暂无门店'}}）</text>
					</u-form-item>
					<u-form-item label="审批人" prop="approvalContent" v-if="pageType==3&&approval.approvalState!=0">
						<text class="f16">{{approval.approvalorName||'-'}}</text>
					</u-form-item>
					<u-form-item label="审批结果" prop="approvalContent"
						v-if="(pageType==2&&approval.approvalRemark)||(pageType==3&&approval.approvalState!=0)">
						<view class="flac-col">
							<view class="fb f16" style="color: #ff4d4b;">
								{{approval.approvalState==1?'不通过':'通过'}}-{{approval.approvalRemark}}
							</view>
							<view class="f16">（{{approval.updateTime}}）</view>
						</view>
					</u-form-item>
					<u-form-item label="审批项目" required>
						<view class="flac-col" v-if="pageType!=3&&approval.approvalState==null">
							<view class="tab-choice" v-for="(item,index) in allowApprovalList" :key="index"
								@click="choiceRule(index)"
								:style="choiceRuleIndex==index?'background-color: #f4f4f5':''">
								<uni-icons type="circle" size="18" color="#909399" v-if="choiceRuleIndex!=index">
								</uni-icons>
								<uni-icons type="circle-filled" size="18" color="#909399" v-if="choiceRuleIndex==index">
								</uni-icons>
								<view class="choice-text">
									<text>{{index+1}}. {{item.ruleTitle}}</text>
								</view>
							</view>
							<view class="f16">
								<text class="fb" style="color: #ff4d4b;">审批要求：</text>
								<text>{{allowApprovalList[choiceRuleIndex].ruleContent}}</text>
							</view>
						</view>
						<view class="flac-col" v-else>
							<view class="f16">
								<text>{{approval.ruleTitle}}</text>
							</view>
							<view class="f16">
								<text class="fb" style="color: #ff4d4b;">审批要求：</text>
								<text>{{approval.ruleContent}}</text>
							</view>
						</view>
					</u-form-item>
					<u-form-item label="审批服务分" prop="approvalIntegral" required v-if="approval.approvalIntegral">
						<uni-number-box v-model="approval.approvalIntegral" :min="1" :max="5" />
					</u-form-item>
					<u-form-item label="审批理由" prop="approvalContent" required>
						<u-textarea placeholder="请填写你的审批理由" v-model="approval.approvalContent"
							:disabled="(approval.approvalState||0)!=0" />
					</u-form-item>
					<u-form-item label="审批图片1">
						<view @longpress="openImgPreview(approval.approvalImg)">
							<img :src="approval.approvalImg||imgUpload" alt="" @click="uploadImg(0)" mode="widthFix"
								style="width: 200rpx;height: auto;">
						</view>
					</u-form-item>
					<view v-for="(item,index) in [1,2,3,4,5,6,7]" v-if="index<imgNum||approval['approvalImg'+item]"
						:key="index">
						<u-form-item :label="'审批图片'+(item+1)">
							<view @longpress="openImgPreview(approval['approvalImg'+item])">
								<img :src="approval['approvalImg'+item]||imgUpload" alt="" @click="uploadImg(item)"
									mode="widthFix" style="width: 200rpx;height: auto;">
							</view>
						</u-form-item>
					</view>
					<view class="flac-row f16" style="color: #ff4d4b;" v-if="pageType != 3&&imgNum<maxImgNum-1"
						@click="imgNum++">
						添加更多图片
						<uni-icons type="plus" size="20" color="#ff4d4b"></uni-icons>
					</view>
					<u-form-item label="审批视频">
						<video :src="approval.approvalVideo" v-if="approval.approvalVideo||pageType==3"
							style="width: 90%;height: 400rpx;margin: 0 5%;"></video>
						<uni-file-picker limit="1" file-mediatype="video" @select="uploadFile" v-else>
							<view class="mg-at w10" style="margin: 20rpx 0;">
								<img style="width: 400rpx;height: 200rpx;margin: 0 125rpx;" :src="uploadFileImg">
								</img>
								<view style="text-align: center;">可上传不超过200MB的视频文件</view>
							</view>
						</uni-file-picker>
					</u-form-item>
					<u-form-item label="审批结果" v-if="pageType==3&&approval.approvalState==0">
						<view class="flac-col">
							<view class="tab-choice" v-for="(item,index) in stateList" :key="index"
								:style="choiceStateIndex==index?'background-color: #f4f4f5':''"
								@click="approval.approvalState==0?choiceStateIndex=index:''">
								<uni-icons type="circle" size="18" color="#909399" v-if="choiceStateIndex!=index">
								</uni-icons>
								<uni-icons type="circle-filled" size="18" color="#909399"
									v-if="choiceStateIndex==index">
								</uni-icons>
								<view class="choice-text">
									<text>{{index+1}}. {{item.text}}</text>
								</view>
							</view>
						</view>
					</u-form-item>
					<u-form-item label="结果备注" prop="approvalRemark" :required="choiceStateIndex==0"
						v-if="pageType==3&&approval.approvalState==0">
						<u-textarea placeholder="请输入审批备注（不通过情况下必填）" v-model="approval.approvalRemark" />
					</u-form-item>
				</u--form>
			</view>
			<!-- 保存按钮 -->
			<view class="btn-big" v-if="pageType==1"><button @click="save()">提 交</button>
			</view>
			<view class="btn-big" v-if="pageType==2"><button @click="update()">修 改</button>
			</view>
			<view class="btn-big" v-if="pageType==3&&approval.approvalState==0"><button @click="confirm()">确认结果</button>
			</view>
		</view>

		<!-- 展示全部 -->
		<view class="bacf" style="position: fixed;bottom: 26%;right:37rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #f4f4f5 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;" @click="changeWeek" v-if="pageType==0">
			<uni-icons type="eye-filled" size="20" v-if="thisWeek" color="#1e1848"></uni-icons>
			<uni-icons type="eye" size="20" v-else color="#1e1848"></uni-icons>
		</view>
		<!-- 添加按钮 -->
		<view class="bacf" style="position: fixed;bottom: 19%;right:37rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #f4f4f5 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;" @click="changeType" v-if="pageType!=3">
			<uni-icons type="plusempty" size="20" v-if="pageType==0" color="#1e1848"></uni-icons>
			<uni-icons type="calendar" size="20" v-else color="#1e1848"></uni-icons>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 当前页面（0：审批记录 1：提交审批 2：审批材料修改 3：进行审批）
				pageType: 0,
				//显示图片数量
				imgNum: 2,
				// 最大图片数量
				maxImgNum: 8,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				labelStyle: {
					'font-weight': 'bold',
					'font-size': '32rpx',
					'letter-spacing': '5rpx',
				},
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				ruleList: [
					'1.可以通过完成服务分审批来提升服务分！',
					'2.选择想要审批的项目，提交相关材料进行提交。',
					'3.未审批的状态下可对审批内容进行修改。',
					'4.若审批不通过可重新提交，否则需等待结果。',
					'5.审批通过服务分将立即更新。',
				],
				tabslist: [{
					name: '全部',
					type: null,
				}, {
					name: '待审批',
					type: 0,
				}, {
					name: '未通过',
					type: 1,
				}, {
					name: '已通过',
					type: 2,
				}],
				stateList: [{
					id: 1,
					text: '不通过'
				}, {
					id: 2,
					text: '通过'
				}],
				popupShow: false,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				uploadFileImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/uploadFileTab.png',
				choiceIndex: 0,
				choiceRuleIndex: 0,
				choiceStateIndex: 0,
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				storeId: uni.getStorageSync("storeId") || null,
				approval: {},
				allowApprovalList: [],
				approvalList: [],
				thisWeek: true,
				agentStoreId: null,
				approvalId: null
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 切换周范围显示
			changeWeek() {
				this.thisWeek = !this.thisWeek
				let msg = this.thisWeek ? '当前只显示本周审批记录' : '当前已显示所有审批记录'
				this.$refs.uNotify.warning(msg)
				this.listServiceScoreRuleApproval()
			},
			// 变更页面状态
			changeType() {
				if (this.pageType == 0) {
					this.pageType = 1
					this.approval = {}
					this.$refs.uNotify.success('开始填写加分审批项吧！')
				} else {
					this.pageType = 0
				}
			},
			clickTabs(e) {
				this.choiceIndex = e.index
				this.listServiceScoreRuleApproval()
			},
			// 选择审批规则
			choiceRule(index) {
				if (this.pageType == 1) {
					this.choiceRuleIndex = index
					this.approval.approvalIntegral = this.allowApprovalList[index].ruleIntegralPresets == 0 ? 1 : 0
				} else if (this.pageType == 2) {
					// 未审批状态下才能更改
					if (this.approval.approvalState == 0) {
						this.choiceRuleIndex = index
						this.$refs.uNotify.success('已变更审批项目，将审批内容补充完整吧！')
					} else {
						this.$refs.uNotify.warning('未审批状态下才能更改！')
					}
				}
			},
			// 打开详情
			openDetail(index) {
				this.pageType = 2
				this.approval = this.approvalList[index]
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			uploadImg(index) {
				let itemName = index != 0 ? 'approvalImg' + index : 'approvalImg'
				if (this.pageType == 3) {
					this.openImgPreview(this.approval[itemName])
					return
				}
				if (this.approval.approvalState) {
					this.openImgPreview(this.approval[itemName])
					// this.$refs.uNotify.warning("暂时无法上传！")
					return
				}
				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								this.$set(this.approval, itemName, result.data)
							}
						});
					}
				});
			},
			// 上传文件
			uploadFile(e) {
				if (this.pageType == 3) {
					return
				}
				if (this.approval.approvalState) {
					return this.$refs.uNotify.warning("暂时无法上传！")
				}
				const tempFilePaths = e.tempFilePaths;
				uni.uploadFile({
					url: 'https://api.xiaoyujia.com/system/uploadFile',
					filePath: tempFilePaths[0],
					name: 'file',
					success: res => {
						let data = JSON.parse(res.data)
						if (data.code == 0) {
							this.$refs.uNotify.success("视频上传成功！")
							this.$set(this.approval, 'approvalVideo',
								data.data)
						} else {
							this.$refs.uNotify.error("视频上传失败！" + data.msg)
						}
					},
					fail: res => {
						this.$refs.uNotify.error("视频上传失败！" + data.msg)
					}
				});
			},
			getScoreInfo() {
				this.http({
					url: 'getAgentStoreInfo',
					data: {
						employeeId: this.employeeId
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.agentStoreId = res.data.id
						}
					}
				})
			},
			// 获取门店本月可进行审批的服务分规则项
			listAllowApprovalRule() {
				this.http({
					url: 'listAllowApprovalRule',
					method: 'GET',
					hideLoading: true,
					path: this.storeId,
					success: res => {
						if (res.code == 0) {
							this.allowApprovalList = res.data
						} else {
							this.allowApprovalList = []
						}
					},
				})
			},
			// 获取审批记录列表
			listServiceScoreRuleApproval() {
				this.http({
					url: 'listServiceScoreRuleApproval',
					method: 'POST',
					hideLoading: true,
					data: {
						storeId: this.storeId || 0,
						approvalState: this.tabslist[this.choiceIndex].type,
						thisWeek: this.thisWeek ? 1 : null
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.approvalList = res.data
						} else {
							this.approvalList = []
						}
					},
				})
			},
			getServiceScoreRuleApprovalById() {
				this.http({
					url: 'getServiceScoreRuleApprovalById',
					method: 'GET',
					hideLoading: true,
					path: this.approvalId,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.approval = res.data
						}
					},
				})
			},
			// 校验审批内容
			checkContent() {
				if (!this.allowApprovalList.length) {
					this.$refs.uNotify.warning('暂时没有可提交的审批项目！')
					return false
				}
				if (!this.approval.approvalContent) {
					this.$refs.uNotify.warning('请填写您的审批理由！')
					return false
				} else if (!this.approval.approvalImg && !this.approval.approvalImg1 && !this.approval.approvalImg2 && !
					this
					.approval.approvalVideo) {
					this.$refs.uNotify.warning('至少提交一项图片/视频素材哦！')
					return false
				}
				return true
			},
			// 更新审批
			update() {
				if (this.approval.approvalState == 1) {
					return this.$refs.uNotify.warning('该审批未通过！请返回后重新提交！')
				} else if (this.approval.approvalState == 2) {
					return this.$refs.uNotify.warning('该审批已通！无法进行修改！')
				} else if (!this.checkContent()) {
					return
				} else {
					uni.showModal({
						title: '确定修改审批材料吗？',
						content: '请确认审批材料与【审批要求】相符合，否则将无法通过！将重新提交材料！',
						success: res => {
							if (res.confirm) {
								this.$set(this.approval, 'ruleId', this.allowApprovalList[this.choiceRuleIndex]
									.id)
								this.http({
									outsideUrl: 'https://biapi.xiaoyujia.com/serviceScore/updateServiceScoreRuleApproval',
									method: 'POST',
									data: this.approval,
									header: {
										'content-type': 'application/json;charset=UTF-8'
									},
									success: res => {
										if (res.status == 200) {
											this.approval = {}
											this.$refs.uNotify.success('审批材料修改已提交！')
											this.listServiceScoreRuleApproval()
											this.pageType = 0
										} else {
											return this.$refs.uNotify.warning(res.msg)
										}
									},
								})
							}
						}
					});
				}
			},
			// 保存审批
			save() {
				if (!this.checkContent()) {
					return
				}
				uni.showModal({
					title: '确定提交审批吗？',
					content: '请确认审批材料与【审批要求】相符合，否则将无法通过！',
					success: res => {
						if (res.confirm) {
							this.insertServiceScoreRuleApproval()
						}
					}
				});
			},
			// 确认审批结果
			confirm() {
				let state = this.stateList[this.choiceStateIndex].id
				let id = uni.getStorageSync('employeeId') || null
				if (!id) {
					return this.$refs.uNotify.warning('未登录状态下无法确认审批结果！')
				}
				if (state == 1 && !this.approval.approvalRemark) {
					return this.$refs.uNotify.warning('请补充不通过原因！')
				}
				uni.showModal({
					title: '确定该审批结果吗？',
					content: '确认后将无法进行更改，若审批通过加分将立即生效！请仔细核对审批材料！',
					success: res => {
						if (res.confirm) {
							this.$set(this.approval, 'approvalState', state)
							this.$set(this.approval, 'approvalor', id)
							this.http({
								outsideUrl: 'https://biapi.xiaoyujia.com/serviceScore/updateServiceScoreRuleApproval',
								method: 'POST',
								data: this.approval,
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								success: res => {
									if (res.status == 200) {
										this.$refs.uNotify.success('审批结果已确认！')
										this.updateServiceScore()
										this.getServiceScoreRuleApprovalById()
									} else {
										return this.$refs.uNotify.warning(res.msg)
									}
								},
							})
						}
					}
				});

			},
			// 更新员工服务分
			updateServiceScore() {
				if (!this.approval.agentStoreId || this.approval.approvalState != 2) {
					return
				}
				this.http({
					url: 'updateServiceScore',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.approval.agentStoreId
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {

					},
				})
			},
			// 添加审批
			insertServiceScoreRuleApproval() {
				this.$set(this.approval, 'storeId', this.storeId)
				this.$set(this.approval, 'employeeId', this.employeeId)
				this.$set(this.approval, 'ruleId', this.allowApprovalList[this.choiceRuleIndex].id)
				this.$set(this.approval, 'agentStoreId', this.agentStoreId)
				this.http({
					url: 'insertServiceScoreRuleApproval',
					method: 'POST',
					data: this.approval,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.approval = {}
							this.$refs.uNotify.warning('加分审批提交成功！请等待审核！')
							this.listServiceScoreRuleApproval()
							this.pageType = 0
						} else {
							return this.$refs.uNotify.warning(res.msg)
						}
					},
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.$refs.uNotify.success('xxx')
					this.$refs.uNotify.error('xxx')
					this.$refs.uNotify.warning('xxx')
					setTimeout(() => {}, 800);
				}
			},
		},
		onLoad(options) {
			this.approvalId = options.approvalId || null
			this.storeId = options.storeId || this.storeId
			if (this.approvalId) {
				this.pageType = 3
				this.getServiceScoreRuleApprovalById()
			}
			this.getScoreInfo()
			this.listAllowApprovalRule()
			this.listServiceScoreRuleApproval()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	// 选项
	.tab-choice {
		width: 100%;
		line-height: 80rpx;
		display: flex;
		padding: 0 20rpx;

		text {
			font-size: 36rpx;
			margin: 0 5rpx;
		}

		text:nth-child(1) {
			margin: 0 5rpx 0 20rpx;
		}
	}
</style>