<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 入驻类型选择菜单 -->
		<view v-if="showTypeMenu&&shareType==0">
			<view class="f20 fb lsp10 text-c lh80">请选择邀请员工类型</view>
			<view class="w9 bacf radius10" style="margin: 30rpx auto;" v-for="(item,index) in typeList" :key="i">
				<view class="w8 mg-at flac-row-b" style="padding: 40rpx 0;" @click="choiceItem(index)">
					<u-icon size="45" :name="item.icon" />
					<view class="f16 fb w6 lh30">
						{{item.title}}
					</view>
					<u-icon size="20" name="arrow-right" />
				</view>
			</view>

			<view class="f16 fb text-r lh20" style="margin-right: 40rpx;color:deepskyblue" @click="openChat">客服咨询
			</view>
		</view>

		<view v-if="showTypeMenu&&shareType==2">
			<view class="f20 fb lsp10 text-c lh80">请选择想要咨询的服务</view>
			<view class="w9 bacf radius10" style="margin: 30rpx auto;" v-for="(item,index) in typeList1" :key="i">
				<view class="w8 mg-at flac-row-b" style="padding: 40rpx 0;" @click="choiceItem(index)">
					<u-icon size="45" :name="item.icon" />
					<view class="f16 fb w6 lh30">
						{{item.title}}
					</view>
					<u-icon size="20" name="arrow-right" />
				</view>
			</view>
		</view>

		<view v-if="!showTypeMenu||shareType==1">
			<view class="img-view">
				<img :src="invitationImg" alt="" class="invitation-img" mode="widthFix" />
			</view>

			<view class="tips" @click="openTips()">
				<text>* {{tips[shareType]}}</text>
				<text v-if="shareType==1">仅能修改简历，不作开发人/门店变更！</text>
			</view>

			<view class="btn-group">
				<button @click="saveToPhone()" style="background-color: #fff;color: #1e1848">保 存</button>
				<button @click="share()" open-type='share'>分 享</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				choiceIndex: 1,
				showTypeMenu: true,
				memberId: uni.getStorageSync("memberId"),
				employeeId: uni.getStorageSync("employeeId") || 0,
				shareImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_register_4.png",
				shareImg1: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_bindreward.png",
				invitationImgDefault: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664524431393invitation-img.png",
				// 保姆月嫂入驻邀请图
				qrImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_%E4%BF%9D%E5%A7%86%E6%9C%88%E5%AB%82%E8%82%B2%E5%84%BF%E5%AB%82%E5%85%A5%E9%A9%BB.png',
				// 标准员工入驻邀请图
				qrImg1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_%E4%BF%9D%E6%B4%81%E6%90%AC%E5%AE%B6%E7%BB%B4%E4%BF%AE%E5%85%A5%E9%A9%BB.png',
				qrImg2: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_%E5%AE%A2%E6%9C%8D%E5%92%A8%E8%AF%A2.png',
				qrImg3: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_%E8%BD%A6%E8%BE%86%E5%8F%B8%E6%9C%BA%E5%85%A5%E9%A9%BB.png',
				invitationImg: "",
				shareType: 0,
				tips: [
					"分享小程序，立即邀约入驻！",
					"分享小程序，邀约绑定经济人！",
					"分享小程序，邀约咨询客服！",
				],
				tipsText: [
					"可选择保存二维码或点击分享按钮进行邀约，邀约成功后，可在保姆-开发栏目查看员工",
					"可选择保存二维码或点击分享按钮进行邀约，邀约成功后，可在保姆-门店栏目查看员工",
					"可分享小程序，邀约员工进行客服咨询",
				],
				typeList: [{
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_invitation_1.png',
						title: '保洁搬家维修',
					},
					{
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_invitation_2.png',
						title: '保姆月嫂育儿嫂',
					},
					{
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_invitation_4.png',
						title: '车辆司机',
					},
				],
				typeList1: [{
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_invitation_3.png',
					title: '课程培训咨询',
				}, ]
			}
		},
		methods: {
			openChat() {
				this.shareType = 2
			},
			choiceItem(index) {
				this.choiceIndex = index
				this.showTypeMenu = false
				this.getShareImg()
			},
			// 分享
			share() {
				this.$refs.uNotify.success(this.tips[this.shareType])
			},
			openTips() {
				this.openCheck(-1, '分享后获得更多权益哦', this.tipsText[this.shareType])
			},
			// 保存图片到手机
			saveToPhone() {
				console.log("想要保存的图片地址：", this.invitationImg)
				uni.downloadFile({
					url: this.invitationImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			// 获取分享图片
			getShareImg() {
				// 获取邀请人会员id和员工id，双重保险
				let scene = "id/" + uni.getStorageSync("memberId") + "*id1/" + this.employeeId
				let backImg = this.qrImg
				if (this.choiceIndex == 0) {
					scene += "*t/1"
					backImg = this.qrImg1
				} else if (this.choiceIndex == 2) {
					scene += "*t/2"
					backImg = this.qrImg3
				}
				let param = {
					source: "aygj",
					path: "pages-mine/resume/resume-simplify",
					type: 1,
					scene: scene,
					title: "小羽佳-家姐联盟",
					backImg: backImg
				}
				if (this.shareType == 1) {
					scene = "id/" + uni.getStorageSync("employeeNo")
					param = {
						source: "aygj",
						path: "pages-mine/invitation/reward",
						type: 1,
						scene: scene,
						title: "小羽佳-家姐联盟",
						backImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_%E7%BB%91%E5%AE%9A%E5%AE%B6%E6%94%BF%E7%BB%8F%E7%BA%AA%E4%BA%BA.png'
					}
				} else if (this.shareType == 2) {
					scene = "t/1"
					let path = "pages-mine/resume/resume-simplify"
					let source = "aygj"
					if (this.choiceIndex == 0) {
						path = "pages-mine/index/course-wish"
						source = 'xyjcourse'
					}
					param = {
						source: source,
						path: path,
						type: 1,
						scene: scene,
						title: '客服咨询',
						backImg: this.qrImg2
					}
				}
				// 请求：获取分享图片
				this.http({
					url: 'getEmployeePoster',
					method: 'POST',
					hideLoading: true,
					data: param,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.invitationImg = res.data
							console.log("获取分享图片-成功！")
						} else {
							console.log("获取分享图片-返回失败！")
						}
					},
					fail: err => {
						console.log("获取分享图片-请求失败！" + res)
					}
				})
			},
			// 分享到好友
			onShareAppMessage(res) {
				if (res.from == 'menu') {
					console.log("开始分享邀请图片！")
				}

				let title = '保姆月嫂育儿嫂...总有喜欢你的客户'
				let scene = "id/" + uni.getStorageSync("memberId") + "*id1/" + this.employeeId
				// 标准员工入驻
				if (this.choiceIndex == 0) {
					scene += "*t/1"
					title = '水电搬家保洁收纳...总有喜欢你的客户'
				}
				let path = "/pages-mine/resume/resume-simplify?scene=" + scene

				let shareImg = this.shareImg
				if (this.shareType == 1) {
					scene = "id/" + uni.getStorageSync("employeeNo")
					path = "/pages-mine/invitation/reward?scene=" + scene
					title = '绑定家政经纪人，快速找到好工作！'
					shareImg = this.shareImg1
				}

				let data = {
					title: title,
					path: path,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: shareImg
				}
				return data
			},
			//分享到朋友圈
			onShareTimeline(res) {
				return {
					title: '多种岗位等你来~',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
		},
		watch: {
			shareType: {
				handler(newValue, oldVal) {
					if (this.shareType == 1) {
						uni.setNavigationBarTitle({
							title: "家政经纪人绑定"
						})
					} else if (this.shareType == 2) {
						uni.setNavigationBarTitle({
							title: "客服咨询"
						})
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.shareType = obj.t || this.shareType
			}
			this.shareType = options.shareType || this.shareType
			if (this.shareType == 1) {
				this.getShareImg()
			}
		},
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.img-view {
		width: 100%;
		height: auto;
	}

	.invitation-img {
		display: block;
		width: 100%;
		height: 100%;
	}

	.tips {
		line-height: 50rpx;
		text-align: right;
		margin: 20rpx 40rpx;

		text {
			display: block;
			font-size: 32rpx;
			color: #909399;
		}
	}

	// 按钮组
	.btn-group {
		width: 100%;
		height: 90rpx;
		display: flex;
		flex-direction: row;
		padding: 50rpx 0 200rpx 0;

		button {
			width: 40%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border: 2rpx solid #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>