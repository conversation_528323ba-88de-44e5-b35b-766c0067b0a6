<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="取消" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 已有的内容-日期选择器 -->
		<view v-for="(work, index) in baomuWorkExperienceList" :key="index">
			<view>
				<u-datetime-picker :show="work.show" v-model="work.startWorkTime" mode="date"
					@cancel="showPicker(0,false,index)" @confirm="confirmState(index)" :maxDate="maxDate"
					:minDate="minDate">
				</u-datetime-picker>
			</view>
			<view>
				<u-datetime-picker :show="work.show1" v-model="work.endWorkTime" mode="date"
					@cancel="showPicker(1,false,index)" @confirm="confirmState(index)" :maxDate="maxDate"
					:minDate="minDate">
				</u-datetime-picker>
			</view>
			<view>
				<u-picker :show="work.show2" @cancel="showPicker(2,false,index)" @confirm="confirmState(index)"
					:columns="formatWorkTypeChoiceList(workTypeList)" keyName="urgentType" @change="changeHandler">
				</u-picker>
			</view>
		</view>

		<!-- 添加的内容-日期选择器 -->
		<view>
			<view>
				<u-datetime-picker :show="show" v-model="startWorkTime" mode="date" @cancel="show = false"
					@confirm="confirmState(-1)" :maxDate="maxDate" :minDate="minDate"></u-datetime-picker>
			</view>
			<view>
				<u-datetime-picker :show="show1" v-model="endWorkTime" mode="date" @cancel="show1 = false"
					@confirm="confirmState(-1)" :maxDate="maxDate" :minDate="minDate"></u-datetime-picker>
			</view>
		</view>

		<view class="tab-tips" :style="isGetScore?'background-color: #19be6b;':'background-color: #1e1848;'"
			v-if="!showTips">
			<text>{{isGetScore?"工作经历填写完整，已获得5分":"工作内容填写20字以上可以获得5分哦～"}}</text>
		</view>

		<view class="tab-tips" style="background-color: #19be6b;" v-if="showTips">
			<text>当前正在完善【{{employeeName}}】的工作经历</text>
		</view>
		<u-gap height="38"></u-gap>

		<!-- 工作经验-已有的内容 -->
		<view v-if="exState==0">
			<view v-for="(work, index) in baomuWorkExperienceList" :key="index"
				v-if="choiceItemIndex==-1||choiceItemIndex==index">
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>工作经历{{index+1}}</text>
							<uni-icons class="picker-text-arrow" type="clear" size="26"
								style="float: right;line-height: 120rpx;" @click="tryDelete(index)"></uni-icons>
						</view>
						<view class="tab-head-smail"><text>工作类型</text></view>
						<view class="tab-picker" @click="showPicker(2,true,index)">
							<text class="picker-text" v-if="!work.workType">点击选择工作类型</text>
							<text class="picker-text" v-else>{{ formatExWorkType(work.workType) }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>

						<view class="tab-head-smail"><text>工作开始时间</text></view>
						<view class="tab-picker" @click="showPicker(0,true,index)">
							<text class="picker-text" v-if="nowDate == work.startWorkTime">点击选择开始时间</text>
							<text class="picker-text"
								v-if="nowDate !== work.startWorkTime">{{ formatDate(work.startWorkTime) }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
						<view class="tab-head-smail"><text>工作结束时间</text></view>
						<view class="tab-picker" @click="showPicker(1,true,index)">
							<text class="picker-text" v-if="nowDate == work.endWorkTime">点击选择开始时间</text>
							<text class="picker-text"
								v-if="nowDate !== work.endWorkTime">{{ formatDate(work.endWorkTime) }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</view>
				</view>

				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您这份工作的內容</text></view>
						<view class="tab-tips-small"><text class="f16">推荐：在XX城市，负责客户家的XX工作...</text></view>
						<view class="tab-inputbox-high">
							<u--textarea class="multiline-input" confirmType="done" maxlength="200"
								v-model="work.workContent" :placeholder="workContentTips" height="150" count
								@change="changeNum++"></u--textarea>
						</view>
					</view>
				</view>
			</view>

			<view v-if="baomuWorkExperienceList.length==0">
				<u-empty text="暂无工作经历" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</view>

			<!-- 保存按钮 -->
			<view class="flac-col" style="padding-top: 40rpx;">
				<view class="btn-big" v-if="baomuWorkExperienceList.length!=0">
					<button @click="trySave()" style="margin: 0rpx auto"
						:style="isShowSave?'background-color:#1e1848;':'background-color:#909399;color:#fff'">保存</button>
				</view>
				<view class="btn-big">
					<button style="margin: 0rpx auto;" @click="openAdd()">添加</button>
				</view>
			</view>
		</view>

		<!-- 工作经历-添加的内容 -->
		<view v-if="exState==1">
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的工作类型？</text>
						<text>（尽量为家政工作哦）</text>
					</view>
					<view class="tab-checkbox" v-for="(tabList, index) in workTypeList" :key="index"
						v-if="showWorkType(index)">
						<view class="checkbox" :class="{ activeBox: tabList.value == choiceWorkType }">
							<text v-model="tabList.value" @click="choiceTab(index)">{{ tabList.urgentType }}</text>
						</view>
					</view>
				</view>
			</view>

			<view v-if="choiceWorkType!==0?true:false">
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>工作类型：{{formatExWorkType(choiceWorkType)}}</text>
						</view>
						<view class="tab-head-smail"><text>工作开始时间</text></view>
						<view class="tab-picker" @click="show = true">
							<text class="picker-text" v-if="nowDate == startWorkTime">点击选择开始时间</text>
							<text class="picker-text"
								v-if="nowDate !== startWorkTime">{{ formatDate(startWorkTime) }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
						<view class="tab-head-smail"><text>工作结束时间</text></view>
						<view class="tab-picker" @click="show1 = true">
							<text class="picker-text" v-if="nowDate == endWorkTime">点击选择开始时间</text>
							<text class="picker-text"
								v-if="nowDate !== endWorkTime">{{ formatDate(endWorkTime) }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</view>
				</view>

				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您这份工作的內容</text></view>
						<view class="tab-inputbox-high">
							<u--textarea class="multiline-input" confirmType="done" maxlength="200"
								v-model="workContent" :placeholder="workContentTips" height="150" count></u--textarea>
						</view>
					</view>
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="btn-big">
				<button @click="tryAdd()"
					:style="choiceWorkType!==0?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">添
					加</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 最少填写字数
				minWords: 15,
				// 日期可选择最小值
				minDate: Number(new Date().setYear(new Date().getFullYear() - 20)),
				// 日期可选择最大值
				maxDate: Number(new Date().setYear(new Date().getFullYear() + 2)),
				// 工作经历状态（0：编辑经历 1：添加经历）
				exState: 0,
				// 选中的工作经历
				choiceItemIndex: -1,
				choiceItemIndex1: 0,
				// 自动保存时间间隔
				autoSaveTime: 7,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				isGetScore: false,
				scrollTop: 0,
				memberId: uni.getStorageSync('memberId') || 0,
				baomuId: null,
				employeeName: '',
				changeNum: 0,
				choiceWorkType: 0,
				allowBackPress: true,
				deleteIndex: 0,
				show: false,
				show1: false,
				show2: false,
				isShowSave: false,
				showTips: false,
				startWorkTime: Number(new Date()),
				endWorkTime: Number(new Date()),
				nowDate: Number(new Date()),
				certificateDate: Number(new Date()),
				workContentTips: '请描述这段工作经历您具体在什么地点、做了多长时间、具体工作内容、表现突出的地方',
				workContent: '',
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				workTypeList: [{
						urgentType: '保姆',
						workContentTips: '如：从事保姆工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 10
					}, {
						urgentType: '单餐阿姨',
						workContentTips: '如：从事单餐阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 12
					},
					{
						urgentType: '白班阿姨',
						workContentTips: '如：从事白班阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 15
					},
					{
						urgentType: '住家阿姨',
						workContentTips: '如：从事住家阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 18
					},
					{
						urgentType: '月嫂',
						workContentTips: '如：从事月嫂工作XX年， 服务过XX个宝宝，服务过几对双胞胎，服务过几个早产儿，月嫂经验丰富，有爱心，主要照顾产妇和新生儿的生活起居。',
						value: 20
					},
					{
						urgentType: '白班育儿嫂',
						workContentTips: '如：从事家政工作XX年，最长一家做了XX年，哪年开始在哪个小区带多大带的宝宝（带睡/不带睡），哪年还在哪个小区带多大的宝宝（带睡/不带睡），上一家在哪里做带多大的宝宝。阿姨性格随和，喜欢宝宝，责任感等。',
						value: 25
					},
					{
						urgentType: '住家育儿嫂',
						workContentTips: '如：从事家政工作XX年，最长一家做了XX年，哪年开始在哪个小区带多大带的宝宝（带睡/不带睡），哪年还在哪个小区带多大的宝宝（带睡/不带睡），上一家在哪里做带多大的宝宝。阿姨性格随和，喜欢宝宝，责任感等。',
						value: 28
					},
					{
						urgentType: '育婴师',
						workContentTips: '如：从事育婴师工作XX年， 服务过XX个宝宝，服务过几对双胞胎，服务过几个早产儿，育婴师经验丰富，有爱心，主要照顾产妇和新生儿的生活起居。',
						value: 30
					},
					{
						urgentType: '护工',
						workContentTips: '如：从事护工工作XX年，最长一家/上一家做了XX年，在哪个（小区或医院）照顾什么样的老人（自理/半自理/不自理）。',
						value: 40
					},
					{
						urgentType: '陪读师',
						workContentTips: '如：从事陪读师工作XX年，最长一家/上一家做了XX年，帮助x岁小孩进行学习。',
						value: 45
					},
					{
						urgentType: '保洁师',
						workContentTips: '如：从事保洁师工作XX年，最长一家/上一家做了XX年，在哪个（小区或医院）进行xx打扫工作。',
						value: 50
					},
					{
						urgentType: '收纳师',
						workContentTips: '如：从事收纳师工作XX年，有xx工作经验，会衣物/橱柜收纳等。',
						value: 55
					},
					{
						urgentType: '搬家师',
						workContentTips: '如：从事搬家师工作XX年，有xx工作经验，从事过哪些大型搬家服务等。',
						value: 60
					},
					{
						urgentType: '清洗师',
						workContentTips: '如：从事清洗师工作XX年，有xx工作经验，从事过哪些清洗服务等。',
						value: 70
					},
					{
						urgentType: '维修师',
						workContentTips: '如：从事维修师工作XX年，有xx工作经验，从事过哪些维修服务等。',
						value: 80
					},
					{
						urgentType: '疏通师',
						workContentTips: '如：从事疏通师工作XX年，有xx工作经验，从事过哪些疏通服务等。',
						value: 90
					},
					{
						urgentType: '钟点工',
						workContentTips: '如：从事钟点工工作XX年，有xx工作经验，从事过哪些钟点服务等。',
						value: 95
					},
					{
						urgentType: '其他',
						workContentTips: '请描述这段工作经历您具体在什么地点、做了多长时间、具体工作内容、表现突出的地方',
						value: 100
					}
				],
				baomuWorkExperience: {
					baomuId: this.baomuId,
					imgs: null,
					startWorkTime: null,
					endWorkTime: null,
					workContent: '',
					remark: null,
					workType: null
				},
				baomuWorkExperienceList: [],
				saveTime: 0,
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选中多选框
			choiceTab(index) {
				if (this.choiceWorkType !== 0 && (index + 1) * 10 == this.choiceWorkType) {
					this.choiceWorkType = 0
				} else {
					this.choiceWorkType = this.workTypeList[index].value
				}
				this.nowDate = Number(new Date())
				this.startWorkTime = Number(new Date())
				this.endWorkTime = Number(new Date())
				this.workContent = ""
				// this.workContent = this.workTypeList[index].workContentTips
				this.workContentTips = this.workTypeList[index].workContentTips
			},
			formatWorkTypeChoiceList() {
				let data = []
				data.push(this.workTypeList)
				return data
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? '0' + MM : MM
				let d = date.getDate()
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d
			},
			formatExWorkType(value) {
				let str = "家政工作"
				this.workTypeList.forEach(item => {
					if (value == item.value) {
						str = item.urgentType
					}
				})
				return str
			},
			//判断两个日期的大小
			checkDate() {
				let result = false
				for (let item of this.baomuWorkExperienceList) {
					let d1 = item.endWorkTime
					let d2 = item.startWorkTime
					let d3 = item.nowDate
					if (new Date(d1).getTime() - new Date(d2).getTime() > 0) {
						result = true
					} else {
						this.$refs.uNotify.error('结束时间应该在开始时间之后哦～');
						return false
					}
					if (new Date(d1).getTime() - new Date(d3).getTime() > 0 || new Date(d2).getTime() - new Date(d3)
						.getTime() > 0) {
						this.$refs.uNotify.error('选择的时间不能超过当前时间哦～');
						return false
					}
					if (d1 == d2 || d2 == d3) {
						this.$refs.uNotify.error('请将工作时间补充完整哦～');
						return false
					}
				}
				return result
			},
			checkDate1() {
				let result = false
				let d1 = this.endWorkTime
				let d2 = this.startWorkTime
				let d3 = this.nowDate
				if (new Date(d1).getTime() - new Date(d2).getTime() > 0) {
					result = true
				} else {
					this.$refs.uNotify.error('结束时间应该在开始时间之后哦～');
					result = false
				}
				if (new Date(d1).getTime() - new Date(d3).getTime() > 0 || new Date(d2).getTime() - new Date(d3)
					.getTime() > 0) {
					this.$refs.uNotify.error('选择的时间不能超过当前时间哦～');
					result = false
				}
				if (d1 == d2 || d2 == d3) {
					this.$refs.uNotify.error('请将工作时间补充完整哦～');
					result = false
				}
				return result
			},
			// 显示日期选择器
			showPicker(value, show, index) {
				if (value == 0) {
					this.baomuWorkExperienceList[index].show = show
				} else if (value == 1) {
					this.baomuWorkExperienceList[index].show1 = show
				} else {
					this.baomuWorkExperienceList[index].show2 = show
				}
				this.choiceItemIndex1 = index
			},
			showWorkType(index) {
				let result = true
				if (index == 0) {
					result = false
				}
				return result
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.baomuWorkExperienceList[this.choiceItemIndex1].workType = this.workTypeList[index].value
			},
			// 确认日期选择
			confirmState(index) {
				if (index == -1) {
					this.show = false
					this.show1 = false
					this.show2 = false
				} else {
					this.baomuWorkExperienceList[index].show = false
					this.baomuWorkExperienceList[index].show1 = false
					this.baomuWorkExperienceList[index].show2 = false
				}
			},
			openAdd() {
				this.exState = 1
				this.$refs.uNotify.success("请填写你的工作经历吧～")
			},
			tryAdd() {
				if (this.choiceWorkType !== 0) {
					this.openCheck(2, '确定添加工作经历？', '内容越贴近实际越好哦～')
				} else {
					this.$refs.uNotify.error("请先选择工作类型！")
				}

			},
			// 尝试删除
			tryDelete(index) {
				this.deleteIndex = index
				this.openCheck(3, '确定删除这段工作经历？', '删除后将无法恢复！');
			},
			// 尝试保存
			trySave() {
				if (this.baomuWorkExperienceList.length !== 0) {
					this.openCheck(0, '确定保存工作经历？', '内容越贴近实际越好哦～');
				} else {
					this.$refs.uNotify.error("请先添加至少一份工作记录！")
				}
			},
			// 保存工作经历内容
			save() {
				if (this.choiceWorkType == 0) {
					this.checkBaomuAndInit()
					if (!this.baomuId) {
						let timer = setTimeout(() => {
							if (this.baomuId) {
								this.updateWorkExperience(0)
							}
						}, 1500);
					} else {
						this.updateWorkExperience(0)
					}
					return
				}

				if (!this.workContent) {
					this.$refs.uNotify.error('工作内容不能为空哦～')
				} else if (this.workContent.length < this.minWords) {
					this.$refs.uNotify.error('工作内容必须大于' + this.minWords + '个字！')
				} else if (this.checkDate()) {
					this.checkBaomuAndInit()
					if (!this.baomuId) {
						let timer = setTimeout(() => {
							if (this.baomuId) {
								this.updateWorkExperience(0)
							}
						}, 1500);
					} else {
						this.updateWorkExperience(0)
					}
				}
			},
			add() {
				console.log('尝试添加工作经历内容！');
				// 手机号校验
				if (!this.workContent) {
					this.$refs.uNotify.error('工作内容不能为空哦～');
				} else if (this.workContent.length <= this.minWords) {
					this.$refs.uNotify.error('工作内容必须大于' + this.minWords + '个字！');
				} else if (this.checkDate1()) {
					this.checkBaomuAndInit()
					if (this.baomuId == null) {
						let timer = setTimeout(() => {
							if (this.baomuId !== null) {
								this.addWorkExperience()
							}
						}, 1500);
					} else {
						this.addWorkExperience()
					}
				}
			},
			delete() {
				this.http({
					url: 'deleteBaomuWorkExperience',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuWorkExperienceList[this.deleteIndex],
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('工作经历删除成功！')
							this.choiceItemIndex = -1
							this.getBaomuDetail()
						} else {
							this.$refs.uNotify.error('工作经历删除失败！' + res.msg)
						}
					}
				});
			},
			// 添加工作经历
			addWorkExperience() {
				this.baomuWorkExperience.baomuId = this.baomuId
				this.baomuWorkExperience.startWorkTime = this.startWorkTime
				this.baomuWorkExperience.endWorkTime = this.endWorkTime
				this.baomuWorkExperience.workContent = this.workContent
				this.baomuWorkExperience.workType = this.choiceWorkType
				this.http({
					url: 'addBaomuWorkExperience',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuWorkExperience,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.$refs.uNotify.success('工作经历添加成功！')
								this.changeNum = 0
								this.nowDate = Number(new Date())
								this.startWorkTime = Number(new Date())
								this.endWorkTime = Number(new Date())
								this.workContent = ""
								this.choiceWorkType = 0
								this.exState = 0
								this.choiceItemIndex = -1
								this.getBaomuDetail()
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					}
				});
			},
			// 更新工作经历
			updateWorkExperience(value) {
				this.http({
					url: 'updateBaomuWorkExperience',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuWorkExperienceList,
					success: res => {
						if (value == 1) {
							return
						}
						if (res.code == 0) {
							if (res.data) {
								uni.setStorageSync("isUpdateResume", true)
								this.changeNum = 0
								this.$refs.uNotify.success('工作经历保存成功！')
								let timer = setTimeout(() => {
									return uni.navigateBack()
								}, 1500);
							} else {
								this.$refs.uNotify.error('工作经历保存失败-返回错误！')
							}
						} else {
							this.$refs.uNotify.error('保存失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				// 请求：通过会员ID获取保姆关联信息
				if (this.baomuId === null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId;
								uni.setStorageSync('baomuId', this.baomuId);
								console.log('通过会员ID获取保姆关联信息-成功！');
								console.log('初始化的保姆ID为' + this.baomuId);
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						}
					});
				}
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								this.baomuDetail = baomuDetail
								this.employeeName = baomuDetail.employee.realName
								this.baomuWorkExperienceList = baomuDetail.baomuWorkExperience

								if (this.baomuWorkExperienceList.length == 0) {
									console.log("工作经历为空！")
									this.isShowSave = false
								} else {
									this.isShowSave = true
									this.startWorkTime = this.baomuWorkExperienceList[0].startWorkTime !=
										null ? this.baomuWorkExperienceList[0].startWorkTime : this.nowDate
									this.endWorkTime = this.baomuWorkExperienceList[0].endWorkTime != null ?
										this.baomuWorkExperienceList[0].endWorkTime : this.nowDate
									this.workContent = this.baomuWorkExperienceList[0].workContent !== null ?
										this.baomuWorkExperienceList[0].workContent : ''
								}
								for (let item of this.baomuWorkExperienceList) {
									this.$set(item, 'show', false)
									this.$set(item, 'show1', false)
									this.$set(item, 'show2', false)
								}
							} else {
								this.baomuWorkExperienceList = []
							}
						}
					});
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：确认不保存退出 1：确认保存
				if (this.checkType == 0) {
					this.save()
				} else if (this.checkType == 1) {
					return history.back()
				} else if (this.checkType == 2) {
					this.add()
				} else if (this.checkType == 3) {
					this.delete()
				}
			},
		},
		watch: {
			workContent: {
				handler(newValue, oldVal) {
					// 更改次数+1
					this.changeNum = this.changeNum + 1;
				},
				deep: true
			},
			startWorkTime: {
				handler(newValue, oldVal) {
					this.changeNum = this.changeNum + 1;
				},
				deep: true
			},
			endWorkTime: {
				handler(newValue, oldVal) {
					this.changeNum = this.changeNum + 1;
				},
				deep: true
			},
			saveTime: {
				handler(newValue, oldVal) {
					if (this.saveTime != 0 && this.saveTime % this.autoSaveTime == 0 && this.baomuId && this.exState ==
						0) {
						// this.updateWorkExperience(1)
					}
				},
				deep: true
			},
		},
		// 返回时校验
		onBackPress(e) {
			this.allowBackPress = false
			// 判断是否修改
			if (this.changeNum > 3) {
				this.openCheck(1, '确认返回吗？', '更改内容还未保存！');
			} else {
				this.allowBackPress = true
			}
			return !this.allowBackPress
		},
		onLoad(options) {
			this.isGetScore = options.isGetScore == "true" ? true : false
			this.baomuId = options.baomuId ? JSON.parse(options.baomuId) : uni.getStorageSync('baomuId') || null
			this.showTips = options.showTips == 1 ? true : false
			this.choiceItemIndex = options.index || -1
			this.getBaomuDetail()
			setInterval(() => {
				this.saveTime += 1
			}, 1000);
		},
	};
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.resume-tab {
		margin-bottom: 40rpx;
	}
</style>