<template>
	<view>
		<u-sticky bgColor="#1e1848">
			<view class="w9 u-page-part1">
				<u-search v-model="param.name" :showAction="true" :actionStyle="actionStyle" actionText="搜索" @custom="changseSearch"
					bgColor="#fff"></u-search>
				<u-icon size="25" color="#fff" name="list-dot" @click="showpopup = true"></u-icon>
			</view>
		</u-sticky>
		<u-modal :show="showModal" title="长按识别小程序，进去后转发" @confirm="confirmModal">
			<view>
				<u-image :src="imgCode" height="900rpx"></u-image>
			</view>
		</u-modal>
		<view v-for="(item,index) in dataList" :key="index" class="w95 mg-at">
			<uni-card :title="item.name"
				:subTitle="'使用量: ' + item.useNum">
				<view class="w9 mg-at flex-col img_con">
					<view @click="showImage(item.img)" style="margin: auto;margin-bottom: 20rpx;">
						<u-image :src="item.img" width="300rpx" height="300rpx"></u-image>
					</view>
					<text class="lh24 text-c">{{item.content}}</text>
				</view>
				<view style="display: flex;margin: 20rpx auto;">
					<u-button text="复制文案" @click="saveText(item)" color="#1e1848" plain></u-button>
					<u-button text="复制链接" @click="fuzhi(item)"  color="#1e1848" plain></u-button>
					<u-button text="打开小程序" @click="openxcx(item)" color="#1e1848" customStyle="color:#f6cc70"></u-button>
				</view>
			</uni-card>
		</view>
		<u-loadmore :status="status" :loading-text="loadingText" :loadmore-text="loadmoreText"
			:nomore-text="nomoreText" />
		<u-popup :show="showpopup" :round="10" mode="bottom" @close="closepopup">
			<u-text text="排序方式" align="center"></u-text>
			<u-cell-group>
				<u-cell icon="arrow-right" title="默认排序" @click="orderBy(0)"></u-cell>
				<u-cell icon="arrow-right" title="按创建时间排序" @click="orderBy(1)"></u-cell>
				<!-- <u-cell icon="arrow-right" title="按浏览量排序" @click="orderBy(2)"></u-cell> -->
				<u-cell icon="arrow-right" title="按使用量排序" @click="orderBy(4)"></u-cell>
				<!-- <u-cell icon="arrow-right" title="按订单量排序" @click="orderBy(3)"></u-cell> -->
			</u-cell-group>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
        actionStyle:{color:'#fff'},
				status: 'nomoreText',
				loadingText: '努力加载中',
				loadmoreText: '加载更多',
				nomoreText: '实在没有了',
				showModal: false,
				dataList: [],
				no: uni.getStorageSync('account'),
				imgCode: '',
				eid: uni.getStorageSync('employeeId'),
				showpopup: false,
				param: {
					"type": 1,
					"size": 10,
					"current": 1,
					"dataType": '',
					"dataLabelId": '',
					"orderBy": 0,
					'operatorId': null,
					"name": ''
				},
				pages: ''

			};
		},
		onReachBottom() {
			let curpage = this.param.current;
			if (curpage >= this.pages) {
				return;
			}
			this.param.current = curpage + 1;
			this.http({
				outsideUrl: 'https://biapi.xiaoyujia.com/qiyeData/selectByCondition',
				header: {
					"content-type": "application/json;charset=UTF-8"
				},
				method: 'POST',
				data: this.param,
				success: res => {
					if (res.data.records.length != 0) {
						this.pages = res.data.pages
						res.data.records.forEach(v => {
							this.dataList.push(v)
						})
					}
				},
				fail: err => {
					console.log(res)
				}
			})
		},
		onLoad(option) {
			this.param.dataLabelId = option.id

			if (option.dataType == '0') {
				this.param.dataType = 1;
			} else if (option.dataType == '1') {
				this.param.dataType = 2;
				this.param.operatorId = this.eid
			}
			this.getData();

		},
		methods: {
			closepopup() {
				this.showpopup = false;
			},
			orderBy(index) {
				this.showpopup = false;
				this.param.orderBy = index;
				this.getData();

			},
			changseSearch() {
				if (!this.param.name) {
					return uni.showToast({
						title: '搜索内容不能为空！'
					})
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/qiyeData/selectByCondition',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.param,
					success: res => {
						if (res.data.records.length <= 0) {
							return uni.showToast({
								title: '该栏目下没有内容'
							})
						} else {
							this.dataList = res.data.records;
						}

					},
					fail: err => {
						console.log(res)
					}
				})
			},
			fuzhi(item) {
				let param = {
					'source': item.source,
					'path': item.path,
					'query': item.query.replace('{0}', this.no),
					'creater': this.no
				}
				uni.request({
					url: 'https://api.xiaoyujia.com/openapi/insertQiyeDataLink',
					method: 'POST',
					data: param,
					success: (res) => {
						if (res.data.code == 0) {
							// window.location.href = res.data.data
							let info = res.data.data
							uni.showModal({
								content: info, //模板中提示的内容
								confirmText: '复制内容',
								success: (res) => { //点击复制内容的后调函数
									if (res.confirm) {
										let result
										// #ifndef H5
										//uni.setClipboardData方法就是讲内容复制到粘贴板
										uni.setClipboardData({
											data: info, //要被复制的内容
											success: () => { //复制成功的回调函数
												uni.showToast({ //提示
													title: '复制成功'
												})
											}
										});
										// #endif

										// #ifdef H5 
										let textarea = document.createElement("textarea")
										textarea.value = info
										textarea.readOnly = "readOnly"
										document.body.appendChild(textarea)
										textarea.select() // 选中文本内容
										textarea.setSelectionRange(0, info.length)
										uni.showToast({ //提示
											title: '复制成功'
										})
										result = document.execCommand("copy")
										textarea.remove()
										// #endif
									} else {
										console.log('取消')
									}
								}
							});
						} else {
							uni.showToast({
								title: '链接生成失败',
								icon: 'error'
							})
						}
					}
				})
			},
			openxcx(item) {
				let param = {
					"source": "appminiprogram",
					"path": "pages/content/news",
					"type": "1",
					"scene": "id/" + item.id + "*no/" + this.no,
					"title": "营销文章",
				}
				uni.request({
					url: 'https://api.xiaoyujia.com/product/getEmployeePoster',
					method: 'POST',
					data: param,
					success: (res) => {
						if (res.data.code == 0) {
							this.imgCode = res.data.data;
						} else {
							this.imgCode = '';
							uni.showToast({
								title: '二维码生成错误',
								icon: 'error'
							})
						}
						this.showModal = true;
					}
				})
			},
			confirmModal() {
				this.showModal = false;
			},
			showImage(url) {
				let imgList = [url]
				uni.previewImage({
					current: 0, //预览图片的下标
					urls: imgList //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
				})
			},
			saveText(item) {
				let text = item.content
				uni.setClipboardData({
					data: text, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: '复制成功'
						})
					}
				});

				// 发送请求，增加使用量
				if (item.isTop !== 2) {
					this.http({
						outsideUrl: 'https://biapi.xiaoyujia.com/qiyeData/addUseNum',
						method: 'GET',
						data: {
							id: item.id
						},
						success: res => {
							if (res.status == 200) {
								console.log("使用量增加成功！")
								item.isTop = 2
								item.useNum++
							} else {
								console.log("使用量增加失败！用户频繁点击！")
							}
						}
					})
				}
			},
			getData() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/qiyeData/selectByCondition',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.param,
					success: res => {
						if (res.data.records.length <= 0) {
							return uni.showToast({
								title: '该栏目下没有内容'
							})
						} else {
							this.dataList = res.data.records;
							this.pages = res.data.pages;
						}
					},
					fail: err => {
						console.log(res)
					}
				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #f4f2f3 !important;
	}

	/*  /deep/.uni-card {
    background-color: #ffffff !important;
  } */
	.u-page-part1 {
		height: 100rpx;
		margin: auto;
		display: flex;
		justify-content: space-around;
	}

	.img-con {
		justify-content: center;
		margin: 20rpx auto;
	}

	/deep/.uni-card--border {
		border-radius: 16rpx !important;
	}

	/deep/.u-button {
		width: 28% !important;
		height: 60rpx !important;
		border-radius: 10rpx !important;
	}

	image,
	/deep/.u-image {
		border-radius: 16rpx !important;
	}

	/deep/.u-button__text {
		font-size: 24rpx !important;
	}

	/deep/.u-text {
		line-height: 100rpx;
	}

	/deep/.u-text__value {
		font-size: 16px !important;
	}
</style>
