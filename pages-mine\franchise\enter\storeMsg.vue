<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>
		
		<!--营业执照底部弹窗-->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="checkboxClass">
				<u-checkbox-group v-model="checkboxValue"  @change="checkboxChange">
					<u-checkbox activeColor="#1e1848"  v-for="(item, index) in checkboxList" :key="index"
						:label="item.name" :name="item.name">
					</u-checkbox>
				</u-checkbox-group>
				</view>
		</u-popup>

		<view class="shadow">
			<view style="height: 25rpx;"/>
			<u-steps activeColor="#1e1848" current="2" >
				<u-steps-item title="认证缴费" />
				<u-steps-item title="主体信息" />
				<u-steps-item title="商户信息" />
				<u-steps-item title="平台审核" />
				<u-steps-item title="账户验证" />
			</u-steps>
			<view style="height: 25rpx;"/>
		</view>

		<view class="shadow">
			<view class="companyMsg">
				<text>店铺基本信息</text>
			</view>

			<view class="companyMsg">店铺名称</text></view>
			<view class="input-state">
				<u--input placeholder="请输入店铺名称" border="bottom" v-model="storeName" clearable
					disabledColor="Light#f4f4f5">
				</u--input>
			</view>
			<view class="text-stateB">
				<text>此处店铺名称不能包含品牌名称，若需要在店铺名称中使用品牌，可以入驻后修改店铺名称</text>
			</view>

			<view class="companyMsg">店铺logo</text></view>
			<!-- 上传图片 -->
			<view class="upload-img">
				<image @click="updateImg" class="img" :src="upImgUrl" />
			</view>
		</view>

		<view class="shadow">
			<view class="companyMsg">
				<text>类目信息</text>
			</view>
			<text class="authentication-type">经营类目</text>
			<view class="input-state" @click="popupShow = true">
				<u--input border="bottom" placeholder="请选择经营类目" v-model="manageType" disabled
					disabledColor="Light#f4f4f5"></u--input>
				<text class="text-arrow">❯</text>
			</view>
			<view class="text-stateC">
				<text>必须从营业执照允许的经营范围内选择，否则无法审核通过</text>
			</view>
			<view style="height: 25rpx;"/>
		</view>
		<view class="shadow">
			<view class="companyMsg">
				<text>店铺管理人信息</text>
			</view>
			<view class="companyMsg">管理人姓名</text></view>
			<view class="input-state">
				<u--input placeholder="请输入管理人姓名" v-model="adminName" border="bottom" clearable
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			<view class="companyMsg">管理人手机号</text></view>
			<view class="input-state">
				<u--input placeholder="请输入管理人手机号" maxlength="11" v-model="adminPhone" border="bottom" clearable
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			<view v-if="type!=1" >
			<view class="companyMsg">验证码</text></view>
			<view class="input-stateC">
				<u--input placeholder="请输入短信验证码" type="number" maxlength="6" v-model="verificationCode" border="bottom"
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			<view  class="verificationCodeA">
			<u-button :class="{ verificationCodeB: second>0 }" customStyle="color:#f6cc70" color="#1e1848"
				@click="getcode"  :text="authCode"></u-button>
				</view>
			<radio-group>
				<radio class="radio" color="#1e1848" :checked="1==radioValue" @click="radioCheck(1)"></radio>
			</radio-group>
			<view class="agreement flac-row-c text-c cf262462 f14">
				<text>我已仔细阅读并同意</text>
				<navigator>
					《服务协议》</navigator>
				<navigator>
					《隐私协议》</navigator>
			</view>
			
			<view class="link-state">
				<u--input disabled border="bottom" clearable disabledColor="Light#f4f4f5"></u--input>
			</view>
			
			<view class="classA">
				<image class="imgB" :src="serviceImgUrl" @click="openChat()"/>
				<image class="imgC" @click="toEnterStrategy" :src="strategyImgUrl" />
				<text class="text-stateF" @click="openChat()">联系客服</text>
				<text class="text-stateG" @click="toEnterStrategy">入驻攻略</text>
				<view class="button-state">
				<u-button  @click="returnUp" plain color="#1e1848" text="上一步"></u-button>
				</view>
				<view  class="button-stateB">
				<u-button  @click="submitMsg" customStyle="color:#f6cc70" color="#1e1848" text="提交审核"></u-button>
				</view>
			</view>
			<view style="height: 25rpx;">
			</view>
		
		</view>

		
		
	</view>

	</view>
</template>
<script>
	import {
		pathToBase64,
		base64ToPath,
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				serviceImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330623335service.png",
				strategyImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330634959strategy.png",
				upImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330663768up.png",
				msgType: "success",
				msgText: "",
				second: 0,
				checkboxValue: [],
				checkboxList: [{
						name: '招工合作',
						disabled: false
					},
					{
						name: '线索开发',
						disabled: false
					},
					{
						name: '业务分销',
						disabled: false
					},
					{
						name: '培训开发',
						disabled: false
					}
				],
				radioValue: '',
				popupShow: false,
				show: false,
				id: '',
				storeName: '',
				storeLogo: '',
				manageType: '',
				adminName: '',
				adminPhone: '',
				requestType: 1,
				verificationCode: '',
				//主体信息
				authenticationType: '',
				businessLicenseUrl: '',
				companyName: '',
				creditCode: '',
				doBusinessAddress: '',
				doBusinessTime: '',
				hometown: '',
				iCardEndTime: '',
				iCardStartTime: '',
				idBack: '',
				idCard: '',
				idPositive: '',
				name: '',
				sex: null,
				birthTime: null,
				type: null,
				legalPerson: '',
				introducer: ''
			}
		},
		computed: {
			authCode() {
				if (this.second == 0) {
					return '获取验证码';
				} else {
					if (this.second < 10) {
						return '重新获取' + this.second;
					} else {
						return '重新获取' + this.second;
					}
				}
			}
		},
		//接收主体信息
		onLoad(option) {
			this.introducer = option.introducer
			this.authenticationType = option.authenticationType
			this.businessLicenseUrl = option.businessLicenseUrl
			this.companyName = option.companyName
			this.creditCode = option.creditCode
			this.doBusinessAddress = option.doBusinessAddress
			this.doBusinessTime = option.doBusinessTime
			this.hometown = option.hometown
			this.iCardEndTime = option.iCardEndTime
			this.iCardStartTime = option.iCardStartTime
			this.idBack = option.idBack
			this.idCard = option.idCard
			this.idPositive = option.idPositive
			this.name = option.name
			this.legalPerson = option.legalPerson
			this.sex = option.sex
			this.birthTime = option.birthTime
			this.type = option.type
		},
		methods: {
			openChat() {
				// #ifdef  MP-WEIXIN
				console.log("开始微信聊一聊")
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				console.log("开始H5聊一聊")
				let param = {
					url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741'
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
			checkboxChange(data) {
				this.manageType =""
				for (var i = 0; i < data.length; i++) {
					this.manageType += data[i]+","
				}
				this.manageType = this.manageType.substring(0,this.manageType.length-1)
			},
			// 上传店铺logo
			updateImg() {
				const url = "https://api2.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: (uploadFileRes) => {
								let result = JSON.parse(uploadFileRes.data)
								this.upImgUrl = result.data
								this.storeLogo = result.data
							}
						});
					}
				});
			},
			radioCheck(index) {
				if (this.radioValue == 1) {
					this.radioValue = null
				} else {
					this.radioValue = 1
				}
			},
			returnUp() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/bodyMsg'
				})
			},
			toEnterStrategy() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/enterStrategy'
				})
			},
			getcode() {
				let _this = this;
				let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
				if (!reg.test(this.adminPhone)) {
					uni.showToast({
						icon: 'none',
						title: '请输入正确的手机号'
					});
					return;
				}
				if (this.second > 0) {
					return;
				}
				this.second = 60;
				uni.request({
					url: 'https://api2.xiaoyujia.com/member/sendLoginCode',
					data: {
						phone: this.adminPhone,
					},
					method: 'POST',
					dataType: 'json',
					success: (res) => {
						if (res.data.code != 0) {
							uni.showToast({
								title: res.data.data,
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: "短信发送成功"
							});
							js = setInterval(function() {
								_this.second--;
								if (_this.second == 0) {
									this.clear()
								}
							}, 1000)
						}
					},
					fail() {
						this.second == 0
					}
				});
			},
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			submitMsg() {
				if (!this.radioValue == 1) {
					this.$refs.uNotify.error("请先阅读并勾选协议!")
				}
				if (!this.verificationCode) {
					this.$refs.uNotify.error("请填写验证码!")
				}
				if (!this.adminPhone) {
					this.$refs.uNotify.error("请填写管理人手机号!")
				} else {
					let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
					if (!reg.test(this.adminPhone)) {
					this.$refs.uNotify.error("请填写正确的手机号!")
					}
				}
				if (!this.adminName) {
					this.$refs.uNotify.error("请填写管理人姓名!")
				}
				if (!this.manageType) {
					this.$refs.uNotify.error("请选择经营类目!")
				}
				if (!this.storeLogo) {
					this.$refs.uNotify.error("请上传店铺logo!")
				}
				if (!this.storeName) {
					this.$refs.uNotify.error("请填写店铺名称!")
				}
				
				if (this.storeName&&this.storeLogo&&this.manageType&&this.adminName&&this.manageType&&this.verificationCode&&this.radioValue == 1) {
					let param = {
						//主体信息
						authenticationType: this.authenticationType,
						businessLicenseUrl: this.businessLicenseUrl,
						companyName: this.companyName,
						creditCode: this.creditCode,
						doBusinessAddress: this.doBusinessAddress,
						introducer: this.introducer,
						doBusinessTime: this.doBusinessTime,
						hometown: this.hometown,
						iCardEndTime: this.iCardEndTime,
						iCardStartTime: this.iCardStartTime,
						idBack: this.idBack,
						idCard: this.idCard,
						sex: this.sex,
						birthTime: this.birthTime,
						idPositive: this.idPositive,
						name: this.name,
						legalPerson: this.legalPerson,
						//商户信息
						storeName: this.storeName,
						storeLogo: this.storeLogo,
						manageType: this.manageType,
						adminName: this.adminName,
						adminPhone: this.adminPhone,
						verificationCode: this.verificationCode,
						franchiseChannelId: "8",
						type: this.requestType,
						userId: uni.getStorageSync("memberId")
					}
					// 提交审核
					this.http({
						url: "partnerEnter",
						method: 'POST',
						data: param,
						header: {
							"content-type":"application/json;charset=UTF-8"
						},
						data: param,
						success: res => {
							if (res.code==0){
							this.$refs.uNotify.success("提交成功!")
							uni.navigateTo({
								url: '/pages-mine/franchise/enter/platformAudit'
							})
							}else{
								this.$refs.uNotify.error(res.msg)
							}
						},
						fail: err => {
							console.log('请求失败！' + res)
						}
					})
				}
			},
			getFranchiseMsgById() {
				if (this.type==1||this.type==2){
				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							this.manageType = res.data.manageType
							this.storeName = res.data.storeName
							this.upImgUrl = res.data.storeLogo
							this.storeLogo = res.data.storeLogo
							this.adminName = res.data.adminName
							this.adminPhone = res.data.adminPhone
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			}
			},// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {
			this.getFranchiseMsgById()
			if (this.type==2){
				this.requestType = 3
			}
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/franchise.scss";
	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}

	.classA {
		margin-top: 0%;
	}

	.checkboxClass {
		margin-bottom: 150rpx;
		margin-top: 250rpx;
		margin-left: 5%;
		
	}
	.verificationCodeA {
		margin-left: 74%;
		height: 55rpx;
		width: 180rpx;
		margin-top: -11%;
	}

	.verificationCodeB {
		color: #999999 !important;
		border: 1rpx solid #999999;
	}
.textA {
		text-align: center;
		display: block;
		font-size: 35rpx;
		color: dimgrey;
		margin-top: 10%;
	}
	.agreement {
		margin-top: -48rpx;
		margin-left: 6%;
	}

	.radio {
		margin-left: 4%;
		margin-top: 10%;
	}

	.img {
		width: 140rpx;
		height: 140rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
	}

	.imgB {
		width: 60rpx;
		height: 60rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
		margin-left: 40rpx;
		padding-bottom: 1%;
	}

	.imgC {
		width: 60rpx;
		height: 60rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
		margin-top: -14%;
		margin-left: 192rpx;
	}

	.imgD {
		width: 700rpx;
		height: 500rpx;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.text-state {
		text-align: center;
		margin-top: 2%;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.authentication-type {
		color: dimgrey;
		margin-left: 30rpx;
		line-height: 100rpx;
	}

	.input-state {
		margin-left: 30rpx;
		margin-top: -2%;
	}

	.input-stateC {
		width: 70%;
		margin-left: 30rpx;
		margin-top: -2%;
		flex: 1;

	}


	.text-stateB {
		margin-left: 30rpx;
		color: darkgrey;
		margin-top: 3%;
	}
	.text-stateC {
		color: darkgrey;
		margin-top: 3%;
		margin-left: 30rpx;
	}

	.upload-img {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		margin-top: -5%;

	}

	.ulink {
		padding-bottom: 5%;
	}

	// 小箭头
	.text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		margin-top: -8%;
		padding-right: 40rpx;
		color: black;
	}

	.upload {
		margin-left: 4%;
		width: 200rpx;
		height: 200rpx;

	}

	.link-state {
		margin-top: 6%;
	}

	.text-stateF {
		margin-left: 3%;
		font-size: 32rpx;
		color: dimgrey;
	}

	.text-stateG {
		margin-left: 4%;
		font-size: 32rpx;
		color: dimgrey;
	}

	.button-state {
		width: 200rpx;
		height: 120rpx;
		margin-top: -14%;
		margin-left: 40%;
	}

	.button-stateB {
		width: 200rpx;
		height: 120rpx;
		margin-top: -16%;
		margin-left: 70%;
	}
</style>
