<template>
	<view>
		<view class="bacf f16" style="padding: 20rpx 40rpx;">
			<view style="display: flex;">
				<view class="lh40 w6">申报信息</view>
				<u-tag text="查看申报记录" class="w4" @click="getHistoryLog" color="#FFFFFF" bgColor="#1e1848" plain shape="circle"
					customStyle="margin-left: 40%;margin-top: 10rpx;" size="large" borderColor="#1e1848"></u-tag>
			</view>
		</view>
		<view class="bacf f16" style="padding: 20rpx 40rpx;">
			<view class="lh40 flac-row">
				<text class="w3">已付金额：</text>
				<u--input disabled type="number" v-model="orderData.amount" border="none" clearable />
			</view>
		<!-- 	<view class="lh40 flac-row" @click="pickerShow = true">
				<text class="w3">申报类型<sup class="red">*</sup>：</text>
				<u--input disabled placeholder="点击选择" v-model="payDeclare.declareName" border="none" clearable />
			</view> -->
			<view class="lh40 flac-row" @click="employeePickerShow = true">
				<text class="w3">垫付人员<sup class="red">*</sup>：</text>
				<u--input disabled placeholder="点击选择" v-model="payDeclare.employeeNo" border="none" clearable />
			</view>
			<view class="lh40 flac-row">
				<text class="w3">申报金额<sup class="red">*</sup>：</text>
				<u--input disabled placeholder="请输入报价金额" type="number" v-model="payDeclare.declareMoney" border="none"
					clearable />
			</view>
			<!-- <view class="lh40 flac-row">
				<text class="w3">申报说明<sup class="red">*</sup>：</text>
				<u--input placeholder="请输入申报说明" v-model="payDeclare.remark" border="none" />
			</view>
			<view class="lh40">
				<text class="w3">申报凭据<sup class="red">*</sup>：</text>
					<u-upload maxCount="1" :fileList="fileList1" :previewFullImage="true"
						@afterRead="afterRead" @delete="deletePic" name="1" multiple></u-upload>
			</view> -->
		</view>
		<u-button text="提交申报" shape="circle" color="#1e1848" customStyle="width:90%;margin-top:20rpx;color:#f6cc70"
			@click="subMsg" >
		</u-button>

		<view>
			<u-picker :show="pickerShow" :columns="columns" @cancel="pickerShow = false"
				@confirm="selectBankType"></u-picker>
		</view>
		
		<view>
			<u-picker :show="employeePickerShow" :columns="employeeColumns" @cancel="employeePickerShow = false"
				@confirm="selectEmployee"></u-picker>
		</view>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current: 0,
				pickerShow: false,
				employeePickerShow: false,
				orderData: {},
				declareProportionList: [],
				payDeclare: {
					declareName: '',
					declareMoney: '',
					remark: '',
					declareImgUrl: '',
					declarePeople: uni.getStorageSync('employeeId'),
					billNo: '',
					employeeNo: '',
				},
				fileList1: [],
				// columns: [['加氨', '其他']],
				columns: [[]],
				employeeColumns: [],
				checkType: 0,
				checkTitle: "",
				checkText: "",
				id: '',
				employeeNo: '',
			}
		},
		onLoad(option) {
			this.id = option.id
			this.employeeNo = option.employeeNo
			this.getOrderData()
			// this.getDeclareProportion()
		},
		methods: {
			getOrderArtificialMoney(){
				this.http({
					url: "getOrderArtificialMoney",
					method: 'GET',
					data: {
						billNo: this.payDeclare.billNo,
						employeeNo: this.payDeclare.employeeNo
					},
					success: res => {
						if (res.code == 0) {
							this.payDeclare.declareMoney = res.data
						}else{
							uni.showToast({
								title: '获取可申报金额失败！',
								icon: 'none'
							})
						}
					}
				})
			},
			getDeclareProportion(){
				this.http({
					url: "getDeclareProportion",
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.declareProportionList = res.data
							for (var i = 0; i < res.data.length; i++) {
								this.columns[0].push(res.data[i].name)
							}
						}
					}
				})
			},
			getOrderData() {
				this.http({
					url: "getOrderData",
					data: {
						id: this.id,
						employeeNo: this.employeeNo
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.orderData = res.data.order
							this.payDeclare.billNo = res.data.order.billNo
							let arr = []
							for (var i = 0; i < res.data.serverEmployee.length; i++) {
								arr.push(res.data.serverEmployee[i].employeeNo)
							}
							this.employeeColumns.push(arr)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			selectBankType(e) {
				this.payDeclare.declareName = e.value[0]
				this.pickerShow = false
			},
			selectEmployee(e){
				this.payDeclare.employeeNo = e.value[0]
				this.employeePickerShow = false
				this.getOrderArtificialMoney()
			},
			getHistoryLog() {
				uni.navigateTo({
					url: "/pages-work/business/order/declareLog?billNo="+this.orderData.billNo+"&amount="+this.orderData.amount
				})
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.fileList1 = []
					this.payDeclare.declareImgUrl = ''
				}
				if (this.checkType == 1) {
					this.http({
						url: 'saveEmployeePayDeclare',
						data: this.payDeclare,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						success: res => {
							if (res.code == 0) {
								uni.showToast({
									title: res.data,
									icon: 'none'
								})
								this.payDeclare.declareImgUrl = ''
								this.payDeclare.declareName = ''
								this.payDeclare.declareMoney = ''
								this.payDeclare.remark = ''
								this.payDeclare.employeeNo = ''
								this.fileList1 = []
								setTimeout(()=>{
								uni.navigateTo({
									url: "/pages-work/business/order/declareLog?billNo="+this.orderData.billNo+"&amount="+this.orderData.amount
								})
								},1500)
							}else{
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					})
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 删除图片
			deletePic(event) {
				this.event = event
				this.openCheck(0, "确认删除该凭据？")
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					this.payDeclare.declareImgUrl = obj.data
					this.fileList1 = [{
						url: obj.data
					}]
					fileListLen++

				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			showToast() {
				uni.showToast({
					icon: 'none',
					title: '信息填写不完全，请补充后提交'
				})
			},
			subMsg() {
				// if (!this.payDeclare.declareName) {
				// 	this.showToast()
				// } else 
				if (!this.payDeclare.employeeNo) {
					this.showToast()
				}
				//  else if (!this.payDeclare.remark) {
				// 	this.showToast()
				// } 
				else if (!this.payDeclare.declareMoney){
					this.showToast()
				} 
				// else if (!this.payDeclare.declareImgUrl){
				// 	this.showToast()
				// }
				 else if (!(!isNaN(parseFloat(this.payDeclare.declareMoney)) && isFinite(this.payDeclare.declareMoney))){
					uni.showToast({
						icon: 'none',
						title: '请输入正确的垫付金额'
					})
				}else if (parseFloat(this.payDeclare.declareMoney)<=0){
					uni.showToast({
						icon: 'none',
						title: '申报金额不能小于等于0元'
					})
				} else if(parseFloat(this.payDeclare.declareMoney)>=parseFloat(this.orderData.amount)) {
					uni.showToast({
						icon: 'none',
						title: '垫付金额错误！不可大于等于已付金额！'
					})
				}else{
					this.openCheck(1, "确认提交该申报？")
				}
			}
		}
	}
</script>

<style>
	.c4c {
		color: #4c4c4c;
	}

	.c0c4d6 {
		color: #c0c4d6;
	}

	.c61 {
		color: #616161
	}

	.border-bottom-2sf3 {
		border-bottom: 2rpx solid #f3f3f3;
	}


	page {
		background-color: #f5f5f5;
		padding-bottom: 20rpx;
	}
</style>