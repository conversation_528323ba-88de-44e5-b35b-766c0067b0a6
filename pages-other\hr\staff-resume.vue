<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 人脸识别相机 -->
		<cameraPage :showCameraPage="showCameraPage" @submitCameraPhoto="submitCameraPhoto"
			@closeCameraPage="closeCameraPage" />

		<u-picker :show="showPicker" :columns="columns" @change="changeHandler" @confirm="confirm"
			@cancel="showPicker = false"></u-picker>

		<u-picker :show="showPickerMine" @cancel="showPickerMine = false" :columns="pickerMineList"
			@confirm="confirmPickerMine" @change="changeHandler" :keyName="pickerMineName"></u-picker>

		<!-- 日期选择器 -->
		<u-datetime-picker :show="showPickerDate" v-model="pickerDate" mode="date" @cancel="showPickerDate = false"
			@confirm="confirmDate()" @change="changeDate" :minDate="minDate">
		</u-datetime-picker>


		<view v-if="!showCameraPage">
			<!-- 员工-头部信息 -->
			<view class="header">
				<view class="head-left" @click="uploadHeadPortrait()" @longpress="openImgPreview()">
					<img :src="checkStr(trialStaff.headPortrait)!='-'?trialStaff.headPortrait:blankImg">
				</view>
				<view class="head-center">
					<view>
						<uni-icons type="person-filled" style="margin-right: 10rpx;" size="20" color="#fdd472">
						</uni-icons>
						<text>{{trialStaff.name}}</text>
					</view>
					<view @click="openCallPhone(trialStaff.tel)">
						<uni-icons type="phone-filled" style="margin-right: 10rpx;" size="20" color="#fdd472">
						</uni-icons>
						<text>{{trialStaff.tel}}</text>
					</view>
				</view>
				<view class="head-right">
					<img :src="headIcon" @click="openTab(0)" />
				</view>
			</view>

			<!-- 头部菜单-一级 -->
			<scroll-view scroll-x="true" :scroll-left="scrollLeft" class="menu-scroll-X">
				<view class="choice-menu">
					<view class="choice-item" v-for="(item, index) in choiceList" :key="index" @click="choiceTab(index)"
						:class="{activeChoice: choiceIndex == index}" :style="item.disable?'color: #909399':''">
						<text>{{item.choiceTitle}}</text>
					</view>

					<view class="choice-item" style="width: 20rpx;">
					</view>
				</view>
			</scroll-view>

			<!-- 资料 -->
			<uni-transition mode-class="slide-left" :show="choiceIndex==0">
				<!-- 头部菜单-二级 -->
				<view class="tab-menu" style="margin: 0rpx 50rpx 20rpx 50rpx;" v-if="menuList.length!=0">
					<u-sticky>
						<u-tabs :list="menuList" @click="choiceMenu" :current="current" lineWidth="22" lineHeight="8"
							:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
						  color: '#1e1848',
						  fontWeight: 'bold',
						  transform: 'scale(1.1)'
					  }" :inactiveStyle="{
						  color: '#333',
						  transform: 'scale(1.05)'
					  }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
						</u-tabs>
					</u-sticky>
				</view>

				<view v-if="choiceIndex==0" style="padding-bottom: 200rpx;">
					<!-- 基本信息 -->
					<view v-if="current==0">
						<view class="tab-item">
							<view class="item-title">
								<text>* </text>
								<text>姓名</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.name" placeholder="请输入姓名"
								:readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item" @click="openPicker(0)">
							<view class="item-title">
								<text>* </text>
								<text>性别</text>
							</view>
							<text class="item-text">{{trialStaff.sex}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item" @click="openPickerDate(3,0)">
							<view class="item-title">
								<text>* </text>
								<text>出生日期</text>
							</view>
							<text class="item-text">{{formatDate(trialStaff.birthTime)}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text>* </text>
								<text>身份证号</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.idCard"
								placeholder="请输入身份证号" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text>* </text>
								<text>民族</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.nation" placeholder="请输入民族"
								:readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text style="color: #ff4d4b">* </text>
								<text>籍贯</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.hometown"
								placeholder="请输入籍贯" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text> </text>
								<text>开户银行</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.banks" placeholder="请输入开户银行"
								:readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text> </text>
								<text>银行卡号</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.bankCards"
								placeholder="请输入银行卡号" :readonly="!isEdit" clearable border="none" />
						</view>

						<pickers @address="address">
							<view class="tab-item">
								<view class="item-title">
									<text>* </text>
									<text>现居地址</text>
								</view>
								<text class="item-text single-row">{{trialStaff.showAreaName}}</text>
								<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
							</view>
						</pickers>

						<view class="tab-item">
							<view class="item-title">
								<text>* </text>
								<text>详细地址</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.address"
								placeholder="请输入详细地址" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item" @click="openPicker(1)">
							<view class="item-title">
								<text>* </text>
								<text>婚否</text>
							</view>
							<text class="item-text">{{trialStaff.married}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item" @click="openPicker(2)">
							<view class="item-title">
								<text>* </text>
								<text>学历</text>
							</view>
							<text class="item-text">{{trialStaff.education}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>身高(cm)</text>
							</view>
							<u--input class="single-input1" type="number" v-model="trialStaff.height"
								placeholder="请输入身高" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>体重(kg)</text>
							</view>
							<u--input class="single-input1" type="number" v-model="trialStaff.weight"
								placeholder="请输入体重" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>紧急联系人</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.urgent"
								placeholder="请输入紧急联系人" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>紧急联系电话</text>
							</view>
							<u--input class="single-input1" type="number" v-model="trialStaff.urgentTel"
								placeholder="请输入紧急联系电话" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item" @click="openPicker(6)">
							<view class="item-title">
								<text></text>
								<text>户口类型</text>
							</view>
							<text class="item-text">{{trialStaff.registeredResidence|| '-'}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>血型</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.bloodType"
								placeholder="请输入血型" :readonly="!isEdit" clearable border="none" />
						</view>
					</view>

					<!-- 工作信息 -->
					<view v-if="current==1">
						<view class="tab-item" @click="openPicker(3)">
							<view class="item-title">
								<text>* </text>
								<text>招聘来源</text>
							</view>
							<text class="item-text">{{trialStaff.recruitChannel}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text>* </text>
								<text>求职岗位</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.post" placeholder="请输入求职岗位"
								:readonly="!isEdit" clearable border="none" maxlength="8" />
						</view>

						<view class="tab-item" @click="openPicker(4)">
							<view class="item-title">
								<text></text>
								<text>工作状态</text>
							</view>
							<text class="item-text">{{trialStaff.workState}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>介绍人工号</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.introducer"
								placeholder="请输入介绍人工号" :readonly="true" clearable border="none" />
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text></text>
								<text>待遇要求</text>
							</view>
							<u--input class="single-input1" type="text" v-model="trialStaff.salary"
								placeholder="请输入待遇要求" :readonly="!isEdit" clearable border="none" />
						</view>

						<view class="tab-item" @click="openPickerDate(4,0)">
							<view class="item-title">
								<text></text>
								<text>从业时间</text>
							</view>
							<text class="item-text">{{formatDate(trialStaff.workingLifeDate)}}</text>
							<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
						</view>
					</view>

					<!-- 家庭信息 -->
					<view v-if="current==2">
						<view class="item-info" v-for="(item,index) in trialStaff.trialFamilyMemberList" :key="index">
							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>家庭成员{{index+1}}</text>
								</view>
								<text class="item-text"></text>
								<uni-icons type="clear" size="18" v-if="isEdit"
									@click="deleteItem(0,index)"></uni-icons>
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>称谓</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.appellation"
									placeholder="请输入称谓" :readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>姓名</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.name" placeholder="请输入姓名"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90" @click="openPickerDate(0,index)">
								<view class="item-title">
									<text></text>
									<text>出生日期</text>
								</view>
								<text class="item-text">{{formatDate(item.date)}}</text>
								<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>工作单位</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.workUnit"
									placeholder="请输入工作单位" :readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>职位</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.post" placeholder="请输入职位"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>联系电话</text>
								</view>
								<u--input class="single-input1" type="number" v-model="item.tel" placeholder="请输入联系电话"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>联系地址</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.address" placeholder="请输入联系地址"
									:readonly="!isEdit" clearable border="none" />
							</view>
						</view>

						<view class="btn-bottom-small">
							<button :disabled="!isEdit" v-if="isEdit" @click="addItem(0)"
								:style="isEdit?'background-color:#1e1848;':'background-color:#909399;color:#fff'">
								<uni-icons type="plus-filled" size="18" v-if="isEdit" color="#ffffff"></uni-icons>
								家庭信息
							</button>
						</view>

						<u-empty v-if="trialStaff.trialFamilyMemberList.length==0&&!isEdit" text="暂无数据"
							icon="http://cdn.uviewui.com/uview/empty/data.png" />
					</view>

					<!-- 工作经历 -->
					<view v-if="current==3">
						<view class="item-info" v-for="(item,index) in trialStaff.trialWorkExperienceList" :key="index">
							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>工作经历{{index+1}}</text>
								</view>
								<text class="item-text"></text>
								<uni-icons type="clear" size="18" v-if="isEdit"
									@click="deleteItem(1,index)"></uni-icons>
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>工作单位</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.company" placeholder="请输入工作单位"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90" @click="openPickerDate(1,index)">
								<view class="item-title">
									<text>* </text>
									<text>开始时间</text>
								</view>
								<text class="item-text">{{formatDate(item.startTime)}}</text>
								<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
							</view>

							<view class="tab-item w90" @click="openPickerDate(2,index)">
								<view class="item-title">
									<text>* </text>
									<text>结束时间</text>
								</view>
								<text class="item-text">{{formatDate(item.endTime)}}</text>
								<uni-icons type="forward" size="18" v-if="isEdit"></uni-icons>
							</view>


							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>部门职务</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.departmentPost"
									placeholder="请输入部门职务" :readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text>* </text>
									<text>工作内容</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.content" placeholder="请输入工作内容"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>证明人</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.witness" placeholder="请输入证明人"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>证明人电话</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.tel" placeholder="请输入证明人电话"
									:readonly="!isEdit" clearable border="none" />
							</view>

							<view class="tab-item w90">
								<view class="item-title">
									<text></text>
									<text>离职原因</text>
								</view>
								<u--input class="single-input1" type="text" v-model="item.quitReason"
									placeholder="请输入离职原因" :readonly="!isEdit" clearable border="none" />
							</view>
						</view>


						<view class="btn-bottom-small">
							<button :disabled="!isEdit" v-if="isEdit" @click="addItem(1)"
								:style="isEdit?'background-color:#1e1848;':'background-color:#909399;color:#fff'">
								<uni-icons type="plus-filled" size="18" v-if="isEdit" color="#ffffff"></uni-icons>
								工作经历
							</button>
						</view>

						<u-empty v-if="trialStaff.trialWorkExperienceList.length==0&&!isEdit" text="暂无数据"
							icon="http://cdn.uviewui.com/uview/empty/data.png" />
					</view>

					<u-gap height="200"></u-gap>
				</view>
			</uni-transition>

			<!-- 面试 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==1">
				<view v-if="choiceIndex==1">
					<view class="tab-item" @click="openPickerDate(5,0)">
						<view class="item-title">
							<text>* </text>
							<text>入职办理</text>
						</view>
						<text class="item-text">{{formatDate(trialStaff.entryTime)}}</text>
						<uni-icons type="forward" size="18" v-if="isEdit&&isAdmin"></uni-icons>
					</view>

					<view class="tab-item" @click="openPickerMine(0)">
						<view class="item-title">
							<text>* </text>
							<text>所属门店</text>
						</view>
						<text class="item-text">{{formatPickerMine(0,trialStaff.storeId)}}</text>
						<uni-icons type="forward" size="18" v-if="isEdit&&isAdmin"></uni-icons>
					</view>

					<view class="tab-item" @click="openPicker(5)">
						<view class="item-title">
							<text></text>
							<text>试用期</text>
						</view>
						<text class="item-text">{{checkStr(trialStaff.probation)}}</text>
						<uni-icons type="forward" size="18" v-if="isEdit&&isAdmin"></uni-icons>
					</view>

					<view class="tab-item">
						<view class="item-title">
							<text></text>
							<text>试用薪资</text>
						</view>
						<u--input class="single-input1" type="number" v-model="trialStaff.probationSalary"
							placeholder="请输入试用薪资" :readonly="!isEdit||!isAdmin" clearable border="none" />
					</view>

					<view class="tab-item">
						<view class="item-title">
							<text></text>
							<text>转正薪资</text>
						</view>
						<u--input class="single-input1" type="number" v-model="trialStaff.regularSalary"
							placeholder="请输入转正薪资" :readonly="!isEdit||!isAdmin" clearable border="none" />
					</view>

					<!-- <view class="tab-item" @click="openPickerMine(1)">
					<view class="item-title">
						<text>* </text>
						<text>系统权限</text>
					</view>
					<text class="item-text">{{formatPickerMine(1,trialStaff.roleId)}}</text>
					<uni-icons type="forward" size="18" v-if="isEdit&&isAdmin"></uni-icons>
				</view> -->

					<view class="tab-item">
						<view class="item-title">
							<text>* </text>
							<text>职位</text>
						</view>
						<u--input class="single-input1" type="text" v-model="trialStaff.position" placeholder="请输入职位"
							:readonly="!isEdit||!isAdmin" clearable border="none" />
					</view>

					<view class="btn-bottom-small">
						<button v-if="isAdmin&&record.messageState<=2" @click="interviewAdopt()">
							<uni-icons type="checkmarkempty" size="18" v-if="isEdit" color="#ffffff"></uni-icons>
							面试通过
						</button>
					</view>
				</view>
				<u-gap height="300"></u-gap>
			</uni-transition>

			<!-- 培训 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==2">
				<view v-if="choiceIndex==2&&record==null">
					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
					<view class="btn-bottom-small">
						<button v-if="isAdmin" @click="addItem(2)">
							<uni-icons type="plus-filled" size="18" v-if="true" color="#ffffff"></uni-icons>
							添加培训
						</button>
					</view>
				</view>

				<view v-if="choiceIndex==2&&record!=null">
					<view class="staff-tab">
						<text>{{trialStaff.name}}的培训记录</text>
						<text>开始时间：{{formatDate(record.creatDate)}}</text>
						<text>状态：{{foramtProcess(record.messageState)}}</text>
						<text style="color: #909399;font-size: 28rpx;" v-if="record.messageState<3">*培训通过后将自动推送考试</text>
					</view>
					<view class="btn-bottom-small">
						<button v-if="isAdmin&&record.messageState<=2" @click="trainAdopt()">
							<uni-icons type="checkmarkempty" size="18" v-if="true" color="#ffffff"></uni-icons>
							培训通过
						</button>
					</view>
				</view>
			</uni-transition>

			<!-- 保险 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==3">
				<view v-if="choiceIndex==3">
					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</uni-transition>

			<!-- 跟单 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==4">
				<view v-if="choiceIndex==4">
					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</uni-transition>

			<!-- 考试 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==5">
				<view v-if="choiceIndex==5&&record!=null" class="f16">
					<view class="staff-tab">
						<text>{{trialStaff.name}}的考试情况</text>
						<text>开始时间：{{formatDate(record.creatDate)}}</text>
						<text>考试成绩：{{!isBlank(record.testScore)?record.testScore:'-'}}</text>
						<text>状态：{{messageStateList[record.messageState].text}}</text>

						<text style="color: #909399;font-size: 28rpx;" v-for="(item,index) of messageStateList"
							:key="index" v-if="record.messageState==item.value">{{item.examMsg}}</text>
					</view>

					<view class="btn-bottom-small">
						<button v-if="!isAdmin" @click="openTab(2)">
							<uni-icons type="calendar" size="18" color="#ffffff"></uni-icons>
							去考试
						</button>
					</view>

					<view class="btn-bottom-small">
						<button v-if="isAdmin&&record.messageState==2" @click="addItem(3)">
							<uni-icons type="reload" size="18" color="#ffffff"></uni-icons>
							重新考试
						</button>
					</view>
				</view>

				<view v-if="choiceIndex==5&&record==null" @click="choiceTab(2)">
					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
					<text class="text-tips">培训通过后推送！</text>
				</view>
			</uni-transition>

			<!-- 入职 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==6">
				<view v-if="choiceIndex==6&&trialStaff.employeeId!=null">
					<view class="tab-item">
						<view class="item-title">
							<text>* </text>
							<text>合同归属</text>
						</view>
						<text class="item-text" style="width: 60%;">{{checkStr(trialStaff.company)}}</text>
					</view>

					<view class="tab-item">
						<view class="item-title">
							<text>* </text>
							<text>合同开始时间</text>
						</view>
						<text class="item-text">{{checkStr(trialStaff.contractStartTime)}}</text>
					</view>

					<view class="tab-item">
						<view class="item-title">
							<text>* </text>
							<text>合同结束时间</text>
						</view>
						<text class="item-text">{{checkStr(trialStaff.contractEndTime)}}</text>
					</view>
					<view class="tab-item">
						<view class="item-title">
							<text>* </text>
							<text>员工工号</text>
						</view>
						<text class="item-text">{{checkStr(trialStaff.no)}}</text>
					</view>
				</view>

				<view v-if="choiceIndex==6&&trialStaff.employeeId==null">
					<u-empty text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</uni-transition>

			<!-- 上架 -->
			<uni-transition mode-class="slide-right" :show="choiceIndex==7">
				<view v-if="choiceIndex==7">
					<view class="tab-item">
						<view class="item-title">
							<text></text>
							<text>员工工号</text>
						</view>
						<text class="item-text" v-if="!needNo">{{checkStr(trialStaff.no)}}</text>
						<u--input class="single-input1" type="text" v-model="trialStaff.no" placeholder="请输入员工工号" v-else
							clearable border="none" />
					</view>
					<view class="btn-bottom-small">
						<button v-if="isAdmin" @click="openTab(1)"
							:style="trialStaff.employeeId!=null?'background-color:#1e1848;':'background-color:#909399;color:#fff'">
							<uni-icons type="auth-filled" size="18" color="#ffffff"></uni-icons>
							{{trialStaff.state==1?'重新上架':'去上架'}}
						</button>
						<button v-if="needNo" @click="createNo()" style="background-color:#1e1848;">
							<uni-icons type="personadd-filled" size="18" color="#ffffff"></uni-icons>
							生成工号
						</button>
					</view>
				</view>
			</uni-transition>

			<view class="btn-bottom-float" @click="edit()" v-if="!showClipper">
				<button v-if="!isEdit">修改资料</button>
				<button v-if="isEdit" style="color: #fff;background-color: #1e1848;
			box-shadow: 4rpx 4rpx 10rpx #909399;">保存资料</button>
			</view>
		</view>

	</view>
</template>
<script>
	import pickers from "@/pages-other/common/components/ming-picker/ming-picker.vue"
	import cameraPage from "@/pages-other/common/components/cameraPage.vue"
	export default {
		components: {
			pickers,
			cameraPage
		},
		data() {
			return {
				// 可配置选项
				// 是否可编辑
				isEdit: false,
				// 是否开启人脸识别相机
				isOpenCamera: true,


				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				showClipper: false,
				showCameraPage: false,

				scrollTop: 0,
				scrollLeft: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#ffffff'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,
				searchCondition: {
					current: 1
				},

				memberId: null,
				trialId: null,
				employeeId: null,
				processId: 1,
				isAdmin: false,
				needNo: false,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				headIcon: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/hr/0_1.png",
				headImg: '',

				trialStaff: {},
				record: [],
				storeList: [],
				roleList: [],
				roleId: uni.getStorageSync("roleId") || 0,

				choiceDeleteIndex: 0,
				choiceDeleteTypeIndex: 0,
				choiceIndex: 0,
				current: 0,
				choiceList: [{
						choiceTitle: "资料",
						value: 0,
						show: true,
						disable: false
					},
					{
						choiceTitle: "面试",
						value: 1,
						show: true,
						disable: false
					},
					{
						choiceTitle: "培训",
						value: 2,
						show: true,
						disable: false
					}, {
						choiceTitle: "保险",
						value: 3,
						show: true,
						disable: true
					}, {
						choiceTitle: "跟单",
						value: 4,
						show: true,
						disable: true
					}, {
						choiceTitle: "考试",
						value: 5,
						show: true,
						disable: false
					}, {
						choiceTitle: "入职",
						value: 6,
						show: true,
						disable: false
					}, {
						choiceTitle: "上架",
						value: 7,
						show: true,
						disable: false
					},
				],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [{
						index: 0,
						name: '基本信息',
					}, {
						index: 1,
						name: '工作信息',
					},
					{
						index: 2,
						name: '家庭信息',
					},
					{
						index: 3,
						name: '工作经历',
					}
				],
				menuListList: [{
						index: 0,
						name: '基本信息',
					}, {
						index: 1,
						name: '工作信息',
					},
					{
						index: 2,
						name: '家庭信息',
					},
					{
						index: 3,
						name: '工作经历',
					}
				],
				messageStateList: [{
					text: "未知",
					value: 0,
					examMsg: ''
				}, {
					text: "未完成",
					value: 1,
					examMsg: '*考试已推送，成绩合格即可上架'
				}, {
					text: "未通过",
					value: 2,
					examMsg: '*考试未通过，可选择重新考试'
				}, {
					text: "已通过",
					value: 3,
					examMsg: '*考试已通过，可进行上架'
				}, {
					text: "已完成",
					value: 4,
					examMsg: '*考试已通过，可进行上架'
				}],
				addItemList: [{
						"trialId": this.trialId,
						"appellation": "",
						"name": "",
						"date": null,
						"workUnit": "",
						"post": "",
						"tel": "",
						"address": "",
						"id": null
					},
					{
						"trialId": this.trialId,
						"startTime": null,
						"endTime": null,
						"company": "",
						"departmentPost": "",
						"content": "",
						"witness": "",
						"tel": "",
						"quitReason": "",
						"id": null
					}
				],

				nowDate: Number(0),
				minDate: Number(new Date(parseInt(-1262332800) * 1000)),
				pickerDate: Number(new Date()),

				pickerIndex: 0,
				choicePickerIndex: 0,

				choicePickerDateValue: 0,
				choicePickerMineValue: 0,
				choiceDateIndex: 0,

				pickerMineName: "",
				pickerMineList: [],

				showPicker: false,
				showPickerDate: false,
				showPickerMine: false,
				columns: [],
				columnsList: [
					[
						['男', '女']
					],
					[
						['未婚未育', '已婚未育', '已婚已育', '未婚已孕', '离异', '其它']
					],
					[
						['无', '小学', '中学', '高中', '大专', '本科及以上', '中专']
					],
					[
						['智联招聘', '58同城', 'BOSS', '厦门人才网', '小鱼网', '朋友圈', '内部推荐', '朋友介绍', '离职员工返岗']
					],
					[
						['无工作急寻新工作', '工作中考虑换一个工作', '工作中暂时不找工作']
					],
					[
						['1个月', '2个月', '3个月', '4个月', '5个月', '6个月']
					],
					[
						['农业', '非农业']
					],
				],


			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			address(e) {
				let code = ""
				this.trialStaff.showAreaName = ''
				for (var i = 0; i < e.value.length; i++) {
					this.trialStaff.showAreaName += e.value[i]
					if (e.code[i] != "") {
						code = e.code[i]
					}
				}
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			changeDate(e) {
				const {
					value
				} = e;
				this.pickerDate = value
			},
			// 打开页面
			openTab(index) {
				let url = ''
				if (index == 0) {
					if (this.roleId == 110 || this.roleId == 112) {
						return
					}
					url = "/pages-other/hr/staff-process?id=" + this.trialId
					if (this.isAdmin) {
						url += "&isAdmin=true"
					}
				} else if (index == 1) {
					if (this.trialStaff.employeeId == null) {
						this.$refs.uNotify.error("工号还未生成，暂时无法上架！")
						return
					}
					url = "/pages-other/hr/staff-grounding?id=" + this.trialId
				} else if (index == 2) {
					url = "/pages-mine/exam/exam-center"
				}

				uni.navigateTo({
					url: url
				})
			},
			// 生成工号
			createNo() {
				if (!this.trialStaff.no) {
					return this.$refs.uNotify.warning("请填写工号！")
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/trialStaffProcessRecord/createStaffNo',
					// outsideUrl: 'http://localhost:8063/trialStaffProcessRecord/createStaffNo',
					data: {
						trialId: this.trialStaff.trialId,
						no: this.trialStaff.no
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.$refs.uNotify.success("工号创建完成！")
							this.needNo = false
							this.getTrialStaffByTrialId()
						} else {
							this.$refs.uNotify.warning(res.msg)
						}
					}
				});
			},
			// 打开选择器
			openPicker(index) {
				if (!this.isEdit) {
					return
				}
				// 非管理不可编辑面试相关
				if (index == 5 && !this.isAdmin) {
					return
				}
				this.choicePickerIndex = index
				this.showPicker = true
				this.columns = this.columnsList[index]
			},
			confirm() {
				let index = this.choicePickerIndex
				let index1 = this.pickerIndex
				let pickerText = this.columnsList[index][0][index1]
				if (index == 0) {
					this.trialStaff.sex = pickerText
				} else if (index == 1) {
					this.trialStaff.married = pickerText
				} else if (index == 2) {
					this.trialStaff.education = pickerText
				} else if (index == 3) {
					this.trialStaff.recruitChannel = pickerText
				} else if (index == 4) {
					this.trialStaff.workState = pickerText
				} else if (index == 5) {
					this.trialStaff.probation = pickerText
				} else if (index == 6) {
					this.trialStaff.registeredResidence = pickerText
				}
				this.showPicker = false
			},
			openPickerDate(value, index) {
				if (!this.isEdit) {
					return
				}

				// 非管理不可编辑面试相关
				if (value == 5 && !this.isAdmin) {
					return
				}

				this.choicePickerDateValue = value
				this.choiceDateIndex = index
				this.showPickerDate = true
			},
			confirmDate() {
				let index = this.choicePickerDateValue
				let index1 = this.choiceDateIndex
				if (index == 0) {
					this.trialStaff.trialFamilyMemberList[index1].date = this.pickerDate
				} else if (index == 1) {
					this.trialStaff.trialWorkExperienceList[index1].startTime = this.pickerDate
				} else if (index == 2) {
					this.trialStaff.trialWorkExperienceList[index1].endTime = this.pickerDate
				} else if (index == 3) {
					this.trialStaff.birthTime = this.pickerDate
				} else if (index == 4) {
					this.trialStaff.workingLifeDate = this.pickerDate
				} else if (index == 5) {
					this.trialStaff.entryTime = this.pickerDate
				}
				this.showPickerDate = false
			},
			// 打开选择器
			openPickerMine(value) {
				if (!this.isEdit) {
					return
				}

				if (value == 0) {
					if (!this.isAdmin) {
						return
					}
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {
					this.pickerMineName = "name"
					this.pickerMineList = this.roleList
				}
				this.choicePickerMineValue = value
				this.showPickerMine = true
			},
			confirmPickerMine() {
				let value = this.choicePickerMineValue
				if (value == 0) {
					this.trialStaff.storeId = this.storeList[0][this.pickerIndex].id
				} else if (value == 1) {
					this.trialStaff.roleId = this.roleList[0][this.pickerIndex].id
				}
				this.showPickerMine = false
			},
			// 选择菜单（一级）
			choiceTab(index) {
				if (this.choiceList[index].disable) {
					return
				}
				this.choiceIndex = index
				// 切换流程
				this.processId = index + 1
				this.selectByTrialIdProcessId()
			},
			// 选择菜单（二级）
			choiceMenu(e) {
				this.current = e.index
			},
			// 添加
			addItem(value) {
				let tips = ["家庭信息已添加！", "工作经历已添加！"]
				// 允许不点击修改按钮
				let allowNotEdit = [2, 3]
				if (!this.isEdit) {
					if (allowNotEdit.findIndex(item => item == value) == -1) {
						this.$refs.uNotify.error("请先点击修改资料！")
						return
					}
				}

				if (value == 0) {
					this.trialStaff.trialFamilyMemberList.push(this.addItemList[value])
					this.$refs.uNotify.success(tips[value])
				} else if (value == 1) {
					this.trialStaff.trialWorkExperienceList.push(this.addItemList[value])
					this.$refs.uNotify.success(tips[value])
				} else if (value == 2) {
					if (!this.isAdmin) {
						this.$refs.uNotify.error("管理员才可添加！")
						return
					}
					this.addTrainRecord()
				} else if (value == 3) {
					if (!this.isAdmin) {
						this.$refs.uNotify.error("管理员才可操作！")
						return
					}
					this.restartStaffExamRecord()
				}
			},
			// 删除
			deleteItem(value, index) {
				this.choiceDeleteIndex = index
				this.choiceDeleteTypeIndex = value
				if (value == 0) {
					this.openCheck(0, "确定删除这条家庭成员信息吗？", "删除后无法恢复！")
				} else if (value == 1) {
					this.openCheck(0, "确定删除这条工作经历吗？", "删除后无法恢复！")
				}
			},
			delete() {
				let tips = ["家庭成员信息删除成功！", "工作经历删除成功！"]
				let value = this.choiceDeleteTypeIndex
				let index = this.choiceDeleteIndex
				let url = ""
				let data = {}
				if (value == 0) {
					url = "deleteTrialFamilyMember"
					data = this.trialStaff.trialFamilyMemberList[index]
					if (data.id == null) {
						this.$refs.uNotify.success(tips[value])
						this.$delete(this.trialStaff.trialFamilyMemberList, index)
						return
					}
				} else if (value == 1) {
					url = "deleteTrialWorkExperience"
					data = this.trialStaff.trialWorkExperienceList[index]
					if (data.id == null) {
						this.$refs.uNotify.success(tips[value])
						this.$delete(this.trialStaff.trialWorkExperienceList, index)
						return
					}
				}

				this.http({
					url: url,
					data: data,
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.$refs.uNotify.success(tips[value])
								this.$delete(this.trialStaff.trialFamilyMemberList, index)
							} else if (value == 1) {
								this.$refs.uNotify.success(tips[value])
								this.$delete(this.trialStaff.trialWorkExperienceList, index)
							}
						}
					}
				});
			},
			//修改/保存
			edit() {
				// 保存修改
				if (this.isEdit) {
					this.save()
				}
				// 修改资料
				else {
					if (!this.trialStaff.name) {
						return this.$refs.uNotify.error("资料未初始化，暂无修改权限！")
					} else {
						this.isEdit = true
					}
				}
			},
			// 保存员工信息
			save() {
				// 基本信息校验
				if (this.isBlank(this.trialStaff.sex) ||
					this.isBlank(this.trialStaff.birthTime) ||
					this.isBlank(this.trialStaff.idCard) ||
					this.isBlank(this.trialStaff.nation) ||
					this.isBlank(this.trialStaff.hometown) ||
					this.isBlank(this.trialStaff.showAreaName) ||
					this.isBlank(this.trialStaff.address) ||
					this.isBlank(this.trialStaff.married) ||
					this.isBlank(this.trialStaff.education)
					// ||this.isBlank(this.trialStaff.urgent) ||
					// this.isBlank(this.trialStaff.urgentTel)
				) {
					return this.$refs.uNotify.error(
						"带红色*号为必填项哦（基本信息不完整）");
				}
				// 工作信息校验
				else if (
					this.isBlank(this.trialStaff.recruitChannel) ||
					this.isBlank(this.trialStaff.post)) {
					return this.$refs.uNotify.error(
						"带红色*号为必填项哦（工作信息不完整）");
				}
				// 家庭信息校验
				else if (this.isBlankInfo(0)) {
					return this.$refs.uNotify.error(
						"带红色*号为必填项哦（家庭信息不完整）");
				}
				// 工作经历校验
				else if (this.isBlankInfo(1)) {
					return this.$refs.uNotify.error(
						"带红色*号为必填项哦（工作经历不完整）");
				} else {
					if (this.trialStaff.isComplete == 0) {
						this.$set(this.trialStaff, "isComplete", 1)
					}

					this.http({
						url: 'insertUpdateById',
						data: this.trialStaff,
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code ==
								0) {
								this.$refs
									.uNotify
									.success(
										"资料保存成功！"
									)
								this.isEdit =
									false
							} else {
								this.$refs
									.uNotify
									.error(
										"资料保存失败！"
									)
							}
						}
					});
				}
			},
			// 添加培训记录
			addTrainRecord() {
				let data = {
					trialId: this.trialId,
					operatorId: uni.getStorageSync("employeeId") || 0
				}
				this.http({
					url: 'addTrainRecord',
					data: data,
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.record = res.data
							this.$refs.uNotify.success("培训记录添加成功！")
						} else {
							this.$refs.uNotify.error(res.msg)
							if (this.trialStaff.isComplete < 2) {
								this.openCheck(1, "员工还未鉴定", "请先进行鉴定吧！")
							}
						}
					}
				});
			},
			// 面试通过
			interviewAdopt() {
				if (this.isBlank(this.trialStaff.entryTime) ||
					this.isBlank(this.trialStaff.storeId) ||
					// this.isBlank(this.trialStaff.probation) ||
					// this.isBlank(this.trialStaff.probationSalary) ||
					// this.isBlank(this.trialStaff.regularSalary) ||
					this.isBlank(this.trialStaff.position)) {
					return this.$refs.uNotify.error(
						"带红色*号为必填项哦（面试信息不完整）");
				}

				this.$set(this.trialStaff, "operatorId", uni.getStorageSync("employeeId") || 0)
				this.http({
					url: 'interviewAdopt',
					data: this.trialStaff,
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("面试已通过！")
							this.record.messageState = 4
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 培训通过
			trainAdopt() {
				this.http({
					url: 'trainAdopt',
					data: {
						trialId: this.trialId,
						operatorId: uni.getStorageSync("employeeId") || 0,
						signature: ""
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("培训已通过！")
							this.record.messageState = 4
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 重新考试
			restartStaffExamRecord() {
				this.http({
					url: 'restartStaffExamRecord',
					path: this.trialId,
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("已重新向员工推送考试！")
							this.record.messageState = 1
							this.record.testScore = ""
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 打开头像预览
			openImgPreview() {
				let data = []
				let img = this.trialStaff.headPortrait
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			closeCameraPage(flag) {
				console.log("关闭人脸相机！")
				this.showCameraPage = flag
			},
			// 提交照片
			submitCameraPhoto(tempFilePaths) {
				console.log("提交的照片地址为：", tempFilePaths[0])
				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						this.headImg = tempFilePaths[0]
						let result = JSON.parse(res.data)
						this.trialStaff.headPortrait = result.data
						this.showCameraPage = false
					}
				});
			},
			// 上传头像
			uploadHeadPortrait() {
				if (!this.isEdit) {
					this.$refs.uNotify.error("请先点击修改资料！")
					return
				}

				// #ifdef  MP-WEIXIN
				// 打开人脸识别相机
				if (this.isOpenCamera) {
					this.showCameraPage = true
					return
				}
				// #endif

				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								this.headImg = tempFilePaths[0]
								let result = JSON.parse(res.data)
								this.trialStaff.headPortrait = result.data
								console.log('上传图片后返回文件地址:', result.data)
							}
						});
					}
				});
			},
			foramtProcess(index) {
				let result = ""
				result = this.messageStateList[index].text
				return result
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/store/getByList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.storeList = []
							this.storeList.push(res.data)
						}
					}
				});
			},
			// 获取系统角色列表
			getByRoleList() {
				this.http({
					url: 'getByRoleList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.roleList = []
							this.roleList.push(res.data)
						}
					}
				});
			},
			//流程
			selectByTrialIdProcessId() {
				this.http({
					url: 'selectByTrialIdProcessId',
					data: {
						trialId: this.trialId || 0,
						processId: this.processId,
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.record = res.data
						} else {
							this.record = null
						}
					}
				});
			},
			// 获取员工信息
			getTrialIdByEmployeeId() {
				this.http({
					url: 'getTrialIdByEmployeeId',
					data: {
						employeeId: this.employeeId
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.trialStaff = res.data
							this.processId = this.trialStaff.processId
						}
					}
				});
			},
			//员工信息
			getTrialStaffByTrialId() {
				this.http({
					url: 'getTrialStaffByTrialId',
					data: {
						trialId: this.trialId
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.trialStaff = res.data
							this.processId = this.trialStaff.processId
							this.needNo = this.trialStaff.no ? false : this.needNo
							if (this.needNo) {
								this.$refs.uNotify.warning("员工已签署入职协议，请根据工种及花名册为其生成工号！")
							}
						}
					}
				});
			},
			getTrialStaffByEmployeeId() {
				this.http({
					url: 'getTrialStaffByEmployeeId',
					data: {
						employeeId: this.employeeId
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.trialStaff = res.data
							this.trialId = this.trialStaff.trialId
							this.processId = this.trialStaff.processId
						}
					}
				});
			},
			// 拨打电话
			openCallPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone,
					success: function(e) {

					},
					fail: function(e) {
						console.log(e)
					}
				});
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "-"
				} else {
					return str
				}
			},
			// 填写详细校验
			isBlank(str) {
				if (str == null || str == "" || str ==
					undefined) {
					return true
				} else {
					return false
				}
			},
			isBlankInfo(index) {
				let result = false
				if (index == 0) {
					this.trialStaff.trialFamilyMemberList.forEach(item => {
						if (this.isBlank(item.appellation) ||
							this.isBlank(item.name) ||
							this.isBlank(item.tel)) {
							result = true
						}
					})
				} else if (index == 1) {
					this.trialStaff.trialWorkExperienceList.forEach(item => {
						if (this.isBlank(item.company) ||
							this.isBlank(item.startTime) ||
							this.isBlank(item.endTime) ||
							this.isBlank(item.departmentPost) ||
							this.isBlank(item.content)) {
							result = true
						}
					})
				}
				return result
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? '0' + MM : MM;
				let d = date.getDate();
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d;
			},
			// 格式化下拉框
			formatPickerMine(value, id) {
				let result = ""
				if (value == 0) {
					this.storeList[0].forEach(item => {
						if (id == item.id) {
							result = item.storeName
						}
					})
				} else if (value == 1) {
					this.roleList[0].forEach(item => {
						if (id == item.id) {
							result = item.name
						}
					})
				}
				return result
			},
			// 打开确认框
			openCheck(checkType, checkTitle,
				checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.delete()
				} else if (this.checkType == 1) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-appraisal?id=' + this.trialId
					})
				}
			},

		},
		onLoad(options) {
			this.trialId = parseInt(options.id) || null
			this.employeeId = options.employeeId || null
			this.isAdmin = options.isAdmin || false
			this.needNo = options.needNo || false

			if (this.trialId == null) {
				this.getTrialStaffByEmployeeId()
			} else {
				this.getTrialStaffByTrialId()
			}
			this.getStoreList()
			this.getByRoleList()

			// 跳转到对应菜单
			let index = options.choiceIndex || 0
			this.choiceTab(parseInt(index))
		}
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/hrbp.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	// 个人信息部分
	.header {
		width: 100%;
		height: 300rpx;
		// box-shadow: 5rpx 10rpx 20rpx #dedede;
		display: flex;
		background-color: #fff;
		margin-bottom: 40rpx;
	}

	// 头像部分
	.head-left {
		width: 30%;
		height: 100%;

		img {
			display: block;
			margin: 70rpx auto;
			width: 160rpx;
			height: 160rpx;
			border-radius: 50%;
		}
	}

	// 个人信息部分
	.head-center {
		width: 40%;
		height: 100%;
		margin: 75rpx 0;
		display: flex;
		flex-direction: column;

		text {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 36rpx;
		}
	}


	.head-right {
		width: 30%;
		height: 100%;

		img {
			display: block;
			margin: 80rpx auto;
			width: 130rpx;
			height: 130rpx;
			border-radius: 50%;
		}
	}

	// 一级菜单
	.choice-menu {
		display: flex;
		flex-wrap: nowrap;
		flex-direction: row;
		width: 100%;
		height: 100rpx;
		margin: 0 0 20rpx 0;
		padding: 10rpx 40rpx;

		text {
			font-size: 32rpx;
		}
	}

	// 选择项目
	.choice-item {
		width: 80rpx;
		height: 50rpx;
		text-align: center;
		margin: 0 20rpx;
		flex-shrink: 0;
	}

	.activeChoice {
		color: #1e1848;
		border-bottom: #1e1848 4rpx solid;
	}


	.staff-tab {
		width: 100%;
		padding: 20rpx 40rpx;
		box-shadow: 5rpx 10rpx 20rpx #dedede;
		line-height: 60rpx;

		text {
			display: block;
			line-height: 60rpx;
			font-size: 32rpx;
		}
	}

	// 单行信息
	.tab-item {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 40rpx;
		display: flex;
		border-bottom: #f4f4f5 2px solid;
		font-size: 32rpx;
	}

	.w90 {
		width: 90%;
	}

	.item-title {
		width: 230rpx;
		display: flex;

		text:nth-child(1) {
			color: #ff4d4b;
			width: 30rpx;
		}

		text:nth-child(2) {}
	}

	.item-text {
		margin: 0 0 0 0rpx;
		font-size: 30rpx;
		width: 49%;
	}

	.item-info {
		width: 90%;
		border-radius: 20px;
		margin: 40rpx auto 60rpx auto;
		padding-bottom: 0rpx;
		background-color: #fff;
		box-shadow: 0 0 20rpx #dedede;
	}

	// 底部固定按钮
	.btn-bottom-float {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 80%;
		height: 120rpx;
		margin: 0rpx 10%;

		button {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			color: #1e1848;
			background-color: #fff;
			border: #1e1848 2rpx solid;
			border-radius: 50rpx;
			font-size: 32rpx;
			box-shadow: 4rpx 4rpx 10rpx #dedede;
		}
	}

	// 底部按钮
	.btn-bottom-small {
		button {
			bottom: 20rpx;
			margin: 60rpx auto;
			width: 50%;
			height: 70rpx;
			line-height: 70rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 32rpx;
		}
	}

	.text-tips {
		display: block;
		text-align: center;
		font-size: 36rpx;
		line-height: 60rpx;
	}

	.single-row {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>