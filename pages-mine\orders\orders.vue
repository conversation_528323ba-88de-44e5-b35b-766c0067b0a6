<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="swiper-menu">
			<u-sticky>
				<u-tabs :list="menuList" @click="choiceMenu" :current="current" lineWidth="22" lineHeight="8" :lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
						color: '#1e1848',
						fontWeight: 'bold',
						transform: 'scale(1.1)'
					}" :inactiveStyle="{
						color: '#333',
						transform: 'scale(1.05)'
					}" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
				</u-tabs>
			</u-sticky>
		</view>

		<!-- 订单信息 -->
		<swiper class="swiper" ref="swiper" :style="contentHeight" :autoplay="false" @change="swiperMenu"
			:current="menuIndex">
			<swiper-item ref="swiperItem" class="swiper-item" v-for="(menu, index) in menuList" :key="index">
				<view class="swiper-tab" v-for="(order, index1) in orderList" :key="index1"
					v-if="checkOrder(index, index1)" @click="openOrderDetail(order.billNo,order.productName)">
					<view class="swiper-head">
						<text class="swiper-title">单号：{{order.billNo}}</text>
						<text class="swiper-tips">{{menu.name}}</text>
					</view>
					<view class="swiper-content">
						<view class="content-left">
							<img :src="orderImg" alt="">
						</view>
						<view class="content-right">
							<view class="content-title">
								<text>{{order.productName}}</text>
							</view>
							<view class="content-text">
								<text>下单时间：</text>
							</view>
							<view class="content-text">
								<text>{{order.createTime}}</text>
							</view>
							<view class="content-text-right">
								<text>￥{{order.realTotalAmount}}</text>
							</view>
						</view>
					</view>

				</view>

				<u-empty text="暂无订单" v-if="itemCount[index].count==0"
					icon="http://cdn.uviewui.com/uview/empty/order.png" />
			</swiper-item>
		</swiper>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				memberId: null,
				employeeId: null,
				menuIndex: 0,
				swiperItemCount: 0,
				contentHeight: 0,
				orderImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				orderList: [],
        lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [{
					name: '待处理',
				}, {
					name: '服务中',
				}, {
					name: '已完成',
				}, {
					name: '已取消',
				}, {
					name: '待评价',
				}],
				itemCount: [{
					count: 0
				}, {
					count: 0
				}, {
					count: 0
				}, {
					count: 0
				}, {
					count: 0
				}],
				// 员工信息
				employee: {
					id: this.employeeId,
					Password: null,
					RealName: null,
					Phone: null,
					CityId: null,
					AreaId: null,
					Address: null,
					HeadPortrait: null,
					Remark: null,
					UpdateDate: null,
					UpdatePerson: null,
					SiteId: null,
					LsTime: null,
					LeTime: null,
					BaomuWorkType: null,
					Score: null
				},
			}
		},
		methods: {
			// 打开订单详情
			openOrderDetail(billNo, productName) {
				console.log("输出要查看详情的订单号：", billNo, "其产品名为：", productName)
				let url = ""
				if (productName !== "积分充值") {
					url = "/pages-mine/orders/orders-detail?&billNo=" + billNo
				} else {
					url = "/pages-mine/orders/orders-detail-course?&billNo=" + billNo
				}
				uni.navigateTo({
					url: url
				})
			},
			// 控制不同类型的订单显示
			checkOrder(index, index1) {
				let orderState = this.orderList[index1].orderState
				let amount = this.orderList[index1].amount
				let realTotalAmount = this.orderList[index1].realTotalAmount
				let result = false
				// 测试时加上
				// return true
				if (index == 0) {
					if (orderState >= 10 && orderState <= 30 && amount !== realTotalAmount) {
						result = true
					}
				} else if (index == 1) {
					if (orderState >= 40 && orderState <= 70) {
						result = true
					}
				} else if (index == 2) {
					if ((orderState == 10 || orderState == 80 || orderState == 90) && amount == realTotalAmount) {
						result = true
					}
				} else if (index == 3) {
					if (orderState == 99 && amount !== realTotalAmount) {
						result = true
					}
				} else if (index == 4) {
					if (orderState > 80 && orderState < 90) {
						result = true
					}
				}
				return result
			},
			// 计算各类订单数量
			calculationItemCount() {
				if (this.orderList == null) {
					return
				}
				for (let item of this.orderList) {
					let orderState = item.orderState
					let amount = item.amount
					let realTotalAmount = item.realTotalAmount
					if (orderState >= 10 && orderState <= 30 && amount !== realTotalAmount) {
						this.itemCount[0].count = this.itemCount[0].count + 1
					} else if (orderState >= 40 && orderState <= 70) {
						this.itemCount[1].count = this.itemCount[1].count + 1

					} else if ((orderState == 10 || orderState == 80 || orderState == 90) && amount == realTotalAmount) {
						this.itemCount[2].count = this.itemCount[2].count + 1

					} else if (orderState == 99 && amount !== realTotalAmount) {
						this.itemCount[3].count = this.itemCount[3].count + 1

					} else if (orderState > 80 && orderState < 90) {
						this.itemCount[4].count = this.itemCount[4].count + 1

					}
				}
				// 初始化第一个栏目高度
				this.calculateContentHeight()
			},
			calculateContentHeight() {
				let height = (this.itemCount[this.menuIndex].count + 1) * 190 + "px"
				this.contentHeight = "height:" + height
			},
			// 滑动切换菜单
			swiperMenu(e) {
				this.menuIndex = e.detail.current
				this.calculateContentHeight()
			},
			// 点击切换菜单
			choiceMenu(e) {
				console.log("切换菜单栏：", e.index)
				this.menuIndex = e.index
				this.calculateContentHeight()
			},
			getMemberOrder() {
				let param = {
					memberId: this.memberId,
					// 家姐联盟
					// productIdList: [
					// 	285, 286, 287, 328, 329, 343, 344, 345, 346
					// ],
					// 家姐课堂
					productIdList: [
						285, 286, 287, 328, 329, 343, 344, 345, 346, 358, 361
					]
				}
				uni.request({
					url: 'https://api.xiaoyujia.com/order/getMemberOrderList',
					method: 'POST',
					data: param,
					dataType: 'json',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.data.code == 0) {
							this.orderList = res.data.data
							this.calculationItemCount()
							console.log("获取用户订单-成功！")
						}
					},
					fail: err => {
						console.log("获取用户订单-请求失败！" + res)
					}
				})
			},
			// 尝试从登录后的缓存中获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				this.memberId = memberId
				// 测试时加上 
				// this.memberId = 710162
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx
				if (this.checkType == 0) {

				}
			},
			// 用户信息初始化
			orginInfo() {
				this.getMemberInfor()
				this.getMemberOrder()
			}
		},
		// 页面载入之后
		mounted() {
			this.orginInfo()
		},
		onLoad() {

		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>
