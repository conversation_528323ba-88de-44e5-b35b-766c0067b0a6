<template>
	<page-meta :root-font-size="getRootFontSize()"></page-meta>
	<view class="index container" :style="'min-height: ' + screenHeight + 'px;'">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()" @close="closeDialog"></uni-popup-dialog>
		</uni-popup>

		<u-popup :show="popupShowMeeting" mode="center" @close="popupShowMeeting = false">
			<view @click="saveToPhone()">
				<u-image :src="meetingRoomImg" width="300" height="500" customStyle="margin:auto"></u-image>
			</view>
		</u-popup>

		<!-- 时间选择器 -->
		<u-datetime-picker :show="showDate" :minDate="nowDate" confirmColor="#f0263d" @confirm="formatDate"
			@cancel="showDate = false"></u-datetime-picker>
		<u-datetime-picker :show="showDate1" v-model="orderNeedData.workTime" mode="date" @cancel="showDate1 = false"
			@confirm="showDate1 = false"></u-datetime-picker>

		<!-- 切换组件 -->
		<view>
			<view class="bac1e1848" style="padding: 40rpx;height: 20vh;">
				<u-steps :current="stepsCurenrt">
					<view @click="toSteps(0)">
						<u-steps-item title="电联" />
					</view>
					<view @click="toSteps(1)">
						<u-steps-item title="需求" />
					</view>
					<view @click="toSteps(2)">
						<u-steps-item title="预算" />
					</view>
					<view @click="toSteps(3)">
						<u-steps-item title="匹配" />
					</view>
					<view @click="toSteps(4)">
						<u-steps-item title="面试" />
					</view>
					<view @click="toSteps(5)">
						<u-steps-item title="签约" />
					</view>
					<view @click="toSteps(6)">
						<u-steps-item title="结算" />
					</view>
				</u-steps>
			</view>
			<view>
				<view style="padding: 40rpx;margin-top: -140rpx;border-radius: 30rpx;">
					<uni-table border>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										线索所属门店:
									</view>
									<view style="color: red;">
										{{needTemplateData.needsStoreName}}
									</view>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										线索来源渠道:
									</view>
									<view>
										{{channelName||'未知'}}
									</view>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										服务类型:
									</view>
									<view class="flac-row">
										{{needTemplateData.productName}}
										{{orderNeedData.productRemark?'('+orderNeedData.productRemark+')':''}}
										<u-icon name="edit-pen" @click="productIdFlag=true"
											v-if="orderNeedData.flowStatus<6&&isEdit"></u-icon>
									</view>
								</view>
								<view>
									<u-input border="bottom" placeholder="可备注：如白班8:30-17:30、中晚餐等"
										v-model="orderNeedData.productRemark" clearable :maxlength="maxRemark"
										v-if="isEdit">
									</u-input>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										经纪人:
									</view>
									<view>
										{{agentName}}
									</view>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										客户姓名:
									</view>
									<view>
										{{orderNeedData.name}}
									</view>
									<u-icon name="eye" @click="customDetails()"></u-icon>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										联系电话:
									</view>
									<view @click="serviceScoreAuth?callPhone(orderNeedData,1):authTips()">
										{{formatPhone(orderNeedData.phone)}}
									</view>
									<u-icon name="phone"
										@click="serviceScoreAuth?callPhone(orderNeedData,1):authTips()"></u-icon>
									<u-icon name="file-text" v-if="serviceScoreAuth" @click="copyPhone()"></u-icon>
								</view>
								<view class="" v-if="!serviceScoreAuth" style="color: #ff4d4b;" @click="authTips()">
									* 服务分低于60，考试通过后可显示完整电话
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>

						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										所在小区:
									</view>
									<view>
										{{orderNeedData.housingEstate||'未知'}}
									</view>
								</view>
							</uni-td>
						</uni-tr>

						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										薪资待遇:
									</view>
									<view>
										{{orderNeedData.salary?orderNeedData.salary+'元/月':'薪资面议'}}
										{{orderNeedData.salaryRemark?'('+orderNeedData.salaryRemark+')':''}}
									</view>
								</view>
								<view>
									<u-input border="bottom" placeholder="可备注：如月休4天、13薪等"
										v-model="orderNeedData.salaryRemark" clearable :maxlength="maxRemark"
										v-if="isEdit">
									</u-input>
								</view>
							</uni-td>
						</uni-tr>

						<uni-tr v-if="orderNeedData.flowStatus==6">
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										售后薪资:
									</view>
									<view>
										{{orderNeedData.afterSalesFee?orderNeedData.afterSalesFee+'元/月':'薪资面议'}}
									</view>
								</view>
								<view>
									<u-input border="bottom" placeholder="更改后分享时将优先显示"
										v-model="orderNeedData.afterSalesFee" clearable v-if="isEdit" type="number">
									</u-input>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;" @click="choiceAddress()">
										工作地点:
									</view>
									<view>
										<u-textarea customStyle="width: 265rpx;" v-model="orderNeedData.street"
											placeholder="客户联系地址" :disabled="!isEdit" />
									</view>
									<!--    <u-icon name="edit-pen" @click="updateAddress()"></u-icon> -->
									<u-icon name="file-text" @click="copyAddress()"></u-icon>
									<!-- <u-icon name="eye" @click="lookMap()"></u-icon> -->
									<u-icon name="pushpin" @click="choiceAddress()" v-if="isEdit"></u-icon>
								</view>

								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17"
									v-if="showAllContent" @click="showDate1=(isEdit?true:false)">
									<view style="text-align: right;padding-right: 20rpx;">
										{{(orderNeedData.productId == 66|| orderNeedData.productId==67)?'预产日期：':'上户日期：'}}
									</view>
									<view>
										{{formatTime(orderNeedData.workTime)}}
									</view>
								</view>

								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										家庭情况:
									</view>
									<view>
										<u-textarea customStyle="width: 360rpx;height: 140rpx;"
											v-model="orderNeedData.remark"
											placeholder="客户的详细家庭情况，如：家里有几口人，面积多大，是否有老人，是否养宠物等" :disabled="!isEdit">
										</u-textarea>
									</view>
									<!-- <u-icon name="edit-pen" @click="updateOrderNeedsContent(1)"></u-icon> -->
								</view>

								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17"
									v-if="showAllContent">
									<view style="text-align: right;padding-right: 20rpx;">
										工作内容:
									</view>
									<view>
										<u-textarea customStyle="width: 360rpx;height: 140rpx;"
											v-model="orderNeedData.workContent"
											placeholder="员工的工作内容，如：需要打扫卫生、洗衣做饭、看护老人、照顾小孩等" :disabled="!isEdit">
										</u-textarea>
									</view>
								</view>

								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17"
									v-if="showAllContent">
									<view style="text-align: right;padding-right: 20rpx;">
										工作要求:
									</view>
									<view>
										<u-textarea customStyle="width: 360rpx;height: 140rpx;"
											v-model="orderNeedData.workRequire"
											placeholder="客户对员工的工作要求，如：需要保证个人卫生，要会哪些特殊家务，需要掌握哪些家常菜等" :disabled="!isEdit">
										</u-textarea>
									</view>
								</view>

								<view style="display: flex;padding-top: 20rpx;" class="fontStyle f17"
									v-if="showAllContent">
									<view style="text-align: right;padding-right: 20rpx;">
										需求备注:
									</view>
									<view>
										<u-textarea customStyle="width: 360rpx;height: 140rpx;"
											v-model="orderNeedData.agentRemark" placeholder="需求备注，仅经纪人可见"
											:disabled="!isEdit">
										</u-textarea>
									</view>
								</view>

								<view style="margin:20rpx 90rpx;">
									<u-icon label="展开更多" labelPos="left" name="arrow-down" labelColor="#000"
										:bold="true" v-if="!showAllContent" @click="showAllContent=!showAllContent">
									</u-icon>
									<u-icon label="收起" labelPos="left" name="arrow-up" labelColor="#000" :bold="true"
										v-else @click="showAllContent=!showAllContent"></u-icon>
								</view>

								<view v-if="lookFlag==0" style="display: flex;padding-top: 20rpx;"
									class="fontStyle f17">
									<view style="text-align: right;">
										相关操作：
									</view>
									<view style="display: flex;">
										<u-tag text="内容修改" :bgColor="!isEdit?'#1e1848':'#909399'"
											:borderColor="!isEdit?'#1e1848':'#909399'"
											@click="modifyContent(0)"></u-tag>
										<view style="width: 50rpx;"></view>
										<u-tag text="保存修改" :bgColor="isEdit?'#1e1848':'#909399'"
											:borderColor="isEdit?'#1e1848':'#909399'" @click="modifyContent(1)"></u-tag>
									</view>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										需求模板:
									</view>
									<view style="display: flex;">
										<u-tag text="点击查看" bgColor="#1e1848" @click="needFlag=true"
											borderColor="#1e1848"></u-tag>
										<view style="width: 50rpx;"></view>
										<u-tag text="复制模板" bgColor="#1e1848" @click="copyTemplate"
											borderColor="#1e1848"></u-tag>
										<!-- 					   <uni-icons @click="playAudio" type="sound-filled" style="margin-left: 10rpx;" size="20" color="#1e1848"
							v-if="orderNeedData.needTemplate!=null&&orderNeedData.needTemplate!=''">
					   </uni-icons> -->
									</view>
								</view>
								<!-- 音频播放组件 -->
								<!-- 	<free-audio
									startPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_play.png'
									endPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_stop.png'
									audioId='audio1' :url="orderNeedData.introduceVoice||''" activeColor="#1e1848"
									:isPlay="isShowAudio" v-show="false" /> -->
							</uni-td>
						</uni-tr>
					</uni-table>
					<view class="w10 mg-at h10">
						<map v-if="mapFlag" id="myMap" style="width: 100%; height: 50vh;" :latitude="mapConfig.latitude"
							:longitude="mapConfig.longitude" :scale="mapConfig.scale" :markers="markers"
							@markertap="openMap"></map>
						<image v-if="mapImgFlag" @click="openMap" :src="mapImgUrl"
							style="width: 50px;height: 50px;position: absolute;bottom: 5%;right: 10%;z-index: 9999;" />
					</view>
				</view>
			</view>
		</view>
		<!-- 电联页面 -->
		<view v-if="stepsCurenrt === 0">
			<view>
				<u-button text="感兴趣人员" @click="nannyFlag = true" color="#1e1848"
					customStyle="width: 80%;height: 77rpx;"></u-button>
			</view>
			<view style="padding: 40rpx;">
				<view>
					<uni-table border v-if="lookFlag==0">
						<uni-tr>
							<uni-td>
								<view v-if="oldNeedsId" style="display: flex;padding-right: 20rpx;"
									class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										原线索：
									</view>
									<view>
										<u-tag text="点击查看" bgColor="#1e1848" @click="lookOldNeeds"
											borderColor="#1e1848"></u-tag>
									</view>
								</view>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										需求状态：
									</view>
									<view>
										<u-tag :text="needState" bgColor="#1e1848" @click="needStateFlag= true"
											borderColor="#1e1848"></u-tag>
									</view>
								</view>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										需求分发：
									</view>
									<view>
										<u-tag text="选择人员" bgColor="#1e1848" @click="popupFlag= true,getAgentList()"
											borderColor="#1e1848"></u-tag>
									</view>
								</view>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										线索标签：
									</view>
									<view>
										<u-tag :text="needTag" bgColor="#1e1848" @click="needTagFlag= true"
											borderColor="#1e1848"></u-tag>
									</view>
								</view>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										开启合单：
									</view>
									<view>
										<u-switch v-model="isOneBill" :activeValue="1" :inactiveValue="0"
											@change="isOneBillPopup=true"></u-switch>
									</view>
								</view>
								<view style="display: flex;padding-right: 20rpx;" class="fontStyle f17">
									<view style="text-align: right;padding-right: 20rpx;">
										是否共享：
									</view>
									<view>
										<u-switch v-model="pushVal" :activeValue="1" :inactiveValue="0"
											@change="changePush"></u-switch>
									</view>
								</view>
							</uni-td>
						</uni-tr>
						<u-picker :show="needStateFlag" @confirm="updateNeedState" keyName="label"
							@cancel="needStateFlag=false" :columns="columns"></u-picker>
						<u-picker :show="needTagFlag" @confirm="selectNeedTag" keyName="label"
							@cancel="needTagFlag=false" :columns="tagColumns"></u-picker>
						<!-- <uni-tr>
							<uni-td width="150" style="font-weight: bold;letter-spacing: 4rpx;font-size: 1.25rem;">
								通话时间：{{list1.time}}
							</uni-td>
						</uni-tr> -->
						<!-- 	<uni-tr>
							<uni-td width="150" style="font-weight: bold;letter-spacing: 4rpx;font-size: 1.25rem;">
								拨打时间：{{list1.Date}}
							</uni-td>
						</uni-tr> -->
					</uni-table>
				</view>
				<!-- <u-button color="#FFC125" v-if="aNextFlag" text="下一步" shape="circle" @click="nextStep(1)"
					style="width: 60%;height: 70rpx;margin: 100rpx auto;"></u-button> -->
			</view>
			<!--线索日志-->
			<view class="w9 mg-at">
				<view class="textClass">
					操作日志：
					<view class="flac-row">
						详细版
						<u-switch v-model="showAllLog" :activeValue="true" :inactiveValue="false"
							@change="changeLogShow"></u-switch>
					</view>
				</view>
				<u-empty v-if="!orderNeedsLogs.length" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				<view v-for="(vos,i) in orderNeedsLogs" :key="i" v-if="showLog(vos)">
					<uni-tr>
						<uni-td>
							<view class="fontStyle f17 flac-row">
								操作人&nbsp;&nbsp;&nbsp;：
								{{vos.operator}}
								<view
									v-if="(vos.type == 5||vos.type == 6)&&vos.operatorId!=employeeId&&(vos.operatorNo||'').includes('B')"
									@click="readOrderNeedsLog(0,vos);getEmployeeMsg(0,vos.operatorId)" class="flac-row">
									<uni-icons type="wallet" size="18"></uni-icons>
									<view :style="vos.logState!=0?'color: #19be6b':'color:#ff4d4b'">简历-
										{{vos.logState!=0?'已读':'未读'}}
									</view>
								</view>
								<view v-if="vos.type == 6" @click="callPhoneBack(i)">
									<uni-icons type="phone" size="18"></uni-icons>
									电话
								</view>
							</view>
							<view class="fontStyle f17 flac-row" v-if="vos.operatorPhone">
								Ta的电话：
								{{vos.operatorPhone}}
							</view>
							<view class="fontStyle f17 flac-row">
								操作类型：
								{{ vos.title }}
							</view>
							<view class="fontStyle f17 flac-row">
								操作时间：
								{{ vos.createTime||"" }}
							</view>
							<view class="fontStyle f17 flac-row">
								操作内容：
								{{ vos.message||"" }}
							</view>

						</uni-td>
					</uni-tr>
				</view>
				<view class="lh50 text-c" v-if="orderNeedsLogs.length&&searchCondition.current>=pageCount">
					<text>已显示全部内容</text>
				</view>
			</view>
		</view>
		<!-- 需求 -->
		<view v-if="stepsCurenrt===1">
			<scroll-view scroll-x="true" class="kite-classify-scroll">
				<view class="kite-classify-cell" :style="index==curNow?'background:#1e1848;color:#fff':''"
					v-for="(item, index) in subsectionList" :key="index" @click="sectionChange(index)">
					{{item.name}}
				</view>
				<!-- <u-subsection fontSize="15px" bold activeColor="#1e1848" mode="subsection" :list="subsectionList" :current="curNow"
				  @change="sectionChange" customStyle="width:90%;margin:auto"></u-subsection> -->
			</scroll-view>

			<view style="padding: 40rpx;">
				<view style="margin-top: 20rpx;border-radius: 10rpx;">
					<view class="tableStyle f16">
						<uni-table border>
							<view v-for="(item,index) in needList" :key="i">
								<uni-section v-if="item.groupName&& item.workCode == getCode&&item.workType==0"
									:title="item.groupName" type="line" />
								<radio-group iconSize="14px" class="radioGroupClass"
									v-if="item.workType==0 && item.workCode == getCode">
									<radio style="padding-top: 25rpx;" color="#1e1848" @click="radioCheck(index)"
										:checked="item.defaultValue==1">{{item.title}}
									</radio>
								</radio-group>
							</view>

							<view v-for="(need,index) in needList" :key="index">
								<uni-section v-if="need.groupName&& need.workCode == getCode&&need.workType==1"
									:title="need.groupName" type="line" />
								<view v-if="need.workType==1 && need.workCode == getCode" class="needClass"
									style="display: flex;align-items: center;">
									<view class="" style="width: 30%;">{{need.title}}</view>
									<u-input border="bottom" :placeholder="need.value" v-model="need.defaultValue"
										customStyle="width: 50rpx !important;" clearable disabledColor="Light#f4f4f5">
									</u-input>
									<view class="" style="width: 30%;">{{need.workUnit}}</view>
								</view>
							</view>
						</uni-table>
						<u-button v-if="moneyFlag&&lookFlag==0" @click="salaryReckon(1)" color="#1e1848"
							text="工资估算"></u-button>
						<view class="" style="display: flex;">
							<u-button customStyle="width: 80%;" color="#1e1848" shape="circle"
								v-if="updateNeed&&lookFlag==0" @click="salaryReckon(2)" text="修改需求">
							</u-button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 工资 -->
		<view v-if="stepsCurenrt===2">
			<view style="padding: 20rpx;">
				<!--  <u-alert type="warning"
          description="注意:此栏只预估薪资作为匹配阿姨,具体订单价格以协商后的薪资为准,若次栏价格与实际不符,请在生成订单之前重回此页面修改!订单价格以此处的中介费为准!!!">
        </u-alert> -->
				<view style="margin-top: 20rpx;border-radius: 10rpx;">
					<view class="tableStyle" style="padding: 60rpx 0;">
						<view class="f24 lh45" style="width: 100%;margin: auto;text-align: center;">
							您选择的{{itemNum}}项需求估算工资为:</view>
						<view style="height: 50rpx;"></view>
						<view class="" style="display: flex;margin: 80rpx auto;">
							<u--input prefixIcon="rmb" type="number"
								customStyle="width: 40%;margin: auto;text-align: center;" v-model="moneyTotal"
								disabledColor="Light#f4f4f5"></u--input>
							<view style="width: 10rpx"></view>
							<!-- <u-button customStyle="width: 30%;" color="#1e1848" shape="circle" v-if="!orderNeedData.billNo"
			    @click="updateBaoMuWages()" text="工资修改">
			  </u-button> -->
						</view>
						<view class="f24 lh45" style="width: 100%;margin: auto;text-align: center;"
							v-if="orderNeedData.salary||orderNeedData.agencyFee">
							中介费为:</view>
						<view class="" style="display: flex;margin: 80rpx auto;"
							v-if="orderNeedData.salary||orderNeedData.agencyFee">
							<u--input prefixIcon="rmb" type="number"
								customStyle="width: 40%;margin: auto;text-align: center;" v-model="agencyFee"
								disabledColor="Light#f4f4f5"></u--input>
							<view style="width: 10rpx"></view>
							<!-- <u-button customStyle="width: 30%;" color="#1e1848" shape="circle" v-if="!orderNeedData.billNo" @click="updateAgencyFee()"
				  text="修改中介费">
				</u-button> -->
						</view>
					</view>


					<view class="" style="display: flex;margin: 80rpx auto;">
						<u-button text="下一步" v-if="affirmMoney&&lookFlag==0" color="#1e1848" shape="circle"
							@click="moneyNext()" customStyle="width: 47%;"></u-button>
						<!-- <u-button customStyle="width: 47%;" color="#1e1848" shape="circle" v-if="affirmMoney" 
			 @click="sendSalaryMsg()" text="客户确认">
			 </u-button> -->
					</view>
				</view>
			</view>
		</view>
		<!-- 匹配页面 -->
		<view v-if="stepsCurenrt === 3">
			<view>
				<u-button text="感兴趣人员" @click="nannyFlag = true" color="#1e1848"
					customStyle="width: 80%;height: 77rpx;"></u-button>
			</view>
			<uni-section title="选择类型" type="line">
				<view class="uni-padding-wrap uni-common-mt" style="width: 90%;margin-left: 5%;">
					<uni-segmented-control :current="fdqCurrent" :values="fdqItems" style-type="button"
						active-color="#1e1848" @clickItem="onClickItem" />
				</view>
			</uni-section>
			<view class="w9 mg-at flac-row-b" style="padding-top: 20rpx;" v-if="fdqCurrent==0">
				<u-input placeholder="请输入工号/姓名/手机号" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"
					v-model="searchData" />
				<u-button text="搜索" customStyle="width: 25%;height: 77rpx" color="#1e1848" @click="getBaoMuList"
					size="large"></u-button>
			</view>
			<checkbox-group @change="changeCheckbox" v-if="fdqCurrent==0">
				<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;" v-for="(item,i) in emps"
					:key="i">
					<view class="checkBox" style="display: flex;">
						<checkbox activeColor="#1e1848" :value="item.id" :checked="checkedArr.includes(item.id)"
							:class="{'checked':checkedArr.includes(item.id)}" :disabled="item.state!=1"></checkbox>
						<view style="color: red;" v-if="item.state!=1">该员工未上架，请完成上架流程再选择！</view>
					</view>
					<view class="f16 lh35 flac-row">
						<u-image :src="item.headPortrait||''" width="80" height="120" radius="5"></u-image>
						<view class="" style="margin: auto 20rpx;">
							<view class="flac-row" style="justify-content: space-between;">
								<view class="" style="width: 300rpx;">姓名：{{item.realName}}</view>
								<u-icon v-if="item.levelId==0"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594071178pt.png"
									size="35" style="width: 120rpx;"></u-icon>
								<u-icon v-if="item.levelId==1"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594100741jin.png"
									size="35" style="width: 120rpx;"></u-icon>
								<u-icon v-if="item.levelId==2"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594116479fei.png"
									size="35" style="width: 120rpx;"></u-icon>
							</view>
							<!-- <view class="lh36">状态：{{item.securityAuth==1?"已认证":"未认证"}}</view> -->
							<view class="">上户状态：{{item.billingFlag==1?"已开单":"未开单"}}</view>
							<view class="">薪资水平：{{item.minSalary}}</view>
							<view class="flac-row">联系方式：
								<u-icon name="phone" @click="callPhone(item,2)"></u-icon>
							</view>
							<view class="flac-row">居住地址：<u--text :lines="1" :text="item.address"></u--text>
							</view>
							<view class="w10 mg-at flac-row-a">
								<u-tag text="查看详情" @click="getEmployeeMsg(0,item.id)" color="#FFFFFF" bgColor="#1e1848"
									plain shape="circle" size="mini" borderColor="#1e1848"></u-tag>
								<u-tag text="一键认证" v-if="item.securityAuth==0" @click="authentication(item)"
									color="#FFFFFF" bgColor="#1e1848" plain shape="circle" size="mini"
									borderColor="#1e1848"></u-tag>
								<u-tag text="查看认证结果" @click="getRzReport(item)" color="#FFFFFF" bgColor="#1e1848" plain
									shape="circle" size="mini" borderColor="#1e1848"></u-tag>
							</view>
						</view>
					</view>
				</view>
			</checkbox-group>
			<view class="w9 mg-at flac-row-b" style="padding-top: 20rpx;" v-if="fdqCurrent==1">
				<u-input placeholder="保姆工号/姓名/手机号/推荐人" prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399" v-model="searchData" />
				<u-button text="搜索" customStyle="width: 25%;height: 77rpx" color="#1e1848"
					@click="getInterviewRecommend()" size="large"></u-button>
			</view>
			<checkbox-group @change="changeCheckbox" v-if="fdqCurrent==1">
				<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
					v-for="(item,i) in interviewRecommend" :key="i">
					<view class="checkBox">
						<checkbox activeColor="#1e1848" :value="item.id" :checked="checkedArr.includes(item.id)"
							:class="{'checked':checkedArr.includes(item.id)}"></checkbox>
					</view>
					<view class="f16 lh35 flac-row">
						<!-- <u-image :src="item.headPortrait||''" width="80" height="120" radius="5"></u-image> -->
						<view class="" style="margin: auto 20rpx;">
							<view class="flac-row" style="justify-content: space-between;">
								<view class="" style="width: 300rpx;">姓名：{{item.realName}}</view>
								<u-icon v-if="item.levelId==0"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594071178pt.png"
									size="35" style="width: 120rpx;"></u-icon>
								<u-icon v-if="item.levelId==1"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594100741jin.png"
									size="35" style="width: 120rpx;"></u-icon>
								<u-icon v-if="item.levelId==2"
									name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594116479fei.png"
									size="35" style="width: 120rpx;"></u-icon>
							</view>
							<!-- <view class="lh36">状态：{{item.securityAuth==1?"已认证":"未认证"}}</view> -->
							<view class="">门店：{{item.storeName}}</view>
							<view class="">上户状态：{{item.billingFlag==1?"已开单":"未开单"}}</view>
							<view class="">薪资水平：{{item.minSalary}}</view>
							<view class="flac-row">联系方式：
								<u-icon name="phone" @click="callPhone(item,2)"></u-icon>
							</view>
							<view class="flac-row">居住地址：<u--text :lines="1" :text="item.address"></u--text>
							</view>
							<view class="">推荐人：{{item.agentRealName+'('+item.agentNo+')'}}</view>
							<view class="">推荐人门店：{{item.agentStoreName}}</view>
							<view class="">推荐时间：{{item.createTime}}</view>
							<view class="flac-row"
								@click="recommendStateFlag=true;recommendState=item.recommendState;choiceItemIndex=i;">
								状态：
								<uni-tag :text="item.recommendStateName" :type="item.recommendState==1?'':'primary'"
									style="margin: 10rpx 5rpx;display: inline-block;"></uni-tag>
							</view>
							<view class="w10 mg-at flac-row-a">
								<u-tag text="查看详情" @click="getEmployeeMsg(0,item.id)" color="#FFFFFF" bgColor="#1e1848"
									plain shape="circle" size="mini" borderColor="#1e1848"></u-tag>
								<u-tag text="一键认证" v-if="item.securityAuth==0" @click="authentication(item)"
									color="#FFFFFF" bgColor="#1e1848" plain shape="circle" size="mini"
									borderColor="#1e1848"></u-tag>
								<u-tag text="查看认证结果" @click="getRzReport(item)" color="#FFFFFF" bgColor="#1e1848" plain
									shape="circle" size="mini" borderColor="#1e1848"></u-tag>
							</view>
						</view>
					</view>
				</view>

				<u-picker :show="recommendStateFlag" @confirm="updateRecommendState" keyName="label"
					@cancel="recommendStateFlag=false" :columns="recommendStateColumns"></u-picker>
			</checkbox-group>
			<u-button color="#1e1848" text="确认选择" shape="circle" @click="confirmSelect()"
				customStyle="width: 60%;height: 70rpx;margin: 50rpx auto;" v-if="lookFlag==0"></u-button>
			<!-- 面试时间选择--弹窗 -->
			<u-popup :show="showPopup" :round="10" mode="bottom" @close="showPopup = false" :closeable="true">
				<view class="f20 fb text-c lh50">面试信息</view>
				<u--form labelPosition="left" :model="model" ref="form">
					<u-form-item label="面试地点" :required="true" borderBottom ref="item">
						<u--input v-model="interviewAddr" disabled border="none"></u--input>
					</u-form-item>
					<u-form-item label="沟通时间" :required="true" borderBottom ref="item" @click="choiceDateTime">
						<u--input v-model="interviewTime" disabled disabledColor="#ffffff" placeholder="点击选择时间"
							border="none">
						</u--input>
					</u-form-item>
				</u--form>
				<u-button text="提交" color="#1e1848" shape="circle" customStyle="width:60%;margin: 30rpx auto"
					@click="chooseEmp()"></u-button>
			</u-popup>
		</view>
		<!-- 面试页面 -- 改匹配页 -->
		<view v-if="stepsCurenrt == 4">
			<u-button text="创建视频面试房间" v-if="!orderNeedData.meetingRoomId&&lookFlag==0"
				@click="insertMeetingRoom()"></u-button>
			<view v-if="meetingRoom" class="f17 w8 mg-at lh30"
				style="padding:20rpx 40rpx;border-radius: 20rpx;box-shadow: 2rpx 2rpx 10rpx #dedede;">
				<view class="fontStyle f17">面试名称：{{meetingRoom.roomTitle}}</view>
				<view class="fontStyle f17">面试内容：{{meetingRoom.roomContent}}</view>
				<view class="fontStyle f17">创建人：{{meetingRoom.createEmployeeName||meetingRoom.createMemberName}}</view>
				<view class="flac-row fontStyle " @click="openMeeting()">
					<text>进入面试</text>
					<u-icon name="mic" style="margin-left: 20rpx;"></u-icon>
				</view>
				<view class="flac-row fontStyle" @click="openPopupImg()">
					<text>分享海报</text>
					<u-icon name="share" style="margin-left: 20rpx;"></u-icon>
				</view>
			</view>


			<u-button text="添加新的面试" v-if="lookFlag==0" @click="addNewInterview"
				customStyle="width:90%;margin:auto"></u-button>
			<u-tabs :list="tabList" @click="tabClick" customStyle="width: 90%;margin: auto;" lineWidth="40"
				lineColor="#f56c6c" :activeStyle="{
            color: '#303133',
            fontWeight: 'bold',
            transform: 'scale(1.05)'
        }" :inactiveStyle="{
            color: '#606266',
            transform: 'scale(1)'
        }" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"></u-tabs>
			<u-notice-bar customStyle="width:85%;margin:auto" text="表格可以往后拉,最后面有通过面试按钮" mode="closable"></u-notice-bar>
			<view v-for="(item,index) in interviews" :key="index">
				<view class="w9 mg-at">
					<uni-table ref="table" :loading="loading" border stripe emptyText="暂无更多数据">
						<uni-tr>
							<uni-th class="thStyle" align="center" width="120">员工</uni-th>
							<uni-th class="thStyle" align="center">工资(元)</uni-th>
							<uni-th class="thStyle" align="center" width="100">面试结果</uni-th>
							<uni-th class="thStyle" align="center">保姆备注</uni-th>
							<uni-th class="thStyle" align="center">不通过原因</uni-th>
							<uni-th class="thStyle" align="center">操作</uni-th>
						</uni-tr>
						<uni-tr v-for="(vos,i) in item.deliveryVos" :key="i">
							<uni-td align="center">
								<view class="flac-row" style="justify-content: center;">
									<u-icon v-if="vos.levelId==0" @click="getEmployeeMsg(0,vos.employeeId)"
										name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594071178pt.png"
										size="30"></u-icon>
									<u-icon v-if="vos.levelId==1" @click="getEmployeeMsg(0,vos.employeeId)"
										name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594100741jin.png"
										size="30"></u-icon>
									<u-icon v-if="vos.levelId==2" @click="getEmployeeMsg(0,vos.employeeId)"
										name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594116479fei.png"
										size="30"></u-icon>
									<view class="" @click="getEmployeeMsg(0,vos.employeeId)">{{ vos.realName }}</view>
									<u-icon name="phone-fill" size="30" @click="callPrivatePhone(vos)"></u-icon>
								</view>
							</uni-td>
							<uni-td align="center">
								<view @click="getEmployeeMsg(0,vos.employeeId)">{{ vos.minSalary }}</view>
							</uni-td>
							<uni-td align="center" v-if="vos.ywStatus==3" style="color: #EE3B3B">通过</uni-td>
							<uni-td align="center" v-if="vos.ywStatus==4" style="color: #6CA6CD">不通过</uni-td>
							<uni-td align="center" v-if="vos.ywStatus==1||vos.ywStatus==0">待面试</uni-td>
							<uni-td align="center">{{ vos.nurseRemark||"" }}</uni-td>
							<uni-td align="center">{{ vos.remark||"" }}</uni-td>
							<uni-td align="center">
								<view class="flac-row-a">
									<u-tag v-if="vos.ywStatus==1||vos.ywStatus==0" customStyle="width: 47%;" text="通过"
										@click="interviewSuccess(vos.id)" type="warning"></u-tag>
									<u-tag v-if="vos.ywStatus==1||vos.ywStatus==0" customStyle="width: 47%;" text="不通过"
										@click="interviewFail(vos.id)" type="error"></u-tag>
								</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
			</view>
			<u-button v-if="toSettlement&&lookFlag==0" customStyle="width: 80%;margin: 50rpx auto" shape="circle"
				@click="settlement()" color="#FFC125" text="去签约">
			</u-button>
		</view>
		<!-- 确认页面 -->
		<view v-if="stepsCurenrt == 5">
			<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;" v-if="baomuInfoData!=null">
				<view class="f16 lh35 flac-row">
					<u-image :src="baomuInfoData.headPortrait||''" width="80" height="120" radius="5" />
					<view class="" style="margin: auto 20rpx;">
						<view class="flac-row-b">
							<view class="" style="width: 300rpx;">姓名：{{baomuInfoData.realName}}</view>
							<u-icon v-if="baomuInfoData.levelId==0"
								name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594071178pt.png"
								size="35" style="width: 120rpx;" />
							<u-icon v-if="baomuInfoData.levelId==1"
								name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594100741jin.png"
								size="35" style="width: 120rpx;" />
							<u-icon v-if="baomuInfoData.levelId==2"
								name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671594116479fei.png"
								size="35" style="width: 120rpx;" />
						</view>
						<view class="">薪资水平：{{orderNeedData.agencyFee||moneyTotal}}</view>
						<view class="flac-row">联系方式：
							<u-icon name="phone" @click="callPhone(baomuInfoData,2)"></u-icon>
						</view>
						<view v-if="(baomuInfoData.realTotalAmount!==baomuInfoData.amount&&!baomuInfoData.status)||
							(baomuInfoData.realTotalAmount!==baomuInfoData.amount&&baomuInfoData.status==1)||!baomuInfoData.billNo"
							class="lh36">当前状态：待支付</view>
						<view
							v-if="baomuInfoData.realTotalAmount==baomuInfoData.amount&&baomuInfoData.status!==2&&baomuInfoData.billNo"
							class="">当前状态：已支付待签约</view>
						<view v-if="baomuInfoData.realTotalAmount==baomuInfoData.amount&&baomuInfoData.status==2"
							class="lh36">
							当前状态：已完成</view>
						<view v-if="baomuInfoData.realTotalAmount!==baomuInfoData.amount&&baomuInfoData.status==2"
							class="lh36">
							当前状态：已签约待支付</view>
						<view v-if="baomuInfoData.orderState==99" class="">当前状态：已取消订单</view>
						<view class="flac-row">
							<u-tag text="查看详情" @click="getEmployeeMsg(0,baomuInfoData.id)" customStyle="margin: auto;"
								color="#FFFFFF" bgColor="#1e1848" plain shape="circle" size="mini"
								borderColor="#1e1848"></u-tag>
						</view>
					</view>
				</view>
			</view>
			<view v-if="createOrderFlag&&lookFlag==0">
				<u-button text="生成订单" customStyle="width: 80%;margin: 50rpx auto" shape="circle" color="#1e1848"
					:disabled="roleId==77" throttleTime="1000" @click="createOrder(null,1)" />
				<u-button text="生成代管订单" customStyle="width: 80%;margin: 50rpx auto" shape="circle" type="info"
					:disabled="roleId==77" throttleTime="1000" @click="createOrder(322,12)" />
				<u-button text="生成分期订单" customStyle="width: 80%;margin: 50rpx auto" shape="circle" type="info"
					:disabled="roleId==77" throttleTime="1000" @click="createOrder(null,11)" />
			</view>
			<u-button v-if="payCodeFlag&&baomuInfoData.orderState!==99&&lookFlag==0"
				customStyle="width: 80%;margin: 50rpx auto" color="#1e1848" shape="circle" text="生成付款码"
				@click="getOrderPayCode()" />
			<u-button :disabled="roleId==77&&lookFlag==1"
				v-if="signingFlag||(!baomuInfoData.status&&baomuInfoData.status!==0&&baomuInfoData.orderState!==99&&baomuInfoData.billNo)"
				customStyle="width: 80%;margin: 50rpx auto" color="#1e1848" shape="circle" text="创建合同"
				@click="showContractModal = true" />
		</view>
		<!-- 结算页面 -->
		<view v-if="stepsCurenrt == 6">
			<view style="padding: 40rpx;">
				<!-- <view class="tableStyle">
					<uni-table>
						<uni-tr>
							<uni-td style="background-color: #FFC125;" class="fontStyle">
								<view>
									<view>收款金额</view>
								</view>
							</uni-td>
						</uni-tr>
						<uni-tr>
							<uni-td style="color: red;" class="fontStyle">
								收款金额：￥{{allMoney}}
							</uni-td>
						</uni-tr>
					</uni-table>
				</view> -->
				<u-button v-if="payCodeFlag&&lookFlag==0" customStyle="width: 80%;margin: 50rpx auto" color="#1e1848"
					shape="circle" text="生成付款码" @click="getOrderPayCode()" />
				<u-button v-if="splitOrderFlag&&baomuInfoData.realTotalAmount==baomuInfoData.amount&&lookFlag==0"
					:disabled="roleId==77" stomStyle="width: 80%;margin: 50rpx auto" color="#1e1848" throttleTime="2000"
					shape="circle" text="拆单" @click="splitOrder()"></u-button>
				<u-tag v-if="splitSuccess" :text="tagText" plain bgColor="#fff" color='#1e1848'>
				</u-tag>
			</view>
		</view>
	</view>

	<!-- 面试不通过备注弹出层 -->
	<view>
		<u-popup mode="center" :show="failRemarkShow" @close="closeFailPopup()">
			<view style="width: 600rpx;height: 340rpx;background-color: white;">
				<uni-table class="tableStyle" style="margin: 0;">
					<uni-tr v-if="interviewYwStatus==4">
						<uni-td align="center">
							请输入不通过原因(必填)
						</uni-td>
					</uni-tr>
					<uni-tr v-if="interviewYwStatus==4">
						<uni-td>
							<u--textarea v-model="failRemark" placeholder="请输入内容"></u--textarea>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">
							请输入保姆备注
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u--textarea v-model="nurseRemark" placeholder="请输入内容"></u--textarea>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-button color="#1e1848" text="确认" @click="updateInterviewState()">
							</u-button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</u-popup>
	</view>
	<!-- 客户验证弹出层 -->
	<view>
		<u-popup mode="center" :show="popupShow" @close="closePopup()" @open="open">
			<view class="bacf" style="width: 600rpx;height: 340rpx;">
				<uni-table class="tableStyle" style="margin: 0;">
					<uni-tr>
						<uni-td align="center">
							请输入客户验证码
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-input v-model="authCode" placeholder="请输入验证码"></u-input>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-button text="确认" @click="verifyAuthCode()" color="#1e1848">
							</u-button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</u-popup>
	</view>
	<!-- 打开小程序二维码 -->
	<u-modal :show="showModal" title="付款码" @confirm="showModal = false">
		<view>
			<u-image :src="imgCode" mode="aspectFit"></u-image>
		</view>
	</u-modal>

	<u-modal :show="showContractModal" @close="showContractModal = false" title="创建类型合同" :showConfirmButton="false">
		<view v-if="orderNeedData.productId == 66&&lookFlag==0">
			<u-row gutter="10">
				<u-col span="12">
					<u-button text="创建月嫂合同" color="#1e1848" throttleTime="1000" @click="checkOrderPay(1)"></u-button>
				</u-col>
			</u-row>
		</view>
		<view v-if="orderNeedData.productId == 336&&lookFlag==0">
			<u-row gutter="10">
				<u-col span="12">
					<u-button text="创建陪读合同" color="#1e1848" throttleTime="1000" @click="checkOrderPay(6)"></u-button>
				</u-col>
			</u-row>
		</view>
		<view v-else>
			<u-row gutter="10" v-if="lookFlag==0">
				<u-col span="4">
					<u-button text="全款合同" color="#1e1848" throttleTime="1000" @click="checkOrderPay(0)"></u-button>
				</u-col>
				<u-col span="4">
					<u-button text="分期合同" color="#1e1848" type="info" throttleTime="1000"
						@click="checkOrderPay(4)"></u-button>
				</u-col>
				<u-col span="4">
					<u-button text="代管合同" color="#1e1848" type="info" throttleTime="1000"
						@click="checkOrderPay(2)"></u-button>
				</u-col>
			</u-row>
		</view>

	</u-modal>
	<u-popup :show="popupFlag" @close="popupFlag = false">
		<view>
			<uni-section title="需求分发" type="line" padding style="height: calc(85vh - 80px);">
				<u-search placeholder="请输入名字或工号" @custom="getAgentList()" v-model="searchMsg" bgColor="#f7f8fa"
					borderColor="#f5f5f5" :clearabled="true">
				</u-search>
				<scroll-view scroll-y="true" class="scroll-Y">
					<view v-for="(item,index) in agentList" :key="index">
						<radio-group class="radioGroupClass">
							<radio style="padding-top: 25rpx;" activeColor="#1e1848" @click="radioCheckAgent(item)"
								:checked="item.id==radioVal.id">
								{{item.realName}}{{item.roleName?'-'+item.roleName:''}}{{item.storeName?'('+item.storeName+')':''}}
							</radio>
						</radio-group>
					</view>
				</scroll-view>
			</uni-section>
		</view>
		<button style="width: 30%;margin-bottom: 40rpx;margin-left: 60%;" class="btnStyle"
			@click="distribution">立即分配</button>
	</u-popup>

	<!-- 服务类型选择 -->
	<u-picker :show="productIdFlag" @confirm="updateProductId" keyName="label" @cancel="productIdFlag=false"
		:columns="columns1"></u-picker>

	<u-popup :show="needFlag" @close="needFlag = false">
		<view>
			<uni-section title="需求模板" type="line" padding style="height: calc(85vh - 85px);">
				<view class="bacf f16">
					<view class="lh40 flac-row">
						<text class="w4">【标题名称】：<sup class="red">*</sup></text>
						<u-input placeholder="请输入标题名称" v-model="needTemplateData.title" border="none" clearable />
					</view>
					<view class="lh40 flac-row">
						<text class="w4">【服务类型】：</text>
						<u-input disabled border="none" v-model="needTemplateData.productName" clearable />
					</view>
					<view class="lh40 border-bottom-2sf3 flac-row">
						<text class="w4">【工作内容】：<sup class="red">*</sup></text>
						<u-input placeholder="请输入工作内容" v-model="needTemplateData.workContent" border="none" />
					</view>
					<view class="lh40 border-bottom-2sf3 flac-row">
						<text class="w4">【工作要求】：<sup class="red">*</sup></text>
						<u-input placeholder="请输入工作要求" v-model="needTemplateData.workRequire" border="none" />
					</view>
					<view class="lh40 border-bottom-2sf3 flac-row">
						<text class="w4">【工作地点】：</text>
						<u-input disabled placeholder="请输入工作地点" v-model="needTemplateData.workAddress" border="none" />
					</view>
					<view class="lh40 border-bottom-2sf3 flac-row">
						<text class="w4">【薪资标准】：</text>
						<u-input disabled border="none" v-model="needTemplateData.salary" clearable />
					</view>
					<view class="lh40 flac-row">
						<text class="w4">【生成链接】：<sup class="red">*</sup></text>
						<view style="margin-left: 140rpx;">
							<u-tag text="点击生成" v-if="!orderNeedData.linkUrl" @click="generateLink" bgColor="#1e1848"
								borderColor="#1e1848"></u-tag>
							<u-tag text="点击复制" v-if="orderNeedData.linkUrl" @click="copyLink" bgColor="#1e1848"
								borderColor="#1e1848"></u-tag>
						</view>
					</view>
					<view class="lh40 flac-row">
						<text class="w4">【家务清单】：</text>
						<view style="margin-left: 140rpx;">
							<u-tag text="点击生成" v-if="!orderNeedData.householdChecklist&&householdChecklistFlag!==1" @click="generateHouseworkList" bgColor="#1e1848"
								borderColor="#1e1848"></u-tag>
							<u-tag text="正在生成中" v-if="!orderNeedData.householdChecklist&&householdChecklistFlag==1"  bgColor="#1e1848" borderColor="#1e1848"></u-tag>
							<u-tag text="点击查看" v-if="orderNeedData.householdChecklist||householdChecklistFlag>1" @click="gotoHouseworkList" bgColor="#1e1848"
								borderColor="#1e1848"></u-tag>
						</view>
					</view>
				</view>
				<u-button v-if="lookFlag==0" text="点击更新" shape="circle" color="#1e1848"
					customStyle="width:90%;margin-top:100rpx;color:#f6cc70;" @click="updateNeedTemplate">
				</u-button>
			</uni-section>
		</view>
	</u-popup>

	<!--保姆查看线索日志--弹窗 -->
	<u-popup :show="nannyFlag" @close="nannyFlag = false">
		<view>
			<uni-section title="人员信息" type="line" padding style="height: 70vh;">
				</u-search>
				<u-empty text="暂无员工查看记录!" width="120" textSize="18"
					v-if="!interestedList.length&&!nannySelectLogList.length" customStyle="padding-top:200rpx">
				</u-empty>
				<scroll-view scroll-y="true" style="height: 60vh">
					<view v-for="(vos,i) in interestedList" :key="i">
						<uni-tr>
							<uni-td>
								<view class="fontStyle f17 flac-row" @click="copyText(vos.employeeName)">
									员工姓名：
									{{vos.employeeName}}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" @click="copyText(vos.employeeNo)">
									员工工号：
									{{ vos.employeeNo }}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" v-if="vos.employeePhone"
									@click="copyText(vos.employeePhone)">
									手机号：
									{{ vos.employeePhone }}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" v-if="vos.workRemark">
									备注：
									{{ vos.workRemark ||'暂无'}}
								</view>
								<view class="fontStyle f17 flac-row">
									<u-tag text="查看简历" @click="readInterested(i);getEmployeeMsg(0,vos.employeeId);"
										color="#FFFFFF" bgColor="#1e1848" plain shape="circle" size="mini"
										borderColor="#1e1848" style="margin-right: 20rpx;"></u-tag>
									<view :style="vos.state!=0?'color: #19be6b':'color:#ff4d4b'">
										{{vos.state!=0?'已读':'未读'}}
									</view>
								</view>
							</uni-td>
						</uni-tr>
					</view>

					<view v-for="(vos,i) in nannySelectLogList" :key="i" v-if="(vos.no||'').includes('B')">
						<uni-tr>
							<uni-td>
								<view class="fontStyle f17 flac-row" @click="copyText(vos.realName)">
									员工姓名：
									{{vos.realName}}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" @click="copyText(vos.no)">
									员工工号：
									{{ vos.no }}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" v-if="vos.phone" @click="copyText(vos.phone)">
									手机号：
									{{ vos.phone }}
									<u-icon name="file-text" style="margin-left: 20rpx;"></u-icon>
								</view>
								<view class="fontStyle f17 flac-row" v-if="vos.workRemark">
									备注：
									{{ vos.workRemark ||'暂无'}}
								</view>
								<view class="fontStyle f17 flac-row">
									查看次数：
									{{ vos.num||"" }}
								</view>
								<view class="fontStyle f17" style="display: flex;">
									最后查看：
									{{ vos.lastSelectTime||"" }}
								</view>
								<view class="fontStyle f17 flac-row"
									v-if="vos.id!=employeeId&&(vos.no||'').includes('B')">
									<u-tag text="查看简历" @click="readOrderNeedsLog(1,vos);getEmployeeMsg(0,vos.id);"
										color="#FFFFFF" bgColor="#1e1848" plain shape="circle" size="mini"
										borderColor="#1e1848" style="margin-right: 20rpx;"></u-tag>
									<view :style="vos.logState!=0?'color: #19be6b':'color:#ff4d4b'">
										{{vos.logState!=0?'已读':'未读'}}
									</view>
								</view>
							</uni-td>
						</uni-tr>
					</view>
				</scroll-view>
			</uni-section>
		</view>
	</u-popup>

	<u-popup :show="isOneBillPopup" :round="10" mode="bottom" @close="cloaseIsOneBillPopup" :closeable="true">
		<view class="f18 fb text-c lh50">合单条件</view>
		<u--form labelPosition="left" labelWidth="100px" ref="form" class="w85 mg-at">
			<u-form-item label="服务费:" prop="userInfo.tags" borderBottom ref="item">
				<u--input v-model="orderNeedData.agencyFee" border="none" placeholder="请输入服务费"></u--input>
			</u-form-item>
			<u-form-item label="中介费:" prop="userInfo.tags" borderBottom ref="item">
				<u--input disabled v-model="orderNeedData.salary" border="none" placeholder="请输入中介费"></u--input>
			</u-form-item>
			<text class="lh40 f16" style="color: #ff4d4b;">* 详细信息需至合单发布页进行编辑</text>
		</u--form>
		<u-button text="去编辑并发布" @click="changeIsOneBill()" color="#1f2120" shape="circle"
			customStyle="width:80%;margin: 30rpx auto"></u-button>
	</u-popup>


	<!-- 无效线索备注 -->
	<view>
		<u-popup mode="center" :show="tagPopupShow" @close="tagPopupShow=false">
			<view class="bacf" style="width: 600rpx;height: 340rpx;">
				<uni-table class="tableStyle" style="margin: 0;">
					<uni-tr>
						<uni-td align="center">
							请输入无效缘由
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u--textarea v-model="orderNeedData.followUpRemark" placeholder="请输入无效缘由"></u--textarea>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-button text="确认" @click="updateOrderNeedsTag(),tagPopupShow=false" color="#1e1848">
							</u-button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</u-popup>
	</view>


	</view>
</template>

<script>
	import base from '@/util/base.js';
	import tableData from '../js/tableData.js';
	import freeAudio from '@/pages-mine/common/components/free-audio.vue';
	export default {
		components: {
			freeAudio
		},
		extends: base,
		data() {
			return {
				// 设置
				// 是否拥有服务分权限
				serviceScoreAuth: true,
				// 详细日志
				showAllLog: false,
				isOneBillPopup: false,
				isEdit: false,
				maxRemark: 12,
				showAllContent: false,
				nannyFlag: false,
				nextOrderFlag: true,
				isShowAudio: false,
				popupFlag: false,
				needFlag: false,
				pageCount: 0,
				orderNeedsLogs: [],
				interviewRecommend: [],
				searchCondition: {
					orderNeedsId: 0,
					current: 1,
					size: 20
				},
				roleId: uni.getStorageSync('roleId'),
				needTemplateData: {},
				searchMsg: '',
				mapFlag: false,
				radioVal: {},
				updateMoneyType: null,
				agentList: [],
				nannySelectLogList: [],
				interestedList: [],
				moneyFlag: true,
				mapImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1675220713630导航 (2).png",
				showContractModal: false,
				tagPopupShow: false,
				contractType: '',
				oldNeedsId: '',
				orderNeedsStatus: '',
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#1e1848',
					},
					customStyle: {
						backgroundColor: '#f6cc70'
					},
				},

				mapImgFlag: false,
				mapConfig: {
					latitude: 24.48541, // 默认定位小羽佳
					longitude: 118.09644,
					scale: 12, // 默认16
				},
				markers: [{
					id: 1,
					latitude: '',
					longitude: '',
					iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1673509966990位置.png',
					width: 30,
					height: 30,
				}],
				needStateFlag: false,
				needTagFlag: false,
				productIdFlag: false,
				recommendStateFlag: false,
				recommendState: 0,
				choiceItemIndex: 0,
				tagColumns: [
					[{
							label: '有效线索',
							key: 1
						},
						{
							label: '无效线索',
							key: 2
						},
					]
				],
				columns: [
					[{
							label: '新分配线索',
							key: 410
						},
						{
							label: '可跟进客户',
							key: 412
						},
						{
							label: '有需求和迫切动机',
							key: 413
						},
						{
							label: '认可服务，对收费不排斥',
							key: 414
						},
						{
							label: '认可服务，认可收费',
							key: 415
						},
						{
							label: '当天可签约',
							key: 417
						},
						{
							label: '毁单客户',
							key: 418
						},
						{
							label: '放弃公海',
							key: 419
						},
						{
							label: '已签约成交',
							key: 420
						},
						{
							label: '长期维护客户',
							key: 429
						},
					]
				],
				columns1: [
					[{
							label: '普通月嫂',
							key: 66
						},
						{
							label: '单餐保姆',
							key: 77
						},
						{
							label: '住家保姆',
							key: 78
						},
						{
							label: '不住家保姆',
							key: 79
						},
						{
							label: '育儿嫂',
							key: 282
						},
						{
							label: '豪宅管家',
							key: 356
						},
					]
				],
				recommendStateColumns: [
					[{
							label: '已推荐',
							key: 0
						},
						{
							label: '已退回',
							key: 1
						},
					]
				],
				showPopup: false,
				needState: '',
				needTag: '',
				signContract: false,
				createOrderFlag: true,
				showModal: false,
				signingFlag: false,
				pushVal: 0,
				isOneBill: 0,
				channelName: '',
				baomuInfoData: {},
				payCodeFlag: false,
				imgCode: '',
				splitSuccess: false,
				splitOrderFlag: true,
				tagText: '',
				toSettlement: false,
				haveInterview: false,
				showDate: false,
				showDate1: false,
				model: {
					interview: {
						address: '',
						date: ''
					}
				},
				tabList: [],
				tableData: [],
				checkedArr: [], //复选框选中的值
				loading: false,
				getCode: 'A',
				details: {},
				msgType: "success",
				msgText: "",
				fdqCurrent: 0,
				fdqItems: ['匹配自选', '门店推荐'],
				updateNeed: false,
				updNeed: true,
				affirmMoney: true,
				authCode: '',
				failRemark: '',
				nurseRemark: '',
				curNow: 0,
				interviewTime: '',
				interviewAddr: '',
				subsectionList: [],
				orderNeedId: 0,
				householdChecklistFlag: 0,
				type: 'center',
				selectAddress: '请选择面试地点',
				screenHeight: 0,
				screenWidth: 0,
				needList: [], //填写项需求列表
				stepsCurenrt: 0,
				nowDate: Number(new Date()),
				value: '',
				allMoney: 10000,
				itemNum: 0,
				sendSMSFlow: 0,
				moneyTotal: 0.00,
				agencyFee: 0.00,
				remarkId: '',
				show: false,
				popupShow: false,
				popupShowMeeting: false,
				meetingRoomImg: '',
				interviewYwStatus: null,
				failRemarkShow: false,
				ywStatus: 0,
				orderNeedData: {},
				lookFlag: 0,
				checkType: 0,
				checkTitle: "",
				workCode: '',
				needStatus: null,
				checkText: "",
				empIndex: '',
				baoMuInterviewId: '',
				timeIndex: 0,
				aNextFlag: true,
				agentName: '',
				flowStatus: null,
				searchData: '',
				billNo: '',
				allData: [],
				selectType: '1',
				index: 0,
				salaryAffirmType: null,
				interviews: [],
				settlementList: [],
				contracts: [{
					emp: {
						customerId: 0,
						id: 1,
						name: "杨华里1",
						age: 51,
						address: "长青路",
						year: 5,
					}
				}],
				emps: [],
				list1: {
					time: "10分钟",
					Date: "0000-00-00"
				},
				empShowArr: [],
				randomNum: '', //生成的随机数数
				meetingRoom: null,
				employeeId: uni.getStorageSync('employeeId') || 0
			}
		},
		// onReady() {
		//   //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
		//   this.$refs.form.setRules(this.rules)
		// },
		mounted() {
			// 获取设备信息，初始化屏幕长宽
			this.handleGetDeviceInfo();
		},
		onLoad(option) {
			if (!uni.getStorageSync('employeeId')) {
				uni.showToast({
					title: '登录失效，请重新登录',
					icon: 'none'
				})
				uni.setStorageSync('redirectUrl', '/pages-work/operations/clew/clewPage?id=' + option.id + '&realName=' +
					option.realName);
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					})
				}, 2000)

			}
			this.orderNeedId = option.id;
			if (option.lookFlag) {
				this.lookFlag = option.lookFlag;
			}
			this.getOrderNeedsById()
			this.selectedIndexs = []
			// this.getData(1)
			this.checkStoreServiceScoreAuth()
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageOrderNeedsLog()
		},
		methods: {
			cloaseIsOneBillPopup() {
				if (this.orderNeedData.isOneBill == 1) {
					this.isOneBill = 1
				} else {
					this.isOneBill = 0
				}
				this.isOneBillPopup = false
			},
			closeDialog() {
				if (this.checkType == 9) {
					if (this.orderNeedData.isOneBill == 1) {
						this.isOneBill = 1
					} else {
						this.isOneBill = 0
					}
				}
			},
			onClickItem(e) {
				this.fdqCurrent = e.currentIndex
				if (this.fdqCurrent == 0) {
					this.getBaoMuList()
				} else {
					this.getInterviewRecommend()
				}
			},
			getInterviewRecommend() {
				this.http({
					url: 'getInterviewRecommend',
					data: {
						orderNeedsId: this.orderNeedId,
						searchData: this.searchData,
						employeeId: uni.getStorageSync("employeeId")
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							for (var i = 0; i < res.data.length; i++) {
								res.data[i].id = res.data[i].id.toString()
							}
							this.interviewRecommend = res.data
						} else {
							this.$refs.uNotify.warning('0结果，请检查该保姆是否上架！')
						}
					}
				});
			},
			lookOldNeeds() {
				uni.navigateTo({
					url: "/pages-work/operations/clew/clewPage?id=" + this.oldNeedsId + '&lookFlag=1'

				})
			},
			customDetails() {
				uni.navigateTo({
					url: "/pages-work/operations/customer/customerDetail?id=" + this.orderNeedData.memberId +
						"&channel=clew"
				})
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 格式化电话号码
			formatPhone(phone) {
				if (!phone) {
					return ''
				}
				if (!this.serviceScoreAuth) {
					var reg = /^(\d{3})\d{4}(\d{4})$/;
					phone = phone.replace(reg, "$1****$2")
				}
				return phone
			},
			// 校验服务分权限
			checkStoreServiceScoreAuth() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/acn/checkStoreServiceScoreAuth",
					method: 'GET',
					path: uni.getStorageSync("storeId") || 0,
					success: res => {
						if (res.code != 0) {
							this.serviceScoreAuth = false
						}
					}
				})
			},
			// 权限提醒
			authTips() {
				if (this.serviceScoreAuth) {
					return
				}
				uni.showModal({
					title: '当前服务分不足60分！暂时无法使用电联功能',
					content: '需要进行服务分规则考试，通过后本周方内可使用该功能！确定前往参加吗？',
					success: res => {
						if (res.confirm) {
							this.http({
								outsideUrl: "https://api.xiaoyujia.com/exam/pushServiceScoreExam",
								method: 'post',
								data: {
									memberId: uni.getStorageSync("memberId") || null,
									employeeId: uni.getStorageSync("employeeId") || null,
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										uni.navigateTo({
											url: '/pages-mine/exam/exam-center'
										})
									} else {
										this.$refs.uNotify.warning(res.msg)
									}
								}
							})
						}
					}
				});
			},
			choiceDateTime() {
				this.showPopup = false
				this.showDate = true
			},
			copyTemplate() {
				if (!this.orderNeedData.needTemplate) {
					this.needFlag = true
					return uni.showToast({
						title: "请先补全模板内容!再复制!",
						icon: 'none'
					})
				} else {
					var that = this;
					let s = " " + this.needTemplateData.title + "【工作地点】" + this.needTemplateData.workAddress +
						"【薪资待遇】" + this.needTemplateData.salary + "【工作类型】" + this.needTemplateData.productName +
						"【工作内容】" + this.needTemplateData.workContent + "【客户要求】" + this.needTemplateData.workRequire +
						"【立即报名】" + this.orderNeedData.linkUrl
					uni.setClipboardData({
						data: s,
						success: function() {
							console.log('复制成功');
						}
					});

				}
			},
			updateNeedTemplate() {
				if (!this.needTemplateData.title) {
					return uni.showToast({
						title: "请输入标题名称!",
						icon: 'none'
					})
				}
				if (!this.needTemplateData.workContent) {
					return uni.showToast({
						title: "请输入工作内容!",
						icon: 'none'
					})
				}
				if (!this.needTemplateData.workRequire) {
					return uni.showToast({
						title: "请输入工作要求!",
						icon: 'none'
					})
				}
				if (!this.orderNeedData.linkUrl) {
					return uni.showToast({
						title: "请生成链接!",
						icon: 'none'
					})
				}
				let param = this.needTemplateData.title + "," + this.needTemplateData.workContent + "," +
					this.needTemplateData.workRequire
				this.http({
					url: "updateNeedTemplate",
					method: 'post',
					data: {
						orderNeedsId: this.orderNeedId,
						needTemplate: param,
						employeeId: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: "更新成功!",
								icon: 'none'
							})
							this.needFlag = false
							setTimeout(() => {
								this.getOrderNeedsById()
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})

			},
			// 获取线索语音播报
			getOrderNeedsVoice() {
				if (this.orderNeedData.introduceVoice) {
					return
				}

				this.http({
					url: 'getOrderNeedsVoice',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						id: this.orderNeedId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.$set(this.orderNeedData, "introduceVoice", res.data)
						}
					}
				})
			},
			// 更新线索语音播报
			updateOrderNeedsVoice() {
				this.http({
					url: 'updateOrderNeedsVoice',
					path: this.orderNeedId || 0,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			copyLink() {
				uni.setClipboardData({
					data: this.orderNeedData.linkUrl,
					success: function() {
						console.log('复制成功');
					}
				});

			},
			gotoHouseworkList(){
				uni.navigateTo({
					url: '/pages-work/operations/clew/houseworkList?needsId=' + this.orderNeedId
				})
			},
			generateHouseworkList(){
				this.http({
					url: "generateHouseworkList",
					method: 'post',
					data: {
						orderNeedsId: this.orderNeedId,
						employeeId: uni.getStorageSync('employeeId'),
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						
					}
				})
				uni.showToast({
					title: "任务提交成功，请稍后查看!",
					icon: 'none',
					duration: 2000
				})
				setTimeout(()=>{
					this.searchCondition.current = 1
					this.pageOrderNeedsLog(1)
					this.needFlag = false
				},1000)
			},
			generateLink() {
				this.http({
					url: "generateLink",
					method: 'post',
					data: {
						orderNeedsId: this.orderNeedId,
						employeeId: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: "生成成功!",
								icon: 'none'
							})
							this.orderNeedData.linkUrl = res.data
						} else {
							uni.showToast({
								title: "生成失败!",
								icon: 'none'
							})
						}
					}
				})
			},
			distribution() {
				if (!this.radioVal.id) {
					return uni.showToast({
						title: '请选择要分配的人员!',
						icon: 'none'
					})
				} else {
					this.http({
						url: 'distributionNeeds',
						method: 'POST',
						data: {
							employeeId: this.radioVal.id,
							id: uni.getStorageSync('employeeId'),
							orderNeedsId: this.orderNeedId
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							setTimeout(() => {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}, 30)
							if (res.code == 0) {
								this.popupFlag = false
								this.setMessage()
							}
						}
					})

				}
			},
			sendSubscribeMsg() {
				this.http({
					url: 'sendSubscribeMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						source: "xyjacn",
						employeeId: this.radioVal.id,
						templateId: 'GD6KOYLLn0ZlmOZLQ0gSVm8ZUC78ex98C-gVNVMk4fk',
						page: "/pages-work/operations/clew/clewIndex?id=" + this.orderNeedId,
						data: {
							"thing2.DATA": {
								"value": '您有新的线索可以接单啦'
							},
							"thing12.DATA": {
								"value": '进入家姐联盟查看吧'
							}
						}
					},
					success: res => {
						if (res.code == 0) {
							this.$toast.toast("入驻成功提醒已发送给员工！")
						} else {
							this.$toast.toast("入驻成功提醒发送失败！" + res.msg)
						}
					}
				})
			},
			setMessage() {
				this.http({
					outsideUrl: 'https://xpush.xiaoyujia.com/goPush/send',
					method: 'POST',
					data: {
						content: '您有一条来自家姐联盟手动分发的线索,请登录家姐联盟->工作台->线索进行查看；https://jiajie.xiaoyujia.com/pages-work/operations/clew/clewIndex?id=' +
							this.orderNeedId,
						agentid: "0",
						no: this.radioVal.no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						// uni.showToast({
						// title: res.data,
						// icon: 'none',
						// })
					}
				})
			},
			getAgentList() {
				this.http({
					url: 'getAgentList',
					method: 'GET',
					data: {
						search: this.searchMsg,
						employeeId: uni.getStorageSync("employeeId")
					},
					success: res => {
						if (res.code == 0) {
							this.agentList = res.data
						} else {
							uni.showToast({
								title: '系统错误!',
								icon: 'none'
							})
						}
					}
				})
			},
			radioCheckAgent(val) {
				this.radioVal = val
			},
			changeIsOneBill() {
				uni.navigateTo({
					url: '/pages-work/operations/clew/oneBill-publish?id=' + this.orderNeedId
				})
				// this.isOneBillPopup = false
				// this.openCheck(9, "确定开启合单吗?")
			},
			changePush() {
				this.openCheck(6, "确定更新线索需求推送状态吗?")
			},
			changeLogShow() {
				let text = this.showAllLog ? '开启后将显示更多日志' : '关闭后只显示基础日志'
				this.$refs.uNotify.warning(text)
			},
			getRzReport(val) {
				uni.navigateTo({
					url: "/pages-work/operations/clew/securityAuth?id=" + val.id +
						'&idCard=' + val.idCard + '&headPortrait=' + val.headPortrait +
						'&realName=' + val.realName
				})
			},
			authentication(data) {
				uni.showModal({
					title: '提示',
					content: '确认执行一键认证操作吗？',
					success: function(res) {
						if (res.confirm) {
							this.http({
								outsideUrl: 'https://biapi.xiaoyujia.com/jizhengyun/jzyAll',
								data: {
									certNo: data.idCard,
									crePerson: uni.getStorageSync("employeeName"),
									employeeId: data.id,
									imgUrl: data.headPortrait || '',
									name: data.realName,
									phone: data.phone,
									storeId: uni.getStorageSync("storeId")
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								method: 'POST',
								success: res => {
									if (res.status == 200) {
										this.http({
											url: 'updateSecurityAuth',
											data: {
												id: data.id,
												securityAuth: 1
											},
											header: {
												"content-type": "application/json;charset=UTF-8"
											},
											method: 'POST',
											success: res => {
												setTimeout(() => {
													uni.showToast({
														title: "认证成功!",
														icon: 'none',
														duration: '2000'
													})
												}, 30)
												this.getBaoMuList()
												this.getInterviewRecommend()
											}
										})
									} else {
										this.$refs.uNotify.error(res.msg)
										// uni.showToast({
										// 	title: res.msg,
										// 	icon: 'none',
										// 	duration: '2000'
										// })
									}
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}.bind(this)
				});
			},
			openMap(item) {
				const longitude = Number(this.markers[0].longitude)
				let name = this.orderNeedData.street
				const latitude = Number(this.markers[0].latitude)
				uni.openLocation({
					latitude: latitude, //纬度 - 目的地/坐标点
					longitude: longitude, //经度 - 目的地/坐标点
					name: name,
					address: "",
					success: function() {
						console.log('success');
					}
				});
			},
			// 打开地图
			lookMap() {
				if (this.mapFlag) {
					this.mapFlag = false
					this.mapImgFlag = false
				} else {
					if (!this.markers[0].longitude || !this.markers[0].latitude) {
						return uni.showToast({
							title: '客户服务地址经纬度获取失败!',
							icon: 'none'
						})
					}
					this.mapFlag = true
					this.mapImgFlag = true
				}
			},
			// 选择地址
			choiceAddress() {
				uni.chooseLocation({
					success: res => {
						if (!res.address) {
							return this.$refs.uNotify.warning('请先选择好地址后再点击确认')
						} else {
							this.orderNeedData.street = res.address
							this.orderNeedData.lat = res.latitude
							this.orderNeedData.lng = res.longitude
						}
					},
					fail: res => {}
				});
			},
			selectNeedTag(val) {
				this.needTagFlag = false
				if (val.value[0].key == 2) {
					this.tagPopupShow = true
				} else {
					this.openCheck(9, "确认更新吗？")
				}
			},
			updateNeedState(val) {
				this.needStateFlag = false
				if (val.value[0].label != this.needState) {
					this.needStatus = val.value[0].key
					this.openCheck(4, "此操作将更新线索级别,是否继续？")
				}
			},
			updateProductId(val) {
				this.productIdFlag = false
				this.orderNeedData.productId = val.value[0].key
				this.needTemplateData.productName = val.value[0].label
			},
			interviewSuccess(val) {
				this.failRemarkShow = true
				this.interviewYwStatus = 3
				this.failRemark = ''
				this.baoMuInterviewId = val
			},
			updateInterviewState() {
				this.http({
					url: 'updateInterviewState',
					data: {
						baoMuInterviewId: this.baoMuInterviewId,
						remark: this.failRemark,
						nurseRemark: this.nurseRemark,
						ywStatus: this.interviewYwStatus,
						orderNeedsId: this.orderNeedId,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						if (res.code == 0) {
							this.getInterviewList(1)
							this.failRemark = ''
							this.nurseRemark = ''
							this.failRemarkShow = false
						}
					}
				})
			},
			updateRecommendState(val) {
				let state = val.value[0].key
				let stateName = val.value[0].label
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/work/updateInterviewRecommend',
					data: {
						id: this.interviewRecommend[this.choiceItemIndex].recommendId || 0,
						recommendState: state
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('推荐状态修改成功！')
							this.$set(this.interviewRecommend[this.choiceItemIndex], 'recommendState',
								state)
							this.$set(this.interviewRecommend[this.choiceItemIndex], 'recommendStateName',
								stateName)
							this.recommendStateFlag = false
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			interviewFail(val) {
				this.baoMuInterviewId = val
				this.interviewYwStatus = 4
				this.failRemarkShow = true
			},
			updateAddress() {
				this.openCheck(1, "确认修改该线索的联系地址吗？")
			},
			updateOrderNeedsContent(index) {
				let tips = ["确定修改线索内容吗？", "确认修改家庭情况吗？", "确认修改经纪人备注吗？", "确认修改服务内容吗？", "确认修改服务要求吗？"]
				this.openCheck(8, tips[index])
			},
			// 点击修改内容按钮
			modifyContent(value) {
				if (value == 0 && !this.isEdit) {
					this.isEdit = true
					this.showAllContent = true
					this.$refs.uNotify.success('请填写好相关内容进行保存吧！')
				} else if (value == 1 && this.isEdit) {
					this.updateOrderNeedsContent(0)
				}
			},
			copyText(val) {
				uni.setClipboardData({
					data: val,
					success: function() {
						console.log('success');
					}
				});

			},
			copyPhone() {
				uni.setClipboardData({
					data: this.orderNeedData.phone,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			copyAddress() {
				uni.setClipboardData({
					data: this.orderNeedData.street,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			getOrderPayCode() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/getOrderPayCode',
					data: {
						billNo: this.billNo
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							this.showModal = true
							this.imgCode = res.data
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			addNursePhoneLog(id, employeeNo) {
				this.http({
					url: 'addNursePhoneLog',
					data: {
						id: this.orderNeedId,
						agentId: uni.getStorageSync("employeeId"),
						channel: employeeNo,
						memberId: id
					},
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {}
				})
			},
			callPhone(val, type) {
				if (type == 2 || type == 3) {
					let employeeNo = ""
					if (type == 3) {
						employeeNo = val.employeeNo
					} else {
						employeeNo = val.no
					}
					this.addNursePhoneLog(val.id, employeeNo)
				}
				uni.makePhoneCall({
					phoneNumber: val.phone, //电话号码
					success: function(e) {},
					fail: function(e) {}
				});
			},
			// 拨打隐私号码
			callPrivatePhone(val) {
				let employeeId = val.employeeId
				let employeeNo = val.employeeNo
				let phone = ""

				this.http({
					url: 'callPrivatePhone',
					data: {
						employeeId: employeeId
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							phone = res.data
						} else {
							// 打不了隐私号就打真实号码
							phone = val.phone
						}
						uni.makePhoneCall({
							phoneNumber: phone,
							success: function(e) {
								this.addNursePhoneLog(val.id, employeeNo)
							},
							fail: function(e) {}
						})
					}
				})
			},
			// 回拨给阿姨（不加密）
			callPhoneBack(index) {
				let employeeId = this.orderNeedsLogs[index].operatorId
				this.http({
					url: 'getEmployeeDtoById',
					method: 'GET',
					path: employeeId || 0,
					success: res => {
						if (res.code == 0) {
							let phone = res.data.phone
							this.$refs.uNotify.success("即将拨打电话（真实号码，可重复回拨）")
							this.$set(this.orderNeedsLogs[index], 'operatorPhone', phone)
							uni.makePhoneCall({
								phoneNumber: phone,
								success: function(e) {},
								fail: function(e) {}
							});
						}
					}
				});
			},
			getSubSheet() {
				this.http({
					url: 'getSubSheet',
					data: {
						billNo: this.billNo
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0 && res.data) {
							this.splitOrderFlag = false
							this.splitSuccess = true
							this.tagText = "该线索已拆单,子单编号:" + res.data
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 3000
							})
						}
					}
				})
			},
			splitOrder() {
				if (this.payCodeFlag) {
					return uni.showToast({
						title: '订单存在欠款!请支付订单后拆单!',
						icon: 'none'
					})
				}
				this.http({
					url: 'orderPayCallBack',
					data: {
						billNo: this.billNo
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							this.addOrderNeedsLog(2, "")
							this.needStatus = 420
							this.orderNeedsStatus = '2'
							setTimeout(() => {
								this.updateOrderNeedState()
							}, 500)
							uni.showToast({
								title: '操作成功!',
								icon: 'none'
							})
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			getBaoMuInfoData() {
				this.http({
					url: 'getBaoMuInfoData',
					path: this.orderNeedId,
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							this.baomuInfoData = res.data
							if (res.data.billNo && res.data.realTotalAmount !== res.data.amount) {
								this.payCodeFlag = true
							}
						}
					}
				})
			},
			checkOrderPay(contractType) {
				this.contractType = contractType;
				this.http({
					url: 'orderDetails',
					data: {
						billNo: this.billNo
					},
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.code == 0) {
							if (res.data.orderState == 99) {
								uni.showToast({
									title: '用户取消订单!',
									icon: 'none'
								})
							} else {
								uni.navigateTo({
									url: '/pages-work/operations/contractEdit?orderNeedId=' + this
										.orderNeedId + '&contractType=' + this.contractType +
										"&employeeNo=" + this.baomuInfoData.no
								})

							}
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			createOrder(productId, totalSplitNum) {
				this.nextOrderFlag = true
				if (this.orderNeedData.billNo) {
					// return uni.showToast({
					// 	title: '该需求已生成过订单！'
					// })
					return this.$refs.uNotify.error("该需求已生成过订单!")
				}
				if (uni.getStorageSync('storeName') !== this.needTemplateData.needsStoreName) {
					uni.showModal({
						title: '提示',
						content: '检测到该线索所属门店与当前登录人所属门店不一致!请谨慎操作!',
						success: res => {
							if (res.confirm) {
								let msg = ""
								if (totalSplitNum == 1) {
									msg = "全款订单"
								} else if (totalSplitNum == 12) {
									msg = "代管订单"
								} else if (totalSplitNum == 11) {
									msg = "分期订单"
								}
								uni.showModal({
									title: '提示',
									content: '确定生成' + msg + "吗？生成后不可修改！",
									success: res => {
										if (res.confirm) {
											this.nextOrderFlag = true
											this.orderNeedsChangeOrder(productId, totalSplitNum)
										} else if (res.cancel) {
											this.nextOrderFlag = false
										}
									}
								});
							} else if (res.cancel) {
								this.nextOrderFlag = false
							}
						}
					});
				} else {
					let msg = ""
					if (totalSplitNum == 1) {
						msg = "全款订单"
					} else if (totalSplitNum == 12) {
						msg = "代管订单"
					} else if (totalSplitNum == 11) {
						msg = "分期订单"
					}
					uni.showModal({
						title: '提示',
						content: '确定生成' + msg + "吗？生成后不可修改！",
						success: res => {
							if (res.confirm) {
								this.nextOrderFlag = true
								this.orderNeedsChangeOrder(productId, totalSplitNum)
							} else if (res.cancel) {
								this.nextOrderFlag = false
							}
						}
					});
				}
			},
			orderNeedsChangeOrder(productId, totalSplitNum) {
				if (this.nextOrderFlag) {
					if (productId) {
						this.orderNeedData.productId = productId;
					}
					let paramData = {
						Name: this.orderNeedData.name,
						Phone: this.orderNeedData.phone,
						ProductId: this.orderNeedData.productId,
						CityId: this.orderNeedData.cityId,
						ArearId: this.orderNeedData.arearId,
						StartTime: this.orderNeedData.startTime,
						StartTimeSpan: this.orderNeedData.startTimeSpan,
						EndTimeSpan: this.orderNeedData.endTimeSpan,
						Tg: this.orderNeedData.tg,
						AgentId: this.orderNeedData.agentId,
						AgentNo: this.orderNeedData.channel,
						MemberId: this.orderNeedData.memberId,
						Channel: 'jjr',
						Remarks: this.orderNeedData.remarks,
						Lat: this.orderNeedData.lat,
						Lng: this.orderNeedData.lng,
						Street: this.orderNeedData.street,
						AreaName: this.orderNeedData.areaName,
						CityName: this.orderNeedData.cityName,
						kd: this.orderNeedData.channel,
						StoreId: uni.getStorageSync("storeId"),
						TotalSplitNum: totalSplitNum,
						depositBillNo: this.orderNeedData.depositBillNo,
						storeIds: this.orderNeedData.needStoreId,
						orderNeedsId: this.orderNeedId
					};
					this.$toast.showLoading();
					this.http({
						url: 'orderNeedsChangeOrder',
						data: paramData,
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: (res) => {
							if (res.code == 0 && res.data) {
								this.addOrderNeedsLog(1, totalSplitNum)
								this.orderNeedData.billNo = res.data
								this.billNo = res.data;
								this.$refs.uNotify.success("订单创建成功，请让用户在保姆管家小程序支付金额！")
								this.createOrderFlag = false
								this.signingFlag = true
								this.payCodeFlag = true
								this.getBaoMuInfoData()
								this.$toast.hideLoading();
							} else {
								setTimeout(() => {
									uni.showToast({
										icon: 'none',
										title: res.data
									})
								}, 30)
							}

						}
					})
				}
			},
			addOrderNeedsLog(type, val) {
				let msg = "";
				let title = "";
				if (type == 1) {
					title = "生成订单"
					if (val == 1) {
						msg = "生成全款订单"
					} else if (val == 12) {
						msg = "生成代管订单"
					} else {
						msg = "生成分期订单"
					}
				} else {
					title = "拆单"
					msg = "已拆单"
				}
				this.http({
					url: 'addOrderNeedsLog',
					data: {
						orderNeedsId: this.orderNeedId,
						operatorId: uni.getStorageSync("employeeId"),
						operator: uni.getStorageSync("employeeName"),
						title: title,
						message: msg,
						type: 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {}
				})
			},
			readOrderNeedsLog(value, vos) {
				let id = vos.id
				if (value == 1) {
					id = vos.lastOrderNeedsLogId
				}

				this.http({
					url: 'updateOrderNeedsLog',
					data: {
						id: id,
						logState: 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							vos.logState = 1
						}
					}
				})
			},
			// 已读感兴趣
			readInterested(index) {
				let data = this.interestedList[index]
				data.state = 1
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/updateInterested',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$set(this.interestedList[index], 'state', 1)
						}
					}
				})
			},
			// 获取视频面试房间
			getMeetingRoomById() {
				if (!this.orderNeedData.meetingRoomId) {
					return
				}
				this.http({
					url: 'getMeetingRoomById',
					path: this.orderNeedData.meetingRoomId,
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.meetingRoom = res.data
						}
					}
				})
			},
			// 创建视频面试房间
			insertMeetingRoom() {
				this.http({
					url: 'insertMeetingRoom',
					data: {
						createMemberId: uni.getStorageSync("memberId") || null,
						createEmployeeId: uni.getStorageSync("employeeId") || null,
						roomTitle: "视频面试",
						roomContent: "参加线索视频面试，匹配优秀员工！",
						roomType: 1,
						orderNeedsId: this.orderNeedId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.meetingRoom = res.data
							this.orderNeedData.meetingRoomId = this.meetingRoom.id
							this.updateOrderNeeds()
							this.$refs.uNotify.success("视频面试创建成功！赶快邀请阿姨进行面试吧！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			addNewInterview() {
				if (this.haveInterview) {
					this.$refs.uNotify.warning("请先处理待面试员工！")
					return
				}
				this.getBaoMuList()
				this.stepsCurenrt = 3
			},
			tabClick(val) {
				let arr = [{}]
				arr[0] = this.allData[val.index]
				this.interviews = arr
			},
			changeCheckbox(e) {
				this.checkedArr = e.detail.value;
			},
			formatDate(time) {
				var date = new Date();
				date.setTime(time.value);
				var month = date.getMonth() + 1;
				var hours = date.getHours();
				if (hours < 10)
					hours = "0" + hours;
				var minutes = date.getMinutes();
				if (minutes < 10)
					minutes = "0" + minutes;
				var time = date.getFullYear() + "-" + month + "-" + date.getDate() +
					" " + hours + ":" + minutes + ":00";
				this.showDate = false
				this.interviewTime = time;
				this.showPopup = true
			},
			// 时间格式化
			formatTime(value) {
				if (!value) {
					return '暂无'
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? '0' + MM : MM
				let d = date.getDate()
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d
			},
			toSteps(val) {
				this.updateNeed = false
				if (this.flowStatus >= val) {
					this.stepsCurenrt = val
					console.log(val)
				}
			},
			previewData() {
				this.stepsCurenrt = 2
				this.getImputedPrice()
			},
			setNeedTag(val) {
				let str = "未标识"
				if (val == 1) {
					str = "有效线索"
				} else if (val == 2) {
					str = "无效线索"
				}
				this.needTag = str
			},
			setNeedState(val) {
				switch (val) {
					case 410:
						this.needState = '新分配线索'
						break;
					case 412:
						this.needState = '可跟进客户'
						break;
					case 413:
						this.needState = '有需求和迫切动机'
						break;
					case 414:
						this.needState = '认可服务，对收费不排斥'
						break;
					case 415:
						this.needState = '认可服务，认可收费'
						break;
					case 417:
						this.needState = '当天可签约'
						break;
					case 418:
						this.needState = '毁单客户'
						break;
					case 419:
						this.needState = '放弃公海'
						break;
					case 420:
						this.needState = '已签约成交'
						break;
					case 429:
						this.needState = '长期维护客户'
						break;
				}
			},
			//获取线索信息
			getOrderNeedsById(flag) {
				this.http({
					url: "getOrderNeedsById",
					data: {
						orderNeedsId: this.orderNeedId,
						employeeId: uni.getStorageSync('employeeId')
					},
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.pageOrderNeedsLog()
							let needsData = res.data.orderNeeds
							this.needTemplateData = res.data.map
							this.oldNeedsId = res.data.oldNeedsId
							this.channelName = res.data.channelName
							if (needsData.isPush == 1) {
								this.pushVal = 1
							}
							if (needsData.isOneBill == 1) {
								this.isOneBill = 1
							}
							this.agentName = res.data.name
							//如果服务地址经纬度转换成功就赋值给map标记点
							if (needsData.longitude && needsData.latitude) {
								this.markers[0].longitude = needsData.longitude
								this.markers[0].latitude = needsData.latitude
								this.mapConfig.longitude = needsData.longitude
								this.mapConfig.latitude = needsData.latitude
							}
							//需求导航栏
							this.subsectionList = res.data.clueVoList
							//设置点联页需求状态
							this.setNeedState(needsData.needStatus)
							this.setNeedTag(needsData.validOrNot)
							this.remarkId = needsData.remarkId;
							this.orderNeedData = needsData

							this.listInterested()
							this.getMeetingRoomById()
							this.getNannySelectLog()
							this.getOrderNeedsVoice()
							this.workCode = needsData.workCode
							this.billNo = needsData.billNo
							this.agencyFee = needsData.agencyFee
							//如果状态=预算或者大于  就获取保存的需求项
							if (needsData.flowStatus >= 2) {
								this.getNeedListByClueId();
							} else {
								//根据客户保存的需求code获取对应的需求项
								this.getNeedList(this.subsectionList[0].id)
							}
							if (needsData.billNo) {
								this.createOrderFlag = false
							}

							//线索状态等于签约或大于  获取保姆信息
							if (needsData.flowStatus >= 5) {
								this.getBaoMuInfoData()
							}
							//状态为结算 获取拆单信息
							if (needsData.flowStatus == 6) {
								this.getSubSheet()
							}
							this.flowStatus = needsData.flowStatus
							if (flag == 1) {
								this.stepsCurenrt = 0
							} else {
								this.stepsCurenrt = needsData.flowStatus
							}
							this.interviewAddr = needsData.street
							this.interviewAddr = needsData.street
							if (needsData.flowStatus > 0) {
								this.aNextFlag = false
							}
							//获取匹配保姆信息
							if (needsData.flowStatus >= 3) {
								this.getBaoMuList()
								this.affirmMoney = false
								this.updNeed = false
							}
							//获取保姆面试信息
							if (needsData.flowStatus >= 4) {
								this.getInterviewList()
							}
						} else {
							if(res.msg=='您无权查看该线索!'){
								this.$refs.uNotify.error(res.msg);
							}else{
								this.$refs.uNotify.error("获取线索信息失败!");
							}
						}
					}
				})
			},
			// 获取线索日志列表
			pageOrderNeedsLog(val) {
				this.searchCondition.orderNeedsId = this.orderNeedId
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/order/pageOrderNeedsLog",
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.searchCondition,
					method: 'POST',
					// hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if(!val){
								this.pageCount = res.data.pages
								this.orderNeedsLogs = this.orderNeedsLogs.concat(res.data.records)
							}
								let count = 0;
								for (var i = 0; i < res.data.records.length; i++) {
									if(res.data.records[i].title==='家务清单'){
										count++;
									}
								}
								if(count==1){
									this.householdChecklistFlag = 1
								}
						}
					}
				})
			},
			closePopup() {
				this.popupShow = false
				this.authCode = ''
			},
			closeFailPopup() {
				this.failRemark = ''
				this.nurseRemark = ''
				this.failRemarkShow = false
			},
			//修改工资/中介费
			updateBaoMuWages() {
				this.http({
					url: "updateBaoMuWages",
					method: 'post',
					data: {
						orderNeedsId: this.orderNeedId,
						salary: this.moneyTotal,
						updateMoneyType: this.updateMoneyType,
						agencyFee: this.agencyFee,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.getOrderNeedsById()
						let msg = ""
						if (res.code == 0) {
							msg = "修改成功!"
						} else {
							msg = "修改失败!"
						}
						return uni.showToast({
							title: msg,
							icon: 'none'
						})
					}
				})
			},
			// 更新线索信息
			updateOrderNeeds() {
				this.http({
					url: "updateOrderNeeds",
					method: 'post',
					data: {
						id: parseInt(this.orderNeedId),
						productId: parseInt(this.orderNeedData.productId),
						street: this.orderNeedData.street,
						remark: this.orderNeedData.remark,
						agentRemark: this.orderNeedData.agentRemark,
						workContent: this.orderNeedData.workContent,
						workRequire: this.orderNeedData.workRequire,
						productRemark: this.orderNeedData.productRemark,
						salaryRemark: this.orderNeedData.salaryRemark,
						afterSalesFee: this.orderNeedData.afterSalesFee,
						meetingRoomId: this.orderNeedData.meetingRoomId,
						workTime: this.orderNeedData.workTime,
						operator: uni.getStorageSync("employeeId") || 0,
						memberId: uni.getStorageSync("memberId") || 0,
						lat: this.orderNeedData.lat,
						lng: this.orderNeedData.lng,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.$refs.uNotify.success("修改成功！")
						this.isEdit = false
						this.updateOrderNeedsVoice()
					}
				})
			},
			confirmSkip() {
				//发送工资短信提醒给客户
				// this.http({
				// 	url: "sendSalaryMsg",
				// 	data: {
				// 		orderNeedsId: this.orderNeedId,
				// 		money: this.moneyTotal
				// 	},
				// 	method: 'post',
				// 	success: res => {}
				// })
				this.http({
					url: "nextSalaryAffirm",
					method: 'post',
					data: {
						orderNeedsId: this.orderNeedId,
						salary: this.moneyTotal,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.stepsCurenrt = 3
							this.flowStatus = 3
							this.affirmMoney = false
							this.updNeed = false
							this.getBaoMuList()
							this.popupShow = false
						} else {
							this.$refs.uNotify.error(res.msg);
						}
					}
				})
			},
			moneyNext() {
				if (this.orderNeedData.salary && this.orderNeedData.agencyFee &&
					this.orderNeedData.salary != 0 && this.orderNeedData.agencyFee != 0) {
					// return uni.showModal({
					// 	title: '提示',
					// 	content: "是否与客户确认？",
					// 	success: res => {
					// 		if (res.confirm) {
								//跳过验证码校验  但通知客户
							return	this.confirmSkip()
					// 		} else if (res.cancel) {
					// 			//客户确认
					// 			this.sendSalaryMsg()
					// 		}
					// 	}
					// });
				}
				let msg = '';
				let flag = 0;
				let number = 0.00;
				let aa = ''
				if (!this.orderNeedData.salary) {
					this.updateMoneyType = 1
					msg = "是否确认阿姨工资为￥" + this.moneyTotal + "？"
					flag = 1;
					number = this.moneyTotal
					aa = "工资预算!"
				} else if (this.orderNeedData.salary && !this.orderNeedData.agencyFee) {
					this.updateMoneyType = 2
					msg = "是否确认中介费为￥" + this.agencyFee + "？"
					flag = 2;
					number = this.agencyFee
					aa = "中介费!"
				}
				uni.showModal({
					title: '提示',
					content: msg,
					success: res => {
						if (res.confirm) {
							if (!number || number == 0) {
								this.$refs.uNotify.error("请输入正确的金额！");
							} else if (isNaN(number)) {
								this.$refs.uNotify.error("请输入数字!")
							} else {
								this.updateBaoMuWages()
							}
						} else if (res.cancel) {
							//重选需求
							if (flag == 1) {
								this.updateNeed = true
								this.selectType = '2'
								this.getNeedListByClueId()
								this.stepsCurenrt = 1
							}
						}
					}
				});
			},
			//客户确认  发送短信给客户
			sendSalaryMsg() {
				this.sendSMSFlow = 1
				this.popupShow = true
				this.http({
					url: "sendSalaryMsg",
					data: {
						orderNeedsId: this.orderNeedId,
						money: this.moneyTotal
					},
					method: 'post',
					success: res => {}
				})
			},
			verifyAuthCode() {
				//sendSMSFlow:1 工资估算确认  2:面试推送   3:保姆面试结果推送
				if (!this.authCode) {
					this.$refs.uNotify.error("请输入验证码！");
				} else
				if (this.sendSMSFlow == 1) {
					//工资估算确认
					this.http({
						url: "salaryAffirm",
						method: 'post',
						data: {
							orderNeedsId: this.orderNeedId,
							verificationCode: this.authCode,
							salary: this.moneyTotal,
							employeeId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.stepsCurenrt = 3
								this.flowStatus = 3
								this.affirmMoney = false
								this.updNeed = false
								this.getBaoMuList()
								this.popupShow = false
							} else {
								this.$refs.uNotify.error(res.msg);
							}
						}
					})
				} else
				if (this.sendSMSFlow == 2) {
					this.getRandomNum()
					let arr = {
						id: this.orderNeedId,
						list: this.checkedArr,
						grouping: this.randomNum,
						verificationCode: this.authCode,
						employeeId: uni.getStorageSync("employeeId"),
						interviewTime: this.interviewTime,
						interviewAddr: this.interviewAddr
					}
					this.http({
						url: 'addOrderDelivery',
						method: 'POST',
						data: arr,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							this.authCode = ''
							if (res.code == 0) {
								this.popupShow = false
								this.stepsCurenrt = 4
								this.flowStatus = 4
								this.tabList = []
								this.getInterviewList()
							} else {
								let msg = res.msg;
								if (!msg) {
									msg = '系统错误';
								}
								this.$toast.errToast(msg);
							}
						}
					})
				} else
				if (this.sendSMSFlow == 3) {
					//保姆面试结果推送
				}
			},
			//设置需求导航栏
			setSubsectionList(val) {
				this.http({
					url: "getWorkNameByCode",
					data: {
						workCode: val
					},
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.subsectionList = res.data
						}
					}
				})
			},
			//获取需求总项、估算工资
			getImputedPrice() {
				this.http({
					url: "getImputedPrice",
					path: this.orderNeedId,
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.moneyTotal = res.data.total || 0.00
							this.itemNum = res.data.itemNum
							if (!this.orderNeedData.agencyFee) {
								this.agencyFee = res.data.total
							}
						} else {
							this.moneyTotal = this.orderNeedData.salary || 0.00
						}
					}
				})
			},
			confirmSelect() {
				if (this.checkedArr.length <= 0) {
					return uni.showToast({
						title: "请选择服务员工!",
						icon: 'none',
						duration: 2500
					})
				}
				for (var i = 0; i < this.checkedArr.length; i++) {
					for (var j = 0; j < this.emps.length; j++) {
						if (this.emps[j].id == this.checkedArr[i] && this.emps[j].securityAuth != 1) {
							return uni.showToast({
								title: "选择的服务员工存在未认证!请认证后再邀约面试!",
								icon: 'none',
								duration: 2500
							})
						}
					}
				}
				this.showPopup = true
			},
			//获取需求项
			getNeedListByClueId() {
				this.needList = []
				this.http({
					url: "getNeedListByClueId",
					data: {
						orderNeedsId: this.orderNeedId,
						type: this.selectType
					},
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.moneyFlag = false
							this.getImputedPrice()
							this.subsectionList = []
							if (this.selectType == '2') {
								this.setSubsectionList('')
								this.curNow = 0
								this.getCode = 'A'
							} else {
								this.setSubsectionList(res.data[0].backup)
								this.getCode = res.data[0].workCode
							}
							for (var i = 0; i < res.data.length; i++) {
								this.needList.push(res.data[i])
							}
						}
					}
				})
			},
			salaryReckon(val) {
				this.salaryAffirmType = val
				this.openCheck(0, "请确认选填需求无误后再提交！")
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			updateOrderNeedState() {
				this.http({
					url: 'updateOrderNeedState',
					data: {
						orderNeedsId: this.orderNeedId,
						needStatus: this.needStatus,
						status: this.orderNeedsStatus,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						let msg = ""
						if (res.code == 0) {
							msg = "操作成功!"
							this.getOrderNeedsById(1)
						} else {
							msg = "操作失败!"
						}
						uni.showToast({
							title: msg,
							icon: 'none'
						})
					}
				})
			},
			// 确认框功能
			popupCheck() {
				//保存需求
				if (this.checkType == 0) {
					this.needList.push({
						orderNeedId: this.orderNeedId
					});
					this.http({
						url: 'saveNeedList',
						data: {
							workSkillsList: this.needList,
							employeeId: uni.getStorageSync("employeeId"),
							id: this.orderNeedId,
							type: this.salaryAffirmType,
							workCode: this.orderNeedData.workCode
						},
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.moneyFlag = false
								this.flowStatus = 2
								this.stepsCurenrt = 2
								this.getOrderNeedsById()
								this.getImputedPrice()
							} else {
								return uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					});
				}
				//修改联系地址
				if (this.checkType == 1) {
					this.http({
						url: 'updateOrderNeedAddress',
						data: {
							orderNeedsId: this.orderNeedId,
							address: this.orderNeedData.street,
						},
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: (res) => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
				}
				if (this.checkType == 4) {
					this.updateOrderNeedState()
				}

				if (this.checkType == 6) {
					this.http({
						url: "updateOrderPushState",
						method: 'post',
						data: {
							orderNeedsId: this.orderNeedId,
							isPush: this.pushVal,
							employeeId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							this.getOrderNeedsById()
						}
					})
				}
				if (this.checkType == 7) {
					this.http({
						url: "updateAgentRemark",
						method: 'post',
						data: {
							id: this.orderNeedId,
							agentRemark: this.orderNeedData.agentRemark,
							agentId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							this.getOrderNeedsById()
						}
					})
				}
				if (this.checkType == 8) {
					this.updateOrderNeeds()
				}
				if (this.checkType == 9) {
					this.orderNeedData.validOrNot = 1
					this.updateOrderNeedsTag()
				}
			},
			updateOrderNeedsTag() {
				this.orderNeedData.operate = uni.getStorageSync('employeeId')
				this.http({
					url: "updateOrderNeedsTag",
					method: 'post',
					data: this.orderNeedData,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.getOrderNeedsById()
					}
				})
			},
			radioCheck(index) {
				if (this.needList[index].defaultValue == 1) {
					this.needList[index].defaultValue = 0
				} else {
					this.needList[index].defaultValue = 1
				}
			},
			sectionChange(index) {
				this.curNow = index;
				this.getCode = this.subsectionList[index].id
				this.getNeedList(this.subsectionList[index].id)
			},
			getNeedList(val) {
				this.http({
					url: 'getNeedList',
					method: 'GET',
					data: {
						code: val,
						display: 1
					},
					success: res => {
						if (res.code == 0) {
							this.getCode = res.data[0].workCode
							let flag = true;
							for (var i = 0; i < this.needList.length; i++) {
								if (this.needList[i].workCode == res.data[0].workCode) {
									flag = false
								}
							}
							if (flag) {
								for (var i = 0; i < res.data.length; i++) {
									this.needList.push(res.data[i])
								}
							}
						}
					}
				});
			},
			/**
			 * 获取当前设备信息
			 */
			handleGetDeviceInfo: function() {
				uni.getSystemInfo({
					success: deviceInfo => {
						this.screenHeight = deviceInfo.screenHeight;
						this.screenWidth = deviceInfo.screenWidth;
					}
				});
			},
			back() {
				// uni.redirectTo({
				// 	url: '/pages/orderNeeds/index'
				// });
				uni.navigateBack({
					url: '/pages/mine/mine'
				});
			},
			open() {
				console.log('open')
			},
			// 是否显示显示日志
			showLog(vos) {
				if (this.showAllLog) {
					if (vos.type != 5) {
						return true
					}
					if (vos.type == 5 && (vos.operatorNo || '').includes('B')) {
						return true
					}
				} else {
					if (vos.type != 5) {
						return true
					}
				}
				return false
			},
			getEmployeeMsg(value, id) {
				if (value == 0) {
					return uni.navigateTo({
						url: "/pages-mine/works/employee-detail?baomuId=" + id
					})
				}
			},
			del(item) {
				console.log(item);
			},
			//生成四位随机数
			getRandomNum() {
				this.randomNum = ''
				for (var i = 0; i < 4; i++) {
					let num = Math.floor(Math.random() * 10);
					this.randomNum += num
				}
			},
			chooseEmp() {
				if (!this.interviewTime) {
					this.$toast.errToast("请选择面试时间！");
				} else if (this.checkedArr.length <= 0) {
					this.$toast.errToast("请选择保姆信息！");
				} else {
					this.popupShow = true
					this.sendSMSFlow = 2
					this.http({
						url: "sendInterviewMsg",
						method: 'post',
						data: {
							interviewTime: this.interviewTime,
							id: this.orderNeedId,
							listId: this.checkedArr
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							this.showPopup = false
							if (res.code == 0) {
								this.authCode = res.data
							} else {
								this.$refs.uNotify.error(res.msg);
							}
						}
					})
				}
			},
			settlement() {
				this.http({
					url: 'updateOrderNeedState',
					data: {
						orderNeedsId: this.orderNeedId,
						flowStatus: 5,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.toSettlement = false
							this.getBaoMuInfoData()
							this.getOrderNeedsById()
						} else {
							uni.showToast({
								title: '系统错误!',
								icon: 'none'
							})
						}
					}
				})
			},
			empInformation(employeeNo) {
				this.getBaoMuList()
				let timer = setTimeout(() => {
					for (var i = 0; i < this.emps.length; i++) {
						if (this.emps[i].no === employeeNo) {
							this.details = this.emps[i]
						}
					}
				}, 1000)
			},
			empChoose() {
				let arr = []
				for (let i in this.checkedArr) {
					for (let j in this.emps) {
						if (this.checkedArr[i] == this.emps[j].id) {
							arr.push(this.emps[j])
						}
					}
				};
				this.empShowArr = arr
			},
			getBaoMuList() {
				this.http({
					url: 'getRecommendNanny',
					data: {
						orderNeedsId: this.orderNeedId,
						searchData: this.searchData,
						employeeId: uni.getStorageSync("employeeId")
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							for (var i = 0; i < res.data.length; i++) {
								res.data[i].id = res.data[i].id.toString()
							}
							this.emps = res.data
							if (!this.emps.length) {
								this.$refs.uNotify.warning('未查询到结果哦，请检查阿姨是否入录！')
							}
						} else {
							this.$refs.uNotify.warning('未查询到结果哦，请检查阿姨是否入录！')
						}
					}
				});
			},
			getNannySelectLog() {
				this.http({
					url: 'getNannySelectLog',
					data: {
						id: this.orderNeedId
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.nannySelectLogList = res.data
						}
					}
				});
			},
			// 获取感兴趣人员
			listInterested() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/listInterested',
					method: 'POST',
					hideLoading: true,
					data: {
						type: 0,
						orderNeedsId: this.orderNeedId,
						employeeType: 10
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.interestedList = res.data
						}
					}
				})
			},
			getInterviewList(val) {
				this.http({
					url: 'getInterviewList',
					data: {
						orderNeedsId: this.orderNeedId,
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.allData = res.data
							let arr = [{}]
							arr[0] = res.data[0]
							this.interviews = arr
							this.toSettlement = false
							for (var i = 0; i < res.data.length; i++) {
								let data = {
									id: i,
									name: res.data[i].remark
								}
								if (!val == 1) {
									this.tabList.push(data)
								}
								if ((!this.toSettlement && this.flowStatus <= 4) || val == 1) {
									for (var j = 0; j < res.data[i].deliveryVos.length; j++) {
										if (res.data[i].deliveryVos[j].ywStatus == 3) {
											this.toSettlement = true
										}
										if (res.data[i].deliveryVos[j].ywStatus == 1) {
											this.haveInterview = true
										}
									}
								}
							}
						}
					},
				});
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.meetingRoomImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			// 打开视频面试
			openMeeting() {
				uni.navigateTo({
					url: '/pages-mine/room/meeting?roomId=' + this.orderNeedData.meetingRoomId
				})
			},
			// 打开弹窗图片
			openPopupImg() {
				this.$refs.uNotify.success("点击保存面试邀请海报，发送给合适的阿姨吧!")
				if (this.meetingRoomImg) {
					this.popupShowMeeting = true
					return
				}

				let scene = "id/" + this.orderNeedData.meetingRoomId
				let param = {
					source: "aygj",
					path: "pages-mine/room/meeting",
					type: 1,
					scene: scene,
					title: "小羽佳-视频面试",
					backImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_meetingRoom.png'
				}
				this.http({
					url: 'getEmployeePoster',
					method: 'POST',
					hideLoading: true,
					data: param,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.meetingRoomImg = res.data
							this.popupShowMeeting = true
						}
					},
				})

			},
			// 播放音频
			playAudio() {
				if (this.isShowAudio) {
					uni.$emit('stop')
				} else {
					uni.$emit('play')
				}
				this.isShowAudio = !this.isShowAudio
			},
		},
		onUnload() {
			uni.$emit('stop')
		},
	}
</script>

<style scoped>
	/deep/.u-steps--row {
		justify-content: center !important;
	}

	/deep/.u-steps-item {
		margin: auto 10rpx;
	}

	/deep/ .u-steps-item__wrapper {
		background-color: transparent !important;
	}

	/deep/.u-steps-item__line {
		width: 50rpx !important;
		left: 67rpx !important;
	}

	>>>.u-steps-item__wrapper--row {
		width: 80rpx !important;
		height: 80rpx !important;
		border-radius: 100% !important;
	}

	>>>.u-steps-item__line--row {
		background-color: #eee !important;
		top: 40rpx !important;
	}

	/deep/.u-steps-item__wrapper__circle {
		width: 50rpx !important;
		height: 50rpx !important;
		background-color: #fff !important;
	}

	/deep/.u-text__value {
		font-size: 1.3rem !important;
	}

	/deep/.u-input__content__field-wrapper__field {
		text-align: center !important;
	}

	/deep/.u-steps-item__wrapper__circle__text {
		font-size: 1.3rem !important;
		color: #1e1848 !important;
	}

	/deep/.u-icon__icon {
		font-size: 1.8rem !important;
		color: #1e1848 !important;
	}

	/deep/.u-text__value--content {
		color: #fff !important;
	}

	/deep/.u-text__value--main {
		color: #fdd472 !important;
	}

	/deep/.u-form {
		width: 80% !important;
		margin: auto !important;
		line-height: 64rpx !important;
	}

	/deep/.u-form-item__body__left {
		width: 200rpx !important;
	}

	>>>.u-cell__body {
		padding: 0rpx !important;
	}

	/deep/.table--border {
		border: none !important;
		border-bottom: 2rpx solid #c5c5c59e !important;
	}

	/deep/ .uni-table-scroll {
		box-shadow: 10rpx 10rpx 16rpx #9999995c;
	}

	/deep/.uni-table-td {
		line-height: 64rpx !important;
	}

	/deep/ .u-search {
		width: 90% !important;
		margin: 20rpx auto !important;
		padding: 20rpx 0 !important;
	}

	/deep/.u-tabs__wrapper__scroll-view {
		width: 90% !important;
		margin: auto !important;
	}

	/deep/.uni-section .uni-section-header__content .distraction {
		font-size: 32rpx !important;
	}
</style>

<style lang="scss">
	.thStyle {
		color: #555;
		font-size: 1.25rem;
	}

	/deep/ .mask .show {
		background-color: none !important;
	}

	// /deep/.u-modal__content {
	// 	text-align: center;
	// 	flex-direction: column;
	// }
	.textClass {
		margin-left: 20rpx;
		font-size: 30rpx;
		font-weight: bold;
	}

	.fontStyle {
		font-weight: bold;
		letter-spacing: 4rpx;
	}

	.nanay-work-contain {
		padding-top: 0rpx;
		width: 80%;
	}

	.radioGroupClass {
		margin-left: 35rpx;
		padding-top: 25rpx;
	}

	.needClass {
		margin-left: 35rpx;
		padding-top: 25rpx;
	}

	.index {
		// background-color: #b5a8a042;
		height: 100%;
	}

	.index .container-main {
		padding: 24rpx 32rpx;
	}

	.tableStyle {
		border-radius: 20rpx !important;
		margin-top: 20rpx !important;
		box-shadow: 10 0 20rpx #d3d3d3 !important;
		-moz-box-shadow: 0 0 20rpx #d3d3d3 !important;
		-webkit-box-shadow: 0 0 20rpx #d3d3d3 !important;
	}

	.scroll-Y {
		height: 700rpx;
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
	}

	.scroll-view-item {
		height: 300rpx;
		line-height: 300rpx;
		text-align: center;
		font-size: 36rpx;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 100%;
		height: 300rpx;
		line-height: 300rpx;
		text-align: center;
		font-size: 36rpx;
	}

	.content {
		.checkInfo {
			/deep/uni-checkbox .uni-checkbox-input {
				border-radius: 50%;
			}

			/deep/uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
				border-color: #ddd;
				color: #fff !important;
				background-color: #1e1848 !important;
			}

			/deep/uni-checkbox .uni-checkbox-input {
				border-color: #ddd;
			}

			/deep/uni-checkbox .uni-checkbox-input:hover {
				border-color: #ddd;
			}

			.dataInfo {
				width: 90%;
				margin: 20rpx auto;

				.dataList {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 2rpx solid #E7E9EE;
					width: 100%;
					padding-bottom: 20rpx;
					padding-top: 20rpx;

					.textImg {
						display: flex;
						align-items: center;

						.img {
							padding: 10rpx;

							image {
								width: 90rpx;
								height: 100rpx;
								display: block;
							}
						}

						.text {
							padding-left: 20rpx;

							text {
								display: block;
								font-size: 32rpx;
								color: #000;
								font-weight: bold;
							}
						}
					}
				}
			}
		}
	}

	.kite-classify-scroll {
		width: 90%;
		margin: auto;
		overflow: hidden;
		white-space: nowrap;
	}

	.kite-classify-cell {
		padding: 0 20rpx;
		text-align: center;
		border: 2rpx solid #1e1848;
		color: #1e1848;
		display: inline-block;
	}

	.kite-classify-cell:active {
		color: #fff;
		background-color: #1e1848;
	}
</style>