	/*
		分列滑动栏目-通用样式
	*/
   
	// 头部图片
    .head-img{

	   img{
		   display: block;
		   width: 100%;
		   height: auto;
	   }
    }
	
	// 顶部菜单栏
	.swiper-menu {
		display: block;
		margin: 20rpx auto 0 auto;
		width: 90%;
	}

	// 内容滑块部分
	.swiper {
		display: block;
		width: 100%;
		// height: 500rpx;
	}

	// 单列滑块
	.swiper-item {
		display: block;
		width: 100%;
		// height: 100%;
	}

	// 栏目
	.swiper-tab {
		width: 90%;
		height: auto;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
	}
	
	// 栏目头部
	.swiper-head {
		width: 100%;
		height: 100rpx;
		display: flex;
	}

	// 栏目标题
	.swiper-title {
		width: 80%;
		font-size: 36rpx;
		height: 100rpx;
		line-height: 100rpx;
		margin-left: 30rpx;
	}
	
	// 栏目提示
	.swiper-tips {
		width: 15%;
		margin: 30rpx 20rpx 0 0;
		text-align: center;
		border-radius: 20rpx;
		width: 120rpx;
		height: 40rpx;
		line-height: 40rpx;
		background-color: #f4f4f5;
	}
	
	// 栏目内容
	.swiper-content {
		display: flex;
		flex-direction: row;
	}

	// 栏目左边
	.content-left {
		float: left;
		width: 25%;
		height: 300rpx;

		img {
			display: block;
			width: 140rpx;
			height: 140rpx;
			border-radius: 20rpx;
			margin: 0rpx 0 0 14%;
		}
	}
	
	// 栏目右边
	.content-right {
		float: right;
		width: 70%;
		height: auto;
		padding: 0 3%;
	}

	// 内容标题
	.content-title {
		font-size: 36rpx;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		/*! autoprefixer: ignore next */
		-webkit-box-orient: vertical;
		/* autoprefixer: on */
		-webkit-line-clamp: 2;
		line-height: 60rpx;
	}
	
	// 栏目单行文本
	.content-text,.content-text-right {
		text {
			font-size: 30rpx;
			line-height: 50rpx;
			
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			/*! autoprefixer: ignore next */
			-webkit-box-orient: vertical;
			/* autoprefixer: on */
			-webkit-line-clamp: 3;
			line-height: 60rpx;
		}
	}
	
	// 栏目单行文本-右侧
	.content-text-right {
		float: right;
		margin: 20rpx 10rpx 0 0;
		text{
			font-size: 36rpx;
		}
	}

	// 空数据图片
	.blank-img {
		img {
			display: block;
			width: 240rpx;
			height: 240rpx;
			margin: 20rpx auto;
		}
	}
	
	// 空数据提示文本
	.blank-img-text {
		text {
			display: block;
			width: 100%;
			text-align: center;
			font-size: 36rpx;
		}
	}