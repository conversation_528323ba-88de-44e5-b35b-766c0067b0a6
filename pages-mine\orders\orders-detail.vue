<template>
	<view>
		<view style="background-color: white;">
			<view class="yt-list-cell b-b">
				<u-steps activeColor="#1e1848" :current="steps" v-if="pro && pro.orderState !=99">
					<u-steps-item title="已接单"></u-steps-item>
					<u-steps-item title="已派单"></u-steps-item>
					<u-steps-item title="执行中"></u-steps-item>
					<u-steps-item title="开始服务"></u-steps-item>
					<u-steps-item title="服务结束"></u-steps-item>
				</u-steps>
				<view v-if="pro && pro.orderState ==99">
					该订单已取消，如有疑问请联系客服
					<u-button :hairline="true" type="warning" shape="circle" text="退款详情" @click="jumpPayRefund">
					</u-button>

				</view>
			</view>
		</view>
		<u-divider></u-divider>

		<!-- 地址 -->
		<view class="address-section">
			<view class="order-content">
				<text class="yticon icon-shouhuodizhi"></text>
				<view class="cen">
					<view class="top">
						<text class="name">{{pro.contact}}</text>
						<text class="mobile">{{pro.tel}}</text>
					</view>
					<text class="address">
						<text v-if="pro.endStreet !=null">出发地：</text>
						{{pro.orderAddress}}
					</text>
					<text class="address" v-if="pro.endStreet !=null">结束地：{{pro.endStreet}}</text>
				</view>
				<!-- <text class="yticon icon-you">ssss</text> -->
			</view>
			<image class="a-bg" style="height: 5rpx;"
				src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAu4AAAAFCAYAAAAaAWmiAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6Rjk3RjkzMjM2NzMxMTFFOUI4RkU4OEZGMDcxQzgzOEYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6Rjk3RjkzMjQ2NzMxMTFFOUI4RkU4OEZGMDcxQzgzOEYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGOTdGOTMyMTY3MzExMUU5QjhGRTg4RkYwNzFDODM4RiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGOTdGOTMyMjY3MzExMUU5QjhGRTg4RkYwNzFDODM4RiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PrEOZlQAAAiuSURBVHjazJp7bFvVHce/1/deXzuJHSdOM+fhpKMllI2SkTZpV6ULYrCHQGwrf41p/LENVk3QTipSWujKoyot1aQN0FYQQxtsMCS2SVuqsfFYHxBKYQNGV9ouZdA8nDipH4mT+HFf+51rO0pN0japrw9HreLe3Pqc3/me3+f3uFdIvfVuDIAPix1C9oceicFRVQWlvRWCkL1omqb1Of9z9rXZY65rhcO6x5ove19oWkX/RAaSMLOEkg+2Zt0wEcvoWOZzYZnXeWEbzmP7XPs11//LnOiDEY9DkGRwGw5a59QUTM2As+1qiD5v0TUvvC9Bc52KpmDSnju4ic7+CIinNVQoElYtcUM8jx2L1bzwPn14DOrHZ0hzEdxOPJtW16FH45CvuBzyZU22aH7Od9LnU/E0xpMqJG6iZ309qeqYNoA1gTJ4ZdF2zY2pJNSTfYCmkb85+GnO1hIbh+DzQVndaiHYTs3ZGJpifE/DyVnzi+X7pWqen8/i+8kPYUSjEORPCd9XtUKs9Fi+KMxjVzE0n9ZNnIgkYXwK+B5LafC4JKyudcMxD2+LqblGfNcY30VxJsfhcOCJ7xr02ATkluXE96DtmrPvPxFLIUH7zY3vOc0Z39O0oGtqy1DlFIuu+Zx8P/Ffa8/hEBey4rh0uuPWS6S6CRUhyGjG0hcfOWex+c9zXSsE5HmFzseP3H294Sl847VBRGJJQHTwy9wJNKAE7otLfXi2K3hRgeB81+bar8IDEPvFMxi6cxebnMx2cjrnDmiIwUAGDTvugX9de9E1L7R9NK1jc+8gnj8dy2rOKY/JRhgV8Cr405ea0HEBOxajeaHtySPvYvD2bUgdP0lmuzkl7oLl6Wn0wX/Dd1D/xG5bNc/f+7NjY9jyzghlM5QxS/ySOGt+Wlt3WwDXBz22a86gHrqjG7Hnekhz5uciN9NVDEBxXYng87vgEoqveZ7y+XsPE99vOTyAs1SkU+bOT3NKIJHUsIb4/rsL8L0YmrMRffQ3GNn8c6L7BOnu4pW10/xR4nsK9T+5FzWda2fXcEXTfLbtYUrc7joSwguno9kilZfsLNmgtaBcxv7rmudN2i9Fc8YRlsvkr6aOvoeBHxDf//MBzVfGke9p8vVhVN2wAQ1P7rFdczYeO34Wm4+Gsr4mcqzWMqQ5IX5rex3W1pUXX/PCRlwkjpEtDyLy9B8sPxcgLWzFpy7rWlTH3eq66AbUj0fh7lyJhn27oFzVck41mTdgdnU5+3fzbczsqqVwQ14aSuCrhwZoo3UEqCLW6biZJZZZom0e0UhlSiY3rvBjd0cdfLJjTrsXYvN8e5TvPEZ2PYbw9l9CrKqAWFNB+2+W/oiTc2l9BFefC/WPdqPyuxts1/zMlIrbqVB7OZSgaSWrC2eUWHUGcLa2MVrLyho3ftvVhNYq1ye6J8XUnI3JFw8idNdOaB+GIS+vsZhf6gMvsP1OJKGFx1H9o1sQeOSBXOcfc9pQDM3Z2PGvEeykxJ0l7AGaTyux4YKVLpOvs0BO/v0UQf17LdUzwdcskuaFHRo1NIrQxq1I9ByEc2kj+ZwDZsk1z/H9I+L7us+j4fHdUFa2FF3zQtv3DyTwrTcGoVFxXOeWKZEoPeNm+E66b7zSj71r6+ERHXN21C5V85nPmo7I3scRvncfxOoyiP7y0vNdyMZ17X9xmGR+43MPwvvtm23XnPH9h68P4u8U2yuJ7wonvmu0pigValf73XhmfRCt1S5bNbd6QK/0ov+2bhjDE8T3aj58p5hujCehjsZQs+lWLNl5N0RvuS2a5z/T8cLOd8K4/72wxdaAXHq+syGT7sOM7xLxvaOe+F5lu+bqYBjDd25H4s+vQ26ugSBL1lsEC+m4C8fQvMhXZXTa/CR8N96MekrapWCdvc1t+rvn32PY3juYrc7cEjjonFuMYQm97QsBPLSq1v7pKJAPbbwHZ3ueoqCyhJIJStqto8/BdMTh8q1A8PcPo+xrXbbP97ehSXydFWpjU0CZzO8xInM+CqSdTV688OVmBBT7O6DRh/dhYOt20nqSdK+f1RIqdRMqRXgrR90Dm+Dfsdn2+QYpeH7/8CBe+mAsq7nIsevKEjivgv1dQdzYUGH7dMlXe3FmwxZMTRyFgiZkW48mF0/XMYWqm75JfH8IUmPA1tlUMnHv+8T3N3J8d3Hkey6I3re6Djvaam1v/urhswjdsQ2jf/kVJRI1xHdPrh1lltzTWUxXai5H07N74P7KettnPDQyjWtf/ohglyJfl7jz/drP+vDrzgYsLZdtP2PRnz6B/u4t9I+U9cYCH81hddoFuBG4bxNq7v9xSfh+G/H9wKkIwF5JkR38fF3VLb73dDXhpsYS8P0Vxve7MZ14E04EkX2SumDj40Lkjz2LS9x1nZVqcK1rh1L/GaiZDB1GYwGPRi9+sA4r63odGEjAoKTZS0mTwUtoS2sTPioc1jd64KJqNZXRP9EtLFrLT5KQOd6H1JtvQ/SUQ1CUC1Z/tjp5MgXn51bAfc1VpAUVb6pqi+bsqRlrOB0ITSI0kUa1IvF7JcribPbxZnt9BYIeBZm0ap1BO2yHLMOIxjH111chmDocXg9XzZFR4fD74e5cA9GtQEulbLGbfaNMvv4+BfG3hiet9wxlUeDGdDPn68uqXVgVKKezbiBN/HHYoTnrqlORkDx0BHr/ABzVVbknbZysZ3wnRVyda6HU1UIjvpt28p2C+T+GEtYeeEh3jqcdKjl2BcWY65q9UAQb+c6+k3iePnaS+P5Pq8spOJ38fJ09RVI1OFuWo6xtJXSD+J6xh++OHN8PEt8HxtNY4pbAczC+m2Rnh8V3J9Q0Fa4LeG97YQdehj4aoSL9NZiZNMTKStp6g5/x5NsW37vWQaS1WXzPHvjihzYS/lgshbeJ75WySHm7wNXXk8SbK/xutOX4ntHtYRxE0eJn6uARaGf6ie++7GPNxVkf/78AAwCn1+RYqusbZQAAAABJRU5ErkJggg==">
			</image>
		</view>


		<!-- 		<view class="tuanzhang" v-for="(item,index) in pro">
			<view class="tz-l">
			</view>
			<view class="tz-m">该团由团长代收包裹（免运费）</view>
			<view class="tz-r">团长：{{item.r}}</view>
		</view> -->
		<view class="goods-section">

			<view class="g-item" style="padding-top: 10px;">
				<image src="https://statics.xiaoyujia.com/productCategory/banner/2018/09/21/14420968791/750x400.jpg">
				</image>
				<view class="right">
					<text class="title clamp">{{pro.productName}}</text>
					<view class="price-box">
						<text class="price">￥{{pro.totalAmount}}</text>
						<!-- <text class="number">x{{item.number}}</text> -->

						<!-- <button style="height: 30px; line-height: 30px;margin-left: 30%;" type="default" size="mini"
							@click="jump_tograde(zxc)">评价</button> -->
					</view>
				</view>
			</view>
		</view>

		<!-- 金额明细 -->
		<view class="yt-list">
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">订单编号</text>
				<text class="cell-tip">{{billNo}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">创建时间</text>
				<text class="cell-tip">{{pro.createTime}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">服务时间</text>
				<text class="cell-tip">{{pro.startTime}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">商品金额</text>
				<text class="cell-tip">￥{{pro.realTotalAmount}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">应付金额</text>
				<text class="cell-tip">￥{{pro.payValue}}</text>
			</view>
			<!-- <view class="yt-list-cell b-b">
				<text class="cell-tit clamp">优惠金额</text>

				<text class="cell-tip red">-￥{{item.discount}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">运费</text>

				<text class="cell-tip">{{item.freight}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">配送方式</text>
				<text class="cell-tip">{{item.mode}}</text>
			</view> -->
			<view class="yt-list-cell b-b" v-if="pro.serviceRemark != null">
				<text class="cell-tit clamp">备注</text>
				<text class="cell-tip">{{pro.serviceRemark}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">订单状态</text>
				<text class="cell-tip">{{pro.orderStateName}}</text>
			</view>
			<view class="yt-list-cell b-b">
				<text class="cell-tit clamp">服务员工</text>
				<text class="cell-tip">{{pro.serviceNo == null ? '' :pro.serviceNo}} </text>
			</view>
			<view class="yt-list-cell b-b">

				<!-- <text class="cell-tip">张三三(1234),张三三(1234),张三三(1234),张三三(1234) </text> -->
				<!-- <textarea :value="pro.serviceNo" disabled="true"></textarea> -->
			</view>
		</view>
		<u-text v-if="pro && pro.amount != pro.realTotalAmount  && pro.orderState == 10" type="error"
			text="注:未支付的订单将在十分钟内自动取消,请您尽快付款!"></u-text>
		<view class="btn">
			<view style="position: fixed; width:100%; bottom:20rpx">
				<u-row>
					<u-col span="6">
						<u-button type="warning" text="支付订单" @click="showPopu = true"
							v-if="pro && pro.orderState !== 99 && pro.amount != pro.realTotalAmount "></u-button>
					</u-col>
					<u-col span="6">
						<u-button v-if="pro &&  pro.orderState == 10" class="btn_3" @click="jump_cancel()" text="取消订单">
						</u-button>
					</u-col>
				</u-row>
				<u-button v-if="pro &&  pro.orderState == 99" class="btn_5" @click="goview()" text="联系客服"
					type="warning"></u-button>
			</view>


			<u-popup :show="showPopu" @close="close" bgColor="RGB(247,247,247)" :overlay="true">
				<u-gap height="10"></u-gap>
				请选择
				<view>
					<uni-card>
						<u-cell-group :border="false">
							<u-cell :border="false" title="微信支付" isLink @click="wxPay">
								<u-icon slot="icon" size="30"
									name="https://xyjcloud.obs.cn-east-3.myhuaweicloud.com/static/wxpayIcon.png">
								</u-icon>
							</u-cell>
						</u-cell-group>
					</uni-card>

					<!-- 	<uni-card>
						<u-cell-group :border="false">
							<u-cell :border="false" title="余额支付" isLink @click="pagPay">
								<u-icon slot="icon" size="30"
									name="https://xyjcloud.obs.cn-east-3.myhuaweicloud.com/static/pagpayIcon.png">
								</u-icon>
							</u-cell>
						</u-cell-group>
					</uni-card> -->
				</view>
			</u-popup>
		</view>


		<u-modal :show="showPayModal" title="请输入验证码进行支付" :showCancelButton="true" @cancel="modalCancel"
			@confirm="payByPag" :asyncClose="true">


			<view>
				<u-input v-model="phone" :disabled="true">
					<u--text text="手机号" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
				</u-input>

				<u-gap height="10"></u-gap>

				<u-input placeholder="请输入验证码" v-model="code">
					<template slot="suffix">
						<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
						<u-button @click="getCode" :text="tips" type="success" size="mini"></u-button>
					</template>
				</u-input>
			</view>



		</u-modal>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				pro: {},
				billNo: null,
				payInfo: {},
				steps: 0,
				showPopu: false,
				showPayModal: false,
				phone: uni.getStorageSync('bindTel'),
				code: '',
				tips: '',
			}
		},
		onLoad(option) {
			this.billNo = option.billNo
			// // 测试时加上
			// this.billNo = "1453697576076"
			this.getOrderDetails(this.billNo)
			if (option.tg) {
				uni.setStorageSync('orderChannel', option.tg)
			}
		},
		onShareAppMessage(res) {
			let shareobj = {
				title: this.billNo, //分享的标题
				path: '/pages-mine/login/login?tg=' + uni.getStorageSync('tg'), //好友点击分享之后跳转的页面
				imageUrl: "https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye16566629320963%E5%9B%BE%E6%A0%87-03.png", //分享的图片
			}

			return shareobj;
		},
		onShow() {
			this.getOrderDetails(this.billNo)
		},
		methods: {
			payByPag() {
				if (!this.code) {
					this.showPayModal = false;
					return uni.showToast({
						title: '验证码不能为空',
						icon: 'none'
					})
				}
				let param = {
					account: uni.getStorageSync('account'),
					billNo: this.billNo,
					code: this.code
				}
				this.http({
					url: 'pagPay',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: param,
					success: res => {
						if (res.code === 0) {
							this.showPayModal = false;
							uni.showToast({
								title: '支付成功',
								icon: 'none'
							})
							return uni.navigateTo({
								url: 'paysuccess?billNo=' + this.billNo
							})
						} else {
							this.showPayModal = false;
							uni.showToast({
								title: res.msg,
								icon: 'error'
							})
						}
					},
					fail: err => {
						this.showPayModal = false;
						uni.showToast({
							title: '余额支付异常',
							icon: 'error'
						})
						console.log(res)
					}
				})
			},
			modalCancel() {
				this.showPayModal = false;
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					// setTimeout(() => {
					// 	uni.hideLoading();
					// 	// 这里此提示会被this.start()方法中的提示覆盖
					// 	uni.$u.toast('验证码已发送');
					// 	// 通知验证码组件内部开始倒计时
					// 	this.$refs.uCode.start();
					// }, 2000);
					this.http({
						url: 'sendPagPaySmsCode',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: {
							account: this.phone
						},
						success: res => {
							if (res.code === 0) {
								uni.hideLoading();
								uni.$u.toast('验证码已发送');
								this.$refs.uCode.start();
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}

					})



				} else {
					uni.$u.toast('请勿重复发送');
				}
			},
			pagPay() {
				this.showPopu = false;
				this.showPayModal = true;
			},
			close() {
				this.showPopu = false
			},
			jumpPayRefund() {
				uni.navigateTo({
					url: 'wxpayrefundlog?billNo=' + this.billNo
				})
			},
			wxPay() {
				console.log(111)
				// #ifdef  MP-WEIXIN  
				this.jumpMiniPay()
				// #endif

				//#ifdef H5
				this.jumpH5Pay()
				//#endif

				//#ifdef APP-PLUS
				this.jump_pay()
				//#endif
			},
			goview() {

				// #ifdef  MP-WEIXIN  
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741' //客服地址链接
					},
					corpId: 'wx8342ef8b403dec4e', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				let param = {
					url: 'https://yzf.qq.com/xv/web/static/chat/index.html?sign=37ef9b97d62400cf71439ebd1ae4e737930ac3307cf527f698aa4b4b8514b36b25eb4e2d7862bc121cd825a129d976a9b1e3b1f8'
				}
				let data = JSON.stringify(param);
				uni.navigateTo({
					url: `../webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
			jump() {
				uni.reLaunch({
					url: '../mine/mine'
				})
			},
			getOrderDetails(billNo) {
				let _this = this;
				uni.request({
					method: "GET",
					url: "https://api.xiaoyujia.com/order/orderDetails",
					data: {
						billNo: billNo
					},
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中

					success: function(res) {
						// console.log(res.data.data)
						let data = res.data.data;
						_this.pro = data;
						_this.setSetp(data.orderState)

					},
					error: function(data) {
						console.log("出现错误")
					}
				})
			},
			setSetp(e) {
				// console.log(e)
				switch (e) {
					case 10:
						this.steps = 0;
						break;
					case 40:
						this.steps = 2;
						break;
					case 50:
						this.steps = 3;
						break;
					case 60:
						this.steps = 4;
						break;
					case 70:
						this.steps = 5;
						break;
					case 80:
						this.steps = 5;
						break;
					case 90:
						this.steps = 5;
						break;
					default:
						break;
				}
			},
			jump_cancel() {
				uni.navigateTo({
					url: '/pages-mine/orders/order-cancel?billNo=' + this.billNo
				})
			},
			async jumpMiniPay() {
				const reuslt = await this.getminiPay();
				if (!reuslt) {
					uni.showToast({
						title: '获取信息失败, 无法支付',
						icon: 'none'
					})
					return;
				}
				const tempPayInfo = this.payInfo;
				console.info('tempPayInfo', tempPayInfo);
				if (!tempPayInfo || !tempPayInfo.package || !tempPayInfo.paySign) {
					uni.showToast({
						title: '获取信息失败, 无法支付',
						icon: 'none'
					})
					return;
				}
				await uni.requestPayment({
					provider: tempPayInfo.provider,
					timeStamp: tempPayInfo.timeStamp,
					nonceStr: tempPayInfo.nonceStr,
					package: tempPayInfo.package,
					signType: tempPayInfo.signType,
					paySign: tempPayInfo.paySign,
					success: res => {
						console.log('success:', res);
						console.log('支付成功');
						uni.showToast({
							title: '支付成功',
							icon: 'none'
						})
						return uni.navigateTo({
							url: 'paysuccess?billNo=' + this.billNo
						})

					},
					fail: res => {
						console.log('fail:', res);
						console.log('支付失败');
						uni.showToast({
							title: '支付失败',
							icon: 'none'
						})
						return;
					},
					complete: res => {
						console.log('complete:', res);
						console.log('complete');
					}
				});

			},
			getminiPay() {
				const openId = uni.getStorageSync('openId');
				if (!openId) {
					uni.showToast({
						title: '未进行微信授权登录'
					})
					reject(false);
				}
				const param = {
					openId: openId,
					orderNo: this.billNo,
					channel: 'xyjCourse'
				};
				return new Promise((resolve, reject) => {
					this.http({
						url: 'getWxMiniProgramPayInfo',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: param,
						success: res => {
							if (res.code === 0) {
								console.log(res.data)
								this.payInfo = res.data;
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
							resolve(true);
						},
						fail: err => {
							console.log(res)
							reject(false);
						}
					})

				})
			},
			getPay() {
				let _this = this;
				return new Promise((resolve, reject) => {
					uni.request({
						method: "POST",
						url: "https://api.xiaoyujia.com/pays/getWxPayment",
						data: {
							orderNo: this.billNo
						},
						dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中

						success: function(res) {
							console.log(res.data.data)
							_this.payInfo = res.data.data;
							resolve(true)
						},
						error: function(data) {
							console.log("出现错误")
							reject(false)
						}
					})

				})
			},
			async jump_pay() {

				await this.getPay();
				let orderInfos = {
					"appid": this.payInfo.appid,
					"partnerid": this.payInfo.partnerid,
					"prepayid": this.payInfo.prepayid,
					"package": this.payInfo.package,
					"noncestr": this.payInfo.noncestr,
					"timestamp": this.payInfo.timestamp,
					"sign": this.payInfo.sign
				};
				let _this = this;
				uni.requestPayment({
					provider: "wxpay",
					orderInfo: orderInfos,
					success(res) {
						// #ifdef APP-PLUS
						const AppLog = uni.requireNativePlugin('RangersAppLogUniPluginCN');
						AppLog.start();
						AppLog.onEventV3("payOrder", {
							'memberId': uni.getStorageSync("memberId"),
						});
						// #endif  
						_this.suc = JSON.stringify(res) + '';
						console.log('success:' + JSON.stringify(res));
						console.log("支付成功");
						return uni.navigateTo({
							url: '/paysuccess?billNo=' + _this.billNo
						})


					},
					fail(err) {

						_this.err = JSON.stringify(err) + '';
						console.log('fail:' + JSON.stringify(err));
						console.log("支付失败");
						uni.showToast({
							title: '支付失败',
							icon: 'error'
						})
						// _this.getOrderDetails(_this.billNo)
						location.reload();
					}
				});

			},
			jumpH5Pay() {
				const param = {
					orderNo: this.billNo
				};
				this.http({
					url: 'getWxH5PayInfo',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: param,
					success: res => {
						if (res.code === 0) {
							// https://m.xiaoyujia.com/#/pages/order/paysuccess?billNo=2022071511244210
							let thisPage = encodeURIComponent(
								'https://m.xiaoyujia.com/pages/order/paysuccess?billNo=' + this.billNo);
							let mUrl = res.data.mwebUrl;
							window.location.href = mUrl + '&redirect_url=' + thisPage;
						} else {
							uni.showToast({
								title: '唤起支付异常',
								icon: 'error'
							})
						}
					},
					fail: err => {
						uni.showToast({
							title: '唤起支付异常',
							icon: 'error'
						})
						console.log(res)
					}
				})
			}
		}
	}
</script>


<style lang="scss">
	page {
		background: #f8f8f8;
		padding-bottom: 100upx;
	}

	.view_botom {
		width: 100%;
		position: fixed;
		bottom: 0;
		/* background-color: #515151; */
	}

	.tuanzhang {
		background: #fff;
		margin-top: 10px;
		padding: 10px;
		display: flex;
		font-size: 12px;

		.tz-l {
			width: 30px;

			input {
				border: 1px solid #000;
			}
		}

		.tz-m {
			flex-grow: 1;
			color: #FF8D42;
			padding-top: 2px;
		}

		.tz-r {
			padding-top: 3px;
		}
	}

	.ptcg {
		display: flex;
		padding: 10px 10px;
		color: #909399;
		font-size: 12px;
		line-height: 30px;

		.ptcg_l {
			font-size: 14px;
		}

		.ptcg_r {
			display: flex;
			line-height: 30px;
			position: relative;
			flex-grow: 1;
			text-align: right;

			.ptcg_img {
				z-index: 99;
				position: absolute;
				top: 0;

				.img {
					width: 30px;
					height: 30px;
					border-radius: 50%;
				}
			}
		}
	}

	.action-box {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		height: 100upx;
		position: relative;
		padding-right: 30upx;
	}

	.action-btn {
		width: 160upx;
		height: 60upx;
		margin: 0;
		margin-left: 24upx;
		padding: 0;
		text-align: center;
		line-height: 60upx;
		font-size: 24upx + 2upx;
		color: #303133;
		background: #fff;
		border-radius: 100px;

		&:after {
			border-radius: 100px;
		}

		&.recom {
			background: #fff9f9;
			color: #fa436a;

			&:after {
				border-color: #f7bcc8;
			}
		}
	}

	.btn {
		display: flex;
		justify-content: flex-end;
		// margin-top: 10px;
		font-size: 30upx;
		text-align: center;

		.btn_1 {
			// margin: 10px;
			// width: 107px;
			// height: 30px;
			// line-height: 25px;
			// border-radius: 5px;
			// // background-color: #3ab54a;
			// background-image: url('@/static/icon/goPay-red.png');
			// color: #f3f3f3;
			position: fixed;
			bottom: 0px;
			margin-left: -15px;
			width: 100%;

		}

		.btn_2 {
			margin: 10px;
			width: 15%;
			height: 30px;
			line-height: 25px;
			border-radius: 5px;
			background-color: $uni-text-color-grey;
			color: #f3f3f3;
		}

		.btn_3 {
			// margin: 10px;
			// width: 107px;
			// height: 30px;
			// line-height: 25px;
			// border-radius: 5px;
			// // background-color: #3ab54a;
			// background-image: url('@/static/icon/cancel-white.png');
			// color: #f3f3f3;
			// position: fixed;
			// bottom: 0px; 
			// margin-left: -15px;
			// width: 100%;

		}



		.btn_5 {
			position: fixed;
			bottom: 0px;
			margin-left: -15px;
			width: 100%;

		}
	}

	.address-section {

		padding: 30upx 0;
		background: #fff;
		position: relative;

		.a-bg {
			position: absolute;
			left: 0;
			bottom: 0;
			display: block;
			width: 100%;
			height: 5upx;
		}

		.order-content {
			display: flex;
			align-items: center;
		}

		.icon-shouhuodizhi {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90upx;
			color: #888;
			font-size: 44upx;
		}

		.cen {
			display: flex;
			flex-direction: column;
			flex: 1;
			font-size: 28upx;
			color: #303133;
		}

		.name {
			font-size: 34upx;
			margin-right: 24upx;
		}

		.address {
			margin-top: 16upx;
			margin-right: 20upx;
			color: #909399;
		}

		.icon-you {
			font-size: 32upx;
			color: #909399;
			margin-right: 30upx;
		}

	}

	.goods-section {
		margin-top: 16upx;
		background: #fff;
		padding-bottom: 1px;

		.g-header {
			display: flex;
			align-items: center;
			height: 84upx;
			padding: 0 30upx;
			position: relative;
		}

		.logo {
			display: block;
			width: 50upx;
			height: 50upx;
			border-radius: 100px;
		}

		.name {
			font-size: 30upx;
			color: #606266;
			margin-left: 24upx;
		}

		.g-item {
			display: flex;
			margin: 20upx 30upx;

			image {
				flex-shrink: 0;
				display: block;
				width: 140upx;
				height: 140upx;
				border-radius: 4upx;
			}

			.right {
				flex: 1;
				padding-left: 24upx;
				overflow: hidden;
			}

			.title {
				font-size: 30upx;
				color: #303133;
			}

			.spec {
				font-size: 26upx;
				color: #909399;
			}

			.price-box {
				display: flex;
				align-items: center;
				font-size: 32upx;
				color: #303133;
				padding-top: 10upx;

				.price {
					margin-bottom: 4upx;
				}

				.number {
					font-size: 26upx;
					color: #606266;
					margin-left: 20upx;
				}
			}

			.step-box {
				position: relative;
			}
		}
	}

	.yt-list {
		margin-top: 16upx;
		background: #fff;
	}

	.yt-list-cell {
		display: flex;
		align-items: center;
		padding: 10upx 30upx 10upx 40upx;
		line-height: 70upx;
		position: relative;

		&.cell-hover {
			background: #fafafa;
		}

		&.b-b:after {
			left: 30upx;
		}

		.cell-icon {
			height: 32upx;
			width: 32upx;
			font-size: 22upx;
			color: #fff;
			text-align: center;
			line-height: 32upx;
			background: #f85e52;
			border-radius: 4upx;
			margin-right: 12upx;

			&.hb {
				background: #ffaa0e;
			}

			&.lpk {
				background: #3ab54a;
			}

		}

		.cell-more {
			align-self: center;
			font-size: 24upx;
			color: #909399;
			margin-left: 8upx;
			margin-right: -10upx;
		}

		.cell-tit {
			flex: 1;
			font-size: 26upx;
			color: #909399;
			margin-right: 10upx;
		}

		.cell-tip {
			font-size: 26upx;
			color: #303133;

			&.disabled {
				color: #909399;
			}

			&.active {
				color: #fa436a;
			}

			&.red {
				color: #fa436a;
			}
		}

		&.desc-cell {
			.cell-tit {
				max-width: 90upx;
			}
		}

		.desc {
			flex: 1;
			font-size: 28upx;
			color: #303133;
		}
	}

	/* 支付列表 */
	.pay-list {
		padding-left: 40upx;
		margin-top: 16upx;
		background: #fff;

		.pay-item {
			display: flex;
			align-items: center;
			padding-right: 20upx;
			line-height: 1;
			height: 110upx;
			position: relative;
		}

		.icon-weixinzhifu {
			width: 80upx;
			font-size: 40upx;
			color: #6BCC03;
		}

		.icon-alipay {
			width: 80upx;
			font-size: 40upx;
			color: #06B4FD;
		}

		.icon-xuanzhong2 {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 60upx;
			height: 60upx;
			font-size: 40upx;
			color: #fa436a;
		}

		.tit {
			font-size: 32upx;
			color: #303133;
			flex: 1;
		}
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 995;
		display: flex;
		align-items: center;
		width: 100%;
		height: 90upx;
		justify-content: space-between;
		font-size: 30upx;
		background-color: #fff;
		z-index: 998;
		color: #606266;
		box-shadow: 0 -1px 5px rgba(0, 0, 0, .1);

		.price-content {
			padding-left: 30upx;
		}

		.price-tip {
			color: #fa436a;
			margin-left: 8upx;
		}

		.price {
			font-size: 36upx;
			color: #fa436a;
		}

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 280upx;
			height: 100%;
			color: #fff;
			font-size: 32upx;
			background-color: #fa436a;
		}
	}

	/* 优惠券面板 */
	.mask {
		display: flex;
		align-items: flex-end;
		position: fixed;
		left: 0;
		top: var(--window-top);
		bottom: 0;
		width: 100%;
		background: rgba(0, 0, 0, 0);
		z-index: 9995;
		transition: .3s;

		.mask-content {
			width: 100%;
			min-height: 30vh;
			max-height: 70vh;
			background: #f3f3f3;
			transform: translateY(100%);
			transition: .3s;
			overflow-y: scroll;
		}

		&.none {
			display: none;
		}

		&.show {
			background: rgba(0, 0, 0, .4);

			.mask-content {
				transform: translateY(0);
			}
		}
	}

	/* 优惠券列表 */
	.coupon-item {
		display: flex;
		flex-direction: column;
		margin: 20upx 24upx;
		background: #fff;

		.con {
			display: flex;
			align-items: center;
			position: relative;
			height: 120upx;
			padding: 0 30upx;

			&:after {
				position: absolute;
				left: 0;
				bottom: 0;
				content: '';
				width: 100%;
				height: 0;
				border-bottom: 1px dashed #f3f3f3;
				transform: scaleY(50%);
			}
		}

		.left {
			display: flex;
			flex-direction: column;
			justify-content: center;
			flex: 1;
			overflow: hidden;
			height: 100upx;
		}

		.title {
			font-size: 32upx;
			color: #303133;
			margin-bottom: 10upx;
		}

		.time {
			font-size: 24upx;
			color: #909399;
		}

		.right {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			font-size: 26upx;
			color: #606266;
			height: 100upx;
		}

		.price {
			font-size: 44upx;
			color: #fa436a;

			&:before {
				content: '￥';
				font-size: 34upx;
			}
		}

		.tips {
			font-size: 24upx;
			color: #909399;
			line-height: 60upx;
			padding-left: 30upx;
		}

		.circle {
			position: absolute;
			left: -6upx;
			bottom: -10upx;
			z-index: 10;
			width: 20upx;
			height: 20upx;
			background: #f3f3f3;
			border-radius: 100px;

			&.r {
				left: auto;
				right: -6upx;
			}
		}
	}
</style>