<template>
	<view class="recharge-container">
		<!-- 页面头部 -->
		<view class="recharge-header">
			<view class="header-bg">
				<view class="bg-circle circle-1"></view>
				<view class="bg-circle circle-2"></view>
			</view>
			<view class="header-content">
				<view class="page-subtitle">选择充值金额和支付方式</view>
			</view>
		</view>

		<!-- 充值内容 -->
		<view class="recharge-content">
			<!-- 快捷金额选择 -->
			<view class="section quick-amounts">
				<view class="section-title">快捷金额</view>
				<view class="amount-grid">
					<view
						class="amount-item"
						:class="{ active: selectedAmount === amount }"
						v-for="amount in quickAmounts"
						:key="amount"
						@click="selectQuickAmount(amount)"
					>
						¥{{ amount }}
					</view>
				</view>
			</view>

			<!-- 自定义金额输入 -->
			<view class="section custom-amount">
				<view class="section-title">自定义金额</view>
				<view class="amount-input-wrapper">
					<text class="currency-prefix">¥</text>
					<input
						class="amount-input"
						type="digit"
						placeholder="请输入充值金额"
						v-model="customAmount"
						@input="onCustomAmountInput"
						@focus="clearQuickSelection"
					/>
				</view>
				<view class="amount-tips">单次充值金额：¥10 - ¥5000</view>
			</view>

			<!-- 支付方式选择 -->
			<view class="section payment-methods">
				<view class="section-title">支付方式</view>
				<view class="payment-list">
					<view
						class="payment-item"
						:class="{ active: selectedPayment === method.value }"
						v-for="method in paymentMethods"
						:key="method.value"
						@click="selectPayment(method.value)"
					>
						<view class="payment-icon">{{ method.icon }}</view>
						<view class="payment-info">
							<view class="payment-name">{{ method.name }}</view>
							<view class="payment-desc">{{ method.desc }}</view>
						</view>
						<view class="payment-check">
							<view class="check-icon" v-if="selectedPayment === method.value">✓</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作区 -->
		<view class="recharge-footer">
			<view class="total-amount">
				充值金额：¥{{ finalAmount }}
			</view>
			<view class="confirm-btn" @click="confirmRecharge" :class="{ disabled: !canConfirm }">
				确认充值
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 充值相关
			selectedAmount: null,
			customAmount: '',
			selectedPayment: 'wechat',
			quickAmounts: [100, 200, 500, 1000, 2000, 5000],
			physicalOrder: {
				memberId: uni.getStorageSync('memberId'),
				employeeName: uni.getStorageSync('employeeName'),
				employeeId: uni.getStorageSync('employeeId'),
				storeId: uni.getStorageSync('storeId'),
				openId: uni.getStorageSync('openId'),
				payableAmount: 0.00
			},
			paymentMethods: [
				{
					value: 'wechat',
					name: '微信支付',
					desc: '推荐使用',
					icon: '💚'
				},
			]
		}
	},
	computed: {
		// 最终充值金额
		finalAmount() {
			if (this.selectedAmount) {
				return this.selectedAmount
			}
			return this.customAmount || '0.00'
		},

		// 是否可以确认充值
		canConfirm() {
			const amount = parseFloat(this.finalAmount)
			return amount >= 0.01 && amount <= 50000 && this.selectedPayment
		}
	},
	onLoad() {
		// 设置页面标题
		uni.setNavigationBarTitle({
			title: '钱包充值'
		})
	},
	methods: {
		// 选择快捷金额
		selectQuickAmount(amount) {
			this.selectedAmount = amount
			this.customAmount = ''
		},

		// 自定义金额输入
		onCustomAmountInput(e) {
			let value = e.detail.value
			// 限制小数点后两位
			if (value.includes('.')) {
				const parts = value.split('.')
				if (parts[1] && parts[1].length > 2) {
					value = parts[0] + '.' + parts[1].substring(0, 2)
				}
			}
			this.customAmount = value
		},

		// 清除快捷选择
		clearQuickSelection() {
			this.selectedAmount = null
		},

		// 选择支付方式
		selectPayment(method) {
			this.selectedPayment = method
		},

		// 确认充值
		async confirmRecharge() {
			if (!this.canConfirm) {
				uni.showToast({
					title: '请检查充值金额和支付方式',
					icon: 'none'
				})
				return
			}

			const amount = parseFloat(this.finalAmount)
			const paymentMethod = this.selectedPayment

			this.physicalOrder.payableAmount = amount
			
			this.http({
				url: 'evokeStoreWalletPay',
				header: {
					"content-type": "application/json;charset=UTF-8"
				},
				method: 'POST',
				data: this.physicalOrder,
				success: res => {
						if (res.code === 0) {
							uni.requestPayment({
								provider: res.data.provider,
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: res.data.signType,
								paySign: res.data.paySign,
								success: payRes => {
									uni.showModal({
										title: '支付结果',
										content: '前往钱包查询充值状态？',
										success: function(res) {
											if (res.confirm) {
												return uni.navigateBack()
											} else if (res.cancel) {
												console.log('用户点击取消');
											}
										}
									})
							
								},
								fail: payfail => {
									console.log('fail:', payfail);
									console.log('支付失败');
									uni.showToast({
										title: '支付失败',
										icon: 'none'
									})
									return;
								}
							});
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
				},
				fail: err => {
					uni.hideLoading();
					console.log(res)
				}
			})
		}
	}
}
</script>

<style scoped>
.recharge-container {
	min-height: 100vh;
	background: #f8f9fa;
	padding-bottom: 160rpx;
}

/* 页面头部 */
.recharge-header {
	position: relative;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 32rpx 40rpx;
	overflow: hidden;
}

.header-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: -100rpx;
	right: -50rpx;
}

.circle-2 {
	width: 150rpx;
	height: 150rpx;
	bottom: -75rpx;
	left: -50rpx;
}

.header-content {
	position: relative;
	z-index: 2;
	text-align: center;
}

.page-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 12rpx;
}

.page-subtitle {
	font-size: 30rpx;
	color: rgba(255, 255, 255, 0.8);
}

/* 充值内容 */
.recharge-content {
	padding: 32rpx;
}

.section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 24rpx;
}

/* 快捷金额 */
.amount-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.amount-item {
	padding: 24rpx;
	text-align: center;
	background: #f8f9fa;
	border: 2rpx solid #f8f9fa;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #6c757d;
	font-weight: 500;
	transition: all 0.3s ease;
}

.amount-item.active {
	background: #e8f4fd;
	border-color: #667eea;
	color: #667eea;
	font-weight: 600;
}

.amount-item:active {
	transform: scale(0.95);
}

/* 自定义金额 */
.amount-input-wrapper {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border: 2rpx solid #f8f9fa;
	border-radius: 12rpx;
	padding: 0 24rpx;
	transition: border-color 0.3s ease;
	margin-bottom: 16rpx;
}

.amount-input-wrapper:focus-within {
	border-color: #667eea;
	background: #ffffff;
}

.currency-prefix {
	font-size: 32rpx;
	color: #6c757d;
	margin-right: 12rpx;
	font-weight: 600;
}

.amount-input {
	flex: 1;
	height: 88rpx;
	font-size: 32rpx;
	color: #2c3e50;
	background: transparent;
	border: none;
	outline: none;
}

.amount-tips {
	font-size: 24rpx;
	color: #95a5a6;
	padding-left: 8rpx;
}

/* 支付方式 */
.payment-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.payment-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border: 2rpx solid #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.payment-item.active {
	background: #e8f4fd;
	border-color: #667eea;
}

.payment-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.payment-info {
	flex: 1;
}

.payment-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 4rpx;
}

.payment-desc {
	font-size: 24rpx;
	color: #6c757d;
}

.payment-check {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.check-icon {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 600;
}

/* 底部操作区 */
.recharge-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 24rpx 32rpx 32rpx;
	border-top: 1rpx solid #e9ecef;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
	z-index: 999;
}

.total-amount {
	text-align: center;
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 24rpx;
}

.confirm-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	letter-spacing: 2rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.confirm-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s ease;
}

.confirm-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.confirm-btn:active::before {
	left: 100%;
}

.confirm-btn.disabled {
	background: #e9ecef;
	color: #adb5bd;
	box-shadow: none;
	pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.recharge-header {
		padding: 40rpx 24rpx 32rpx;
	}

	.page-title {
		font-size: 36rpx;
	}

	.page-subtitle {
		font-size: 24rpx;
	}

	.recharge-content {
		padding: 24rpx;
	}

	.section {
		padding: 24rpx;
		margin-bottom: 20rpx;
	}

	.section-title {
		font-size: 30rpx;
	}

	.amount-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.amount-item {
		padding: 20rpx;
		font-size: 26rpx;
	}

	.amount-input {
		height: 80rpx;
		font-size: 28rpx;
	}

	.currency-prefix {
		font-size: 28rpx;
	}

	.payment-item {
		padding: 16rpx;
	}

	.payment-name {
		font-size: 26rpx;
	}

	.payment-desc {
		font-size: 22rpx;
	}

	.recharge-footer {
		padding: 20rpx 24rpx 24rpx;
	}

	.total-amount {
		font-size: 28rpx;
	}

	.confirm-btn {
		height: 80rpx;
		font-size: 28rpx;
	}

	.recharge-container {
		padding-bottom: 140rpx;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.recharge-container {
		background: #1a1a1a;
	}

	.section {
		background: #2d2d2d;
		border-color: #404040;
	}

	.section-title {
		color: #ffffff;
	}

	.amount-item {
		background: #3a3a3a;
		border-color: #3a3a3a;
		color: #b0b0b0;
	}

	.amount-item.active {
		background: #4a5568;
		border-color: #667eea;
		color: #667eea;
	}

	.amount-input-wrapper {
		background: #3a3a3a;
		border-color: #3a3a3a;
	}

	.amount-input-wrapper:focus-within {
		background: #2d2d2d;
		border-color: #667eea;
	}

	.currency-prefix {
		color: #b0b0b0;
	}

	.amount-input {
		color: #ffffff;
	}

	.amount-tips {
		color: #6c757d;
	}

	.payment-item {
		background: #3a3a3a;
		border-color: #3a3a3a;
	}

	.payment-item.active {
		background: #4a5568;
		border-color: #667eea;
	}

	.payment-name {
		color: #ffffff;
	}

	.payment-desc {
		color: #b0b0b0;
	}

	.recharge-footer {
		background: #2d2d2d;
		border-color: #404040;
	}

	.total-amount {
		color: #ffffff;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
		box-shadow: 0 8rpx 24rpx rgba(74, 85, 104, 0.3);
	}

	.confirm-btn:active {
		box-shadow: 0 4rpx 12rpx rgba(74, 85, 104, 0.4);
	}
}
</style>