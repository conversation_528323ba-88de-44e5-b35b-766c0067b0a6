<template>
  <view>
    <!-- 上方信息弹窗 -->
    <u-notify ref="uNotify"></u-notify>

    <!-- 我的收益-头部信息 -->
    <view class="header">
      <view class="head-left">
        <img :src="headImg!==''?headImg:blankImg">
      </view>
      <view class="head-center cf">
        <text>{{ memberName!==''?memberName:"暂无用户名"}}</text>
        <text>邀请码：{{invitationCode }}</text>
      </view>
    </view>

    <view class="profit-tab">
      <view class="tab-head">
        <text>累计收益</text>
      </view>
      <view class="tab-bottom">
        <view class="profit-left">
          <view class="profit-content">
            <text>￥</text>
            <text>{{profitNum}}</text>
            <text>现金</text>
          </view>
        </view>
        <view class="profit-right">
          <view class="profit-content">
            <text style="color:black">已邀请：</text>
            <text>{{profitNum1}}</text>
            <text>位</text>
          </view>
        </view>
      </view>
    </view>

    <view class="blank-img" v-if="profitList.length==0">
      <img :src="blankDataImg" alt="">
      <view>
        <text class="blank-img-text">您还没有收益，快去邀请吧～</text>
      </view>
      <button class="tab-btn" @click="openInvitation()">去邀请</button>
    </view>


    <view class="profit-list">
      <u-sticky>
        <u-tabs :list="menuList" @click="choiceMenu" :current="menuIndex" lineWidth="22" lineHeight="8"
          :lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
                color: '#1e1848',
                fontWeight: 'bold',
                transform: 'scale(1.1)'
            }" :inactiveStyle="{
                color: '#333',
                transform: 'scale(1.05)'
            }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
        </u-tabs>
      </u-sticky>
      <view class="my-list">
        <view class="list-item" v-for="(item,index) in profitList" :key="index">
          <view class="item-left">
            <text>员工工号：{{item.employeeId}}</text>
            <text>上架时间：{{item.data}}</text>
          </view>
          <view class="item-right">
            <text>￥{{item.amount}}</text>
          </view>
        </view>
        </uni-list>
      </view>
    </view>

  </view>
</template>

<script>
  export default {
    data() {
      return {
        // 确认弹框
        checkType: 0,
        checkTitle: "",
        checkText: "",

        memberId: uni.getStorageSync("memberId"),
        memberName: uni.getStorageSync("memberName"),
        headImg: uni.getStorageSync("memberHeadImg"),
        invitationCode: uni.getStorageSync("memberId"),
        menuIndex: 0,
        profitNum: 2342.00,
        profitNum1: 3,
        blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
        lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
        menuList: [{
          name: '今日',
        }, {
          name: '本周',
        }, {
          name: '本月',
        }, {
          name: '自定义',
        }],
        profitList: [{
            id: 0,
            data: "2022-09-24 14:12:27",
            employeeId: 1032,
            amount: 1.30,
            validDay: 0
          },
          {
            id: 1,
            data: "2022-09-24 14:12:27",
            employeeId: 1032,
            amount: 2.50,
            validDay: 1
          },
          {
            id: 2,
            data: "2022-09-24 14:12:27",
            employeeId: 1032,
            amount: 3.30,
            validDay: 2
          }
        ],
        // 经过合并处理后的列表
        profitListDeal: []
      }
    },
    methods: {
      // 对列表进行处理
      dealProfitList() {
        // let result = []
        // for(let item of profitList) {
        // 	if(result.getItem(item.id)==null) {
        // 		result.push(item.id, item.id)
        // 	}
        // 	for(let item1 of result) {
        // 		if(item.employeeId==item1.employeeId) {
        // 			item1.amount=item1.amount+1
        // 		}
        // 	}
        // }
      },
      // 获取收益列表
      getProfitList() {

      },
      // 点击切换菜单
      choiceMenu(e) {
        this.menuIndex = e.index
      },
      // 打开邀请页面
      openInvitation() {
        uni.navigateTo({
          url: "/pages-mine/invitation/invitation-detail",
        })
      },
      // 打开确认框
      openCheck(checkType, checkTitle, checkText) {
        this.checkType = checkType
        this.checkTitle = checkTitle
        this.checkText = checkText
        this.$refs.popupCheck.open()
      },
      // 确认框功能
      popupCheck() {
        // Tpye的值可以控制，0：xxx确认
        if (this.checkType == 0) {

        }
      },
    },
    onLoad(options) {

    },
    // 页面加载后
    mounted() {

    }
  }
</script>

<style lang="scss">
  page {
    height: auto;
    background-color: #ffffff;
  }

  .img-view {
    width: 100%;
    height: auto;
  }

  .invitation-img {
    display: block;
    width: 100%;
    height: 100%;
  }

  .invitation-img-out {
    width: 580rpx;
    height: 800rpx;
    border-radius: 40rpx;
    margin: 0rpx auto;
  }

  .invitation-imgout {
    display: block;
    width: 580rpx;
    height: auto;
    margin: 20rpx auto;
  }

  .header {
    width: 100%;
    height: 300rpx;
    background-color: #FFE102;
  }

  // 头像部分
  .head-left {
    float: left;
    width: 30%;
    height: 100%;
  }

  .head-left img {
    display: block;
    margin: 40rpx auto;
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
  }

  // 个人信息部分
  .head-center {
    float: left;
    width: 50%;
    height: 80%;
    padding-top: 20rpx;
  }

  .head-center text {
    display: block;
    float: left;
    width: 80%;
    height: 40rpx;
    margin: 30rpx 10rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .profit-tab {
    z-index: 999;
    position: absolute;
    width: 90%;
    height: 200rpx;
    top: 240rpx;
    left: 5%;
    border-radius: 20rpx;
    box-shadow: 5rpx 10rpx 20rpx #dedede;
    background-color: #ffffff;
  }

  .tab-head {
    width: 100%;
    font-size: 36rpx;
    font-weight: bold;
    margin: 40rpx 0rpx 0rpx 40rpx;
  }

  .tab-bottom {
    width: 100%;
    display: flex;
  }

  .profit-left,
  .profit-right {
    width: 50%;
    height: 100rpx;
  }

  .profit-content {
    width: 100%;
    height: 80rpx;
    margin: 20rpx auto;
    text-align: center;
    font-size: 32rpx;

  }

  .profit-content text {
    padding-right: 10rpx;
  }

  .profit-content text:first-child {
    color: #ff4d4b;
  }

  .profit-content text:nth-child(2) {
    color: #ff4d4b;
    font-size: 40rpx;
  }

  .profit-content text:nth-child(3) {}

  .blank-img {
    width: 100%;
    height: auto;

    img {
      display: block;
      width: 240rpx;
      height: 240rpx;
      margin: 20rpx auto;
    }
  }

  .blank-img-text {
    width: 100%;

    text {
      display: block;
      text-align: center;
      font-size: 36rpx;
    }
  }

  .tab-btn {
    margin: 80rpx auto 0rpx auto;
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    color: #f6cc70;
    background-color: #1e1848;
    font-size: 36rpx;
    padding: 0 0 0 0rpx;
  }

  .profit-list {
    display: block;
    width: 88%;
    margin: 180rpx auto 0 auto;
  }

  // 自定义列表
  .my-list {
    margin: 20rpx 0;
  }

  .list-item {
    width: 100%;
    height: 140rpx;
    margin: 0 auto;
    font-size: 32rpx;
    border-bottom: #f4f4f5 2px solid;
  }

  .item-left {
    float: left;
    width: 80%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .item-left text {
    margin: 10rpx 10rpx;
  }

  .item-right {
    float: right;
    width: 15%;
    line-height: 140rpx;
  }

  .item-right text {
    color: #ff4d4b;
  }
</style>