<template>
	<view class="page cf">
		<view class="w9 mg-at flac-row-a">
			<view class="flac-row-b">
				<image style="width: 100rpx;border-radius: 50%;margin-right: 20rpx;" :src="headImg||blankImg"
					mode="widthFix">
				</image>
				<view class="">
					<view class="flac-row f18 lh40">
						<view class="">{{ employeeName||memberName||'暂无用户名'}}</view>
						<image style="width: 120rpx;" :src="levelIconList[memberLevel-1]" mode="widthFix"></image>
					</view>
					<view class="f12 lh25 cc">会员时长：<text
							style="color: #F7D4A7;margin-right: 10rpx;">{{formatDate(unionMerchant.validity)||'2099-12-31'}}</text>到期
					</view>
				</view>
			</view>
			<image class="w35 mg-at" :src="levelList[current].levelIcon" mode="widthFix" v-if="levelList.length">
			</image>
		</view>
		<view class="w9 mg-at cardBox">
			<swiper style="height: 400rpx;" :indicator-dots="false" :autoplay="false" :current="current"
				@change="changeSwiper">
				<swiper-item v-for="(item,index) in levelList" :key="index">
					<view :style="{ backgroundImage: 'url(' + item.levelImg + ')' }" class="cardStyle"
						style="position: relative;">
						<view class="t-indent2 f14 ce lh25">
							{{memberLevel == item.id ? '当前等级' : memberLevel>item.id?'已达成':'即将解锁'}}
						</view>
						<view class="f12 c0" style="position: absolute;top: 40.8%;left: 8%;" @click="openDetail()">
							<text>财富值：{{wealthValue}}/{{item.maxWealthValue>=99999?'+∞':item.maxWealthValue}}</text>
							<text style="margin-left: 10rpx;">></text>
						</view>
						<view class="w9 mg-at" :class="levelStyleList[current].progress"
							style="position: absolute;top: 65%;left: 5%;">
							<view :class="levelStyleList[current].progressbar"
								:style="{ width: (wealthValue/item.maxWealthValue*100) + '%' }">
							</view>
						</view>
						<view class="w9 mg-at f12" :class="item.levelColor"
							style="position: absolute;top: 75%;left: 5%;">
							{{item.levelTips|| ''}}
						</view>
					</view>
				</swiper-item>
			</swiper>

			<view class="tagBox">
				<view class="w10 flac-row-a" style="position: absolute;top: -20rpx;left: 0;">
					<image class="w2"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/left.png"
						mode="widthFix"></image>
					<view class="">可获得如下权益</view>
					<image class="w2"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/right.png"
						mode="widthFix"></image>
				</view>
				<view class="w10 mg-at flac-row" style="flex-wrap: wrap;">
					<view class="w5 text-c flex-col-c" style="margin: 20rpx 0;" v-for="(item,index) in tasklist"
						:key="index">
						<image style="width: 90rpx;" :src="item.equityIcon" mode="widthFix"></image>
						<view class="flac-col" style="height: 90rpx;">
							<view class="f14 lh30">{{item.equityName}}</view>
							<view class="f12 lh20" style="color: #F7D4A7;">{{item.equityRemark||''}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 种子员工财富值
				seedWealthValue: 10,

				showPopup: false, //弹窗
				memberName: uni.getStorageSync('memberName') || "",
				employeeName: uni.getStorageSync('employeeName') || "",
				headImg: uni.getStorageSync("memberHeadImg"),
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				current: 0,
				tasklist: [],
				levelList: [],
				levelStyleList: [{
					progress: 'progress',
					progressbar: 'progress-bar',
				}, {
					progress: 'progressB',
					progressbar: 'progress-barB',
				}, {
					progress: 'progressH',
					progressbar: 'progress-barH',
				}, {
					progress: 'progressX',
					progressbar: 'progress-barX',
				}, {
					progress: 'progressW',
					progressbar: 'progress-barW',
				}],
				levelIconList: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag01.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag02.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag03.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag04.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag05.png',
				],
				unionMerchant: {},
				memberLevel: 1,
				wealthValue: 0,
			};
		},
		methods: {
			changeSwiper(e) {
				this.current = e.detail.current
				this.tasklist = this.levelList[this.current].unionMemberEquityList || []
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '/' + MM + '/' + d
			},
			openDetail() {
				uni.navigateTo({
					url: '/pages-other/member/wealth-detail?id=' + this.unionMerchant.id
				})
			},
			// 获取等级列表
			listUnionMemberLevel() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/member/listUnionMemberLevel",
					method: 'POST',
					data: {
						unionMerchantId: uni.getStorageSync('unionMerchantId') || 0
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.levelList = res.data
							this.tasklist = this.levelList[this.current].unionMemberEquityList || []
						}
					}
				})
			},
			// 获取联盟会员信息
			getUnionMerchantData() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/work/getUnionMerchantData",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId") || 0
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.unionMerchant = res.data
							this.memberLevel = res.data.memberLevel || 1
							this.current = this.memberLevel - 1
							this.wealthValue = res.data.wealthValue || 0
							uni.setStorageSync("unionMerchantId", res.data.id)
						}
					}
				})
			},
		},
		onLoad(options) {
			this.getUnionMerchantData()
			this.listUnionMemberLevel()
		}
	}
</script>

<style lang="less" scoped>
	.page {
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_bg.png') no-repeat center;
		background-size: 100% 100%;
		width: 100%;
		min-height: 100vh;
		background-repeat: no-repeat;
		padding: 30rpx 0;
	}

	.cardStyle {
		width: 100%;
		height: 360rpx;
		margin: 20rpx auto;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_level111.png') no-repeat;
		background-size: 100% 100%;
	}

	.tagBox {
		width: 90%;
		border: 2rpx solid #F7D4A7;
		border-radius: 20rpx;
		padding: 30rpx;
		border-top: none;
		position: relative;
		margin: 80rpx auto;
	}

	.cc {
		color: #ccc;
	}

	.line-wrap {
		width: 90%;
		position: absolute;
		top: 45%;
		left: 5%;
	}

	.levelColor {
		color: #FEF0EC;
	}

	.progress {
		height: 10rpx;
		background: linear-gradient(to right, rgba(223, 144, 123, 1) 0, rgba(192, 102, 53, 1) 30%, rgba(146, 67, 9, 1) 100%);
		box-shadow: none;
		overflow: hidden;
		border-radius: 50px;
	}

	.progress-bar {
		background: linear-gradient(to right, rgba(255, 225, 217, 1) 50%, rgba(255, 255, 255, 1) 100%);
		height: 10rpx;
		border-radius: 50px;
	}

	.levelColorB {
		color: #E9E9E9;
	}

	.progressB {
		height: 10rpx;
		background: linear-gradient(to right, rgba(150, 150, 150, 1) 0, rgba(143, 143, 143, 1) 30%, rgba(109, 109, 109, 1) 100%);
		box-shadow: none;
		overflow: hidden;
		border-radius: 50px;
	}

	.progress-barB {
		background: linear-gradient(to right, rgba(204, 204, 204, 1) 50%, rgba(255, 255, 255, 1) 100%);
		height: 10rpx;
		border-radius: 50px;
	}


	.levelColorH {
		color: #333333;
	}

	.progressH {
		height: 10rpx;
		background: linear-gradient(to right, rgba(169, 134, 2, 1) 0, rgba(149, 104, 0, 1) 30%, rgba(95, 67, 0, 1) 100%);
		box-shadow: none;
		overflow: hidden;
		border-radius: 50px;
	}

	.progress-barH {
		background: linear-gradient(to right, rgba(255, 245, 208, 1) 50%, rgba(255, 255, 255, 1) 100%);
		height: 10rpx;
		border-radius: 50px;
	}

	.levelColorX {
		color: #BDD7F0;
	}

	.progressX {
		height: 10rpx;
		background: linear-gradient(to right, rgba(70, 73, 110, 1) 0, rgba(60, 63, 101, 1) 30%, rgba(64, 67, 105, 1) 100%);
		box-shadow: none;
		overflow: hidden;
		border-radius: 50px;
	}

	.progress-barX {
		background: linear-gradient(to right, rgba(183, 202, 214, 1) 50%, rgba(253, 254, 254, 1) 100%);
		height: 10rpx;
		border-radius: 50px;
	}

	.levelColorW {
		color: #E6E4FF;
	}

	.progressW {
		height: 10rpx;
		background: linear-gradient(to right, rgba(60, 50, 140, 1) 0, rgba(53, 45, 136, 1) 30%, rgba(27, 21, 89, 1) 100%);
		box-shadow: none;
		overflow: hidden;
		border-radius: 50px;
	}

	.progress-barW {
		background: linear-gradient(to right, rgba(226, 223, 255, 1) 50%, rgba(253, 254, 254, 1) 100%);
		height: 10rpx;
		border-radius: 50px;
	}

	.maskBox {
		width: 100%;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.6);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 99;
	}
</style>