<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-popup :show="showPopu" @close="showPopu = false" bgColor="RGB(247,247,247)" :overlay="true">
			<u-gap height="10"></u-gap>
			<view class="textA">请选择支付方式</view>

			<view>

				<!-- <uni-card>
					<u-cell-group :border="false">
						<u-cell :border="false" title="余额支付" isLink @click="pagPay">
							<u-icon slot="icon" size="30"
								name="https://xyjcloud.obs.cn-east-3.myhuaweicloud.com/static/pagpayIcon.png">
							</u-icon>
						</u-cell>
					</u-cell-group>
				</uni-card> -->

				<uni-card>
					<u-cell-group :border="false">
						<u-cell :border="false" title="微信支付" isLink @click="wxPay">
							<u-icon slot="icon" size="30"
								name="https://xyjcloud.obs.cn-east-3.myhuaweicloud.com/static/wxpayIcon.png">
							</u-icon>
						</u-cell>
					</u-cell-group>

				</uni-card>

			</view>
		</u-popup>


		<view class="shadow">
			<view style="height: 25rpx;" />
			<u-steps current="0" activeColor="#1e1848" class="steps">
				<u-steps-item title="认证缴费" />
				<u-steps-item v-if="renewFlag!=1" title="主体信息" />
				<u-steps-item v-if="renewFlag!=1" title="商户信息" />
				<u-steps-item v-if="renewFlag!=1" title="平台审核" />
				<u-steps-item v-if="renewFlag!=1" title="账户验证" />
			</u-steps>
			<view style="height: 25rpx;" />
		</view>
		<view class="shadow">
			<view class="companyMsg">
				<text>缴费金额</text>
			</view>
			<view class="input-state">
				<u--input border="bottom" disabled v-model="moeny" prefixIconStyle="font-size: 22px;color: #909399"
					prefixIcon="rmb" disabledColor="Light#f4f4f5" />
			</view>
			<view class="text-state">
				<text>认证费金额：￥{{moeny}}，应缴金额：￥{{moeny}}</text>
			</view>

			<view class="button-state">
				<u-button color="#1e1848" customStyle="color:#f6cc70" @click="clickBtn" >{{buttonText}}</u-button>
			</view>
		</view>


	</view>
</template>
<script>
	export default {

		data() {
			return {
				msgText: "",
				msgType: "success",
				buttonText: "认证缴费",
				renewFlag: null,
				showPopu: false,
				moeny: '',
				orderNo: '',
				payInfo: {},
				price: "",
				address: [1648265],
				address: [1648265],
				addressList: [],
				addressInfo: {
					"memberId": uni.getStorageSync("memberId"),
					"id": "",
					"name": uni.getStorageSync("memberName") || "用户" + uni.getStorageSync("memberId"),
					"phone": uni.getStorageSync("account"),
					"street": "厦门小羽佳家政股份有限公司",
					"city": "厦门市",
					"area": "湖里区",
					"cityId": 1,
					"areaId": 2,
					"lng": "118.101760",
					"lat": "24.489437",
					"isDefault": false
				},
				productDetail: {},
				skuDes: [],
				memberId: '',
				flag: false
			}
		},
		onLoad(option) {
			this.getIfEmployeeByMemberId()
			this.getPlaceOrder()
			this.memberId = option.id
			this.renewFlag = option.renewFlag
			if (option.renewFlag == 1) {
				this.buttonText = "联盟续费"
			}
			// 尝试获取邀请员工信息
			if (option.scene) {
				let scene = decodeURIComponent(option.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				console.log("输出从邀请二维码获取的邀请人信息：", obj)
				let introducerId = obj.id
				uni.setStorageSync('introducerId', introducerId)
				this.inviteeId = introducerId
			} else {
				console.log("未获取邀请人信息！")
			}
		},
		methods: {
			//如果是员工并开通体验权限就生成商户信息
			getIfEmployeeByMemberId() {
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/unionMerchant/getIfEmployeeByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						agent: this.agent,
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if(res.data==2){
								uni.navigateTo({
									url: '/pages-work/index'
								});
							}
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			clickBtn() {
				if(!uni.getStorageSync("memberId")) {
					return this.reLogin()
				}
				this.showPopu = true
			},
 			reLogin() {
				this.$toast.toast('您还未进行登录哦，先去登录吧！')
				uni.setStorageSync('redirectUrl', '/pages-mine/franchise/enter/topUpMoney')
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					});
				}, 2000);
			},
			getOrderByMemberId() {
				this.http({
					url: "getOrderByMemberId",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							this.orderNo = res.data
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			getproductDetails() {
				let _this = this;
				let param = {
					areaId: 2,
					productId: "358"
				}
				uni.request({
					method: "post", //type可以为post也可以为get
					url: "https://api2.xiaoyujia.com/product/productDetails",
					data: param, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
					success: function(data) {
						if (data.data.code == 0) {
							_this.moeny = data.data.data.productPrice
						}
					},
					error: function(data) {
						console.log("出现错误")
					}
				})

			},
			getPlaceOrder() {
				let _this = this;
				//只是读取产品信息，不作为下单依据
				let param = {
					areaId: 2,
					cityId: 1,
					memberId: 0,
					productId: "358"
				}
				uni.request({
					method: "post", //method可以为post也可以为get
					url: "https://yibanapi.xiaoyujia.com/product/placeOrder",
					data: param, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
					success: function(data) {
						_this.productDetail = data.data.data;
						if (data.data.data.surcharges.length > 0) {
							let surcharges = data.data.data.surcharges;
							surcharges.forEach(i => {
								let pa = {
									id: null,
									value: null,
									price: null,
									unit: null,
									name: null
								}
								pa.id = i.id;
								pa.price = i.price;
								this.price = i.price
								pa.value = i.num;
								pa.unit = i.unit;
								pa.name = i.name;
								_this.selectMaterialList.push(pa);
							})
						}
						// _this.getProductRemark(data.data.data.productCategoryId);
						_this.productDetail.skulist.forEach(v => {
							let sku = {
								skuPropertylist: [],
								units: []
							};
							sku.skuPropertylist = v.skuPropertylist

							sku.units = [];
							v.units.forEach(vs => {
								let unit = {
									id: null,
									name: null,
									number: null,
								};
								unit.id = vs.id;
								unit.name = vs.name;
								unit.number = vs.minValue;

								sku.units.push(unit)
							});
							_this.skuDes.push(sku)
						})
						console.log(JSON.stringify(_this.skuDes))
					},
					error: function(data) {
						console.log("出现错误")
					}
				})
			},
			// 获取用户地址列表
			getAddressList() {
				if (uni.getStorageSync("memberId") == null) {
					return
				}
				this.http({
					url: 'selectMemberAddressAndInit',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.addressInfo,
					success: res => {
						if (res.code == 0) {
							this.address = []
							this.address.push(res.data[0].id)
							console.log("输出默认地址Id：", this.address[0])
						}
					}
				})
			},
			createOrder() {
				let param = {
					'cityId': 1,
					'areaId': 2,
					'productId': "358",
					'startTime': new Date(),
					'startTimeSpan': "08:00",
					'endTimeSpan': "18:00",
					'skuDes': this.skuDes,
					'address': this.address,
					'serviceRemark': null,
					'buChannel': 37,
					'source': '',
					'phone': uni.getStorageSync("account"),
					'memberId': uni.getStorageSync("memberId"),
					'totalAmount': "0.01",
					'realTotalAmount': "0.01",
					'couponId': null,
					'selectMaterialList': [],

				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/order/addOrder',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: param,
					success: res => {
						uni.hideLoading();
						console.log("res-------------->", res);
						if (res.code === 0) {
							this.orderNo = res.data
							//  uni.redirectTo({
							// 	url: '/pages/order/orderdetail?billNo=' + res.data
							// })
						} else {
							this.$toast.toast(res.msg)
							this.flag = true
						}
					},
					fail: err => {
						uni.hideLoading();
						console.log(res)
					}
				})
			},
			wxPay() {
				this.getOrderByMemberId()

				if (!this.orderNo) {
					this.createOrder()
				}

				if (!this.flag) {
					let timer = setTimeout(() => {
						// #ifdef  MP-WEIXIN
						this.jumpMiniPay()
						// #endif

						//#ifdef H5
						// this.jumpH5Pay()
						//#endif

						//#ifdef APP-PLUS
						// this.jump_pay()
						//#endif
					}, 2000)
				}
			},
			async jumpMiniPay() {

				const reuslt = await this.getminiPay()

				if (!reuslt) {
					uni.showToast({
						title: '获取信息失败, 无法支付',
						icon: 'none'
					})
					return;
				}
				const tempPayInfo = this.payInfo;
				if (!tempPayInfo || !tempPayInfo.package || !tempPayInfo.paySign) {
					uni.showToast({
						title: '获取信息失败, 无法支付',
						icon: 'none'
					})
					return;
				}
				await uni.requestPayment({
					provider: tempPayInfo.provider,
					timeStamp: tempPayInfo.timeStamp,
					nonceStr: tempPayInfo.nonceStr,
					package: tempPayInfo.package,
					signType: tempPayInfo.signType,
					paySign: tempPayInfo.paySign,
					success: res => {
						uni.showToast({
							title: '支付成功',
							icon: 'none'
						})
						if (this.renewFlag == 1) {
							this.http({
								url: 'allianceRenew',
								data: {
									id: uni.getStorageSync("memberId")
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								method: 'POST',
								success: res => {
									if (res.code == 0) {
										return uni.navigateTo({
											url: "/pages-work/index"
										})
									}
								}
							})
						}
						uni.navigateTo({
							url: '/pages-mine/franchise/enter/bodyMsg?id=' + this.memberId
						})
					},
					fail: res => {
						console.log('fail:', res);
						console.log('支付失败');
						uni.showToast({
							title: '支付失败',
							icon: 'none'
						})
						return;
					},
					complete: res => {
						console.log('complete:', res);
						console.log('complete');
					}
				});

			},
			getminiPay() {
				const openId = uni.getStorageSync('openId');
				if (!openId) {
					uni.showToast({
						title: '未进行微信授权登录'
					})
					reject(false);
				}
				const param = {
					openId: openId,
					orderNo: this.orderNo,
					channel: 'XYJACN'
				};
				return new Promise((resolve, reject) => {
					this.http({
						outsideUrl: 'https://api2.xiaoyujia.com/pays/getWxMiniProgramPayInfo',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: param,
						success: res => {
							if (res.code === 0) {
								this.payInfo = res.data;
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
							resolve(true);
						},
						fail: err => {
							console.log(res)
							reject(false);
						}
					})

				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {
			this.getproductDetails()
			this.getAddressList()
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}

	.textA {
		text-align: center;
		display: block;
	}

	.button-state {
		margin-top: 900rpx;
	}

	.steps {
		padding-bottom: 30rpx;
		padding-top: 25rpx;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.text-state {
		color: darkgrey;
		margin-top: 3%;
		margin-left: 30rpx;
	}

	.companyMsg {
		color: black;
		font-size: 35rpx;
		margin-left: 4%;
		line-height: 100rpx;
	}

	.input-state {
		margin-top: -2%;
		margin-left: 4%;
	}
</style>