<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="swiper-menu">
			<u-sticky>
				<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="22" lineHeight="8"
					:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
					color: '#1e1848',
					fontWeight: 'bold',
					transform: 'scale(1.1)'
				}" :inactiveStyle="{
					color: '#333',
					transform: 'scale(1.05)'
				}" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
				</u-tabs>
			</u-sticky>
		</view>


		<view class="swiper-tab" v-for="(item, index) in list" :key="index" @click="openDetail(index)">
			<view class="swiper-head">
				<text class="swiper-title">线索编号：{{item.orderNeedsID}}</text>
				<text class="swiper-tips">{{formatStatus(item.ywStatus)}}</text>
			</view>

			<view class="swiper-content">
				<view class="content-left">
					<img :src="item.orderNeeds.agentHeadImg||blankHeadImg" alt="">
				</view>
				<view class="content-right">
					<view class="content-title">
						<text>{{item.orderNeeds.productName}}</text>
					</view>
					<view class="content-text">
						<text>经纪人：{{formatStr(0,1,item.orderNeeds.realName)}}老师</text>
					</view>
					<view class="content-text">
						<text>薪资待遇：{{formatSalary(item.orderNeeds.salary)}}</text>
					</view>
					<view class="content-text">
						<text>工作内容：{{checkStr(item.orderNeeds.workContent)}}</text>
					</view>
					<view class="content-text">
						<text>工作要求：{{checkStr(item.orderNeeds.workRequire)}}</text>
					</view>
					<view class="content-text">
						<text>工作地点：{{formatAddress(item.orderNeeds.street)}}</text>
					</view>
					<view class="content-text">
						<text>报名时间：{{formatDate(item.creTime)}}</text>
					</view>
					<u-button customStyle="width:32%;float:right" text="视频面试" color="#1e1848"
						v-if="item.orderNeeds.meetingRoomId" @click="openMeeting(index)">
					</u-button>
					<u-gap height="60"></u-gap>
				</view>
			</view>
		</view>

		<u-empty v-if="list.length == 0" text="暂无面试记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				memberName: uni.getStorageSync("memberName") || '',
				headImg: '',
				contractCode: uni.getStorageSync("memberId"),

				choiceIndex: 0,
				list: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				searchCondition: {
					employeeId: uni.getStorageSync("employeeId"),
					// ywStatusList: [0, 1, 2, 3, 4],
					orderBy: "del.CreTime DESC"
				},
				menuList: [{
						name: '全部',
						ywStatusList: null,
					},
					{
						name: '待面试',
						ywStatusList: [0, 1, 2],
					},
					{
						name: '已面试',
						ywStatusList: [3, 4],
					}
				],
				ywStatusList: [{
						text: '已取消',
						value: 0
					}, {
						text: '待面试',
						value: 1
					},
					{
						text: '面试中',
						value: 2
					},
					{
						text: '未通过',
						value: 3
					},
					{
						text: '已通过',
						value: 4
					}
				],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			onReachBottom() {
				this.searchCondition.current++
				this.getList()
			},
			// 格式化时间
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				var dt = new Date(value)
				let year = dt.getFullYear()
				let month = (dt.getMonth() + 1).toString().padStart(2, '0')
				let date = dt.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${date}`
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				if (index == -1) {
					result = str
				}
				return result
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 格式化期望薪资
			formatSalary(salary) {
				// 如果工资字段值存在，则直接返回值
				if (salary != null && salary != 0) {
					return salary + "元/月"
				} else {
					return "工资面议"
				}
			},
			formatAddress(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
					}
				}
				return result
			},
			formatStatus(status) {
				return this.ywStatusList[status].text
			},
			openMeeting(index) {
				// 获取订阅消息授权
				// #ifdef  MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: [
						"h8NonHTTvPPiRw7mWNcduq8L6GIILhyUrEdGkmWzdp4",
					],
					success: res => {
						console.log("用户同意进行小程序消息订阅！")
					},
					fail: res => {}
				})
				// #endif
				uni.navigateTo({
					url: '/pages-mine/room/meeting?roomId=' + this.list[index].orderNeeds.meetingRoomId
				})
			},
			// 跳转到详情页面
			openDetail(index) {
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + this.list[index].orderNeedsID
				})
			},
			// 点击切换菜单（一级）
			choiceMenu(e) {
				this.choiceIndex = e.index
				this.refreshList()
			},
			// 刷新列表
			refreshList() {
				let index = this.choiceIndex
				this.searchCondition.ywStatusList = this.menuList[index].ywStatusList
				this.list = []
				this.startFilter()
			},
			// 开始筛选
			startFilter() {
				this.getList()
			},
			getList() {
				if (!uni.getStorageSync("employeeId")) {
					return
				}

				this.http({
					url: 'getOrderDeliveryList',
					method: 'POST',
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						} else {
							this.list = []
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {}
			},
		},
		onLoad() {
			this.getList()
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>