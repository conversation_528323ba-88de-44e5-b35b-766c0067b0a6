<template>
	<view>
		<view class="bg-white f14 lh35">
			<view class="f16 fb" style=" display: flex;justify-content: flex-end;"><u-button :text="contractLog.serverMonth+'/12次'" type="primary" size="large"
						customStyle="width:170rpx;height:60rpx;margin:auto 0"></u-button></view>
			<view @click="uploadImg(1)">
				<view class="flac-row-b c6">伴手礼照片：
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic"
					 :maxCount="4" name="1" multiple accept="all">
					</u-upload>
				</view>
			</view>
			<view @click="uploadImg(2)" v-if="contractLog.serverMonth==1">
				<view class="flac-row-b c6">家务清单表：
					<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePicB" 
					:maxCount="4" name="2" multiple accept="all">
					</u-upload>
				</view>
			</view>
			<view class="flac-row-b c6">沟通内容：
				<u--textarea border="bottom" autoHeight v-model="contractLog.communicate" placeholder="请输入沟通内容" ></u--textarea>
			</view>
			<view class="flac-row-b c6" v-if="contractLog.serverMonth==12">续签订单号：
				<u--input v-model="contractLog.renewalBillNo"  border="bottom" placeholder="请输入续签订单号" />
			</view>
			<view class="flac-row-b c6" v-if="contractLog.serverMonth>1&&contractLog.serverMonth<12">复购订单号：
				<u--input v-model="contractLog.repurchaseBillNo" border="bottom" placeholder="请输入复购订单号" />
			</view>
		
		</view>
		<view style="text-align: center;">
			<view class="c9">注：提交后由家务管家进行审核评分！</view>
			<view class="c9">1-3星级：<text style="color: red;font-size: 30rpx;">60%</text>奖励结算</view>
			<view class="c9">4星级：<text style="color: red;font-size: 30rpx;">80%</text>奖励结算</view>
			<view class="c9">5星级：<text style="color: red;font-size: 30rpx;">100%</text>奖励结算</view>
		</view>
		<view
			style="position: fixed;bottom: 0;left: 0;z-index: 999;width: 100%;height: 150rpx;background-color: #e6e6e6;">
			<view class="flac-row lh30">
				<u-button
					customStyle="width: 80%;margin: 50rpx auto;text-align: center;border-radius: 40rpx;color: white;background-color: #1f2120;"
					@click="addContractLog" shape="circle" text="确认提交"></u-button>
			</view>
			
		</view>
		


	</view>
</template>
<script>
	export default {
		data() {
			return {
				uploadFlag: null,
				fileList1: [],
				fileList2: [],
				contractLog: {
					souvenirImg: '',
					contractId: '',
					serverMonth: '',
					houseworkImg: '',
					communicate: '',
					renewalBillNo: '',
					repurchaseBillNo: '',
					deliverPeople: uni.getStorageSync('employeeId')
				},
				physicalPatient: {
					patientGender: '',
					maritalStatus: '',
					memberId: uni.getStorageSync('memberId')
				},
				memberId: uni.getStorageSync('memberId'),
				title: 'picker',
				index: 0,
			}
		},
		onLoad(option) {
			this.contractLog.contractId = option.id
			this.contractLog.serverMonth = option.serverMonth
		},
		methods: {
			deletePic(event){
					this[`fileList${event.name}`].splice(event.index, 1)
			},
			deletePicB(event){
					this[`fileList${event.name}`].splice(event.index, 1)
			},
			uploadImg(val) {
				this.uploadFlag = val
			},
			// 新增图片
			async afterRead(event) {
				console.log("--------------,",this.uploadFlag);
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					if (this.uploadFlag == 1) {
						this.updateSouvenirImg()
					}
					if (this.uploadFlag == 2) {
						this.updateHouseworkImg()
					}
					fileListLen++
				}
			},
			updateSouvenirImg(){
				let imgs = '';
				if (this.fileList1.length > 0) {
					for (var i = 0; i < this.fileList1.length; i++) {
						imgs += this.fileList1[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.contractLog.souvenirImg = imgs
			},
			updateHouseworkImg(){
				let imgs = '';
				if (this.fileList2.length > 0) {
					for (var i = 0; i < this.fileList2.length; i++) {
						imgs += this.fileList2[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.contractLog.houseworkImg = imgs
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api2.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			addContractLog(){
				if(!this.contractLog.communicate||!this.contractLog.souvenirImg){
					 return	uni.showToast({
							title: '伴手礼照片/沟通内容不能为空！',
							icon: 'none'
						})
				}
				if(this.contractLog.serverMonth==1&&!this.contractLog.houseworkImg){
					return	uni.showToast({
												title: '请上传家务清单表！',
												icon: 'none'
											})
				}
				this.http({
					outsideUrl: 'http://localhost:15012/submitContractLog',
					// url: 'submitContractLog',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.contractLog,
					success: res => {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
						setTimeout(()=>{
							if (res.code === 0) {
								uni.navigateBack();
							}
						},1000)
					},
					fail: err => {
						uni.hideLoading();
						console.log(res)
					}
				})
			},
		}
	}
</script>


<style lang="scss">
	page {
		width: 100%;
		min-height: 100%;
		padding-bottom: 100upx;
		background: url('') no-repeat #f5f5f5;
		background-size: 100%;
	}

	.bgd8866d {
		background-color: #d8866d;
		color: #fff;
	}


	.bg-white {
		width: 80%;
		margin: 30rpx auto;
		padding: 40rpx;
		border-radius: 20rpx;
		background-color: #fff;
	}

	.card_img {
		height: 160rpx;
		width: 160rpx;
		border-radius: 10px;
	}



	.textA {
		text-align: center;
		display: block;
	}
</style>