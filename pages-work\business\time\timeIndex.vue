<template>
	<view class="page">
		<!-- <u-empty text="暂无时间管理,待开发中..."
      icon="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671439056143sjgl.png" width="120"
      textSize="18" customStyle="padding-top: 300rpx;">
    </u-empty> -->
		<u-sticky>
			<u-subsection activeColor="#1e1848" :list="titleList" :current="curNow" @change="sectionChange"
				fontSize="16" mode="button"></u-subsection>
		</u-sticky>

		<!-- 时间模板用于展示作用 -->
		<view v-for="(item,i) in modelList" :key="i" class="f16 fb flac-col" style="margin-bottom: 20rpx;"
			v-if="curNow === 0&&modelList.length>0" >
			
			<view class="w9 mg-at flac-row-b lh50 border-bottom-2se" >
				<view class="flac-row">
					<u-icon
						name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672710100644week.png"
						size="22"></u-icon>
					<text style="margin: auto 20rpx;">{{item.week==0?"星期一":
											  item.week==1?"星期二":
											  item.week==2?"星期三":
											  item.week==3?"星期四":
											  item.week==4?"星期五":
											  item.week==5?"星期六":
											  "星期天"}}</text>
				</view>
				<!-- <view class="">{{item.startDate}}-{{item.endDate}}</view> -->
			</view>

			<view style="display: flex;flex-wrap: wrap;" v-if="item.isEnable==1">
				<view v-for="(item1,index1) in item.areaNameList" :key="index1" class="blockStyle2">
					{{item1.text}}
				</view>
			</view>
		</view>
		
		<u-empty :text="isAdmin==1?'未设置时间模板!':'未设置时间模板，请联系主管或店长初始化！'"
		  icon="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671438708293pinlun.png" width="120"
		  textSize="18" v-if="curNow === 0&&modelList.length<=0" customStyle="padding-top:300rpx">
		</u-empty>
		<view class="btn-big" v-if="curNow === 0&&modelList.length<=0&&isAdmin==1">
			<button @click="openTime" style="margin-top: 50rpx;">去初始化时间模版</button>
		</view>


	<u-empty :text="isAdmin==1?'未生成每日时间信息!':'未生成时间信息，请联系主管或店长初始化！'"
		  icon="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671438708293pinlun.png" width="120"
		  textSize="18" v-if="curNow === 1&&dataList.length<=0" customStyle="padding-top:300rpx">
		</u-empty>
		<view class="btn-big" v-if="curNow === 1&&dataList.length<=0&&isAdmin==1">
			<button @click="generateProject" style="margin-top: 50rpx;">一键生成</button>
		</view>
		
		<!-- 每日时间管理 -->
		<view class="bacf f16 fb" style="padding-bottom: 50rpx;margin-bottom: 20rpx;" v-for="(item,i) in dataList"
			:key="i" v-if="curNow === 1&&dataList.length>0">
			<view class="w10 mg-at flac-row lh50"
				style="justify-content: space-between;border-bottom: 2rpx solid #eee;">
				<view class="flac-row">
					<u-icon
						name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672710100644week.png"
						size="22" customStyle="margin: auto 20rpx;"></u-icon>
					<text>{{item.day}}({{item.remark}})</text>
				</view>
				<view class="flac-row">
					<!-- <view class="">{{item.startDate}}-{{item.endDate}}</view> -->
					<!-- <u-icon v-if="item.timeEditFlag==1"
						name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672714501956edit2.png"
						size="20" customStyle="margin: auto 20rpx;" @click="goEdit(item)"></u-icon> -->
						<u-icon v-if="isAdmin==1"
							name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672714501956edit2.png"
							size="20" customStyle="margin: auto 20rpx;" @click="goEdit(item)"></u-icon>
					<u-icon  size="20" customStyle="margin: auto 20rpx;"></u-icon>
				</view>
			</view>

			<view style="display: flex;flex-wrap: wrap;" v-if="item.isEnable==1">
				<view v-for="(item1,index1) in item.areaNameList" :key="index1" class="blockStyle2">
					{{item1.text}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				titleList: [{
					name: '时间模板'
				}, {
					name: '每日时间管理'
				}],
				curNow: 0,
				modelList: [],
				isAdmin: 0,
				dataList: [],
				employeeId: uni.getStorageSync('employeeId') || 0
			};
		},
		onLoad(options) {
			this.isAdmin = options.isAdmin
			this.employeeId = options.employeeId || this.employeeId
			this.getTimeData()
		},
		onShow: function() {
			if (uni.getStorageSync('timeDetailShow')) {
				// 获取数据函数
				this.getTimeData()
			}
		},
		methods: {
			openTime(){
				uni.navigateTo({
					url: "/pages-work/business/time/timeTemplate?employeeId="+this.employeeId
				})
			},
			generateProject(){
				this.http({
					outsideUrl: 'https://inside.xiaoyujia.com/api/order/EmployeeTimeMan?employeeid='+this.employeeId,
					method: 'POST',
					success: res => {
						if (res.Meta.State == 200) {
							uni.showToast({
								title: '操作成功!',
								icon: 'none'
							})
							setTimeout(()=>{
								this.getTimeData()
							},1500)
						} else {
							uni.showToast({
								title: res.Meta.Msg,
								icon: 'none',
								duration: 2000
							})
						}
					}
				})
			},
			getTimeData() {
				this.http({
					url: 'getTimeData',
					path: this.employeeId,
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.modelList = res.data
						} else {
							uni.showToast({
								title: "获取时间模板失败!",
								icon: 'none'
							})
						}
					}
				})
				this.http({
					url: 'getDayTimeData',
					data: {
						employeeId: this.employeeId,
						id: ""
					},
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.dataList = res.data
						} else {
							uni.showToast({
								title: "获取每日时间管理失败!",
								icon: 'none'
							})
						}
					}
				})
			},
			sectionChange(index) {
				this.curNow = index;
			},
			goEdit(val) {
				uni.navigateTo({
					url: "/pages-work/business/time/timeDetail?id=" + val.id + "&day=" + val.day + "&employeeId=" +
						this.employeeId
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		height: 100vh;
		background-color: #fff !important;
	}

	.blockStyle2 {
		width: 220rpx;
		height: 60rpx;
		line-height: 60rpx;
		border: 4rpx solid #1e1848;
		margin: 20rpx 0rpx 20rpx 20rpx;
		text-align: center;

		// &:before {
		// 	content: "";
		// 	position: absolute;
		// 	bottom: 0;
		// 	right: 0;
		// 	border: 24rpx solid #1e1848;
		// 	border-top-color: transparent;
		// 	border-left-color: transparent;
		// }

		// &:after {
		// 	content: "";
		// 	width: 10rpx;
		// 	height: 20rpx;
		// 	position: absolute;
		// 	right: 8rpx;
		// 	bottom: 8rpx;
		// 	border: 2rpx solid #fff;
		// 	border-top-color: transparent;
		// 	border-left-color: transparent;
		// 	transform: rotate(45deg);
		// }
	}
	
	.btn-big {
		padding-bottom: 60rpx;
	
		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>