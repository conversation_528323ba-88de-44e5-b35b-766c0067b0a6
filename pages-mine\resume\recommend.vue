<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="取消" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="tab-tips" :style="isGetScore?'background-color: #19be6b;':'background-color: #1e1848;'"
			v-if="!showTips">
			<text>{{isGetScore?"参与推荐的亲友越多，简历越受欢迎哦":"参与推荐的亲友越多，简历越受欢迎哦"}}</text>
		</view>

		<view class="tab-tips" style="background-color: #19be6b;" v-if="showTips">
			<text>当前正在完善【{{employeeName}}】的亲友推荐</text>
		</view>
		<u-gap height="38"></u-gap>

		<!-- 亲友推荐-已有的内容 -->
		<view v-if="exState==0">
			<view v-for="(item, index) in recommendList" :key="index"
				v-if="choiceItemIndex==-1||choiceItemIndex==index">
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>{{formatRecommender(item.recommender)}}推荐</text>
							<uni-icons class="picker-text-arrow" type="clear" size="26"
								style="float: right;line-height: 120rpx;" @click="tryDelete(index)"></uni-icons>
						</view>

						<view class="tab-inputbox-high">
							<textarea class="multiline-input" confirmType="done" maxlength="200"
								v-model="item.recommendContent" :placeholder="recommendContentTips" height="150"
								count></textarea>
						</view>
					</view>
				</view>
			</view>

			<view v-if="recommendList.length==0">
				<u-empty text="暂无亲友推荐" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</view>

			<!-- 保存按钮 -->
			<view class="flac-col" style="padding-top: 40rpx;">
				<view class="btn-big" v-if="recommendList.length!=0">
					<button @click="trySave()" style="margin: 0rpx auto"
						:style="isShowSave?'background-color:#1e1848;':'background-color:#909399;color:#fff'">保存</button>
				</view>
				<view class="btn-big">
					<button style="margin: 0rpx auto;" @click="openAdd()">添加</button>
				</view>
			</view>
		</view>

		<!-- 亲友推荐-添加的内容 -->
		<view v-if="exState==1">
			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的亲友类型？</text>
						<text>（尽量为周边亲友哦）</text>
					</view>
					<view class="tab-checkbox" v-for="(tabList, index) in familyList" :key="index">
						<view class="checkbox" :class="{ activeBox: tabList.value == choiceRecommender }">
							<text v-model="tabList.value" @click="choiceTab(index)">{{ tabList.text }}</text>
						</view>
					</view>
				</view>
			</view>

			<view>
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>亲友的描述内容</text></view>
						<view class="tab-inputbox-high">
							<textarea class="multiline-input" confirmType="done" maxlength="200"
								v-model="recommendContent" :placeholder="recommendContentTips" height="150"
								count></textarea>
						</view>
					</view>
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="btn-big">
				<button @click="tryAdd()"
					:style="choiceRecommender!=-1?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">添
					加</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 最少填写字数
				minWords: 15,
				// 亲友推荐状态（0：编辑经历 1：添加经历）
				exState: 0,
				// 选中的亲友推荐
				choiceItemIndex: -1,
				// 自动保存时间间隔
				autoSaveTime: 7,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				isGetScore: false,
				scrollTop: 0,
				memberId: uni.getStorageSync('memberId') || 0,
				baomuId: null,
				employeeName: '',

				changeNum: 0,
				choiceRecommender: 0,
				allowBackPress: true,
				deleteIndex: 0,
				show: false,
				show1: false,
				isShowSave: false,
				showTips: false,
				recommendContentTips: '请描述平时生活中表现突出的地方',
				recommendContent: '',
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				familyList: [{
						text: '配偶',
						value: 0
					},
					{
						text: '子女',
						value: 1
					},
					{
						text: '父母',
						value: 2
					},
					{
						text: '朋友',
						value: 3
					},
					{
						text: '亲戚',
						value: 4
					},
					{
						text: '雇主',
						value: 5
					},
				],
				recommendList: [],
				saveTime: 0,
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选中多选框
			choiceTab(index) {
				this.choiceRecommender = this.familyList[index].value
				// this.recommendContent = ""
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? '0' + MM : MM
				let d = date.getDate()
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d
			},
			formatRecommender(value) {
				let str = "暂无"
				return this.familyList[value].text
			},
			// 显示日期选择器
			showPicker(value, show, index) {
				if (value == 0) {
					this.recommendList[index].show = show
				} else {
					this.recommendList[index].show1 = show
				}
			},
			// 确认日期选择
			confirmState(index) {
				if (index == -1) {
					this.show = false
					this.show1 = false
				} else {
					this.recommendList[index].show = false
					this.recommendList[index].show1 = false
				}
			},
			openAdd() {
				this.exState = 1
				this.$refs.uNotify.success("请填写你的亲友推荐吧～")
			},
			tryAdd() {
				this.openCheck(2, '确定添加亲友推荐？', '内容越贴近实际越好哦～')
			},
			// 尝试删除
			tryDelete(index) {
				this.deleteIndex = index
				this.openCheck(3, '确定删除这段亲友推荐？', '删除后将无法恢复！');
			},
			// 尝试保存
			trySave() {
				if (this.recommendList.length !== 0) {
					this.openCheck(0, '确定保存亲友推荐？', '内容越贴近实际越好哦～');
				} else {
					this.$refs.uNotify.error("请先添加至少一份推荐记录！")
				}
			},
			// 保存亲友推荐内容
			save() {
				if (this.choiceRecommender == 0) {
					this.checkBaomuAndInit()
					if (!this.baomuId) {
						let timer = setTimeout(() => {
							if (this.baomuId) {
								this.updateEmployeeRecommendList(0)
							}
						}, 1500);
					} else {
						this.updateEmployeeRecommendList(0)
					}
					return
				}

				if (!this.recommendContent) {
					this.$refs.uNotify.error('推荐内容不能为空哦～')
				} else {
					this.checkBaomuAndInit()
					if (!this.baomuId) {
						let timer = setTimeout(() => {
							if (this.baomuId) {
								this.updateEmployeeRecommendList(0)
							}
						}, 1500);
					} else {
						this.updateEmployeeRecommendList(0)
					}
				}
			},
			add() {
				if (!this.recommendContent) {
					this.$refs.uNotify.error('推荐内容不能为空哦～');
				} else {
					this.checkBaomuAndInit()
					if (!this.baomuId) {
						let timer = setTimeout(() => {
							if (this.baomuId) {
								this.insertEmployeeRecommend()
							}
						}, 1500);
					} else {
						this.insertEmployeeRecommend()
					}
				}
			},
			delete() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/deleteEmployeeRecommend',
					method: 'GET',
					hideLoading: true,
					path: this.recommendList[this.deleteIndex].id,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('亲友推荐删除成功！')
							this.$delete(this.recommendList, this.deleteIndex)
							this.choiceItemIndex = -1
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 添加亲友推荐
			insertEmployeeRecommend() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/insertEmployeeRecommend',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId,
						recommendContent: this.recommendContent,
						recommender: this.choiceRecommender
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('亲友推荐添加成功！')
							this.changeNum = 0
							this.recommendContent = ""
							this.choiceRecommender = 0
							this.exState = 0
							this.choiceItemIndex = -1
							this.listEmployeeRecommend()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 更新亲友推荐
			updateEmployeeRecommendList(value) {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/updateEmployeeRecommendList',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.recommendList,
					success: res => {
						if (value == 1) {
							return
						}
						if (res.code == 0) {
							this.$refs.uNotify.success('保存成功！')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			listEmployeeRecommend() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/listEmployeeRecommend',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId || 0,
					success: res => {
						if (res.code == 0) {
							this.recommendList = res.data
						}
						this.isShowSave = this.recommendList.length ? true : false
					}
				});
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				if (!this.baomuId) {
					return
				}
				this.http({
					url: 'getBaomuCollectByMemberId',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						nearStoreId: uni.getStorageSync("nearStoreId") || -1
					},
					success: res => {
						if (res.code == 0) {
							this.baomuId = res.data.baomuId;
						} else {
							this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
						}
					}
				});

			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (!this.baomuId) {
					return
				}
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							let baomuDetail = res.data
							this.baomuDetail = baomuDetail
							this.employeeName = baomuDetail.employee.realName
							this.listEmployeeRecommend()
						}
					}
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：确认不保存退出 1：确认保存
				if (this.checkType == 0) {
					this.save()
				} else if (this.checkType == 1) {
					return history.back()
				} else if (this.checkType == 2) {
					this.add()
				} else if (this.checkType == 3) {
					this.delete()
				}
			},
		},
		watch: {
			saveTime: {
				handler(newValue, oldVal) {
					if (this.saveTime != 0 && this.saveTime % this.autoSaveTime == 0 && this.baomuId && this.exState ==
						0) {
						this.updateEmployeeRecommendList(1)
					}
				},
				deep: true
			},
		},
		// 返回时校验
		onBackPress(e) {
			this.allowBackPress = false
			// 判断是否修改
			if (this.changeNum > 3) {
				this.openCheck(1, '确认返回吗？', '更改内容还未保存！');
			} else {
				this.allowBackPress = true
			}
			return !this.allowBackPress
		},
		onLoad(options) {
			// this.isGetScore = options.isGetScore == "true" ? true : false
			this.baomuId = options.baomuId ? JSON.parse(options.baomuId) : uni.getStorageSync('baomuId') || null
			this.showTips = options.showTips == 1 ? true : false
			this.choiceItemIndex = options.index || -1
			this.getBaomuDetail()
			setInterval(() => {
				this.saveTime += 1
			}, 1000);
		},
	};
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.resume-tab {
		margin-bottom: 40rpx;
	}
</style>