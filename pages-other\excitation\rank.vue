<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<view class="head-img">
			<img :src="rankImg" mode="widthFix" />
			<text>表彰次数排行</text>
			<view class="btn-switch">
				<uni-icons type="loop" size="20" color="#F9AE3D" @click="listMonthExcitationRank"></uni-icons>
			</view>
		</view>

		<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
			<view class="comment-tab" v-for="(item,index) in rankList" :key="index">
				<view class="flac-row" style="padding: 0 20rpx;">
					<view class="w2" @click="openImgPreview(item.employeeHeadImg||blankImg)">
						<u-avatar :src="item.employeeHeadImg||blankImg" size="50" />
					</view>
					<view class="flac-col w65" style="padding: 10rpx 0;" @click="openDetail(index)">
						<text style="display: block;">{{item.employeeName}}</text>
						<text style="display: block;">{{item.departName}}</text>
					</view>
					<view class="w15" style="text-align: left;" @click="openDetail(index)">
						<uni-icons type="medal-filled" size="24" color="#ff4d4b"></uni-icons>
						<text>{{item.num||''}}次</text>
					</view>
				</view>
			</view>

			<u-empty v-if="rankList.length==0" text="暂无排行" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>

		<view class="list-bottom" v-if="rankList.length!=0">
			<text>已显示全部内容</text>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#ffffff'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				rankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/ranking.png",
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				rankList: [],
				searchYear: null,
				searchMonth: null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开详情
			openDetail(index) {
				let id = this.rankList[index].id
				uni.navigateTo({
					url: '/pages-other/excitation/rank-detail?id=' + id
				})
			},
			// 获取打赏排行
			listMonthExcitationRank() {
				let date = new Date()
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/content/listMonthExcitationRank',
					method: 'POST',
					hideLoading: true,
					data: {
						searchYear: this.searchYear || date.getFullYear(),
						searchMonth: this.searchMonth || date.getMonth() + 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.rankList = res.data
						}
					}
				});
			},
		},
		onLoad(options) {
			this.searchYear = options.searchYear || this.searchYear
			this.searchMonth = options.searchMonth || this.searchMonth
			this.listMonthExcitationRank()
		},
	}
</script>


<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	.btn-switch {
		position: absolute;
		left: 250rpx;
		top: 64rpx;
	}

	.comment-tab {
		width: 100%;
		height: auto;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
		padding-bottom: 10rpx;
	}

	.list-bottom {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
			color: #909399;
		}
	}
</style>