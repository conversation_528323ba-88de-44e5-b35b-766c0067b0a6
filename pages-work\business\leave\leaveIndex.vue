<template>
  <view>
    <view class="w9 mg-at text-r cf262462 f16 lh45 fb" @click="toLeaveLog">请假记录</view>
    <u-cell-group>
      <u-cell icon="*" title="请假类型" @click="labelFlag= true" :value="typeVal" isLink iconStyle="color:red"
        titleStyle="color:#333;font-weight:bold;font-size:32rpx">
      </u-cell>
	  <view v-if="leaveType==1" style="margin-left: 65%;font-size: 25rpx;color: red;">本周剩余公休天数<text style="font-size: 30rpx;">{{remainingDay}}</text>天</view>
      <u-cell icon="*" title="选择日期" @click="dateFlag = true,dateTimeShow=true" :value="leaveType==1?pickerValue:timeVal" isLink iconStyle="color:red"
        titleStyle="color:#333;font-weight:bold;font-size:32rpx"></u-cell>
      <!-- 日期选择器 -->
      <view v-if="dateFlag&&leaveType!==1">
        <uni-datetime-picker :start="newTime" v-model="datetimerange" type="datetimerange" rangeSeparator="至" />
      </view>
	   <u-datetime-picker :show="dateTimeShow&&leaveType==1" :minDate="Number(new Date())"  v-model="pickerValue" mode="date"
	   @confirm="pickerConfirm()"></u-datetime-picker>
      <view class="" style="margin: auto 30rpx;">
        <view class="f16 fb lh40"><text class="red" style="letter-spacing: 10rpx;">*</text>请假原因</view>
        <u--textarea v-model="value" placeholder="请输入请假原因" border="bottom"></u--textarea>
      </view>
      <view class="" style="margin: auto 30rpx;">
        <view class="f16 fb lh40">材料证明<text class="f14 c9" style="margin: auto 30rpx;">提供照片更加有说服力哦~</text></view>
        <u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple
          :maxCount="10"></u-upload>
      </view>
    </u-cell-group>
    <u-picker :columns="range" :show="labelFlag" @cancel="labelFlag=false" @confirm="changeSelect" />
    <u-button text="提交申请" :customStyle="btnStyle" @click="subMsg" />
  </view>
</template>

<script>
  export default {
    data() {
      return {
        btnStyle: {
          color: '#f6cc70',
          backgroundColor: '#1e1848',
          borderRadius: 0,
          borderColor:'#1e1848',
          position:'fixed',
          bottom: 0,
          left: 0
        },
        timeVal: '选择请假时间',
		pickerValue: '选择请假时间',
		remainingDay: 0,
		dateTimeShow: false,
        dateFlag: false,
        datetimerange: ["", ""],
        // typeVal: '事假',
        typeVal: '公休',
        newTime: Number(new Date()),
        value: '',
        endTime: '',
        startTime: '',
        leaveType: 1,
        labelFlag: false,
        range: [
          ["公休","事假", "病假"]
        ],
		// range: [
		//   ["事假", "病假"]
		// ],
        fileList1: [],
      };
    },
    onLoad(option) {
		this.getRemainingDay(0)
    },
    watch: {
      datetimerange(newval) {
        this.timeVal = '开始:' + this.datetimerange[0].substring(0, 10) +
          '结束:' + this.datetimerange[1].substring(0, 10);
        this.startTime = this.datetimerange[0].substring(0, 19)
        this.endTime = this.datetimerange[1].substring(0, 19)
        this.dateFlag = false
      },
	  pickerValue(newval){
		  this.pickerValue = this.formatTime(new Date(newval))
	  }
    },
    methods: {
		pickerConfirm(){
			this.dateTimeShow=false
		},
		// 时间格式化
		formatTime(value) {
			if (!value) {
				return '暂无'
			}
			if (typeof(value) == "string" && value.indexOf('-') != -1) {
				value = value.replace(/-/g, '/')
			}
			let date = new Date(value)
			let y = date.getFullYear()
			let MM = date.getMonth() + 1
			MM = MM < 10 ? '0' + MM : MM
			let d = date.getDate()
			d = d < 10 ? '0' + d : d;
			return y + '-' + MM + '-' + d
		},
	  getRemainingDay(){
		  this.http({
		  	url: 'getRemainingDay',
		  	method: 'GET',
		  	data: {
		  		employeeNo: uni.getStorageSync("employeeNo"),
				selectTime: this.pickerValue
		  	},
		  	success: res => {
		  		if(res.code==0){
		  			this.remainingDay = res.data
		  		}
		  	}
		  });
	  },
      deletePic(event) {
        console.log(event);
        this[`fileList${event.name}`].splice(event.index, 1)
      },
      subMsg() {
			  if(this.leaveType==1){
				  this.http({
				  	url: 'getRemainingDay',
				  	method: 'GET',
				  	data: {
				  		employeeNo: uni.getStorageSync("employeeNo"),
				  		selectTime: this.pickerValue
				  	},
				  	success: res => {
				  		if(res.code==0){
				  					if(this.leaveType==1&&res.data==0){
				  						return uni.showToast({
				  						  title: '公休剩余天数不足，请选择其他请假类型!',
				  						  icon: 'none'
				  						})
				  					}
				  						this.startTime = this.pickerValue+ " 00:00:00"
				  						this.endTime = this.pickerValue+ " 23:59:59"
				  		}
				  	}
				  });
			  }
			  setTimeout(()=>{
				  
				  if (!this.startTime || !this.endTime) {
				    return uni.showToast({
				      title: '请选择请假时间!',
				      icon: 'none'
				    })
				  }
				  if (!this.value) {
				    return uni.showToast({
				      title: '请填写请假原因!',
				      icon: 'none'
				    })
				  }
				  let imgs = '';
				  if (this.fileList1.length > 0) {
				    for (var i = 0; i < this.fileList1.length; i++) {
				      imgs += this.fileList1[i].url + ","
				    }
				  }
				  if (imgs) {
				    imgs = imgs.substring(0, imgs.length - 1)
				  }
				  this.http({
				    outsideUrl: 'https://yibanapi.xiaoyujia.com/leave/insert',
				    data: {
				      employeeNo: uni.getStorageSync("employeeNo"),
				      endTime: this.endTime,
				      prove: imgs,
				      reason: this.value,
				      startTime: this.startTime,
				      type: this.leaveType,
				    },
				    header: {
				      "content-type": "application/json;charset=UTF-8"
				    },
				    method: 'POST',
				    success: res => {
				      if (res.code == 0) {
				        uni.showToast({
				          title: '提交申请成功!',
				          icon: 'none'
				        })
				        uni.redirectTo({
				          url: '/pages-work/business/leave/leaveLog'
				        })
				      } else {
				        uni.showToast({
				          title: res.msg,
				          duration: 2500,
				          icon: 'none'
				        })
				      }
				    }
				  })
				  },1500)
      },
      toLeaveLog() {
        uni.redirectTo({
          url: '/pages-work/business/leave/leaveLog'
        })
      },
      // 新增图片
      async afterRead(event) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file)
        let fileListLen = this[`fileList${event.name}`].length
        lists.map((item) => {
          this[`fileList${event.name}`].push({
            ...item,
            status: 'uploading',
            message: '上传中'
          })
        })
        for (let i = 0; i < lists.length; i++) {
          const result = await this.uploadFilePromise(lists[i].url)
          let obj = JSON.parse(result);
          let item = this[`fileList${event.name}`][fileListLen]
          this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
            status: 'success',
            message: '',
            url: obj.data
          }))
          fileListLen++
        }
      },
      uploadFilePromise(url) {
        let obj = {}
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: 'https://api.xiaoyujia.com/system/imageUpload',
            filePath: url,
            name: 'file',
            formData: obj,
            success: (res) => {
              setTimeout(() => {
                resolve(res.data)
              }, 1000)
            }
          });
        })
      },
      changeSelect(e) {
        this.typeVal = e.value[0]
		if (e.value[0] == '公休') {
		  this.leaveType = 1
		}
        if (e.value[0] == '事假') {
          this.leaveType = 2
        }
        if (e.value[0] == '病假') {
          this.leaveType = 3
        }
        this.labelFlag = false
      }
    },
  }
</script>

<style lang="scss">

</style>