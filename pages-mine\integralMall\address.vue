<template>
  <view style="background: #f4f3f2;">
    <!-- #ifdef MP-WEIXIN || H5 -->
    <u-swipe-action>
      <u-swipe-action-item :options="options" v-for="(row,index) in addressList" :key="index"
        style="margin-bottom: 20rpx;" @click="del(row)">
        <view class="flac-row-b" style="padding: 40rpx 20rpx;">
          <view class="w85 mg-at" @click="select(row)">
            <view class="flac-row lh40">
              <view class="fb">{{row.name}}</view>
              <view class="t-indent2">{{row.phone}}</view>
              <view class="default" v-if="row.isDefault">
                默认
              </view>
            </view>
            <view class="f16">{{row.street}}</view>
            <view class="f16 c6 lh30">{{row.streetinfo}}</view>
          </view>
          <u-icon class="icon" name="edit-pen" size="30px" @click="edit(row)"></u-icon>
        </view>
      </u-swipe-action-item>
    </u-swipe-action>
    <!-- #endif -->
    <!-- #ifdef MP-TOUTIAO  || MP-ALIPAY -->
    <view class="bacf" v-for="(row,index) in addressList" :key="index" style="margin-bottom: 20rpx;">
      <view class="flac-row-b" style="padding: 40rpx 20rpx;">
        <view class="w85 mg-at" @click="select(row)">
          <view class="flac-row lh40">
            <view class="fb">{{row.name}}</view>
            <view class="t-indent2">{{row.phone}}</view>
            <view class="default" v-if="row.isDefault">
              默认
            </view>
          </view>
          <view class="f16">{{row.street}}</view>
          <view class="f16 c6 lh30">{{row.streetinfo}}</view>
        </view>
        <view class="flac-row-a">
          <u-icon customStyle="margin-right:30rpx;background:#3c9cff;border-radius:50%;padding:5rpx" color="#fff"
            name="edit-pen" size="30" @click="edit(row)"></u-icon>
          <u-icon customStyle="background:#f56c6c;border-radius:50%;padding:10rpx" color="#fff" name="trash" size="25"
            @click="del(row)"></u-icon>
        </view>
      </view>
    </view>
    <!-- #endif -->
    <view class="add text-c" @click="add">
      新增地址
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        isSelect: false,
        memberId: uni.getStorageSync('memberId'),
        addressList: [],
        type: null,
        options: [{
          icon: 'trash-fill',
          iconSize: 20,
          style: {
            backgroundColor: '#f56c6c',
            width: '40px',
            height: '40px',
            borderRadius: '100px',
            margin: '0 6px'
          }
        }]
      };
    },
    onShow() {
      this.getAddressList();

    },
    onLoad(e) {
      if (e.cmid) {
        this.memberId = e.cmid;
      }
      this.getAddressList();
      this.type = e.type;
      if (e.type == 'selectAddress' || e.type == 'selectAddr2' || e.type == 'selectAddr1') {
        this.isSelect = true;
      }
    },
    methods: {
      changeBtn(e) {
      },
      del(v) {
        let data = {
          memberId: v.memberId,
          id: v.id
        }
        uni.showModal({
          title: '删除提示',
          content: '你将删除这个服务地址',
          success: (res) => {
            if (res.confirm) {
              this.http({
                outsideUrl: 'https://api2.xiaoyujia.com/member/deleteMemberAddr',
                method: 'POST',
				header: {
					"content-type": "application/json;charset=UTF-8"
				},
                data: data,
                success: res => {
                  if (res.code === 0) {
                    uni.showToast({
                      title: '删除成功'
                    });
                    // uni.navigateBack();
                    this.getAddressList();
                  } else {
                    uni.showToast({
                      title: res.data,
                      icon: 'error'
                    });
                    return;
                  }
                },
                fail: err => {
                }
              })
            } else if (res.cancel) {
            }
          }
        });

      },
      getAddressList() {
        let param = {
          memberId: this.memberId
        }
        this.http({
          outsideUrl: 'https://api2.xiaoyujia.com/member/selectMemberAddress',
          method: 'POST',
          data: param,
		  header: {
		  	"content-type": "application/json;charset=UTF-8"
		  },
          success: res => {
            if (res.code === 0) {
              this.addressList = res.data;
              let arr = res.data.map(item => {
                return item.street;
              })
              let list = []

              for (let item of arr) {
                let newarr = item.split('|');
                // 将数组中的每一项，赋值给对象中属性对应的属性值
                var obj = {
                  street: '',
                  streetinfo: '',
                }
                var i = 0;
                // 循环对象
                for (let key in obj) {
                  obj[key] = newarr[i]
                  i++;
                }
                list.push(obj)
              }
              this.addressList.forEach((value, index) => {
                value['street'] = list[index].street;
                value['streetinfo'] = list[index].streetinfo;
              })
            } else {
              uni.showToast({
                title: res.data,
                icon: 'error'
              });
              return;
            }
          },
          fail: err => {
          }
        })
      },
      edit(row) {
        uni.setStorage({
          key: 'editAddress',
          data: row,
          success: (res) => {
            uni.navigateTo({
              url: "/pages-mine/integralMall/edit?type=edit&memberId=" + this.memberId
            })
          }
        });

      },
      add() {
        uni.navigateTo({
          url: "/pages-mine/integralMall/edit?type=add&memberId=" + this.memberId
        })
      },
      select(row) {
        // console.log(JSON.stringify(row))
        // uni.setStorage({
        // 	key:'selectAddress',
        // 	data:null,
        // })
        //是否需要返回地址(从订单确认页跳过来选收货地址)
        if (!this.isSelect) {
          return;
        }
		uni.setStorageSync('userAddress',encodeURIComponent(JSON.stringify(row)))
            // uni.navigateTo({
            //   url: "/pages-mine/integralMall/productDetails"
            // })
			 uni.navigateBack();
      }
    }
  }
</script>

<style lang="scss" scoped>
  .default {
    line-height: 24rpx;
    font-size: 24rpx;
    background-color: #f9ae3d;
    color: #fff;
    padding: 10rpx 20rpx;
    border-radius: 24rpx;
    margin-left: 20upx;
  }

  .imgBg {
    width: 280rpx;
    height: 180rpx;
    margin: auto 20rpx;
    border-radius: 10rpx;
    background: #c0c0c0;
  }

  .u-page {
    padding: 0;
  }

  .u-demo-block__title {
    padding: 10px 0 2px 15px;
  }

  .swipe-action {
    &__content {
      padding: 25rpx 0;

      &__text {
        font-size: 15px;
        color: $u-main-color;
        padding-left: 30rpx;
      }
    }
  }

  .iconStyle {
    position: absolute;
    top: 50;
    right: 0;
    z-index: 999;
  }

  .content {
    background-color: #fff;
  }

  .add {
    width: 70%;
    height: 80upx;
    line-height: 80upx;
    margin: auto;
    bottom: 5%;
    right: 15%;
    position: fixed;
    justify-content: center;
    align-items: center;
    background-color: #f9ae3d;
    color: #fff;
    border-radius: 80upx;
  }

  .list {
    flex-wrap: wrap;

    .row {
      width: 730rpx;
      padding: 23upx 2%;
      border: 2rpx solid #f9f9f9;

      .left {
        width: 90upx;
        flex-shrink: 0;
        align-items: center;

        .head {
          width: 70upx;
          height: 70upx;
          background: linear-gradient(to right, #ccc, #aaa);
          color: #fff;
          justify-content: center;
          align-items: center;
          border-radius: 60upx;
          font-size: 35upx;
        }
      }

      .center {
        width: 100%;
        flex-wrap: wrap;

        .name-tel {
          width: 100%;
          align-items: baseline;

          .name {
            font-size: 30upx;
          }

          .tel {
            margin-left: 30upx;
            font-size: 24upx;
            color: #777;
          }


        }

        .address {
          width: 100%;
          font-size: 24upx;
          align-items: baseline;
          color: #777;
        }
      }

      .right {
        flex-shrink: 0;
        align-items: center;
        margin-left: 25upx;

        .icon {
          justify-content: center;
          align-items: center;
          width: 80upx;
          height: 60upx;
          border-left: solid 1upx #aaa;
          font-size: 40upx;
          color: #777;
        }
      }
    }
  }
</style>