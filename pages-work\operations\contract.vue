<template>
	<view class="w10 h10 page">
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>
		<!-- 搜索框+筛选列表 -->
		<view class=" navTop w10">
			<view class="w10 bac1e1848">
				<uni-search-bar radius="5" placeholder="请输入客户名称/手机号/保姆名称/合同编号" @confirm="search" @cancel="search"
					cancel-text="搜索" v-model="keyword" />
			</view>
			<view style="background-color: #f4f3f2 ;" v-if="listData.length>0">
				<u-tabs @click="tabsClick" :list="emps" :current="tabscurrent" :scrollable="true" lineColor="#FF6344"
					lineWidth="40">
				</u-tabs>
			</view>
			<u-tabs :list="navList" lineWidth="22" lineHeight="8" :lineColor="`url(${lineBg}) 100% 100%`"
				:activeStyle="{ color: '#1e1848',fontWeight: 'bold',transform: 'scale(1.05)'}" @click="clickTabs"
				:current="current"></u-tabs>
			<HM-filterDropdown :filterData="filterData" :defaultSelected="defaultSelected" :updateMenuName="true"
				@confirm="confirm" dataFormat="Object"></HM-filterDropdown>
			<view v-if="showTime">
				<uni-datetime-picker v-model="datetimerange" type="datetimerange" rangeSeparator="至"
					@change="confirmTime" />
			</view>
		</view>

		<scrollList ref="list" :option="option" @scrolltolower="sc" @load="loadPage">

			<view class="list bacf h9 f16 lh45" v-for="(item,index) in dataList" :key="index">
				<view class="w85 mg-at flac-row-b f18 fb lh50">
					<view class="cf262462" @click="copy(item.no)">
						{{item.no}}
						<uni-tag text="补签" type="warning" v-if="item.employeeNames" @click="contractSupport(item)"
							style="margin-left: 10rpx;" />
					</view>
					<u-button :text="getStatusName(item.status)" :type="getTypeName(item.status)" size="large"
						customStyle="width:170rpx;height:60rpx;margin:auto 0"></u-button>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<u-icon size="30" :name="item.ifPay==1?'checkmark-circle-fill':'close-circle-fill'"
						:color="item.ifPay==1?'green':'red'"></u-icon>
					<view>{{item.ifPay==1?'已付款':'未付款'}}</view>
					<u-icon size="30"
						:name="item.memberSignDate&&item.employeeSignDate?'checkmark-circle-fill':'close-circle-fill'"
						:color="item.memberSignDate&&item.employeeSignDate?'green':'red'"></u-icon>
					<view>{{item.memberSignDate&&item.employeeSignDate?'已签约':'未签约'}}</view>
					<u-icon size="30" :name="item.ifUpwardInsurance==1?'checkmark-circle-fill':'close-circle-fill'"
						:color="item.ifUpwardInsurance==1?'green':'red'"></u-icon>
					<view>{{item.ifUpwardInsurance==1?'已上保':'未上保'}}</view>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<view class="fb">集团单号：</view>
					<view class="c6" @click="copy(item.billNo)">{{item.billNo}}</view>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<view class="fb">客户信息：</view>
					<view class="c6">{{item.memberName}}({{item.memberPhone}})</view>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<view class="fb">服务金额：</view>
					<view class="red">¥{{item.servicePay}}</view>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<view class="fb">服务员工：</view>
					<view class="c6">{{item.employeeName}} - {{item.employeeNo}}</view>
				</view>
				<view class="w85 mg-at flac-row-b border-bottom-2se">
					<view class="fb">服务地址：</view>
					<u-text :text="item.memberAdress" :lines="2"></u-text>
				</view>
				<view class="w9" style="display: flex;margin: 30rpx auto;">
					<u-button text="编辑合同" color="#1e1848" plain customStyle="width:30%;" v-if="!item.memberSignDate"
						@click="editContract(item.id)"></u-button>
					<u-button text="合同报险" type="error" customStyle="width:30%;" v-if="item.memberSignDate"
						@click="addInsurance(item)"></u-button>
					<u-button text="合同上保" type="error" customStyle="width:30%;"
						v-if="getDayTime(item.createDate) && item.memberSignDate && item.employeeSignDate && item.haveDz !== 1"
						@click="doInsurance(item)"></u-button>
					<u-button text="更多操作" color="#1e1848" customStyle="width:30%;color:#f6cc70"
						@click="showpopul(item)"></u-button>
				</view>
			</view>
		</scrollList>

		<u-popup :show="popul" @close="popul = false">
			<uni-section title="更多操作" type="line">
				<scroll-view scroll-y="true" class="scroll-Y" style="height: 64vh">
					<view v-if="roleId && roleId == 110">
						<u-button customStyle="width:60%;margin:20rpx auto" text="复制阿姨签名链接" color="#1e1848" plain
							size="large" @click="copyContractQmUrl(actionDom,1)"
							v-if="!actionDom.memberSignDate&&!actionDom.employeeSignDate"></u-button>
						<u-button customStyle="width:60%;margin:20rpx auto" text="复制客户签名链接" color="#1e1848" plain
							size="large" @click="copyContractQmUrl(actionDom,2)"
							v-if="!actionDom.memberSignDate&&!actionDom.employeeSignDate"></u-button>
					</view>
					<u-button customStyle="width:60%;margin:20rpx auto" text="查看订单详情" color="#1e1848" plain size="large"
						@click="gotoContractOrder(actionDom.billNo)" v-if="actionDom.billNo"></u-button>
					<!-- <u-button customStyle="width:60%;margin:20rpx auto" text="查看合同日志" color="#1e1848" plain size="large"
						@click="gotoContractLog(actionDom.id)"></u-button> -->
					<u-button customStyle="width:60%;margin:20rpx auto" text="查看合同" color="#1e1848" plain size="large"
						@click="lookContract" v-if="roleId !== 110"></u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="签约电子合同" color="#1e1848" plain size="large"
						@click="showFadadaPopul(actionDom)" v-if="roleId !== 110&&actionDom.status !== '5'"></u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="签约链接合同" color="#1e1848" plain size="large"
						v-if="roleId !== 110&&actionDom.status !== '5'"@click="showagentdom"> </u-button>		
					<u-button customStyle="width:60%;margin:20rpx auto" text="查看拟定合同" color="#1e1848" plain size="large"
						@click="getContractByAgent(actionDom.id,actionDom.contractType)"
						v-if="roleId !== 110"></u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="签署合同" color="#1e1848" plain size="large"
						v-if="actionDom.status > 0 && roleId == 110" @click="sign()"></u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="查看投保单" color="#1e1848" plain size="large"
						@click="showInSureConfirm(actionDom.no)"></u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="生成分期子单" color="#1e1848" plain size="large"
						@click="genOrder(actionDom)"
						v-if="actionDom.status!=='0' && actionDom.status!=='4' && actionDom.status!=='5'
                                            && actionDom.status!=='99' && actionDom.status!=='100' && actionDom.orderId!=null && actionDom.contractType == 4">
					</u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="生成代管子单" color="#1e1848" plain size="large"
						@click="genOrder(actionDom)"
						v-if="actionDom.status!=='0' && actionDom.status!=='4' && actionDom.status!=='5'
                                            && actionDom.status!=='99' && actionDom.status!=='100' && actionDom.orderId!=null && actionDom.contractType == 2">
					</u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="合同作废" color="#1e1848" plain
						v-if="actionDom.status == '0' || actionDom.status == '1'" size="large"
						@click="changeState(actionDom,99)">
					</u-button>
					<!-- <u-button customStyle="width:60%;margin:20rpx auto" text="暂存下户" color="#1e1848" plain
						@click="changeState(actionDom,100)"
						v-if="actionDom.status=='1' || actionDom.status=='2' || actionDom.status == '3'" size="large">
					</u-button> -->
					<u-button customStyle="width:60%;margin:20rpx auto" text="合同补签" color="#1e1848" plain
						@click="contractSupport(actionDom)" size="large" v-if="actionDom.status !== '5'"> </u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" text="清除电子签合同" color="#1e1848" plain
						@click="clearContract(actionDom)" v-if="roleId !== 110&&actionDom.status !== '5'" size="large">
					</u-button>
					<u-button customStyle="width:60%;margin:20rpx auto" v-if="actionDom.status !== '5'" text="合同终止"
						color="#1e1848" plain @click="contractStop(actionDom)" size="large"> </u-button>
					<view style="height: 10rpx;"></view>
					<u-safe-bottom></u-safe-bottom>
				</scroll-view>
			</uni-section>
		</u-popup>

		<!-- 打开收款码 -->
		<u-modal :show="showPayModal" title="付款码" @confirm="showPayModal = false">
			<view class="slot-content">
				<view style="text-align: center">子订单号：{{ billNo }}</view>
				<view style="text-align: center">集团订单号：{{ originBillNo }}</view>
				<view v-if="imgCode">
					<u-image :src="imgCode" mode="aspectFit"></u-image>
					<u-gap height="20"></u-gap>
					<u-button text="客户已付款,开始拆单" color="#1e1848" customStyle="color:#f6cc70"
						@click="payGenSubOrder"></u-button>
				</view>
				<view v-else>
					<u-text :text="text"></u-text>
					<u-button text="客户已付款,开始拆单" color="#1e1848" customStyle="color:#f6cc70"
						@click="payGenSubOrder"></u-button>
				</view>
			</view>
		</u-modal>


		<u-modal :show="showSignModal" title="签名地址" @confirm="showSignModal = false">
			<view class="slot-content">
				<view>客户是否已签名:
					<text v-if="actionDom.memberSignDate">已签名</text>
					<text v-else>未签名</text>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制客户签名地址" color="#1e1848" @click="copyContractUrl(0)"></u-button>
				<u-gap height="30"></u-gap>
				<view>员工是否已签名:
					<text v-if="actionDom.employeeSignDate">已签名</text>
					<text v-else>未签名</text>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制员工签名地址" color="#1e1848" @click="copyContractUrl(1)"></u-button>
			</view>
		</u-modal>

		<!-- 法大大查看弹窗 -->
	<!-- 	<u-modal :show="showFadadaModal" title="查看合同" @confirm="showFadadaModal = false">
			<view class="slot-content" v-if="actionDom">
				<u-button text="在线查看法大大合同" color="#1e1848" @click="getContract(actionDom)"></u-button>
				<u-gap height="20"></u-gap>
				<u-button text="下载法大大合同文件" color="#1e1848" @click="downloadContract(actionDom.no)"></u-button>
				<u-gap height="20"></u-gap>
				<u-button text="下载腾讯电子签合同" color="#1e1848" @click="getTencentContract(actionDom.no)"></u-button>
			</view>
		</u-modal> -->

		<!-- 法大大分享弹窗 -->
		<u-modal :show="showFadadaShareModal" title="签约线上合同" @confirm="showFadadaShareModal = false">
			<view class="slot-content" v-if="actionDom">
				<view class="flac-row">
					<text>客户是否已签名：</text>
					<text>{{actionDom.memberSignDate?'已签名':'未签名'}}</text>
					<uni-icons :type="actionDom.memberSignDate?'checkmarkempty':'closeempty'"
						:color="actionDom.memberSignDate?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="分享客户签名地址" color="#1e1848" @click="shareFadada(0)" open-type="share"></u-button>
				<u-gap height="30"></u-gap>
				<view class="flac-row">
					<text>员工是否已签名：</text>
					<text>{{actionDom.employeeSignDate?'已签名':'未签名'}}</text>
					<uni-icons :type="actionDom.employeeSignDate?'checkmarkempty':'closeempty'"
						:color="actionDom.employeeSignDate?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="分享员工签名地址" color="#1e1848" @click="shareFadada(1)" open-type="share"></u-button>
				<u-gap height="10"></u-gap>
				<view style="color: #ff4d4b;">注：请确保客户/员工的手机号准确无误哦
				</view>
			</view>
		</u-modal>
		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>
		<u-modal :show="showagent" title="签约链接合同" @confirm="showagent = false">
			<view class="slot-content" v-if="actionDom">
				<view class="flac-row">
					<text>客户是否已签名：</text>
					<text>{{actionDom.memberSignDate?'已签名':'未签名'}}</text>
					<uni-icons :type="actionDom.memberSignDate?'checkmarkempty':'closeempty'"
						:color="actionDom.memberSignDate?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制客户签名地址" color="#1e1848" @click="viewagent(0,actionDom.contractType)" ></u-button>
				<u-gap height="30"></u-gap>
				<view class="flac-row">
					<text>员工是否已签名：</text>
					<text>{{actionDom.employeeSignDate?'已签名':'未签名'}}</text>
					<uni-icons :type="actionDom.employeeSignDate?'checkmarkempty':'closeempty'"
						:color="actionDom.employeeSignDate?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制员工签名地址" color="#1e1848" @click="viewagent(1,actionDom.contractType)"></u-button>
				<u-gap height="10"></u-gap>
				<view style="color: #ff4d4b;">注：请确保客户/员工的手机号准确无误哦
				</view>
			</view>
		</u-modal>

		<!-- <movable-area class="movableArea" @click="trigger">
			<movable-view class="movableView" :position="position" x="300" y="540" :direction="direction"
				:damping="damping">
				<view style="font-size: 24rpx;margin-top: -8rpx;margin-left: 11rpx;">
					<view>
						上户
					</view>
					<view>
						合同
					</view>
				</view>
			</movable-view>
		</movable-area> -->

	</view>
</template>

<script>
	import data from '../operations/js/contractData.js'; //筛选菜单数据
	import moment from 'moment'; //时间格式化
	moment.locale('zh-cn');
	import scrollList from "@/pages-work/components/scroll-list/scroll-list.vue"
	export default {
		components: {
			scrollList
		},
		data() {
			return {
				showagent:false,
				checkTitle: "",
				checkText: "",
				checkType: 0,
				storeType: uni.getStorageSync('storeType'),
				showSignModal: false,
				contractData: {},
				showFadadaModal: false,
				showFadadaShareModal: false,
				roleId: uni.getStorageSync('roleId'),
				datetimerange: [],
				showTime: false,
				tabscurrent: 0,
				defaultSelected: [],
				emps: [],
				filterData: [],
				text: '',
				showPayModal: false,
				listData: [],
				imgCode: '',
				billNo: '',
				originBillNo: '',
				option: {
					size: 10,
					auto: false
				},
				param: {
					agentId: uni.getStorageSync('employeeId'),
					search: '',
					current: 1,
					status: null,
					startDateTime: null,
					contractType: null,
					endDateTime: null,
					searchType: null
				},
				dataList: [],
				moreAction: true,
				popul: false,
				keyword: '',
				actionDom: {},
				catch: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				shareImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_contract_sign.png',
				navList: [{
						name: '全部',
						status: null,
					},
					{
						name: '暂存',
						status: 0,
					},
					{
						name: '暂存下户',
						status: 100
					},
					{
						name: '生效中',
						status: 1
					},
					{
						name: '已完成',
						status: 2
					},
					{
						name: '补签单',
						status: 3
					},
					{
						name: '服务到期',
						status: 4
					},
					{
						name: '合同终止',
						status: 5
					},
					{
						name: '已作废',
						status: 99
					},
				],
				shareContent: '',
				searchParam: '',
				current: 0,
			}
		},
		props: {
			damping: {
				type: Number,
				default: 10
			},
			direction: {
				type: String,
				default: "all"
			},
			position: {
				type: Number,
				default: 4
			}
		},
		onLoad(options) {
			this.searchParam = options.search || ''
			if (options.searchType) {
				this.param.searchType = parseInt(options.searchType)
			}

			if (this.param.searchType == 2) {
				this.current = 5
			}
			this.filterData = data;
			if (!uni.getStorageSync('employeeId')) {
				return uni.showToast({
					title: '员工ID为空'
				})
			}
			this.catch = {
				"name": uni.getStorageSync("employeeName"),
				"value": uni.getStorageSync("employeeName")
			}
			this.searchEmps();
			// #ifdef MP-WEIXIN
			wx.hideShareMenu({})
			// #endif
			this.getdata();
		},
		methods: {
			showagentdom() {
				this.popul = false
				this.showagent = true;
			},
			viewagent(userType,contractType) {
			let urlPrefix = 'https://agent.xiaoyujia.com/upbaomu/';
			const type = Number(contractType);
			let typeName = '';
			const contractId = this.actionDom.id
			if(userType == 1) {
				typeName = 'baomu'
			}
			if (type === 0 || type === 1 || type === 6 || type === 7) {
				urlPrefix = urlPrefix + typeName + 'contractInfo/' + contractId 
			} else if (type === 2) {
				urlPrefix = urlPrefix + typeName +'contractEscrowInfo/' + contractId 
			} else if (type === 4) {
				urlPrefix = urlPrefix + typeName + 'contractInstallmentInfo/' + contractId 
			}
			this.copy(urlPrefix);
				
			},
			lookContract(){
				this.popul= false;
				//腾讯电子签优先
				this.http({
					url: 'showSignContractByTencent',
					method: 'GET',
					hideLoading: true,
					data: {
						no: this.actionDom.no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							return uni.downloadFile({
								url: res.data.url,
								success: function(res) {
									var filePath = res.tempFilePath;
									uni.openDocument({
										filePath: filePath,
										showMenu: true,
										success: function(res) {
											console.log('打开文档成功');
										}
									});
								}
							});
						}
					}
				})
				
				setTimeout(()=>{
					
				//法大大合同
				this.http({
					url: 'downloadPdfUrl',
					method: 'GET',
					data: {
						contractId: this.actionDom.no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('将链接复制到浏览器打开下载')
							return this.copy(res.data)
						}
				
					}
				
				})
				},1200)
				
				
				
				setTimeout(()=>{
					
				//链接
				let no = this.actionDom.no
				let contractType = this.actionDom.contractType
				let contractId = this.actionDom.id
				this.http({
					url: 'viewContractPdfUrl',
					method: 'GET',
					hideLoading: true,
					data: {
						contractId: no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let param = {
								url: res.data
							}
							let data = JSON.stringify(param);
							uni.navigateTo({
								url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
							})
						} else {
							if (res.msg === '该合同未签署法大大合同') {
								if (contractType == null) {
									return uni.showToast({
										title: '合同状态错误',
										icon: 'error'
									})
								}
								let urlPrefix = 'https://agent.xiaoyujia.com/upbaomu/';
								const type = Number(contractType);
								if (type === 0 || type === 1 || type === 6) {
									urlPrefix = urlPrefix + 'contractInfo/' + contractId + '?flag=0'
								} else if (type === 2) {
									urlPrefix = urlPrefix + 'contractEscrowInfo/' + contractId + '?flag=0'
								} else if (type === 4) {
									urlPrefix = urlPrefix + 'contractInstallmentInfo/' + contractId + '?flag=0'
								}
								this.copy(urlPrefix);
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
				
					}
				
				})
				
				},1200)
				
			},
			searchEmps() {
				this.http({
					url: 'getEmpByStoreId',
					method: 'GET',
					hideLoading: true,
					data: {
						storeId: uni.getStorageSync('storeId')
					},
					success: res => {
						if (res.code == 0) {
							if (this.roleId == 104 || this.roleId == 95 || this.roleId == 66 || this.roleId ==
								1 || this.roleId == 77 || this.roleId == 110 || this.roleId == 112) {
								this.listData = res.data
								this.getEmpName(res.data)
							}
						}
					}
				});
			},
			getEmpName(item) {
				const list = item;
				this.emps = [];
				this.emps.push(this.catch)
				for (var i = 0; i < list.length; i++) {
					if (list[i].id == uni.getStorageSync('employeeId')) {
						continue;
					}
					const li = list[i];
					const param = {
						name: li.realName,
						value: li.realName
					}
					this.emps.push(param);
				}
			},
			tabsClick(val) {
				this.param.current = 1
				this.dataList = [];
				for (var i = 0; i < this.listData.length; i++) {
					if (this.listData[i].realName == val.name) {
						this.param.agentId = this.listData[i].id
						break;
					}
				}
				this.getdata();
			},
			getTencentContract(no) {
				
			},
			doInsurance(dom) {
				uni.showModal({
					title: '请核对阿姨身份证/姓名/手机号三者无误后在操作以免后续理赔产生争议',
					confirmText: '确认无误',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.http({
								url: "doInsurance",
								method: 'POST',
								data: {
									insureType: 1,
									contractType: 1,
									contractId: dom.id
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										uni.showToast({
											title: '上保成功',
											icon: 'none',
											duration: 1500
										})
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none'
										})
									}
								}
							})
						}
					}
				})
			},
			trigger() {
				uni.navigateTo({
					url: '/pages-other/uetoContract/index'
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					this.http({
						url: "contractStop",
						method: 'POST',
						data: {
							employeeId: uni.getStorageSync('employeeId'),
							contractNo: this.contractData.no,
							storeType: this.storeType
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								uni.showToast({
									title: '合同终止成功!',
									icon: 'none',
									duration: 1500
								})
								setTimeout(() => {
									this.getPageData();
								}, 1500)
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					})
				}
			},
			contractStop(item) {
				this.contractData = item
				this.popul = false
				this.openCheck(1, "确认将合同终止吗？此操作不可回退！")
			},
			copyContractQmUrl(item, copyType) {
				let url = ''
				let url2 = ''
				let respUrl = ''
				let type = item.contractType
				if (type === 0 || type === 1) {
					url2 = 'https://agent.xiaoyujia.com/upbaomu/contractInfo/' + item.id
					url = 'https://agent.xiaoyujia.com/upbaomu/baomucontractInfo/' + item.id
				} else if (type === 2) {
					url2 = 'https://agent.xiaoyujia.com/upbaomu/contractEscrowInfo/' + item.id
					url = 'https://agent.xiaoyujia.com/upbaomu/baomucontractEscrowInfo/' + item.id
				} else if (type === 4) {
					url2 = 'https://agent.xiaoyujia.com/upbaomu/contractInstallmentInfo/' + item.id
					url = 'https://agent.xiaoyujia.com/upbaomu/baomucontractInstallmentInfo/' + item.id
				}
				if (copyType == 1) {
					respUrl = url
				} else {
					respUrl = url2
				}
				uni.setClipboardData({
					data: respUrl,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			sign() {
				this.showSignModal = true;
				this.popul = false;
			},
			copyContractUrl(val) {
				let urlPrefix = "https://agent.xiaoyujia.com/upbaomu/";
				const type = Number(this.actionDom.contractType);
				//0是客户，1是员工
				if (val == 0) {
					if (type === 0 || type === 1) {
						urlPrefix = urlPrefix + 'contractInfo/' + this.actionDom.id
					} else if (type === 2) {
						urlPrefix = urlPrefix + 'contractEscrowInfo/' + this.actionDom.id
					} else if (type === 4) {
						urlPrefix = urlPrefix + 'contractInstallmentInfo/' + this.actionDom.id
					}
				} else {
					if (type === 0 || type === 1) {
						urlPrefix = urlPrefix + 'baomucontractInfo/' + this.actionDom.id
					} else if (type === 2) {
						urlPrefix = urlPrefix + 'baomucontractEscrowInfo/' + this.actionDom.id
					} else if (type === 4) {
						urlPrefix = urlPrefix + 'baomucontractInstallmentInfo/' + this.actionDom.id
					}
				}
				uni.setClipboardData({
					data: urlPrefix, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
			},
			contractSupport(dom) {
				if (!dom.memberSignDate || !dom.employeeSignDate) {
					return uni.showToast({
						title: '原合同未完结不允许补签',
						icon: 'none'
					})
				}
				uni.navigateTo({
					url: '/pages-work/operations/contractSupport?id=' + dom.id + '&no=' + dom.no
				})
			},
			gotoContractOrder(billNo) {
				uni.navigateTo({
					url: '/pages-work/operations/order/orderDetail?billNo=' + billNo
				})
			},
			//接收菜单结果
			confirm(e) {
				this.showTime = false;
				let result = e.value[0][0]
				let contractType = e.value[1][0]
				this.param.contractType = contractType
				switch (result) {
					case 'all':
						this.param.startDateTime = null;
						this.param.endDateTime = null;
						break;
					case 'today':
						this.param.startDateTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
						this.param.endDateTime = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');
						break;
					case 'week':
						this.param.startDateTime = moment().startOf('week').format('YYYY-MM-DD HH:mm:ss');
						this.param.endDateTime = moment().endOf('week').format('YYYY-MM-DD HH:mm:ss');
						break;
					case 'month':
						this.param.startDateTime = moment().startOf('month').format('YYYY-MM-DD HH:mm:ss');;
						this.param.endDateTime = moment().endOf('month').format('YYYY-MM-DD HH:mm:ss');;
						break;
					case 'lastMonth':
						this.param.startDateTime = moment().subtract(1, 'months').startOf('month').format(
							'YYYY-MM-DD HH:mm:ss');;
						this.param.endDateTime = moment().subtract(1, 'months').endOf('month').format(
							'YYYY-MM-DD HH:mm:ss');;
						break;
					case 'init':
						this.showTime = true;
						break;
					default:
						break;
				}
				this.getPageData();

			},
			confirmTime(val) {
				this.param.startDateTime = val[0]
				this.param.endDateTime = val[1]
				this.getPageData();
			},
			clearContract(dom) {
				this.popul = false
				uni.showModal({
					content: '该操作会删除腾讯电子签已生成的合同信息包括所有签名。并重新生成', //模板中提示的内容
					confirmText: '确定',
					success: (res) => { //点击复制内容的后调函数
						if (res.confirm) {
							this.contractClean(dom);
						} else {
							console.log('取消')
						}
					}
				});
			},
			contractClean(dom) {
				this.http({
					url: 'cleanTencentSign',
					hideLoading: true,
					data: {
						no: dom.no,
						// employeeId: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: res.data
							})
							setTimeout(() => {
								this.getPageData()
							}, 1500)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			showInSureConfirm(no) {
				// let url = 'https://agent.xiaoyujia.com/inSureConfirm/' + id;
				// return this.copy(url);
				uni.navigateTo({
					url: '/pages-work/operations/contractInsurance?contractNo=' + no
				})
			},
			gotoContractLog(val) {
				uni.navigateTo({
					url: '/pages-work/operations/contractLog?id=' + val
				})
			},
			getContractByAgent(contractId, contractType) {
				this.popul = false;
				if (contractType == null) {
					return uni.showToast({
						title: '合同状态错误',
						icon: 'error'
					})
				}
				let urlPrefix = 'https://agent.xiaoyujia.com/upbaomu/';
				const type = Number(contractType);
				if (type === 0 || type === 1 || type === 6 || type === 7) {
					urlPrefix = urlPrefix + 'contractInfo/' + contractId + '?flag=0'
				} else if (type === 2) {
					urlPrefix = urlPrefix + 'contractEscrowInfo/' + contractId + '?flag=0'
				} else if (type === 4) {
					urlPrefix = urlPrefix + 'contractInstallmentInfo/' + contractId + '?flag=0'
				}
				this.copy(urlPrefix);

			},
			copy(info) {
				// uni.showModal({
				// 	content: info, //模板中提示的内容
				// 	confirmText: '复制',
				// 	success: (res) => { //点击复制内容的后调函数
				// 		if (res.confirm) {
				// 			let result
				// 			//uni.setClipboardData方法就是讲内容复制到粘贴板
				// 			uni.setClipboardData({
				// 				data: info, //要被复制的内容
				// 				success: () => { //复制成功的回调函数
				// 					uni.showToast({ //提示
				// 						title: '复制成功'
				// 					})
				// 				}
				// 			});


				// 		} else {
				// 			console.log('取消')
				// 		}
				// 	}
				// });

				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif

				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
			bindOrder() {

			},
			payGenSubOrder() {
				this.http({
					url: 'orderPayCallBack',
					data: {
						billNo: this.originBillNo
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: '拆单成功',
								icon: 'success'
							})
							this.showPayModal = false;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			changeState(item, state) {
				if (!state) {
					return uni.showToast({
						title: '合同状态错误',
						icon: 'none'
					})
				}

				// 合同终止
				if (state === 5) {
					item.serviceEndDate = new Date();
				}
				this.popul = false;
				uni.showModal({
					title: '提示',
					content: '是否变更合同信息',
					success: res => {
						if (res.confirm) {
							item.status = state;
							item.operator = uni.getStorageSync('employeeId');
							this.http({
								url: 'updateContractById',
								method: 'POST',
								data: item,
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.data == true) {
										setTimeout(() => {
											this.getdata();
										}, 500)
										uni.showToast({
											title: '更新成功'
										})

									} else {
										uni.showToast({
											title: '更新失败',
											icon: 'none'
										})
									}
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			},
			editContract(id) {
				uni.navigateTo({
					url: '/pages-work/operations/contractEdit?id=' + id
				})
			},
			genOrder(item) {
				if (item == null) {
					return uni.showToast({
						title: '获取不到合同',
						icon: 'error'
					})
				}
				const id = item.id;
				if (id == null) {
					return uni.showToast({
						title: '获取不到合同信息',
						icon: 'error'
					})
				}
				this.popul = false;
				uni.showModal({
					title: '提示',
					content: '合同号:' + item.no + '\n确认生成子单?',
					success: res => {
						if (res.confirm) {
							console.log('用户点击确定');
							this.http({
								url: 'genSubOrder',
								method: "POST",
								data: {
									id: item.id
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										const datum = res.data;
										this.billNo = datum.subBillNo;
										this.originBillNo = datum.originBillNo;
										//生成付款二维码
										this.showPayCode(this.originBillNo);
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none'
										})
									}
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			},
			showPayCode(originBillNo) {
				this.http({
					url: 'getOrderPayCode',
					method: 'GET',
					data: {
						billNo: originBillNo
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.showPayModal = true;
						if (res.code == 0) {
							this.imgCode = res.data;
						} else {
							this.text = res.msg;
						}

					}

				})
			},
			downloadContract(no) {
				this.http({
					url: 'downloadPdfUrl',
					method: 'GET',
					data: {
						contractId: no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('将链接复制到浏览器打开下载')
							this.copy(res.data)
						} else {
							this.$refs.uNotify.error(res.msg)
						}

					}

				})
			},
			getContract(item) {
				
			},
			showpopul(e) {
				this.actionDom = e
				this.popul = true
			},
			showFadadaPopul(e) {
				this.popul = false
				this.showFadadaShareModal = true
			},
			// 分享签约链接
			shareFadada(userType) {
				let url = '/pages-mine/contract/contract-sign?userType=' + userType + '&contractNo=' + this
					.actionDom.no
				// #ifndef MP-WEIXIN
				this.copy('https://jiajie.xiaoyujia.com' + url)
				// #endif
				// #ifdef MP-WEIXIN
				let title = '签约入户，用心服务，就在小羽佳'
				title += userType == 0 ? '（客户版）' : '（员工版）'
				this.shareContent = {
					title: title,
					path: url,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: this.shareImg
				}
				// #endif
			},
			// 分享
			onShareAppMessage(res) {
				return new Promise((resolve, reject) => {
					wx.showLoading({
						title: '请分享给对应客户或员工...',
						icon: 'none'
					})
					setTimeout(() => {
						wx.hideLoading()
						resolve(this.shareContent)
					}, 600)
				})
			},
			search(res) {
				this.param.search = res.value;
				this.param.current = 1;
				this.dataList = [];
				this.http({
					url: 'contractPage',
					method: 'POST',
					hideLoading: true,
					data: this.param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.data.records.length > 0) {
							this.dataList = [];
							this.dataList = [...this.dataList, ...res.data.records];
							this.$refs.list.loadSuccess({
								list: this.dataList,
								total: res.data.total
							});
						}

					}
				})
			},
			clickTabs(e, item) {
				console.log(e.status)
				this.param.status = e.status;
				this.param.current = 1;
				this.dataList = [];
				this.getdata();
			},
			loadPage(e) {
				this.param.current = e.page;
			},
			getdata() {
				// 若为管理员且有合同号入参，可直接搜索到该合同，方便管理
				if (this.roleId == 1) {
					this.param.agentId = 12
				}
				this.param.search = this.searchParam
				this.http({
					url: 'contractPage',
					method: 'POST',
					data: this.param,
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.data.records.length > 0) {
							this.dataList = [...this.dataList, ...res.data.records];
							this.$refs.list.loadSuccess({
								list: res.data.records,
								total: res.data.total
							});
						}

					}
				})
			},
			getPageData() {
				this.param.current = 1;
				this.dataList = [];
				this.http({
					url: 'contractPage',
					method: 'POST',
					data: this.param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.data.records.length > 0) {
							this.dataList = [...this.dataList, ...res.data.records];
							this.$refs.list.loadSuccess({
								list: res.data.records,
								total: res.data.total
							});
						}

					}
				})
			},
			sc() {
				this.getdata();
			},
			getTypeName() {

			},
			getStatusName(status) {
				if (status == null) {
					return "未知";
				}
				switch (Number(status)) {
					case 0:
						return "暂存";
					case 1:
						return "生效中";
					case 2:
						return "已完成";
					case 3:
						return "补签单";
					case 4:
						return "未续签/已过期";
					case 5:
						return "合同终止";
					case 99:
						return "作废";
					case 100:
						return "暂存下户";
					default:
						return "未知";
				}
			},
			getTypeName(status) {
				if (status == null) {
					return "default";
				}
				switch (Number(status)) {
					case 0:
					case 1:
					case 2:
					case 3:
					case 100:
						return "primary";
					case 4:
					case 5:
					case 99:
						return "error";
					default:
						return "default";
				}
			},
			addInsurance(item) {
				uni.navigateTo({
					url: '/pages-work/operations/reportInsurance?type=1&billNo=' + item.billNo
				})
			},
			getDayTime(time) {
				const date = moment(time, "YYYY-MM-DD HH:mm:ss");
				const compare = moment('2025-04-01', "YYYY-MM-DD");
				if (date.isAfter(compare)) {
					return true;
				} else {
					return false;
				}
			}

		}
	}
</script>
<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background-color: #f4f2f3 !important;
	}

	.navTop {
		position: sticky;
		top: 0;
		left: 0;
		z-index: 9999;
	}

	.list {
		width: 92%;
		margin: 30rpx auto;
		padding: 10rpx 0;
		border-radius: 20rpx;
	}

	.con {
		display: flex;
		justify-content: space-between;
		border-bottom: 2rpx solid #ececec;
	}

	/deep/.uni-searchbar__cancel {
		color: #fff !important;
	}


	.movableArea {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
		z-index: 999;

		.movableView {
			pointer-events: auto; //可以点击
			width: 70rpx;
			height: 70rpx;
			padding: 10rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 100%;
			border: 2px solid #1e1848;

			.iconImage {
				display: block;
				width: 50rpx;
				height: 50rpx;
				margin-left: 10rpx;
				// animation: iconImage 5s linear infinite;
			}

			@keyframes iconImage {
				0% {
					-webkit-transform: rotate(0deg);
				}

				25% {
					-webkit-transform: rotate(90deg);
				}

				50% {
					-webkit-transform: rotate(180deg);
				}

				75% {
					-webkit-transform: rotate(270deg);
				}

				100% {
					-webkit-transform: rotate(360deg);
				}
			}

		}
	}
</style>