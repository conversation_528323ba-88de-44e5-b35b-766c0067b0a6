<template>
	<view class="f14">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 评价规则弹窗 -->
		<u-popup :show="showPopup" @close="showPopup=false" :closeable="true" mode="bottom" round="10">
			<view class="w9 mg-at" style="padding: 50rpx 0;">
				<view class="f18 fb lh35">用户评价规则</view>
				<scroll-view scroll-y="true" class="scroll-Y w95" style="padding: 25rpx;height: calc(75vh - 50px);">
					<view v-for="(item,index) in ruleList" :key="index">
						<view class="f16 fb lh40">{{item.title}}</view>
						<view style="margin:20rpx auto;" v-for="(item1,index2) in item.sectionList" :key="index2">
							<view class="flac-row c3" style="align-items: unset;">
								{{index2 + 1 + '.'}}
								<view style="margin-left: 10rpx;">{{item1}}</view>
							</view>
						</view>
						<view v-for="(item2,index3) in item.imgList" :key="index3">
							<image :src="item2" mode="widthFix"></image>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 评价回复 -->
		<u-popup :show="showPopupReply" @close="showPopupReply = false" round="10">
			<view class="f18 fb text-c lh60">评价回复</view>
			<view class="tab-inputbox-high" style="margin: 10rpx 0rpx 30rpx 30rpx;height: 600rpx;">
				<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="formReply.replyContent"
					placeholder="请填写您对该评价的回复" height="100" count></u--textarea>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopupReply = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="submit()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 门店选择栏目 -->
		<view class="boxStyle bacf flac-row-b lh40">
			<view class="flac-row">
				<view class="text-l f16 fb" style="margin-right: 10rpx;" @click="openPickerMine(0,0)">
					{{storeName || '未知门店'}}
				</view>
				<u-icon name="arrow-down-fill" size='12' />
			</view>
			<view class="f15" @click="openDetail(2,0)">投诉记录</view>
		</view>

		<!-- 评分统计栏目 -->
		<view class="boxStyle bacf">
			<view class="flac-row-b lh50">
				<view class="flac-row fb f16">
					用户评分<view class="t-indent2 c9 fb4">更新于{{updatetime}}</view>
				</view>
				<view class="c6" @click="showPopup = true">评分规则</view>
			</view>
			<view class="">
				<view class="flac-row-c lh50">
					<view class="ce7405d fb f22">{{acceptanceScore}}</view>
					<u-rate style="margin: auto 20rpx;" size="20" activeIcon="heart-fill" inactiveIcon="heart" count="5"
						readonly :value="acceptanceScore"></u-rate>
					<view class="">{{acceptanceScore < 2 ? '差' : ( acceptanceScore < 4 ? '一般' : '好' )}}</view>
				</view>
				<view class="flac-row-c">
					具体分值{{acceptanceScore}}
					<view class="t-indent">{{formatScoreTips(acceptanceScoreList)}}</view>
				</view>
				<view class="lh35 text-c c9" @click="openDetail(0,0)"> 查看评分详情 > </view>
			</view>
		</view>

		<!-- 评价栏目 -->
		<view class="boxStyle bacf">
			<view class="flac-row-b lh50">
				<view class="fb f16">用户评价</view>
				<view class="c6 flac-row">默认排序<u-icon name="arrow-down-fill" size="10" /></view>
			</view>

			<!-- 评价分类菜单 -->
			<view class="tagStyle" v-for="(item, index) in menuList" :key="index">
				<u-tag shape="circle" :text="item.name + '（' + (item.count || '0')+ '）'" :plain="!item.checked"
					type="primary" :name="index" @click="menuClick">
				</u-tag>
			</view>

			<!-- 评价列表 -->
			<view class="commentBox">
				<view class="listStyle" v-for="(item,index) in commentList" :key="index">
					<view class="flac-row-b" @click="openDetail(1,index)">
						<view class="flac-row">
							<u-avatar :src="item.memberHeadImg||blankHeadImg" size="28" shape="circle" />
							<view class="t-indent fb">{{item.memberName || '匿名用户'}}</view>
						</view>
						<view class="f12 c9">{{item.productName || ''}} | {{item.creatDate || '暂无创建时间'}}</view>
					</view>
					<view class="flac-row" style="margin: 20rpx auto;">
						<view class="iconStyle flac-row" v-if="item.evaluateGrade == 2">
							<u-icon :name="item.icon || 'thumb-up-fill'" color="#8f755d" shape="circle" size="15" />
							<view class="f12">{{item.tagName || '认为可推荐'}}</view>
						</view>
					</view>
					<view class="" @click="openReply(index)">
						<view :class="item.isShow ? '':'textStyle'">
							{{formatEvaluate(index) || '暂无评价内容'}}
						</view>
						<view class="imgBox">
							<image class="imgStyle" v-for="(item1,index1) in item.serviceAcceptanceFormImgList"
								v-if="item.isShow||index1<maxShowPhoto" :key="index1" :src="item1.imgUrl"
								mode="widthFix" @click="openImgPreview(index,index1)">
							</image>
						</view>
						<view class="tab" v-if="item.isShow&&item.serviceAcceptanceFormReplyList.length!=0">
							<view v-for="(item1,index1) in item.serviceAcceptanceFormReplyList" :key="index1">
								<view class="flac-row-b">
									<view class="flac-row">
										<u-avatar :src="item1.memberHeadImg||blankHeadImg" size="28" shape="circle" />
										<view class="t-indent fb">{{item1.memberName || '匿名用户'}}</view>
									</view>
									<view class="f12 c9">{{item1.createTime || ''}}</view>
								</view>
								<view style="margin: 20rpx 0;">
									{{item1.replyContent || '暂无回复内容'}}
								</view>
							</view>
						</view>
					</view>
					<view class="text-r" style="color: #1e1848;" @click="changeShow(index)" v-if="item.evaluate&&item.evaluate.length>maxTextLength
						||item.serviceAcceptanceFormImgList.length>maxShowPhoto
						||item.serviceAcceptanceFormReplyList">
						{{item.isShow ? '收起':'展开'}}
					</view>
				</view>

				<u-empty v-if="total==0" text="暂无评价" icon="http://cdn.uviewui.com/uview/empty/data.png" />

				<view class="list-bottom" v-if="searchCondition.current>=pageCount&&total!=0">
					<text>已显示全部内容</text>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import {
		data
	} from '../../uni_modules/uview-ui/libs/mixin/mixin';
	import wSelect from "@/pages-work/components/w-select/w-select.vue"
	export default {
		components: {
			wSelect
		},
		data() {
			return {
				// 可设置
				// 评价最大显示字数
				maxTextLength: 40,
				// 评价最大照片显示数
				maxShowPhoto: 3,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				choiceIndex: 0,
				choiceItemIndex: 0,
				choiceOrderByIndex: 0,
				orderByList: [{
					index: 0,
					value: "remindTime DESC,createDate DESC",
					showText: "默认排序",
				}, {
					index: 1,
					value: "remindTime DESC,createDate DESC",
					showText: "时间排序",
				}],
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				roleId: uni.getStorageSync("roleId") || 0,
				acceptanceScore: '',
				acceptanceScoreList: [],
				updatetime: new Date().toISOString().slice(0, 10),
				blankHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',

				storeName: "",
				pickerIndex: 0,
				openPickerMineValue: 0,
				pickerMineName: "",
				searchPickerMineText: '',
				choicePickerMineValue: 0,
				pickerMineList: [],
				showPickerMine: false,

				showPopup: false,
				showPopupReply: false,
				photoArray: [],
				ruleList: [{
					title: '评分作用',
					sectionList: ['评分是以用户评价为主，用于客观反映商户在用户心中的真实经营状况', '评分越高，商户可获得的推荐流量越多，用户的消费意愿也越高'],
					imgList: ['https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-bzyw.png', ]
				}],
				storeId: uni.getStorageSync("storeId") || 0,
				storeIndex: 0,
				storeName: "",
				storeList: [],
				menuList: [],
				commentList: [],
				total: 0,
				pageCount: 0,
				// 查询条件
				searchCondition: {
					storeId: uni.getStorageSync("storeId") || 2,
					current: 1,
					size: 10,
					orderBy: "s.creatDate DESC",
				},
				formReply: {
					replyContent: ''
				}
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			// 打开选择器
			openPickerMine(value, index) {
				if (value == 0) {
					this.pickerMineName = "label"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.openPickerMineValue = index
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.storeIndex = index
					this.storeId = this.storeList[index].key
					this.storeName = this.storeList[index].label
					this.getServiceAcceptanceFormMenu()
				}
				this.showPickerMine = false
				if (this.openPickerMineValue == 1) {
					this.popupShare = true
				}
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 选择评价菜单
			menuClick(name) {
				this.menuList.map((item, i) => {
					item.checked = i === name ? true : false
					if (item.checked) {
						this.choiceIndex = i
					}
				})
				this.refreshList()
			},
			formatScoreTips(data) {
				if (data.length == 0) {
					return
				}
				let scoreNow = data[0].score
				let scoreLast = data[1].score
				let result = '较上周 -'
				if (scoreNow < scoreLast) {
					result = '较上周 ↓'
				}

				if (scoreNow > scoreLast) {
					result = '较上周 ↑'
				}
				return result
			},
			// 格式化评价
			formatEvaluate(index) {
				let evaluate = this.commentList[index].evaluate || ""
				let isShow = this.commentList[index].isShow || false
				let maxTextLength = this.maxTextLength
				if (!isShow && evaluate.length > maxTextLength) {
					evaluate = evaluate.substring(0, maxTextLength) + "..."
				}
				return evaluate
			},
			changeShow(index) {
				this.commentList[index].isShow = !this.commentList[index].isShow
			},
			// 打开图片浏览
			openImgPreview(index, index1) {
				console.log(index, index1);
				let imgs = this.commentList[index].serviceAcceptanceFormImgList.map(item => {
					return item.imgUrl
				})
				uni.previewImage({
					urls: imgs,
					current: index1
				})
			},
			// 打开评价
			openReply(index) {
				this.choiceItemIndex = index
				this.showPopupReply = true
			},
			// 打开订单详情
			openDetail(value, index) {
				if (value == 0) {
					uni.navigateTo({
						url: '/pages-work/comment/detailPage?storeId=' + this.storeId
					})
				} else if (value == 1) {
					let billNo = this.commentList[index].orderNo
					uni.navigateTo({
						url: '/pages-work/operations/order/orderDetail?billNo=' + billNo
					})
				} else if (value == 2) {
					uni.navigateTo({
						url: '/pages-work/comment/orderTsList?storeId=' + this.storeId
					})
				}
			},
			submit() {
				let data = this.commentList[this.choiceItemIndex]
				let reply = {
					serviceAcceptanceFormId: data.id,
					memberId: this.memberId,
					memberName: uni.getStorageSync("memberName") || "",
					memberHeadImg: uni.getStorageSync("memberHeadImg") || "",
					employeeId: this.employeeId,
					replyContent: this.formReply.replyContent
				}
				if (!this.formReply.replyContent) {
					this.$refs.uNotify.error('回复内容不能为空')
				} else {
					this.http({
						url: 'insertServiceAcceptanceFormReply',
						data: reply,
						method: 'POST',
						hideLoading: true,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.$refs.uNotify.success('回复已提交！')
								this.commentList[this.choiceItemIndex].serviceAcceptanceFormReplyList.push(
									reply)
								this.showPopupReply = false
								this.formReply.replyContent = ""
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					})
				}
			},
			// 刷新列表
			refreshList() {
				this.startFilter()
			},
			// 开始筛选
			startFilter() {
				this.commentList = []
				this.total = 0
				this.searchCondition.current = 1
				this.searchCondition.evaluateGrade = this.menuList[this.choiceIndex].id

				this.pageServiceAcceptanceForm()
			},
			getStoreList() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/work/getStoreListData",
					data: {
						employeeId: uni.getStorageSync("employeeId") || 0,
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
							if (this.roleId == 1) {
								let index = 0
								this.storeId = this.storeList[index].key
							}
							let id = this.storeId
							for (let i = 0; i < this.storeList.length; i++) {
								if (id == this.storeList[i].key) {
									this.storeName = this.storeList[i].label
									this.storeIndex = i
									this.pickerIndex = i
									break
								}
							}
							this.getServiceAcceptanceFormMenu()
						}
					}
				})
			},
			// 获取服务评价菜单
			getServiceAcceptanceFormMenu() {
				this.http({
					url: "getServiceAcceptanceFormMenu",
					method: "POST",
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						storeId: this.storeId,
					},
					success: res => {
						if (res.code == 0) {
							this.menuList = res.data
							this.getServiceAcceptanceScore()
							this.refreshList()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 获取用户评分
			getServiceAcceptanceScore() {
				this.http({
					url: "getServiceAcceptanceScore",
					method: "POST",
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						storeId: this.storeId,
					},
					success: res => {
						if (res.code == 0) {
							this.acceptanceScoreList = res.data
							this.acceptanceScore = this.acceptanceScoreList[0].score
						}
					}
				})
			},
			// 获取服务评价
			pageServiceAcceptanceForm() {
				this.$set(this.searchCondition, "storeId", this.storeId)
				this.http({
					url: "pageServiceAcceptanceForm",
					method: "POST",
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							let list = res.data.records
							this.total = list.length
							this.pageCount = res.data.pages
							list.forEach(item => {
								this.$set(item, "isShow", false)
							})
							this.commentList = this.commentList.concat(list)
						} else {
							if (this.searchCondition.current == 1) {
								// this.$refs.uNotify.error('暂无服务评价')
							}
						}
					}
				})
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageServiceAcceptanceForm()
		},
		onLoad(option) {
			this.getStoreList()
		},
	}
</script>

<style lang="scss" scoped>
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		background-color: #f8f9fb;
	}

	.tab {
		width: 94%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
		padding: 20rpx 20rpx;
		border-radius: 20rpx;
	}

	.ce7405d {
		color: #e7405d;
	}

	.boxStyle {
		padding: 20rpx 40rpx;
		margin-bottom: 30rpx;
	}

	.tagStyle {
		display: flex;
		flex-wrap: nowrap;
		align-items: center;
		width: auto;
		margin: 10rpx;
		display: inline-block;

	}

	/deep/.u-tag__text--medium {
		line-height: 26px;
	}

	.commentBox {
		border-top: 2rpx solid #eee;
		margin: 60rpx auto;
	}

	.listStyle {
		padding: 40rpx 20rpx;
	}

	.iconStyle {
		background-color: #f3efeb;
		color: #8f755d;
		padding: 0 10rpx;
		border-radius: 4rpx;
	}

	.textStyle {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box; //作为弹性伸缩盒子模型显示。
		-webkit-box-orient: vertical; //设置伸缩盒子的子元素排列方式--从上到下垂直排列
		-webkit-line-clamp: 3; //显示的行
	}

	.imgBox {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.imgStyle {
		display: block;
		width: 30%;
		margin: 10rpx;
		border-radius: 10rpx;
	}

	.list-bottom {
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;
		margin-bottom: 200rpx;

		text {
			display: block;
			text-align: center;
			color: #909399;
		}
	}
</style>