<template>
	<view class="page">
		<view class="cf lh40">
			<view class="f18 bac1e1848 t-indent2" style="text-align: center;">门店运营日报</view>
		</view>
		<view style="height: 30rpx;"></view>
		<view class="flac-row-a">
		<u-tag :text="storeName" shape="circle" borderColor="#1e1848" plain size="large" color="#1e1848" @click="selectStore"></u-tag>
		 <u-tag text="查看历史日报" shape="circle" borderColor="#1e1848" plain size="large" plain bgColor="#1e1848" color="#f6cc70" @click="pickerDateFlag=true"></u-tag>
		  </view>
		   <u-datetime-picker
		                  :show="pickerDateFlag"
		                  v-model="pickerDate"
						  :maxDate="nowTime"
						  @cancel="pickerDateFlag=false"
						  @confirm="selectTime"
		                  mode="date"
		          ></u-datetime-picker>
		<view class="w9 mg-at">
			<u--form :model="dailyPaperData"  labelPosition="top" labelWidth="330"
				:labelStyle="labelStyle">
				<!-- <u-form-item label="今日添加好友数" prop="friendsNum" required>
					<u-input placeholder="请填写今日添加好友数" type="number" v-model="dailyPaperData.friendsNum" />
				</u-form-item>
				<u-form-item label="今日新增粉丝数" prop="fansNum" required>
					<u-input placeholder="请填写今日新增粉丝数" type="number" v-model="dailyPaperData.fansNum" />
				</u-form-item> -->
				<!-- <u-form-item label="今日进店人数" prop="peopleNum" required>
					<u-input placeholder="请填写今日进店人数" type="number" v-model="dailyPaperData.peopleNum" />
				</u-form-item> -->
				<u-form-item label="今日门店收入" prop="amount" required>
					<u-input placeholder="请填写今日门店收入" type="number" v-model="dailyPaperData.amount" />
				</u-form-item>
				<u-form-item label="今日招工人数" prop="employeeNum" required>
					<u-input placeholder="请填写今日招工人数" type="number" v-model="dailyPaperData.employeeNum" />
				</u-form-item>
			<!-- 	<u-form-item label="今日朋友圈点赞数" prop="friendLikesNum" required>
					<u-input placeholder="请填写今日朋友圈点赞数" type="number" v-model="dailyPaperData.friendLikesNum" />
				</u-form-item> -->
				<u-form-item label="今日线索数" prop="needsNum" required>
					<u-input placeholder="请填写今日线索数" type="number" v-model="dailyPaperData.needsNum" />
				</u-form-item>
				<u-form-item label="今日上户数" prop="taskNum" required>
					<u-input placeholder="请填写今日上户数" type="number" v-model="dailyPaperData.taskNum" />
				</u-form-item>
				<!-- <u-form-item label="今日工作项" prop="workContent" required>
					<u-textarea placeholder="请填写今日工作项" v-model="dailyPaperData.workContent"/>
				</u-form-item> -->
				<u-form-item label="今日复盘与感悟" prop="improvesContent" required>
					<u-textarea placeholder="请填写今日复盘与感悟" v-model="dailyPaperData.improvesContent"/>
				</u-form-item>
				<u-form-item label="明日目标与待办" prop="requirements" required>
					<u-textarea placeholder="请填写明日目标与待办" v-model="dailyPaperData.requirements"/>
				</u-form-item>
				<u-form-item label="现场素材">
						<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple accept="all">
						</u-upload>
						<u-tag text="去上传素材" shape="circle" borderColor="#1e1848"  plain 
						bgColor="#1e1848" color="#f6cc70" style="margin-right: 40%;width: 22%;" size="mini" @click="goToUploadSc" ></u-tag>
				</u-form-item>
			</u--form>
			<u-button v-if="subFlag" @click="submitForm" text="提交" color="#1e1848" customStyle="color:#f6cc70"></u-button>
		</view>
		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>
		<u-picker :defaultIndex="defaultIndex" @cancel="pickerShow=false" @confirm="changePicker" :show="pickerShow"
		   keyName="label" :columns="selectColumns"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				labelStyle: {
					'font-weight': 'bold',
					'font-size': '32rpx',
					'letter-spacing': '5rpx',
				},
				fileList1: [],
				checkTitle: "",
				nowTime: Number(new Date()),
				pickerDateFlag: false,
				pickerDate: Number(new Date()),
				defaultIndex: [0],
				subFlag: true,
				selectColumns: [],
				getDateTime: '',
				pickerShow: false,
				storeName: null,
				checkText: "",
				dailyPaperData: {
					storeId: null,
					creater: uni.getStorageSync("employeeId"),
					activityImg: null,
					friendsNum: 0,
					fansNum: 0,
					peopleNum: 0,
					employeeNum: null,
					friendLikesNum: 0,
					amount: null,
					needsNum: null,
					taskNum: null,
					improvesContent: null,
					requirements: null,
					workContent: null,
				},
			}
		},
		onLoad() {
			this.getDateTime = this.$u.timeFormat(this.nowTime,"yyyy-mm-dd")
			uni.setStorageSync('redirectUrl',"/pages-work/operations/daily/dailyPaper")
			this.checkLogin()
			this.getStoreListData()
		},
		methods: {
			goToUploadSc(){
				uni.redirectTo({
				  url: '/pages-work/promote/material/mine/upload'
				})
			},
			checkLogin() {
			  console.log("正在检查登录状态...")
			  if (!uni.getStorageSync('memberId')) {
			    this.$toast.toast('还未登录哦，请重新登录')
			    uni.setStorageSync('redirectUrl', '/pages-work/operations/daily/dailyPaper')
			    setTimeout(() => {
			      uni.redirectTo({
			        url: '/pages-mine/login/login'
			      })
			    }, 2000)
			  }else {
				  //校验是否有权限查看此页面
				  if(uni.getStorageSync("roleId")!=66&&uni.getStorageSync("roleId")!=95&&uni.getStorageSync("roleId")!=77&&uni.getStorageSync("roleId")!=1){
					  	setTimeout(() => {
					  uni.showToast({
					    icon: 'none',
					    title: '您暂无权限操作此页面!',
						duration:2000
					  })
					  }, 30)
					  setTimeout(()=>{
					  uni.redirectTo({
					    url: '/pages-mine/index/mine'
					  })
					   },2000)
				  }
			  }
			},
			selectTime(time){
					var date = new Date();
					date.setTime(time.value);
					var month = date.getMonth() + 1;
					var time = date.getFullYear() + "-" + month + "-" + date.getDate();
					this.getDateTime = this.$u.timeFormat(time,"yyyy-mm-dd")
					this.pickerDate = this.$u.timeFormat(time,"yyyy-mm-dd")
					this.fileList1 = []
					this.getStoreWorkLog()
					this.pickerDateFlag = false
				},
			changePicker(e) {
				this.dailyPaperData = {}
				this.dailyPaperData.creater = uni.getStorageSync("employeeId")
				this.fileList1 = []
			  let arr = []
			  arr.push(e.indexs[0])
			  this.defaultIndex = arr
			  uni.setStorageSync("dayStoreName", e.value[0].label)
			  uni.setStorageSync("dayStoreId",e.value[0].key)
			  this.storeName = e.value[0].label
			  this.dailyPaperData.storeId = e.value[0].key
			  this.pickerShow = false
			  this.getStoreWorkLog()
			},
			selectStore() {
			  if (!uni.getStorageSync("roleId")) {
					  uni.showToast({
			      icon: 'none',
			      title: '门店信息为空'
			    })
			  } else if(this.selectColumns[0].length>1){
			    this.pickerShow = true
			  }
			},
			getStoreListData(){
					  this.http({
					    url: "getStoreListData",
					    data: {
					      employeeId: uni.getStorageSync("employeeId"),
					    },
					    method: "GET",
						hideLoading: true,
					    success: res => {
					      if (res.code == 0) {
							  this.selectColumns.push(res.data)
							this.storeName = res.data[0].label
							this.dailyPaperData.storeId = res.data[0].key
							uni.setStorageSync("dayStoreName", res.data[0].label)
							uni.setStorageSync("dayStoreId",res.data[0].key)
							this.getStoreWorkLog()
					      }
					    }
					  })
			},
			getStoreWorkLog(){
				this.http({
					url: 'getStoreWorkLog',
					method: 'GET',
					hideLoading: true,
					data:{
						storeId: uni.getStorageSync("dayStoreId"),
						selectTime: this.getDateTime
						},
					success: res=>{
						if(res.code==0&&res.data){
							this.dailyPaperData = res.data
							if(res.data.id){
								this.subFlag = false
							if (res.data.activityImg) {
								let arr = res.data.activityImg.split(',');
								for (var i = 0; i < arr.length; i++) {
									let obj = {
										url: arr[i]
									}
									this.fileList1.push(obj)
								}
							}
							}else {
								let nowTime = new Date();
								let nowTimeStr =this.$u.timeFormat(nowTime,"yyyy-mm-dd");
								if(nowTimeStr!==this.getDateTime){
									this.subFlag = false
								}else {
								this.subFlag = true
								}
							}
						}else {
							let nowTime = new Date();
							let nowTimeStr =this.$u.timeFormat(nowTime,"yyyy-mm-dd");
							if(nowTimeStr!==this.getDateTime){
								this.subFlag = false
							}else {
							this.subFlag = true
							}
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					this[`fileList${this.event.name}`].splice(this.event.index, 1)
				}
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			// 删除图片
			deletePic(event) {
				this.event = event
				this.openCheck(1, "确认删除该附件图片吗？")
			},
			submitForm(){
				//校验是否有权限查看此页面
				if(uni.getStorageSync("roleId")!=66&&uni.getStorageSync("roleId")!=1){
									  	setTimeout(() => {
									  uni.showToast({
									    icon: 'none',
									    title: '请使用店长账号提交运营日报！',
										duration:2000
									  })
									  }, 30)
				}
				let imgs = '';
				if (this.fileList1.length > 0) {
					for (var i = 0; i < this.fileList1.length; i++) {
						imgs += this.fileList1[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.dailyPaperData.activityImg = imgs
				if(!this.dailyPaperData.employeeNum||!this.dailyPaperData.improvesContent
				||!this.dailyPaperData.requirements){
					return uni.showToast({
							title: '请填写完整后再提交!',
							icon: 'none'
						})
				}
				this.dailyPaperData.creater = uni.getStorageSync('employeeId')
				this.http({
					url: 'addStoreWorkLog',
					data: this.dailyPaperData,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
				
			},
		}
	}
</script>

<style lang="less" scoped>
	.page {
		width: 100%;
		// height: 100vh;
		// background-color: #edf4ec;
		padding-bottom: 50rpx;

		.c63b468 {
			background-color: #FFC125;
		}
	}
</style>
<style lang="scss" scoped>

</style>
