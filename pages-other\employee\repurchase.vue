<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="fb f24 text-c lh50" style="margin: 20rpx 0;">
			{{searchTime}}-月度复购统计
		</view>

		<view class="w10 mg-at">
			<uni-table ref="tableData" stripe emptyText="暂无更多数据">
				<!-- 表头行 -->
				<uni-tr>
					<uni-th class="thStyle" align="center" width="60">工号</uni-th>
					<uni-th class="thStyle" align="center" width="60">姓名</uni-th>
					<uni-th class="thStyle" align="center" width="60">订单量</uni-th>
					<uni-th class="thStyle" align="center" width="60">复购量</uni-th>
					<uni-th class="thStyle" align="center" width="60">复购额</uni-th>
					<uni-th class="thStyle" align="center" width="60">复购率</uni-th>
				</uni-tr>
				<!-- 表格内容 -->
				<uni-tr v-for="(item, index) in orderInfo" :key="index">
					<uni-td align="center">{{item.serviceNo||'-'}}</uni-td>
					<uni-td align="center">{{item.serviceName||'-'}}</uni-td>
					<uni-td align="center">{{item.orderCount||'-'}}</uni-td>
					<uni-td align="center">{{item.repurchaseCount||'-'}}</uni-td>
					<uni-td align="center">￥{{item.amount|| 0.00}}</uni-td>
					<uni-td align="center">{{item.repurchaseRate}}%</uni-td>
				</uni-tr>
			</uni-table>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				employeeNo: uni.getStorageInfoSync('employeeNo') || 'xxx',
				searchTime: null,
				orderInfo: null
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			listEmployeeOrderInfo() {
				this.http({
					// outsideUrl: 'https://api.xiaoyujia.com/task/listEmployeeOrderInfo',
					outsideUrl: 'http://**************:9999/task/listEmployeeOrderInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						serviceNo: this.employeeNo,
						searchTime: this.searchTime
					},
					success: res => {
						if (res.code == 0) {
							this.orderInfo = res.data
						} else {
							this.$refs.uNotify.warning('查询不到相关数据！')
						}
					}
				})
			}

		},
		onLoad(options) {
			this.employeeNo = options.employeeNo || this.employeeNo
			this.searchTime = options.searchTime
			this.listEmployeeOrderInfo()
		},
	}
</script>
<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}
</style>