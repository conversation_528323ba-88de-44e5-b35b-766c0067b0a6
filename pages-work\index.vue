<template>
	<view class="">

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 图片裁切 -->
		<l-clipper v-if="showClipper" @success="clipperSuccess" @cancel="showClipper = false" :width="500" :height="500"
			:min-width="300" :min-height="300" :scale-ratio="6" />

		<view class="u-page w10 h10">
			<!-- 头部信息 -->
			<view class="u-head">
				<view class="u-head-part1 w10">
					<view style="margin: auto;">
						<image style="width: 120rpx; height: 120rpx;margin-left: 40rpx;border-radius: 50%;"
							:src="headImg  ? headImg : src1" @click="uploadHeadPortrait(0)"
							@longpress="uploadHeadPortrait(1)"></image>
					</view>
					<view class="u-head-part1-title flex-col" style="width: 80%;">
						<view class="w10" style="display: flex;">
							<!-- title + vip -->
							<view style="display: flex;">
								<text class="f18" @click="openStore()">{{ userInfo.name }}-{{userInfo.job}}</text>
								<sup>
									<image :src="src3" :hidden="true" mode=""
										style="width: 40rpx;height: 40rpx;margin: auto 10rpx;">
									</image>
									<image :src="src4" :hidden="false" mode=""
										style="width: 40rpx;height: 40rpx;margin: auto 10rpx;">
									</image>
									<image :src="src5" :hidden="true" mode=""
										style="width: 80rpx;height: 80rpx;margin: auto 10rpx;">
									</image>
								</sup>
							</view>
						</view>
						<text v-if="textShowB" class="f14">{{validity}}</text>
						<text v-if="textShow" @click="gotoOtherUrl('/pages-mine/franchise/enter/topUpMoney')"
							class="f14">去续费</text>
						<!-- 第二行--技能认证 -->
						<!-- <view class="f14 text-c" style="position: absolute;top: 100rpx;" :hidden="true">
              <view style="width: 140rpx;height: 50rpx;line-height: 50rpx;background-color:#f0b438;color: #fff;border-radius: 6rpx;">{{tags}}</view>
            </view> -->
					</view>
					<!-- 设置 -->
					<view style="width: 25%;">
						<!-- <image style="width: 100%; height: 60rpx;" @click="newWork()"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1718871760936切换新版本.png">
						</image> -->
						<image style="width: 60rpx; height: 60rpx;margin-left: 30rpx;" @click="setting()" :src="src2">
						</image>
					</view>

				</view>
			</view>
			<view class="u-head-part2 bacf" v-if="storeType!=3">
				
				
				
				
				<!-- 奖金区 -->
				<view class="u-head-part2-con">
					<view class="u-head-part2-text f18 cf">
						<text style="margin-right: 60rpx;">账户余额</text>
						<text>{{ amount||0.00 }}</text>
					</view>
					<button plain="true" @click="withdraw()" class="u-head-part2-btn">提现&nbsp;></button>
				</view>
				<view>
							<scroll-view @scroll="changeScroll" class="scroll-view_H" :enable-flex="true" :scroll-x="true">
									<view class="u-head-part2-con2-col text-c scroll-view-item_H uni-bg-green">
										<view class="u-head-part2-con2-col-num f15 fb">{{ bonus.zg }}</view>
										<button class="u-head-part2-con2-col-btn"
											@click="gotoOtherUrl('/pages-work/bonus/recruit')">招工奖</button>
									</view>
									<view class="u-head-part2-con2-col text-c scroll-view-item_H uni-bg-green">
										<view class="u-head-part2-con2-col-num f15 fb">{{ bonus.xs }}</view>
										<button class="u-head-part2-con2-col-btn"
											@click="gotoOtherUrl('/pages-work/bonus/clew')">线索奖</button>
									</view>
									<view class="u-head-part2-con2-col text-c scroll-view-item_H uni-bg-green">
										<view class="u-head-part2-con2-col-num f15 fb">{{ bonus.kf }}</view>
										<button class="u-head-part2-con2-col-btn"
											@click="gotoOtherUrl('/pages-work/bonus/exploit')">开发奖</button>
									</view>
									<view class="u-head-part2-con2-col text-c scroll-view-item_H uni-bg-green">
										<view class="u-head-part2-con2-col-num f15 fb">{{ bonus.sh }}</view>
										<button class="u-head-part2-con2-col-btn"
											@click="gotoOtherUrl('/pages-work/bonus/ueto')">合单奖</button>
									</view>
									<view class="u-head-part2-con2-col text-c scroll-view-item_H uni-bg-green">
										<view class="u-head-part2-con2-col-num f15 fb">{{ bonus.sh }}</view>
										<button class="u-head-part2-con2-col-btn"
											@click="gotoOtherUrl('/pages-work/bonus/ueto')">其他</button>
									</view>
								</scroll-view>
								<view class="scroll_bar" :style="{width:scrollwidth+'rpx'}">
									<view class="left_line" :style="{left:scrollLeft+'rpx'}"></view>
								</view>
							</view>
							<view class="uni-common-pb"></view>
			</view>

			<!-- 待办事项 -->
			<view class="u-page-part2 bacf">
				<view class="u-page-part2-title f18 fb">待办事项</view>
				<view style="padding: 0rpx 20rpx 20rpx 20rpx;">
					<view class="flac-col-c w95" style="padding: 0 2.5%;">
						<view class="flac-row w10 f16" style="margin-bottom: 10rpx;" v-for="(item,index) in toDoList"
							:key="index">
							<view class="w2 flac-col-c" style="line-height: 25rpx;" @click="openInform(index,0)">
								<view class="tips-top" :style="'height: 40rpx;background-color:'+colorList[index]">
									{{item.title}}
								</view>
								<view class="tips-bottom"
									:style="'height: 40rpx;border: 1rpx solid'+colorList[index]+';color:'+colorList[index]">
									{{item.total}}
								</view>
							</view>

							<view class="w7 lh20" style="margin: 0 3%;" v-if="index!=1" @click="openInform(index,0)">
								{{item.content}}
							</view>
							<view class="w7 lh20" style="margin: 0 3%;" v-else>
								近7天到期合同<text class="tips-text" @click="openInform(1,0)">{{item.num}}</text>个、
								将服务合同<text class="tips-text" @click="openInform(1,1)">{{item.num1}}</text>个、
								保险时间异常的合同<text class="tips-text" @click="openInform(1,2)">{{item.num2}}</text>个、
								{{item.content}}
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 门店运营 -->
			<view class="u-page-part2 bacf" v-if="menuCountA>0">
				<view class="u-page-part2-title f18 fb">门店运营</view>
				<view class="u-page-part2-con flex-col">
					<view class="" style="display: flex;flex-wrap: wrap;">
						<view class="u-page-part2-con2 flex-col" v-for="(item,index) in workMenuList" :key="index"
							v-if="item.titleType==1">
							<image class="u-page-part2-icon" :src="item.menuIcon" mode="" @click="goto(item)">
							</image>
							<text class="u-page-part2-text f14 text-c">{{item.menuName}}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 推广中心 -->
			<view class="u-page-part2 bacf" v-if="menuCountB>0">
				<view class="u-page-part2-title f18 fb">推广中心</view>
				<view class="u-page-part2-con flex-col">

					<view class="" style="display: flex;flex-wrap: wrap;">
						<view class="u-page-part2-con2 flex-col" v-for="(item,index) in workMenuList" :key="index"
							v-if="item.titleType==2">
							<image class="u-page-part2-icon" :src="item.menuIcon" mode="" @click="goto(item)">
							</image>
							<text class="u-page-part2-text f14 text-c">{{item.menuName}}</text>
						</view>
					</view>

				</view>
			</view>
		</view>
	<!-- 	
			<view class="flex-col" style="margin-top: -450rpx;z-index: 999;margin-left: 75%">
				<image class="u-page-part2-icon" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1712654383010在线客服.png" mode="" @click="goto(item.url)">
				</image>
				<text class="u-page-part2-text f14 text-c">在线客服</text>
			</view> -->

		<movable-area class="movableArea" @click="trigger">
			<movable-view class="movableView" :position="position" :x="x" :y="y" :direction="direction"
				:damping="damping">
				<image src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1712654383010在线客服.png"
					mode="widthFix" class="iconImage"></image>
				<view style="font-size: 24rpx;margin-top: -8rpx;margin-left: 11rpx;">客服</view>
			</movable-view>
		</movable-area>
		<movable-area class="movableArea" @click="robotHelp" v-if="storeType==2">
			<movable-view class="movableView" :position="position" :x="x2" :y="y2" :direction="direction"
				:damping="damping">
				<image :src="robotIconUrl" mode="widthFix" class="iconImage"></image>
				<view style="font-size: 24rpx;margin-top: -7rpx;margin-left: 11rpx;">工单</view>
			</movable-view>
		</movable-area>

	</view>
	</view>
</template>
<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				robotIconUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************机器人.png',
				x: 320,
				y: 300,
				x1: 0,
				scrollLeft: 0,
				scrollwidth: 50,
				x2: 0,
				y1: 0,
				y2: 0,
				move: {
					x: 0,
					y: 0
				},
				x2: 320,
				y2: 360,
				// 可设置
				// 是否开启图片裁剪
				isOpenClipper: true,

				showClipper: false,
				userInfo: {
					name: "",
					job: "",
					Dname: ''
					// account:"2023/10/13"
				},
				roleId: 0,
				no: '',
				merchantId: '',
				textShow: false,
				textShowB: true,
				amount: 0.00,
				authStatus: null,
				storeType: uni.getStorageSync('storeType'),
				employeeId: uni.getStorageSync("employeeId") || null,
				validity: '',
				rewardTypeList: ['招工奖','线索奖','开发奖','合单奖','其他'],
				bonus: {
					zg: 0.00,
					xs: 0.00,
					kf: 0.00,
					fz: 8000,
					sh: 0.00,
					sd: 417.04,
					qt: 945.27
				},
				headImg: '',
				// tags:"保姆技能",
				flag: 1,
				workMenuList: [],
				toDoList: [],
				colorList: ['#fb423f', '#fc811c', '#0abe87'],
				menuCountA: 0,
				menuCountB: 0,
				src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				src2: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/certificate.png',
				src3: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1668683917694hy.png',
				src4: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1668683976984xt.png',
				src5: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669628254423fei.png',
				title: 'uni-fab',
			}
		},
		props: {
			damping: {
				type: Number,
				default: 10
			},
			direction: {
				type: String,
				default: "all"
			},
			position: {
				type: Number,
				default: 4
			}
		},
		onBackPress() {
			if (this.$refs.fab.isShow) {
				this.$refs.fab.close()
				return true
			}
			return false
		},
		onShow() {
			// this.checkEmployeeUnread()
			this.getNewChatMsg()
		},
		onLoad() {
			this.checkLogin()
		},
		mounted() {
			uni.getSystemInfo({
				success: (res) => {
					this.x1 = 0;
					this.x2 = parseInt(res.windowWidth) - 50;
					this.y1 = 0;
					this.y2 = parseInt(res.windowHeight) - 20;
					setTimeout(() => {
						if (this.position == 1 || this.position == 2) this.y = parseInt(this.y2 * 0.2);
						if (this.position == 3 || this.position == 4) this.y = parseInt(this.y2 * 0.8);
						if (this.position == 1 || this.position == 3) this.x = parseInt(this.x1);
						if (this.position == 2 || this.position == 4) this.x = parseInt(this.x2);
						this.move.x = this.x;
						this.move.y = this.y;
					}, 1000)
				}
			})
		},
		methods: {
			changeScroll(e) {
				let columnLength = 5 * 3
				this.scrollLeft = (e.detail.scrollLeft / columnLength) * 2 
			},
			getWorkMenu() {
				this.http({
					url: "getWorkMenu",
					method: 'GET',
					data: {
						employeeId: uni.getStorageSync('employeeId'),
						merchantId: this.merchantId
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.workMenuList = res.data.menuList
							this.menuCountA = res.data.menuCountA
							this.menuCountB = res.data.menuCountB
						} else {
							return uni.showToast({
								title: '获取菜单失败，请退出后重试！',
								icon: 'none'
							})
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			newWork() {
				uni.reLaunch({
					url: '/pages-work/newIndex'
				})
			},
			robotHelp() {
				// return uni.showToast({
				// 	title: '内测中，请敬请期待!',
				// 	icon: 'none'
				// })
				uni.navigateTo({
					url: "/pages-other/smartPartner/index"
				})
			},
			trigger() {
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfcda2587f0eecab0c8' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  MP-TOUTIAO || H5
				uni.showToast({
					icon: 'none',
					title: '请在小程序内操作!'
				})
				// #endif
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '发掘好保姆，开单就分佣，家姐带你干副业',
					path: '/pages-work/index',
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '发掘好保姆，开单就分佣，家姐带你干副业',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast('还未登录哦，请重新登录')
					uni.setStorageSync('redirectUrl', '/pages-work/index')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
				} else {
					this.getEmployee()
					this.getMerchantByMemberId()
					this.getUnionInformStatData()
					setTimeout(() => {
						this.getWorkMenu()
					}, 100)
				}
			},
			// 打开店长主页
			openStore() {
				let id = uni.getStorageSync('storeId')
				uni.navigateTo({
					url: "/pages-other/store/index?id=" + id
				})
			},
			//提现
			withdraw() {
				uni.navigateTo({
					url: '/pages-work/withdraw/workWithdraw'
				})
			},
			getMerchantByMemberId() {
				// 获取商户信息
				this.http({
					url: 'getMerchantByMemberId',
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					hideLoading: true,
					success: res => {
						if (res.code == 0 && res.data.id) {
							this.mobile = res.data.contactPhone
							this.identityCard = res.data.idCard
							this.authStatus = res.data.authStatus
							let timeA = Date.parse(new Date())
							let timeB = Date.parse(res.data.validity)
							if (res.data.validity == '' || res.data.validity == null) {
								this.validity = '暂无'
							} else if (timeA > timeB) {
								this.textShow = true
								this.textShowB = false
							} else {
								let s = res.data.validity.substring(0, 10);
								this.validity = "有效期至:" + s
							}
							uni.setStorageSync('merchantCode', res.data.merchantCode);
							this.merchantId = res.data.id
							uni.setStorageSync('merchantId', res.data.id);
						}
					},
					fail: err => {}
				});

			},
			// 获取员工信息
			getEmployee() {
				if (uni.getStorageSync("employeeId")) {
					this.http({
						url: 'getEmployeeById',
						method: 'GET',
						path: uni.getStorageSync("employeeId"),
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								this.employee = res.data
								this.withdrawName = res.data.realName
								this.no = res.data.no
								this.headImg = res.data.headPortrait
								uni.setStorageSync('employeeHeadImg', res.data.headPortrait)
								uni.setStorageSync('storeId', res.data.storeId)
								uni.setStorageSync('entryTime', res.data.entryTime)
								uni.setStorageSync('employeeNo', res.data.no)
								uni.setStorageSync('employeeId', res.data.id)
								uni.setStorageSync('score', res.data.score)
								uni.setStorageSync('employeeName', res.data.realName)
								uni.setStorageSync("employeeType", res.data.employeeType)
								uni.setStorageSync("employeeState", res.data.state)
								uni.setStorageSync("token", res.data.token)
								uni.setStorageSync("weChatCodeImgUrl", res.data.weChatCodeImgUrl)
								let roleId = res.data.roleId
								this.roleId = roleId
								uni.setStorageSync('roleId', roleId)

							}
						},
						fail: err => {}
					});
				}
			},
			getNewChatMsg() {
				this.http({
					url: "getNewChatMsg",
					method: 'GET',
					data: {
						employeeNo: uni.getStorageSync('employeeNo')
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (res.data == 2) {
								this.robotIconUrl =
									'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1716448221412*************机器人.png'
							}
						}
					},
				})
			},
			// // 检查员工未读消息
			// checkEmployeeUnread() {
			// 	this.http({
			// 		url: "checkEmployeeUnread",
			// 		method: 'GET',
			// 		path: uni.getStorageSync("employeeId") || 0,
			// 		hideLoading: true,
			// 		success: res => {
			// 			if (res.code == 0) {
			// 				let unreadNum = res.data
			// 				if (unreadNum.num) {
			// 					this.workMenuList[1].menuIcon = this.workMenuList[1].redDotMenuIcon
			// 					this.menuList1[1].icon = this.menuList1[1].icon1
			// 				}
			// 				if (unreadNum.num1) {
			// 					this.workMenuList[11].menuIcon = this.workMenuList[11].redDotMenuIcon
			// 					this.menuList1[11].icon = this.menuList1[11].icon1
			// 				}
			// 				if (unreadNum.num2) {
			// 					this.menuList1[2].icon = this.menuList1[2].icon1
			// 				}
			// 				if (unreadNum.num3) {
			// 					this.menuList1[4].icon = this.menuList1[4].icon1
			// 				}
			// 			}
			// 		},
			// 	})
			// },
			// 上传头像
			uploadHeadPortrait(value) {
				if (this.isOpenClipper && value == 0) {
					this.showClipper = true
					return
				}

				const url = 'https://api2.xiaoyujia.com/system/imageUpload'
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos'
							},
							dataType: 'json',
							success: res => {
								this.headImg = tempFilePaths[0]
								let result = JSON.parse(res.data)
								this.employee.headPortrait = result.data
								this.updateEmployee()
							}
						});
					}
				});
			},
			// 图片裁剪成功
			async clipperSuccess(e) {
				this.showClipper = false
				let path = []
				// #ifdef H5
				await base64ToPath(e.url).then(res => {
					path.push(res)
				})
				// #endif

				// #ifdef MP-WEIXIN || APP-PLUS
				path.push(e.url)
				// #endif

				const url = 'https://api2.xiaoyujia.com/system/imageUpload';
				const tempFilePaths = path
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						// 本地临时地址
						this.headImg = tempFilePaths[0]
						// 上传到服务器后的地址
						let result = JSON.parse(res.data)
						this.employee.headPortrait = result.data
						this.updateEmployee()
					}
				});
			},
			// 更新员工信息
			updateEmployee() {
				let data = {}
				this.$set(data, 'id', uni.getStorageSync("employeeId") || 0)
				this.$set(data, 'headPortrait', this.employee.headPortrait)
				this.http({
					url: 'updateEmployee',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: data,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('头像更新成功！')
							this.setStorageSync("headPortrait", this.headImg)
						} else {
							this.$refs.uNotify.error('头像更新失败！' + res.msg)
						}
					},
				})
			},
			setting() {
				uni.navigateTo({
					url: "/pages-work/authentication"
				})
				console.log("45");
			},
			openInform(index, value) {
				let item = this.toDoList[index]
				if (index == 0) {
					uni.navigateTo({
						url: '/pages-other/income/manage/toDoList'
					})
				}
				if (index == 1) {
					let param = '?searchType=' + value
					uni.navigateTo({
						url: '/pages-work/operations/contract' + param
					})
				}
				if (index == 2) {
					uni.navigateTo({
						url: '/pages-mine/inform/inform?state=0'
					})
				}
			},
			// 获取待办事项
			getUnionInformStatData() {
				this.http({
					url: 'getUnionInformStatData',
					method: 'GET',
					path: this.employeeId || 0,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.toDoList = res.data
						}
					},
				})
			},
			getFranchiseWorkData() {
				let param = {
					startTime: "",
					endTime: "",
					merchantCode: uni.getStorageSync("merchantCode"),
					id: uni.getStorageSync("memberId"),
					employeeId: uni.getStorageSync("employeeId")
				}
				this.http({
					url: "getFranchiseWorkData",
					method: 'POST',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							// if (!res.data.no) {
							// 	this.ywFlag = false
							// }
							uni.setStorageSync('storeName', res.data.storeName)
							this.userInfo.name = res.data.realName
							this.userInfo.job = res.data.roleName
							this.amount = res.data.accountMoney
							this.bonus.zg = res.data.recruit.toFixed(2)
							this.bonus.xs = res.data.needs.toFixed(2)
							this.bonus.kf = res.data.fengyong.toFixed(2)
							this.bonus.sh = res.data.upFamily.toFixed(2)
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			goto(item) {
				if(item.ifShow==0){
					return uni.showToast({
						title: '暂未开放，敬请期待！',
						icon: 'none'
					})
				}
				let url = item.jumpUrl
				let path = ""
				if (uni.getStorageSync('employeeNo')) {
					path = 'pages/dateIndex?employeeNo=' + uni.getStorageSync('employeeNo') + '&source=saoma'
				} else {
					path = 'pages/dateIndex?source=saoma'
				}
				if (url === '/pages-work/promote/douyin/DCindex') {
					return wx.navigateToMiniProgram({
						appId: 'wx0bacb3edec2f4cbd', // pord
						path: path,
						envVersion: 'release',
						success(res) {
							// 打开成功
						}
					})
				}

				// 根据角色id切换不同的鉴定列表
				if (url.includes("employee-appraisal")) {
					if (this.roleId == 108) {
						url = '/pages-other/hr/index'
					}
				}

				// 管理员或行政跳转到全局服务分列表
				if (url.includes("serviceScore/index")) {
					if (this.roleId == 1 || this.roleId == 9 || this.roleId == 78) {
						return uni.navigateTo({
							url: '/pages-other/serviceScore/scoreRank?pageType=1'
						})
					}
				}

				// 管理员或行政跳转到门店星级列表
				if (url.includes("store/store-develop")) {
					if (this.roleId == 1 || this.roleId == 9 || this.roleId == 78) {
						return uni.navigateTo({
							url: '/pages-other/store/levelRank?pageType=1'
						})
					}
				}

				uni.navigateTo({
					url: url + "?no=" + this.no
				})
			},
			gotoOtherUrl(url){
				if (url === '/pages-mine/franchise/enter/topUpMoney') {
					return uni.navigateTo({
						url: url + "?renewFlag=1"
					})
				}
				uni.navigateTo({
					url: url + "?no=" + this.no
				})
			},
		},
		// 页面加载后
		mounted() {
			this.getFranchiseWorkData()
		}
	}
</script>
<style lang="scss" scoped>
	.u-page {
		background-color: #f4f2f3;
		padding-bottom: 10rpx;
		// padding-bottom: 220rpx;
	}

	.u-head {
		display: flex;
		justify-content: space-between;
		margin: auto;
	}

	.u-head-part1 {
		display: flex;
		justify-content: space-between;
		margin: 20rpx;
	}

	.u-head-part1-title {
		margin-left: 20rpx;
		line-height: 60rpx;
	}

	.u-head-part2 {
		width: 90%;
		height: 100%;
		margin: auto;
		border-radius: 20rpx;
	}
	
	.scroll-Y {
			height: 300rpx;
		}
		.scroll-view_H {
			white-space: nowrap;
			width: 100%;
		}
		.scroll-view-item {
			height: 300rpx;
			line-height: 300rpx;
			text-align: center;
			font-size: 36rpx;
		}
		.scroll-view-item_H {
			display: inline-block;
			width: 33%;
			height: 200rpx;
			line-height: 300rpx;
			text-align: center;
			font-size: 36rpx;
		}

	.u-head-part2-con {
		height: auto;
		border-radius: 20rpx;
		// background-color: #f0b438;
		background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
	}

	.u-head-part2-text {
		padding: 20rpx 30rpx;
	}

	.u-head-part2-btn {
		border: none !important;
		font-size: 32rpx;
		color: #fff;
		text-align: right;
	}

	.u-head-part2-con2 {
		padding: 40rpx 0;
	}

	.u-head-part2-con2-col {
		line-height: 60rpx;
	}

	.u-head-part2-con2-col-btn {
		border: 4rpx dashed #1e1848;
		background: transparent;
		color: #1e1848;
		width: 200rpx;
		height: 70rpx;
		font-size: 32rpx;
		line-height: 70rpx;
		margin: 20rpx auto;
	}

	.u-head-part2-con2-col-num {
		color: #1e1848;
	}

	.u-page-part2 {
		width: 90%;
		height: 100%;
		margin: 40rpx auto;
		border-radius: 20rpx;
	}

	.u-page-part2-title {
		width: 90%;
		padding: 30rpx 0;
		margin: auto;
		color: #1e1848;
	}

	.u-page-part2-con {
		width: 100%;
		margin: 0 auto;
	}
	
	.card_swiper {
		white-space: nowrap;
		width: 90%;
		margin: auto;
	}

	.u-page-part2-con2 {
		width: 33%;
		margin: 20rpx 0;
		// background-color: red;
	}

	.u-page-part2-icon {
		margin: 0 auto;
		width: 60rpx;
		height: 60rpx;
		padding: 10rpx;
	}

	.u-page-part2-text {
		line-height: 60rpx;
	}
	
	.card_swiper_out {
		width: 100%;
		;
		position: relative;
		// padding: 20rpx 30rpx;
		background-color: #fff;
		border-top-right-radius: 20rpx;
		border-top-left-radius: 20rpx;
		overflow-y: auto;
	}


	.movableArea {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
		z-index: 999;

		.movableView {
			pointer-events: auto; //可以点击
			width: 70rpx;
			height: 70rpx;
			padding: 10rpx;
			border-radius: 100%;
			border: 2px solid #1e1848;

			.iconImage {
				display: block;
				width: 50rpx;
				height: 50rpx;
				margin-left: 10rpx;
				// animation: iconImage 5s linear infinite;
			}

			@keyframes iconImage {
				0% {
					-webkit-transform: rotate(0deg);
				}

				25% {
					-webkit-transform: rotate(90deg);
				}

				50% {
					-webkit-transform: rotate(180deg);
				}

				75% {
					-webkit-transform: rotate(270deg);
				}

				100% {
					-webkit-transform: rotate(360deg);
				}
			}

		}
	}

	.tips-top,
	.tips-bottom {
		font-size: 28rpx;
		width: 100rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
	}

	.tips-top {
		width: 102rpx;
		color: #fff;
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
	}

	.tips-bottom {
		border-bottom-left-radius: 10rpx;
		border-bottom-right-radius: 10rpx;
	}

.scroll_bar {
		margin-top: 20rpx;
		background: #2d2fa13a;
		height: 8rpx;
		border-radius: 5rpx;
		position: relative;
		left: 50%;
		transform: translate(-50%, 0);
		z-index: 9;
		bottom: 0;
		transition: all 0.5s ease-in;
	}
	
	.left_line {
		content: ' ';
		width: 25rpx;
		height: 8rpx;
		background: #2d2fa1;
		border-radius: 5rpx;
		position: absolute;
		top: 0;
	}
	
	
	.tips-text {
		margin: 0 10rpx;
		color: #fc811c;
	}
</style>