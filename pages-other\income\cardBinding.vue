<template>
	<view>
		<!-- <view class="w95 mg-at f14 c9 lh40">余额准出将汇入以下银行卡，如信息有误解绑后重新绑定</view> -->
		<view class="bacf f16" style="padding: 20rpx 40rpx;">
			<view style="display: flex;">
				<view class="lh40 w5">收款方</view>
			<u-tag v-if="!id" text="查看历史提交记录" class="w5" @click="getHistoryLog" color="#FFFFFF" bgColor="#1e1848"
				plain shape="circle" customStyle="margin-left: 40%;margin-top: 10rpx;" size="large" borderColor="#1e1848"></u-tag>
			</view>
			<view class="c9 lh40 flac-row-b">{{updateList.storeName}}</view>
			<!-- <view class="lh40">门店信息</view> -->
			<view class="lh40">
				<text class="w3">营业执照<sup class="red">*</sup></text>
				<view @click="uploadFile(1)">
				<u-upload maxCount="1" :fileList="updateList.fileList1" :previewFullImage="true" @afterRead="afterRead"
					@delete="deletePic" name="1" multiple></u-upload>
				</view>
			</view>
			<view class="lh40">
				<text class="w3">企业名称</text>
				<u--input placeholder="请输入企业名称" v-model="updateList.companyName" border="none" clearable />
			</view>
			<view class="lh40">
				<text class="w3">企业类型</sup></text>
				<u--input placeholder="请输入企业类型" v-model="updateList.companyType" border="none" clearable />
			</view>
			<view class="lh40">
				<text class="w3">法定代表人</sup></text>
				<u--input placeholder="请输入企业类型" v-model="updateList.legalRepresentative" border="none" clearable />
			</view>
			<view class="lh40">
				<text class="w3">统一社会信用代码</text>
				<u--input placeholder="请输入统一社会信用代码" v-model="updateList.businessCode" border="none" clearable />
			</view>
		</view>
		<view class="bacf f16" style="padding: 20rpx 40rpx;">
			<view class="lh40">
				<text class="w3">绑卡类型</text>
				<radio-group @change="radioChange" style="display: flex;">
					<label v-for="(item, index) in items" style="margin-right: 35%;" :key="item.value">
						<view>
							<radio :value="item.value" :checked="index === current" />
						</view>
						<view>{{item.name}}</view>
					</label>
				</radio-group>
			</view>
			<view class="c9" style="color: red;">注：个体户可选个人非个体户必须公账户！为了确保快速提现出账，感谢您的配合！</view>
			<view class="lh40" v-if="current==0">
				<text class="w3">基本存款账户信息<sup class="red">*</sup></text>
				<view @click="uploadFile(2)">
				<u-upload maxCount="1" :fileList="updateList.fileList2" :previewFullImage="true" @afterRead="afterRead"
					@delete="deletePicB" name="1" multiple></u-upload>
				</view>
				<view style="margin-left: 40%;margin-top: -25%;margin-bottom: 15%;color: red;" @click="lookEx">查看示例
				</view>
			</view>
			<view class="c9" style="color: red;" v-if="current!=0">请填写法人的相关信息，否则审核不通过</view>
			<view class="lh40 flac-row">
				<text class="w3">开户名称<sup class="red">*</sup></text>
				<u--input placeholder="请输入开户名称" v-model="updateList.accountName" border="none" clearable />
			</view>
			<view class="lh40 flac-row">
				<text class="w3">银行卡号<sup class="red">*</sup></text>
				<u--input placeholder="请输入对应银行卡的卡号" type="number" v-model="updateList.bankCode" border="none"
					clearable />
			</view>
			<view class="lh40 border-bottom-2sf3 flac-row" @click="pickerShow=true">
				<text class="w3">账户类型<sup class="red">*</sup></text>
				<u--input disabled placeholder="点击选择账户类型" v-model="updateList.bankType" border="none" />
			</view>
			<!-- <view class="lh40 border-bottom-2sf3 flac-row">
				<text class="w3">所属银行<sup class="red">*</sup></text>
				<u--input placeholder="请输入所属银行" v-model="updateList.bankName" border="none" />
			</view> -->
			<view class="lh40 border-bottom-2sf3 flac-row">
				<text class="w3">开户地<sup class="red">*</sup></text>
				<pickers @address="address">
					<u--input disabled placeholder="请选择" v-model="updateList.accountOpenAddress" border="none"
						suffixIcon="arrow-right" suffixIconStyle="color: #909399" />
				</pickers>
			</view>
			<view class="lh40 flac-row">
				<text class="w3">开户支行<sup class="red">*</sup></text>
				<u--input placeholder="请输入开户支行" v-model="updateList.accountOpenBank" border="none" />
			</view>
			<view class="c9" style="color: red;">注：请填写完整的开户支行名称，开户支行错误会导致提现失败，如不清楚请联系银行客服确认，完整开户支行名称示例如下：</view>
			<view class="c9">1、中国人民银行厦门市分行</view>
			<view class="c9">2、中国建设银行股份有限公司厦门上李支行</view>
			<view class="c9">3、中国工商银行股份有限公司郑州农业西路支行</view>
		</view>
		<view class="bacf f16" style="padding: 20rpx 40rpx;" v-if="id">
			<view class="lh40 flac-row">
				<text class="w3">审核反馈</sup></text>
			</view>
			<view class="lh40 flac-row">
				<text class="w3">状态</sup></text>
				<text>{{updateList.state==1?'待审核':updateList.state==2?'绑卡成功':'未通过'}}</text>
			</view>
			<view class="lh40 border-bottom-2sf3 flac-row" v-if="updateList.remark" >
				<text class="w3">备注</text>
				<text>{{updateList.remark}}</text>
			</view>
			<view class="lh40 border-bottom-2sf3 flac-row">
				<text class="w3">处理时间</text>
				<text>{{updateList.operateTime}}</text>
			</view>
			
		</view>
		<u-button :text="id?'修改并重新提交审核':'提交审核'" shape="circle" color="#1e1848" customStyle="width:90%;margin-top:20rpx;color:#f6cc70"
			@click="subMsg" :disabled="updateList.state==2">
		</u-button>
		<view>
			<u-picker :show="pickerShow" :columns="columns" @cancel="pickerShow = false"
				@confirm="selectBankType"></u-picker>
		</view>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>
		<u-modal :show="showModal" title="基本存款账户信息" @confirm="showModal = false">
			<view>
				<u-image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1705891976499d2063ecc539e4afe897f25237914f70.png"
					mode="aspectFit"></u-image>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import bankData from './js/bankData.js'; //银行数据
	import pickers from "@/pages-other/common/components/ming-picker/ming-picker.vue"
	export default {
		components: {
			pickers
		},
		data() {
			return {
				items: [{
						value: '0',
						name: '公账户',
						checked: 'true'
					},
					{
						value: '1',
						name: '个人'
					},
				],
				current: 0,
				pickerShow: false,
				showModal: false,
				accountType: null,
				fileList1: [],
				columns: [
					['对公账户', '储蓄卡', '信用卡', '准贷记卡', '预付费卡']
				],
				updateList: {
					storeId: uni.getStorageSync("selectStoreId"),
					storeName: uni.getStorageSync("selectStoreName"),
					accountOpenAddress: '',
					state: 1,
					// bankName: '',
					bankType: '',
				},
				carTypeMap: [],
				bankDataMap: [],
				id: null,
				checkType: 0,
				checkTitle: "",
				checkText: "",
			}
		},
		onLoad(option) {
			if(option.id){
				this.id = option.id
				this.getStoreAccountAuditing()
			}else{
				this.getStoreData()
			}
			this.carTypeMap = new Map(Object.entries(bankData.carType))
			this.bankDataMap = new Map(Object.entries(bankData.bankData));
		},
		methods: {
			getStoreAccountAuditing(){
				this.http({
				  url: 'getStoreAccountAuditing',
				  method: 'GET',
				  data: {
				    storeId: uni.getStorageSync("selectStoreId"),
					id: this.id
				  },
				  success: res => {
				    if (res.code == 0) {
					this.updateList = res.data[0]
				    }else{
						uni.showToast({
							icon: 'none',
							title: '获取申请记录详情失败！'
						})
					}
				  }
				})
			},
			getHistoryLog(){
				uni.navigateTo({
					url: "/pages-other/income/storeAccountHistoryLog"
				})
			},
			uploadFile(val){
				this.accountType = val
			},
			radioChange(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			lookEx() {
				this.showModal = true
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.updateList.fileList1 = []
					this.updateList.businessLicenseImg = ''
				}
				if(this.checkType==1){
					this.updateList.fileList2 = []
					this.updateList.depositAccountImg = ''
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 删除图片
			deletePic(event) {
				this.event = event
				this.openCheck(0, "确认删除该营业执照吗？")
			},
			deletePicB(event){
				this.event = event
				this.openCheck(1, "确认删除该基本存款账号信息吗？")
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					if(this.accountType==1){
						this.http({
							url: "OcrBusinessLicense",
							method: 'POST',
							data: {
								imageUrl: obj.data
							},
							header: {
								"content-type": "application/json;charset=UTF-8"
							},
							success: res => {
								if (res.code == 0) {
									this.updateList.businessLicenseImg = obj.data
									this.updateList.businessCode = res.data.regNum
									this.updateList.companyName = res.data.name
									this.updateList.companyType = res.data.type
									this.updateList.legalRepresentative = res.data.person
									this.updateList.fileList1 = [{
										url: obj.data
									}]
									fileListLen++
								} else {
									uni.showToast({
										title: "解析营业执照失败!请手动填写!",
										icon: 'none'
									})
								}
							}
						})
					}else{
						this.updateList.depositAccountImg = obj.data
						this.updateList.fileList2 = [{
							url: obj.data
						}]
						fileListLen++
					}
					
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			getStoreData() {
				this.http({
					url: "getStoreData", 
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					method: "GET",
					success: res => {
						if (res.code == 0) {
							this.updateList = res.data
						}
					}
				})
			},
			selectBankType(e) {
				this.updateList.bankType = e.value[0]
				this.pickerShow = false
			},
			address(e) {
				this.updateList.accountOpenAddress = ''
				for (var i = 0; i < e.value.length; i++) {
					this.updateList.accountOpenAddress += e.value[i]
				}
			},
			//解析银行卡信息
			// changeBank() {
			//   if (this.updateList.bankCode.length >= 16) {
			//     this.http({
			//       outsideUrl: 'https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8',
			//       data: {
			//         cardNo: this.updateList.bankCode,
			//         cardBinCheck: true
			//       },
			//       method: "GET",
			//       success: res => {
			//         if (res.stat === 'ok' && res.validated) {
			//           this.updateList.bankType = this.carTypeMap.get(res.cardType)
			//           this.updateList.bankName = this.bankDataMap.get(res.bank)
			//         } else {
			//           uni.showToast({
			//             title: '卡号解析失败！请检查卡号是否正确！',
			//             icon: 'none'
			//           })
			//         }
			//       }
			//     })
			//   }
			// },
			showToast() {
				uni.showToast({
					icon: 'none',
					title: '信息填写不完全，请补充后提交'
				})
			},
			subMsg() {
				if (!this.updateList.depositAccountImg&&this.current==0) {
					this.showToast()
				} else if (!this.updateList.businessLicenseImg) {
					this.showToast()
				} else if (!this.updateList.accountName) {
					this.showToast()
				} 
				// else if (!this.updateList.bankName) {
				// 	this.showToast()
				// }
				 else if (!this.updateList.accountOpenAddress) {
					this.showToast()
				} else if (!this.updateList.bankCode && this.updateList.bankCode.length == 18) {
					this.showToast()
				} else if (!this.updateList.accountOpenBank) {
					this.showToast()
				} else {
					let url = ""
					if(this.id){
						url = "updateStoreAccountAuditing"
						this.updateList.state = 1
					}else{
						url = "saveStoreAccountAuditing"
					}
					this.http({
						url: url,
						method: 'POST',
						data: this.updateList,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							setTimeout(() => {
							if (res.code == 0) {
								if(this.id){
								uni.setStorageSync('catchCardBingFlag',1)
								uni.navigateBack();
								}else{
									uni.navigateTo({
									  url: './storeAccountHistoryLog'
									})
								}
							}
							}, 1500)
						}
					})
				}
			}
		}
	}
</script>

<style>
	.c4c {
		color: #4c4c4c;
	}

	.c0c4d6 {
		color: #c0c4d6;
	}

	.c61 {
		color: #616161
	}

	.border-bottom-2sf3 {
		border-bottom: 2rpx solid #f3f3f3;
	}


	page {
		background-color: #f5f5f5;
		padding-bottom: 20rpx;
	}
</style>