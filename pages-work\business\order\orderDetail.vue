<template>
	<view class="" style="padding-bottom:100rpx">
		<u-sticky customStyle="padding: 40rpx 0" bgColor="#fff">
			<u-steps :current="current" dot activeColor="#fdd472">
				<u-steps-item title="待接单" />
				<u-steps-item title="已接单" />
				<u-steps-item title="开始服务" />
				<u-steps-item title="服务结束" />
				<u-steps-item title="已完成" />
				<u-steps-item title="已评价" />
			</u-steps>
		</u-sticky>
		<!-- 订单信息 -->
		<view class="w10 flac-row" style="justify-content: space-between;background-color: #f6f7fb;">
			<uni-section class="" title="订单信息" type="line" titleFontSize="36rpx">
			</uni-section>
			<u-button text="代客付款" customStyle="width:30%;height:60rpx;border-radius:0;" color="#1e1848" plain
				v-if="(orderData.orderState==50||orderData.orderState==60||orderData.orderState==70)&&orderData.realTotalAmount!=orderData.amount"
				@click="showModal8 = true"></u-button>
			<u-button text="扫码支付" customStyle="width:30%;height:60rpx;border-radius:0;" color="#1e1848" plain
				v-if="(orderData.orderState==50||orderData.orderState==60||orderData.orderState==70)&&orderData.realTotalAmount!=orderData.amount"
				@click="showModal8 = true"></u-button>
			<u-button text="垫付申报" customStyle="width:20%;height:60rpx;border-radius:0;" color="#1e1848" plain
				v-if="(orderData.orderState == 80||orderData.orderState == 90)&&orderData.amount>=orderData.realTotalAmount&&orderData.isLeader==1&&advanceStr!='不在提现时间范围内，将自动并入次月薪资发放'"
				@click="advance()"></u-button>
			<u-button text="去签单" customStyle="width:20%;height:60rpx;border-radius:0;" color="#1e1848" plain
				v-if="(orderData.orderState == 70||orderData.orderState == 80)&&!orderData.autographPicture"
				@click="goPj()"></u-button>
		</view>
		<!-- 订单信息详情 -->
		<view class="w9 mg-at">
			<view class="flac-row f16 lh45" v-if="orderData.buChannel==47||orderData.buChannel==48||orderData.buChannel==56">
				<view class="c6" style="letter-spacing: 4rpx;">抖音订单</view>
				<image v-if="orderData.buChannel==47||orderData.buChannel==48||orderData.buChannel==56" style="width: 60rpx;margin-right: 40%;" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1716197639159抖音.png" mode="widthFix"></image>
			</view>
			<view class="flac-row f16 lh45" v-if="orderData.buChannel==57">
				<view class="c6" style="letter-spacing: 4rpx;">金刚订单</view>
				<image v-if="orderData.buChannel==57" style="width: 60rpx;margin-right: 40%;" src="https://jgobs-web.obs.cn-south-1.myhuaweicloud.com/publicImage/LOGO金刚(圆).png" mode="widthFix"></image>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">会员等级：</view>
				<image style="width: 60rpx;" :src="orderData.level==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_1.png':
													orderData.level==2?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_2.png':
													orderData.level==3?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_3.png':
													orderData.level==4?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_4.png':
													orderData.level==5?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_5.png':''"
					mode="widthFix"></image>
				<view class="" style="color: red;font-size: 40rpx;">{{orderData.level==1?'白金会员':
							orderData.level==2?'黄金会员':
							orderData.level==3?'铂金会员':
							orderData.level==4?'钻石会员':
							orderData.level>=5?'黑钻会员':'未知'}}</view>
			</view>
			<!-- <view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">用户标签：</view>
				<view class="" style="color: red;font-size: 40rpx;">{{orderData.level==1?'白金会员':
							orderData.level==2?'黄金会员':
							orderData.level==3?'铂金会员':
							orderData.level==4?'钻石石会员':
							orderData.level>=5?'黑钻会员':'未知'}}</view>
			</view> -->
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">订单编号：</view>
				<view class="">{{orderData.billNo || '无'}}</view>
				<u-icon name="file-text" @click="copyText(orderData.billNo)"></u-icon>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">订单状态：</view>
				<view class="">
					{{orderData.orderState == 20 ? "派单待确认" :
		   orderData.orderState == 40 ? "已派单" : 
		   orderData.orderState == 50 ? "执行中" : 
		   orderData.orderState == 60 ? "服务中" :
			orderData.orderState == 70 ? "服务结束" :
			orderData.orderState == 80 ? "待评价" :
			orderData.orderState == 90 ? "已评价" :
			 orderData.orderState == 99 ? "已取消": ''}}
				</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">订单金额：</view>
				<view class="red">{{'￥' + (orderData.realTotalAmount || 0 )}}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">已付金额：</view>
				<view class="red">{{'￥' + (orderData.amount || 0) }}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">优惠券金额：</view>
				<view class="red">{{'￥' + (orderData.syhqAmount || 0) }}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">系统优惠金额：</view>
				<view class="red">{{'￥' + (orderData.preferentialAmount || 0) }}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">实付金额：</view>
				<view class="red">{{'￥' + (orderData.realPayAmount || 0) }}</view>
			</view>
			<view class="w6 flac-row f16 lh45" v-if="orderData.isLeader==1">
				<view class="c6" style="letter-spacing: 4rpx;">订单标签：</view>
				<view>
					<u-tag :text="orderData.lblId" bgColor="#fdd472" @click="labelFlag= true" borderColor="#fdd472" />
				</view>
				<u-picker v-model="orderData.lblId" :columns="range" :show="labelFlag" @cancel="labelFlag=false"
					@confirm="changeSelect" />
			</view>
			<view class="w6 flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">保修期限：</view>
				<view>
					<u-tag :text="orderData.maintenance" bgColor="#fdd472" @click="bxLabelFlag= true" borderColor="#fdd472" />
				</view>
				<u-picker v-model="orderData.maintenance" :columns="bxRange" :show="bxLabelFlag" @cancel="bxLabelFlag=false"
					@confirm="confirmMaintenance" />
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">是否估价单：</view>
				<view class="">{{orderData.isEvaluation == 1 ? "是" : '否'}}</view>
			</view>
			<u-button :disabled="orderData.updateAmountNum==0" :text="getUpdateAmountButtonText()" type="warning" v-if="orderData.orderState<70&&orderData.isLeader==1" @click="updateAmountFlag = true"></u-button>
		</view>
		<!-- 服务信息 -->
		<uni-section class="" title="服务信息" type="line" titleFontSize="36rpx" />
		<view class="w9 mg-at">
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">服务项目：</view>
				<view class="">{{orderData.productName || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">服务时间：</view>
				<view class="">{{orderData.startTime || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45" v-if="orderData.realStartTime">
				<view class="c6" style="letter-spacing: 4rpx;">实际开始时间：</view>
				<view class="">{{orderData.realStartTime || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45" v-if="orderData.orderState>=70">
				<view class="c6" style="letter-spacing: 4rpx;">实际结束时间：</view>
				<view class="">{{orderData.realEndTime || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45" v-if="phoneShowFlag&&orderData.isLeader==1">
				<view class="c6" style="letter-spacing: 4rpx;">联系方式：</view>
				<!-- <view class="">{{orderData.bindTel || '无'}}</view> -->
				<u-button customStyle="width:300rpx" text="虚拟号拨打" type="error" v-if="orderData.bindTel"
					@click="privatePhone"></u-button>
				<!-- <u-icon
					name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672820643559phone.png"
					size="25" @click="callPhone(orderData.bindTel)" v-if="orderData.bindTel"></u-icon> -->
			</view>
			<view v-if="!orderData.callCarFlag">
				<view class="flac-row f16 lh45">
					<view class="c6" style="letter-spacing: 4rpx;">客户地址：</view>
					<view class="c6">{{orderData.street || '无'}}</view>
					<u-icon size="25" name="file-text" @click="copyText(orderData.street)"></u-icon>
					<u-icon size="25" name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1751880828254导航.png" @click="lookMap"></u-icon>
				</view>
			</view>
			<view v-if="orderData.callCarFlag">
				<view class="flac-row f16 lh45">
					<view class="c6" style="letter-spacing: 4rpx;">出发地：</view>
					<view class="c6">{{orderData.street || '无'}}</view>
				</view>
				<view class="flac-row f16 lh45">
					<view class="c6" style="letter-spacing: 4rpx;">出发地小区：</view>
					<view class="">{{orderData.rquartersName || '无'}}</view>
				</view>
				<view class="flac-row f16 lh45">
					<view class="c6" style="letter-spacing: 4rpx;">目的地：</view>
					<view class="c6">{{orderData.endStreet || '无'}}</view>
				</view>
				<view class="flac-row f16 lh45">
					<view class="c6" style="letter-spacing: 4rpx;">目的地小区：</view>
					<view class="">{{orderData.endRQuartersName || '无'}}</view>
				</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">服务明细：</view>
				<view class="">{{orderData.serverDetails || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">订单备注：</view>
				<view class="">{{orderData.remark || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;">服务备注：</view>
				<view class="">{{orderData.serviceRemark || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45">
				<view class="c6" style="letter-spacing: 4rpx;color:red">社区管家备注：</view>
				<view class="">{{orderData.agentRemark || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45" v-if="current != 0 && current != 1">
				<view class="c6" style="letter-spacing: 4rpx;">预计开始时间：</view>
				<view class="">{{orderData.startTime || '无'}}</view>
			</view>
			<view class="flac-row f16 lh45" v-if="current != 0 && current != 1">
				<view class="c6" style="letter-spacing: 4rpx;">预计结束时间：</view>
				<view class="">{{orderData.endTime || '无'}}</view>
			</view>
			<u-button :disabled="orderData.updateTimeNum==0" :text="getUpdateTimeButtonText()" type="error" v-if="orderData.orderState==40&&dispathFlag==0&&orderData.isLeader==1" @click="getUpdateTimeNumber"></u-button>
			<hTimeAlert title="改时时间" subhead="列表只显示可预约时间段" dayStartIntTime="0" rangeDay="7" :no="orderData.billNo"
				:isShow="showhTime" :maskHide="maskHide" :rangeType="rangeType" :closeBtn="closeBtn"
				@closeAlert="handelClose"></hTimeAlert>
		</view>
		<!-- 服务员工 -->
		<uni-section class="" title="服务员工" type="line" titleFontSize="36rpx" />
		<view v-for="(item,i) in serverEmployee" :key="i">
			<view class="w9 mg-at flac-row f16 lh50">
				<u-icon
					name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672819685664server.png"
					size="25"></u-icon>
				<view class="" style="margin: auto 50rpx auto 20rpx;">{{item.employeeNo}}</view>
				<view class="">{{item.serviceName}}</view>
				<u-icon
					name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672820643559phone.png"
					size="25" customStyle="margin: auto" @click="callPhone(item.bindTel)"></u-icon>

				<view class="" style="color: red;margin-left: 30%;" v-if="item.isLeader==1">队长</view>
			</view>
		</view>
		<!-- 叫车 -->
		<uni-section class="" v-if="orderData.callCarFlag&&addCarFlag" title="叫车" type="line" titleFontSize="36rpx" />
		<view v-if="orderData.callCarFlag&&addCarFlag" class="w9 mg-at flac-row f16 lh50">
			<!-- <view class="w6 flac-row f16 lh45"> -->
			<view class="c6" style="letter-spacing: 4rpx;">车辆大小：</view>
			<uni-data-select v-model="rangeValue2" :localdata="range2" :clear="false">
			</uni-data-select>
			<u-button text="叫车" throttleTime="2000" color="#1e1848" @click="addCar" plain
				customStyle="width:25%;height:70rpx"></u-button>
		</view>
		<!-- 服务记录 -->
		<uni-section class="" title="服务记录" type="line" titleFontSize="36rpx" />
		<uni-table ref="serverLog" stripe emptyText="暂无更多服务信息">
			<!-- 表头行 -->
			<uni-tr>
				<uni-th align="center" width="90">服务项目</uni-th>
				<uni-th align="center" width="110">服务时间</uni-th>
				<uni-th align="center" width="90">服务员工</uni-th>
				<uni-th align="center" width="70">操作</uni-th>
			</uni-tr>
			<!-- 表格内容 -->
			<uni-tr v-for="(item, i) in serverLog" :key="i">
				<uni-td align="center">{{item.productName}}</uni-td>
				<uni-td align="center">{{item.startTime}}</uni-td>
				<uni-td align="center">
					<view class="lh40" style="display: flex;align-items: center;">
						<u-icon
							name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672819685664server.png"
							size="25" />
						<view class="text-c">{{item.serviceName}}</view>
					</view>
				</uni-td>
				<uni-td align="center">
					<u-icon
						name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1672820643559phone.png"
						size="25" customStyle="margin: auto" @click="callPhone(item.bindTel)"></u-icon>
				</uni-td>
			</uni-tr>
		</uni-table>
		<!-- 补单信息 -->
		<view class="w10 flac-row" style="background-color: #f6f7fb;">
			<uni-section class="" title="补单信息" type="line" titleFontSize="36rpx" />
			<!-- 开始服务状态才能补单 -->
			<!-- <u-tag text="补单" icon="plus-circle" type="error" plain v-if="orderData.state === 2"></u-tag> -->
		</view>
		<uni-table ref="tableData2" stripe emptyText="暂无补单信息">
			<!-- 表格内容 -->
			<uni-tr v-for="(item, i) in repairOrder" :key="i">
				<uni-td align="center">{{item.billNo}}</uni-td>
				<uni-td align="center">{{item.productName}}</uni-td>
				<uni-td align="center">{{item.realTotalAmount}}</uni-td>
				<uni-td align="center">
					<u-icon name="close-circle-fill" size="18" @click="delRepair(item.billNo)"
						customStyle="margin: auto"></u-icon>
				</uni-td>
			</uni-tr>
		</uni-table>
		<!-- 增值服务 -->
		<uni-section class="" title="增值服务" type="line" titleFontSize="36rpx" />
		<view class="" style="padding: 40rpx;" @click="uploadImg(1)">
			<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" :previewFullImage="true" name="1"
				multiple :maxCount="1">
			</u-upload>
		</view>
		<!-- 纸签信息 -->
		<uni-section class="" title="纸签信息(上传后不可更改)" type="line" titleFontSize="36rpx"
			v-if=" current === 3 || orderData.signUrl " />
		<view class="" style="padding: 40rpx;" @click="uploadImg(3)" v-if=" current === 3 || orderData.signUrl ">
			<u-upload :fileList="fileList3" @afterRead="afterRead" :previewFullImage="true" name="3" multiple
				:maxCount="1">
			</u-upload>
		</view>
		<uni-section class="" title="电子签单" type="line" titleFontSize="36rpx" v-if="orderData.autographPicture" />
		<view style="padding: 40rpx;" v-if="orderData.autographPicture">
			<u-image :src="orderData.autographPicture" width="340" height="150"></u-image>
		</view>
		<!-- 附件图片 -->
		<uni-section class="" title="附件图片(可传多张,服务前/服务后)" type="line" titleFontSize="36rpx" />
		<view class="" style="padding: 40rpx;" @click="uploadImg(2)">
			<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePicB" name="2" multiple>
			</u-upload>
		</view>
		<!-- 录音 -->
		<uni-section class="" title="开始服务录音" type="line" titleFontSize="36rpx" />
		<view class="page-body" style="display: flex;">
			<view class="page-section page-section-gap" style="text-align: center;">
				<audio style="text-align: left" :src="soundData.src"  :author="soundData.author" :name="soundData.name" :action="audioAction" controls></audio>
			</view>
			<u-icon name="close" size="20" style="margin-top: -80rpx;" @click="delSound(1)"></u-icon>
		</view>
		<!-- 录音 -->
		<uni-section class="" title="服务结束录音" type="line" titleFontSize="36rpx" v-if=" current === 3 "/>
		<view class="page-body" style="display: flex;" v-if=" current === 3 ">
			<view class="page-section page-section-gap" style="text-align: center;">
				<audio style="text-align: left" :src="soundEndData.src"  :author="soundEndData.author" :name="soundEndData.name" :action="endAudioAction" controls></audio>
			</view>
			<u-icon name="close" size="20" style="margin-top: -80rpx;" @click="delSound(2)"></u-icon>
		</view>
		<!-- 其他信息 -->
		<view class="w10 flac-row" style="background-color: #f6f7fb;">
			<uni-section class="" title="其他信息" type="line" titleFontSize="36rpx">
			</uni-section>
		</view>
		<!-- 其他信息详情内容 -->
		<view class="w9 mg-at f16" v-if="collect && current != 0 && current != 1">
			<view class="fb lh50 c6 border-bottom-2se">空调</view>
			<view class="flac-row" style="justify-content: space-between;">
				<view class="w5 lh40 c3">定频挂机：<text class="letter-spacing40">{{(kt1 || 0) + '台'}}</text></view>
				<view class="w5 lh40 c3">定频柜机：<text class="letter-spacing40">{{(kt2|| 0) + '台'}}</text></view>
			</view>
			<view class="flac-row" style="justify-content: space-between;">
				<view class="w5 lh40 c3">变频挂机：<text class="letter-spacing40">{{(kt3 || 0) + '台'}}</text></view>
				<view class="w5 lh40 c3">变频柜机：<text class="letter-spacing40">{{(kt4 || 0) + '台'}}</text></view>
			</view>
			<view class="lh40 c3">中央空调：<text class="letter-spacing40">{{(kt5 || 0) + '台'}}</text></view>
			<view class="fb lh50 c6 border-bottom-2se">洗衣机</view>
			<view class="w5 lh40 c3">涡轮洗衣机：<text class="letter-spacing40">{{(xyj1 || 0) + '台'}}</text></view>
			<view class="w5 lh40 c3">滚筒洗衣机：<text class="letter-spacing40">{{(xyj2 || 0) + '台'}}</text></view>
			<view class="lh50 c6 border-bottom-2se">冰箱</view>
			<view class="w5 lh40 c3">普通冰箱：<text class="letter-spacing40">{{(bx1|| 0) + '台'}}</text></view>
			<view class="w5 lh40 c3">对开门冰箱：<text class="letter-spacing40">{{(bx2 || 0) + '台'}}</text></view>
			<view class="fb lh50 c6 border-bottom-2se">其他</view>
			<view class="lh40 c3">户型：<text class="letter-spacing20">{{collect.huxing==1?'三房一厅一卫':
											   collect.huxing==2?'三房二厅一卫':
											   collect.huxing==3?'三房二厅二卫':
											   collect.huxing==4?'二房一厅一卫':
											   collect.huxing==5?'二房二厅一卫':
											   collect.huxing==6?'二房二厅二卫':
											   collect.huxing==7?'四房二厅二卫':
											   collect.huxing==8?'一房一厅一卫':
											   collect.huxing==9?'楼中楼':
											   collect.huxing==10?'别墅':
											   collect.huxing==11?'其它':
											   '未知'}}</text></view>
			<view class="lh40 c3">面积：<text class="letter-spacing20">{{collect.mianji==1?'小于80平米':
											   collect.mianji==2?'80-100平米':
											   collect.mianji==3?'100-120平米':
											   collect.mianji==4?'120-150平米':
											   collect.mianji==5?'大于150平米':
											   '未知'}}</text></view>
			<view class="lh40 c3">使用性质：<text class="letter-spacing20">{{collect.useXz==1?'业主自住':
																			collect.useXz==2?'家庭租住':
																			collect.useXz==3?'商业办公':'未知'}}</text></view>
			<view class="lh40 c3">保洁频次：<text class="letter-spacing20">{{collect.bzpc==1?'大于2次/周':
											   collect.bzpc==2?'1次/周':
											   collect.bzpc==3?'1次/2周':
											   collect.bzpc==4?'1次/月':
											   collect.bzpc==5?'大于1次/月':
											   '未知'}}</text></view>
			<view class="lh40 c3">抽油烟机：<text class="letter-spacing20">{{(collect.cyyj || 0 ) + '台'}}</text></view>
			<view class="fb lh50 c6 border-bottom-2se">家庭成员</view>
			<view class="lh40 c3 letter-spacing20">
				老人:{{collect.hasOldMan || '未知'}},小孩:{{collect.hasChild|| '未知'}},孕妇:{{collect.hasPregnantWoman|| '未知'}}
			</view>
			<view class="fb lh50 c6 border-bottom-2se">备注</view>
			<view class="lh40 c3 letter-spacing20">{{collect.familyRemark || '无'}}</view>
		</view>

		<view class="w9 mg-at flac-row" style="padding: 40rpx 0 20rpx 0">
			<!-- <u-tag text="服务需求" type="error" plain @click="goto('../order/needInfo')"></u-tag> -->
			<u-tag v-if="collectFlag" text="收集信息" color="#1e1848" plain plainFill @click="goto('../order/otherInfo')">
			</u-tag>
			<u-tag text="查看评价" v-if="orderData.orderState == 90" color="#1e1848" plain
				@click="goto('../order/evaluate')">
			</u-tag>
			<u-tag text="给客户发短信" color="#1e1848" plain @click="goto('../order/noteInfo')"></u-tag>
			<u-tag text="上报服务结束" v-if="orderData.orderState==60" color="#1e1848" plainFill plain @click="popupShow=true"></u-tag>
		</view>
		<!-- 派单待确认 -->
		<view class="footer w10 mg-at bacf flac-row" v-if="current === 0">
			<u-button text="查看路线" color="#1e1848" plain @click="lookMap"></u-button>
			<u-button text="拒绝接单" color="#1e1848" v-if="orderData.isLeader==1&&dispathFlag==0" plain
				@click="showModal1 = true"></u-button>
			<u-button text="确认接单" color="#1e1848" v-if="orderData.isLeader==1&&dispathFlag==0"
				@click="openTxTc(2)"></u-button>
			<u-button text="订单回退" color="#1e1848" v-if="dispathFlag==1" @click="orderFallback"></u-button>
		</view>
		<!-- 已派单 -->
		<view class="footer w10 mg-at bacf flac-row" v-if="current === 1">
			<u-button text="查看路线" color="#1e1848" @click="lookMap" v-if="orderData.orderState!=50"></u-button>
			<u-button text="订单回退" color="#1e1848" v-if="dispathFlag==1" @click="orderFallback"></u-button>
			<u-button v-if="orderData.orderState==40&&orderData.isLeader==1&&dispathFlag==0" text="出工" color="#1e1848"
				@click="openTxTc(1)"></u-button>
			<all-speech @startRecord="start" @endRecord="end" @cancelRecord="cancel" v-if="orderData.orderState>40"></all-speech>
			<u-button v-if="orderData.orderState>40&&voicePath" text="播放录音"
				color="#1e1848" @click="playVoice"></u-button>
			<u-button v-if="orderData.orderState>40&&voicePath" text="上传录音"
				color="#1e1848" @click="uploadLy"></u-button>
			<u-button v-if="orderData.orderState==50&&orderData.isLeader==1&&fileList4.length>=2" text="开始服务"
				color="#1e1848" @click="showModa20 = true"></u-button>
			<u-button v-if="orderData.orderState>40&&fileList4.length<2" text="上传工作台照片" color="#1e1848"
				@click="showModa21 = true"></u-button>
		</view>
		<!-- 开始服务 -->
		<view class="footer w10 mg-at bacf flac-row" v-if="current === 2&&orderData.isLeader==1">
			<u-button text="预报时" v-if="!orderData.endDateTime" color="#1e1848" @click="showModal4 = true"></u-button>
			<u-button text="补单" color="#1e1848" @click="goto('../order/addOrder')"></u-button>
			<u-button text="修改预报时" v-if="orderData.endDateTime" color="#1e1848" @click="showModal4 = true"></u-button>
			<u-button v-if="fileList5.length<2" text="上传结束图片" color="#1e1848"
				@click="showModa22 = true"></u-button>
			<u-button text="服务结束" v-if="fileList5.length>=2" color="#1e1848" @click="serviceEnd"></u-button>
		</view>
		<!-- 服务结束 -->
		<view class="footer w10 mg-at bacf flac-row" v-if="current === 3&&orderData.isLeader==1">
			<all-speech @startRecord="start" @endRecord="serverEnd" @cancelRecord="cancel"></all-speech>
			<u-button v-if="voiceEndPath" text="播放录音"
				color="#1e1848" @click="playEndVoice"></u-button>
			<u-button v-if="voiceEndPath" text="上传录音"
				color="#1e1848" @click="uploadEndLy"></u-button>
		</view>
		<!-- 拒绝接单弹窗 -->
		<u-modal :show="showModal1" title="拒绝接单" :showCancelButton="true" @confirm="updateOrderState(30)"
			@cancel="showModal1 = false">
			<view class="slot-content" style="display: flex; align-items: center;">
				<u--input v-model="refuseValue" placeholder="请输入拒绝原因" border="bottom" inputAlign="center" clearable>
				</u--input>
			</view>
		</u-modal>
		<!-- 接单弹窗 -->
		<u-modal :show="showModal2" title="确认接单？" :showCancelButton="true" @confirm="updateOrderState(40)"
			@cancel="showModal2 = false">
		</u-modal>
		<!-- 出工弹窗 -->
		<u-modal :show="showModal3" title="确认出工？" :showCancelButton="true" @confirm="updateOrderState(50)"
			@cancel="showModal3 = false">
		</u-modal>
		<!-- 开始服务 -->
		<u-modal :show="showModa20" title="开始服务？" :showCancelButton="true" @confirm="updateOrderState(60)"
			@cancel="showModa20 = false">
		</u-modal>
		<!-- 预报时 -->
		<u-datetime-picker :show="showModal4" v-model="timeVlaue" mode="time" title="预报时" @confirm="confirm4"
			@cancel="showModal4 = false"></u-datetime-picker>
		<!-- 	<u-datetime-picker :show="showModal4" :minHour="nowHour" :minMinute="nowMin" v-model="timeVlaue" mode="time"
				title="预报时" @confirm="confirm4" @cancel="showModal4 = false"></u-datetime-picker> -->
		<!-- 服务结束 -->
		<u-modal :show="showModal5" :showCancelButton="true" @confirm="updateOrderState(70)"
			@cancel="showModal5 = false">
			<view class="f16 lh50">
				确认订单金额是否为{{orderData.realTotalAmount || 0 }}元？
			</view>
		</u-modal>
		<!-- 微信扫码付款 -->
		<!-- 代客付款弹窗 -->
		<!-- <u-modal :show="showModal7" title="优惠券列表" :showCancelButton="true" confirmColor="#f0263d" @confirm="confirm7"
			@cancel="showModal7 = false">
			<view class="w10 text-c lh50 c6" v-if="!radiolist.length">无可用优惠券</view>
			<u-radio-group v-model="radiovalue" placement="column">
				<u-radio :customStyle="{marginBottom: '8px'}" v-for="(item, i) in radiolist" :key="i" :label="item.name"
					:name="item.name" @change="radioChange" shape="square" activeColor="#f9ae3d" size="20"
					labelSize="16px" iconSize="16">
				</u-radio>
			</u-radio-group>
		</u-modal> -->
		<!-- 微信支付--二维码弹窗 -->
		<u-popup :show="showModal8" mode="center" @close="showModal8 = false" @open="clickShow1">
			<view>
				<image :src="imgCode1" width="350" show-menu-by-longpress="true" height="350" customStyle="margin:auto">
				</image>
			</view>
			<u-button text="我已付款" @click="updateOrderState(80)" color="#1e1848" customStyle="margin:auto"></u-button>
		</u-popup>
		<!-- 去评价--二维码弹窗 -->
		<u-popup :show="showModal9" mode="center" @close="closePopup()" @open="openGopj">
			<!-- <view>
				<u-image :src="imgCode2" width="150" height="150" customStyle="margin:auto"></u-image>
			</view> -->
		</u-popup>
		<u-popup :show="mapFlag" @close="mapFlag = false">
			<view>
				<map id="myMap" style="width: 150%; height: 50vh;" :latitude="mapConfig.latitude"
					:longitude="mapConfig.longitude" :scale="mapConfig.scale" :markers="markers"
					@markertap="openMap"></map>
			</view>
			<u-button text="打开地图APP导航" style="width: 95%;" color="#1e1848" @click="openMap"></u-button>
			<view @click="copyAddress">
				<text>服务地址:{{orderData.street}}(点击复制)</text>
			</view>
		</u-popup>
		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()" @close="closeDialog()"></uni-popup-dialog>
			</uni-popup>
		</view>
		
		<u-popup mode="center" :show="popupShow" @close="popupShow = false" @open="popupShow=true">
			<view class="bacf" style="width: 600rpx;height: 340rpx;">
				<uni-table class="tableStyle" style="margin: 0;">
					<uni-tr>
						<uni-td align="center">
							上报原由
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-input v-model="reason" placeholder="请输入原由"></u-input>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-button text="确认" @click="confirmReportReason()" color="#1e1848">
							</u-button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</u-popup>


		<u-popup :show="showModa21" @close="closeFwtPopup">
			<uni-section title="上传工作台照片" type="line"></uni-section>
			<scroll-view scroll-y="true" class="scroll-Y" style="height:75vh;">
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">上传要求：
					<view style="color: red;">上传2张照片，一张工作台照，另一张为员工个人与工作台合拍的现场照，服务开始前的准备不计入服务时长，点击开始之后才算服务时长！</view>
				</view>
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">工作台照片示例：</view>
				<image style="margin-left: 15%;"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17140259161631714025875036.jpg"
					class="w7" mode="widthFix"></image>
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">与工作台合照示例：</view>
				<image style="margin-left: 15%;"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17140264279391714026415337.jpg"
					class="w7" mode="widthFix"></image>
				<view class="" style="margin-left: 15%;ma" @click="uploadImg(4)">
					<view>上传照片（最多五张）</view>
					<u-upload :maxCount="5" :fileList="fileList4" @afterRead="afterRead" @delete="deletePicC" name="4"
						multiple>
					</u-upload>
				</view>
			</scroll-view>

		</u-popup>
		
		<u-popup :show="showModa22" @close="closeJsPopup">
			<uni-section title="上传服务结束图片" type="line"></uni-section>
			<scroll-view scroll-y="true" class="scroll-Y" style="height:75vh;">
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">上传要求：
					<view style="color: red;">上传服务后的场景图，可以是服务前后对比，也可以都是服务后的图片，至少2张图片！</view>
				</view>
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">示例服务前：</view>
				<image style="margin-left: 15%;"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1720686819292cec9ce6236d511b21fe0b01ce46951b.jpg"
					class="w7" mode="widthFix"></image>
				<view style="font-size: 35rpx;margin-left: 15%;margin-bottom: 20rpx;">示例服务后：</view>
				<image style="margin-left: 15%;"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17206868370438b177b835c528addad7da974def8d71.jpg"
					class="w7" mode="widthFix"></image>
				<view class="" style="margin-left: 15%;ma" @click="uploadImg(5)">
					<view>上传照片（最多五张）</view>
					<u-upload :maxCount="5" :fileList="fileList5" @afterRead="afterRead" @delete="deletePicD" name="5"
						multiple>
					</u-upload>
				</view>
			</scroll-view>
		
		</u-popup>

		<u-popup :show="serviceLanguageFlag" mode="center" :round="10" @close="serviceLanguageFlag = false"
			customStyle="width: 80%;height: 60%;">
			<view class="w10 mg-at flex-col-c radius20"
				style="position: absolute;left: 50%;transform: translateX(-50%);margin-top: -8%;background-color: #1e1848 ;">
				<image style="width: 120rpx;margin-top: -50rpx;" :src="orderData.level==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_1.png':
		 										orderData.level==2?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_2.png':
		 										orderData.level==3?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_3.png':
		 										orderData.level==4?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_4.png':
		 										orderData.level==5?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_5.png':''"
					mode="widthFix"></image>
				<view class="f20 fb lh50" style="color: white;">{{orderData.level==1?'白金会员':
		 								orderData.level==2?'黄金会员':
		 								orderData.level==3?'铂金会员':
		 								orderData.level==4?'钻石会员':
		 								orderData.level>=5?'黑钻会员':'未知'}}</view>
			</view>
			<view
				style="margin-top: 30%;width: 60%;position: absolute;left: 50%;transform: translateX(-50%);font-size: 40rpx;"
				v-if="tcType==1">已完成以下服务语？</view>
			<view style="margin-top: 50%;width: 80%;position: absolute;left: 50%;transform: translateX(-50%);"
				v-if="tcType==1">{{orderData.serviceLanguage}}</view>
			<view
				style="margin-top: 30%;width: 60%;position: absolute;left: 65%;transform: translateX(-50%);font-size: 40rpx;"
				v-if="tcType==2">牢记四感恩</view>
			<view style="margin-top: 50%;width: 80%;position: absolute;left: 50%;transform: translateX(-50%);"
				v-if="tcType==2">感恩您的购买，让我为您服务！</view>
			<view style="margin-top: 60%;width: 80%;position: absolute;left: 50%;transform: translateX(-50%);"
				v-if="tcType==2">感恩您的家人让我入户！</view>
			<view style="margin-top: 70%;width: 80%;position: absolute;left: 50%;transform: translateX(-50%);"
				v-if="tcType==2">感恩您的指导（挑剔）让我进步！</view>
			<view style="margin-top: 80%;width: 80%;position: absolute;left: 50%;transform: translateX(-50%);"
				v-if="tcType==2">感恩您的复购让我养家糊口！</view>
			<view style="display: flex;margin-top: 100%;">
				<button v-if="tcType==1" type="primary" plain="true" @click="serviceLanguageFlag = false" size="mini"
					style="width: 30%;">否</button>
				<button v-if="tcType==1" type="primary" size="mini" @click="showModal3=true,serviceLanguageFlag=false"
					style="width: 30%;background-color: #1e1848;">是</button>
				<button v-if="tcType==2" type="primary" size="mini" @click="showModal2=true,serviceLanguageFlag=false"
					style="width: 30%;background-color: #1e1848;">我已知晓</button>
			</view>
		</u-popup>
		
		<!-- 改价--弹窗 -->
		<u-popup :show="updateAmountFlag" :round="10" mode="bottom" @close="updateAmountFlag=false" :closeable="true">
		  <view class="f18 fb text-c lh50">订单改价</view>
		  <u--form labelPosition="left" labelWidth="100px" ref="form" class="w85 mg-at">
		  	<u-form-item label="订单金额:" prop="userInfo.name" borderBottom ref="item">
		  		<u--input disabled v-model="orderData.realTotalAmount" border="none" placeholder="订单金额"></u--input>
		  	</u-form-item>
			<u-form-item label="已支付金额:" prop="userInfo.name" borderBottom ref="item">
				<u--input disabled v-model="orderData.amount" border="none" placeholder="已支付金额"></u--input>
			</u-form-item>
		  	<u-form-item label="修改后金额:" prop="userInfo.tags" borderBottom ref="item">
		  		<u--input type="number" v-model="updateAmount" border="none" placeholder="请填写修改后金额"></u--input>
		  	</u-form-item>
		  </u--form>
		  <u-button text="修改价格" @click="updateOrderAmount()" color="#1e1848" shape="circle"
		  	customStyle="width:80%;margin: 30rpx auto"></u-button>
		</u-popup>

		<u-popup :show="qaShow" :round="10" mode="bottom" @close="qaShow = false">
			<view>
				<uni-section title="问卷调查" type="line" padding style="height: calc(90vh - 90px);">
					<scroll-view scroll-y="true" class="scroll-Y">
					<view v-for="(qa, i) in questionnaireList" :key="i" @click="radioClick(i)">
					<view style="margin-bottom: 30rpx;">{{i+1}}、{{qa.title}}</view>
					<radio-group @change="radioChange">
						<label style="display: flex;font-size: 30rpx;padding: 25rpx;" v-for="(item, index) in qa.options"
							:key="item.value">
							<view>
								<radio :value="item.value" :checked="index == qa.radioCurrent" />
							</view>
							<view style="margin-left: 20rpx;padding-bottom: 20rpx;">{{item.name}}</view>
						</label>
					</radio-group>
					</view>
					</scroll-view>
					<view style="height: 100rpx;"></view>
					<view class="footer w10 mg-at bacf flac-row">
						<u-button text="点击提交" color="#1e1848" @click="subQa"></u-button>
					</view>
				</uni-section>
			</view>
		</u-popup>
	</view>
</template>

<script>
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	
	innerAudioContext.autoplay = true;
	import hTimeAlert from '@/pages-work/components/h-time-alert/h-time-alert.vue';
	export default {
		components: {
			hTimeAlert
		},
		data() {
			return {
				questId: null,
				current: 0,
				qaShow: false,
				popupShow: false,
				questionnaireList: [],
				isDelFlag: 1,
				updateAmount: 0.00,
				voicePath: '',
				reason: '',
				voiceEndPath: '',
				soundEndData: {
					poster: '',
					name: '',
					author: '',
					src: '',
				},
				soundData: {
					poster: '',
					name: '',
					author: '',
					src: '',
				},
				audioAction: {
					method: 'pause'
				},
				endAudioAction: {
					method: 'pause'
				},
				isDelFlagB: 1,
				tcType: null,
				serviceLanguageFlag: false,
				memberLevel: null,
				maskHide: true, //预约时间打开组件
				closeBtn: true, //预约时间打开组件
				rangeType: false, //预约时间打开组件
				serverEmployee: [],
				addCarFlag: false,
				dispathFlag: 0,
				phoneShowFlag: true,
				refuseValue: '',
				labelFlag: false,
				bxLabelFlag: false,
				mapFlag: false,
				showhTime: false,
				mapConfig: {
					latitude: 24.48541, // 默认定位小羽佳
					longitude: 118.09644,
					scale: 12, // 默认16
				},
				markers: [{
					id: 1,
					latitude: '',
					longitude: '',
					iconPath: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1673509966990位置.png',
					width: 30,
					height: 30,
				}],
				nowHour: '',
				nowMin: '',
				serverNo: uni.getStorageSync("employeeNo"),
				serverName: uni.getStorageSync("employeeName"),
				current: null, //控制进度条位置，以及对应按钮的显示隐藏
				showModal1: false, //拒绝出单
				showModal2: false, //接单
				showModal3: false, //出工
				showModal4: false, //预报时
				showModal5: false, //服务结束
				showModal7: false, //微信扫码付款--优惠券
				showModal8: false, //微信扫码付款--二维码
				showModal9: false, //去评价
				showModa20: false, //开始服务
				showModa21: false, //上传工作台照片
				showModa22: false, //上传服务结束图片
				timeValue: Number(new Date()), // 时间选择器---预报时
				money: '', //订单金额 -- 服务结束
				orderData: {},
				repairOrder: [],
				collectFlag: true,
				updateAmountFlag: false,
				rangeValue1: 0,
				rangeValue2: 15,
				range: [
					["分时", "转账", "长途", "异常", "其他", "年卡", "长途转账", "转账分时", "返工", "推荐案例"]
				],
				bxRange: [
					["无", "24小时", "3天", "7天", "1个月", "3个月"]
				],
				range2: [{
						value: 15,
						text: "3.2米平板车"
					},
					{
						value: 17,
						text: "3.8米厢式车"
					}
				],
				serverLog: [],
				collect: {},
				tableData2: [],
				checkType: 0,
				timeCath: {},
				timeVlaue: '',
				checkTitle: "",
				checkText: "",
				id: '',
				kt1: '',
				kt2: '',
				kt3: '',
				kt4: '',
				kt5: '',
				bx1: '',
				bx2: '',
				advanceStr: '',
				xyj1: '',
				xyj2: '',
				fileList1: [],
				event: {},
				fileList2: [],
				fileList3: [], //上传纸签
				fileList4: [], //上传工作台照片
				fileList5: [], //上传服务结束照片
				// 基本案列数据
				radiolist: [],
				// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
				radiovalue1: '',
				imgCode1: '',
				imgCode2: '',
				uploadFlag: null,
				timeParam: {}
			};
		},
		onLoad(option) {
			let time = new Date();
			this.advanceStr = option.advanceStr
			this.nowMin = time.getMinutes();
			this.nowHour = time.getHours();
			this.id = option.id
			if (option.dispathFlag == 1) {
				this.dispathFlag = option.dispathFlag
				this.serverNo = option.employeeNo
			}
			setTimeout(() => {
				this.getOrderData()
			}, 500)
			// uni.getLocation({
			// 	success(res) {
			// 		this.mapConfig.latitude = res.latitude;
			// 		this.mapConfig.longitude = res.longitude;
			// 	}
			// })
			
			uni.getLocation({
				type: 'gcj02',
				isHighAccuracy: true,
				geocode: true,
				success: res => {
					console.log("定位调用成功")
					console.log('纬度：' + res.latitude);
					console.log('经度：' + res.longitude)
					console.log("（高精度）当前的纬度：", res.latitude, "当前的经度", res.longitude)
					uni.setStorageSync("lat", res.latitude)
					uni.setStorageSync("lng", res.longitude)
				},
				fail: err => {
					return uni.showToast({
						title: '当前未授权定位信息，请授权！',
						icon: 'none'
					})
				},
			})
		},
		methods: {
			confirmReportReason(){
				this.http({
					url: 'confirmReportReason',
					data: {
						billNo: this.orderData.billNo,
						remark: this.reason,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.reason = ''
							uni.showToast({
								title: '上报成功，已通知主管处理！',
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: '上报失败，请稍后重试！',
								icon: 'none'
							})
						}
					}
				})
			},
			playEndVoice() {
						console.log('播放录音');
						console.log("1111111",this.voiceEndPath);
						if (this.voiceEndPath) {
							innerAudioContext.src = this.voiceEndPath;
							innerAudioContext.play();
						}
						
					},
			playVoice() {
						console.log('播放录音');
						console.log("1111111",this.voicePath);
						if (this.voicePath) {
							innerAudioContext.src = this.voicePath;
							innerAudioContext.play();
						}
						
					},
			start() {
			    // 开始录音
				console.log("开始录音");
			},
			end(event) {
			    // 结束录音并处理得到的录音文件
			    // event中，app端仅有tempFilePath字段，微信小程序还有duration和fileSize两个字段
				console.log("结束录音",event);
				this.voicePath = event.tempFilePath
			},
			serverEnd(event) {
			    // 结束录音并处理得到的录音文件
			    // event中，app端仅有tempFilePath字段，微信小程序还有duration和fileSize两个字段
				console.log("结束录音",event);
				this.voiceEndPath = event.tempFilePath
			},
			uploadEndSound(){
				this.http({
					url: 'uploadEndSound',
					data: {
						billNo: this.orderData.billNo,
						soundUrl: this.soundEndData.src,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						if (res.code == 0) {
							this.getOrderData()
						}
					}
				})
			},
			uploadSound(){
				this.http({
					url: 'uploadSound',
					data: {
						billNo: this.orderData.billNo,
						soundUrl: this.soundData.src,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						if (res.code == 0) {
							this.getOrderData()
						}
					}
				})
			},
			uploadEndLy(){
				if(this.orderData.soundEndUrl){
					return uni.showToast({
									title: '请删除原录音再上传！',
									icon: 'none'
								})
				}
				uni.uploadFile({
					url: 'https://api.xiaoyujia.com/acn/uploadFiles',
					filePath: this.voiceEndPath,
					name: 'file',
					success: (uploadFileRes) => {
						console.log("uploadFileRes-----------:",uploadFileRes);
						let result = JSON.parse(uploadFileRes.data)
						this.soundEndData.src = result.data
						this.uploadEndSound()
					}
				});	
			},
			uploadLy(){
				if(this.orderData.soundUrl){
					return uni.showToast({
									title: '请删除原录音再上传！',
									icon: 'none'
								})
				}
				uni.uploadFile({
					url: 'https://api.xiaoyujia.com/acn/uploadFiles',
					filePath: this.voicePath,
					name: 'file',
					success: (uploadFileRes) => {
						console.log("uploadFileRes-----------:",uploadFileRes);
						let result = JSON.parse(uploadFileRes.data)
						this.soundData.src = result.data
						this.uploadSound()
					}
				});	
			},
			cancel() {
			      // 用户取消录音
				  console.log("用户取消录音");
			},
			getUpdateTimeNumber(){
				if(this.orderData.orderState==10||this.orderData.orderState==40){
					this.http({
						url: 'getUpdateTimeNumber',
						data: {
								billNo: this.orderData.billNo
						},
						method: 'GET',
						success: res => {
							if (res.code === 0) {
								if(res.data>=1){
									uni.showToast({
													title: '操作失败，只可变更一次服务时间！',
													icon: 'none'
												})
								}else{
									this.showhTime = true
								}
							}
						}
					})
					}else{
						return uni.showToast({
												title: '请在已接单或已派单状态下更改！',
												icon: 'none',
												duration: 2000
											})
					}
			},
			advance(){
				if(!this.orderData.declareFlag){
					return uni.showToast({
									title: '请等待薪酬结算后申报！',
									icon: 'none'
								})
				}
				uni.navigateTo({
					url: '/pages-work/business/order/declare?id='+this.id+'&employeeNo=' +this.serverNo
				})
			},
			radioClick(i){
				this.questId = i
			},
			serviceEnd(){
				// if(this.orderData.surveyResult){
					this.showModal5 = true
				// }else{
				// 	this.http({
				// 		url: 'getQuestionnaireList',
				// 		method: 'GET',
				// 		success: res => {
				// 			if (res.code === 0) {
				// 				this.questionnaireList = res.data
				// 				this.qaShow = true
				// 			} else {
				// 				uni.showToast({
				// 					title: '获取问卷信息失败，请退出重试！',
				// 					icon: 'none'
				// 				})
				// 			}
				// 		}
				// 	})
				// }
			},
			subQa(){
				this.http({
					url: 'saveQuestionnaireResult',
					data: {
						billNo: this.orderData.billNo,
						questionnaireList: this.questionnaireList,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.getOrderData()
							this.qaShow = false
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			radioChange(evt) {
				setTimeout(()=>{
				this.questionnaireList[this.questId].radioCurrent = evt.detail.value
				this.questionnaireList[this.questId].result =this.questionnaireList[this.questId].options[evt.detail.value].name
				},100)
			},
			openTxTc(val) {
				this.tcType = val
				this.serviceLanguageFlag = true
			},
			closeFwtPopup() {
				this.showModa21 = false
				this.getOrderData()
			},
			closeJsPopup() {
				this.showModa22 = false
				this.getOrderData()
			},
			orderFallback() {
				this.openCheck(5, "是否确认该操作？");
			},
			privatePhone() {
				if (this.orderData.orderState == 20 || this.orderData.orderState == 30) {
					return uni.showToast({
						title: '非接单状态不可操作!',
						icon: 'none'
					})
				}
				uni.showModal({
					title: '提示',
					content: '将使用虚拟号拨打客户手机号(手机号非真实号码)',
					success: res => {
						if (res.confirm) {
							this.callPrivate();
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			callPrivate() {
				this.http({
					url: 'callPrivatePhoneByBillNo',
					data: {
						billNo: this.orderData.billNo,
						employeePhone: uni.getStorageSync('account')
					},
					method: 'GET',
					success: res => {
						if (res.code === 0) {
							uni.makePhoneCall({
								phoneNumber: res.data
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			updateOrderAmount(){
				if (!(!isNaN(parseFloat(this.updateAmount)) && isFinite(this.updateAmount))){
					return uni.showToast({
						icon: 'none',
						title: '请输入正确的金额'
					})
				}
				this.http({
					url: 'updateOrderAmount',
					data: {
						id: this.orderData.id,
						updateAmount: this.updateAmount,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: res.data,
								icon: 'none'
							})
							this.updateAmountFlag = false
							setTimeout(()=>{
								this.getOrderData()
							},1500)
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			addCar() {
				let carName = '';
				if (this.rangeValue2 == 15) {
					carName = '3.2米平板车'
				}
				if (this.rangeValue2 == 17) {
					carName = '3.8米厢式车'
				}
				this.http({
					url: 'generateAddCarToken',
					data: {
						id: uni.getStorageSync("employeeId"),
						no: uni.getStorageSync("employeeNo"),
						remark: uni.getStorageSync("employeeToken")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {
						if (res.code == 0 && res.data) {
							uni.setStorageSync("employeeToken", res.data)
							this.http({
								outsideUrl: 'https://appapi.xiaoyujia.com/api/customer/addCar',
								data: {
									BillNo: this.orderData.billNo,
									Num: '1',
									carId: this.rangeValue2,
									carName: carName,
								},
								header: {
									"content-type": "application/json;charset=UTF-8",
									'Authorization': "Basic " + res.data,
								},
								method: 'POST',
								success: res => {
									if (res.Meta.State == 200) {
										uni.showToast({
											title: '叫车成功',
											icon: 'none'
										})
										setTimeout(() => {
											this.getOrderData();
										}, 1000)
									} else {
										uni.showToast({
											title: res.Meta.Msg,
											icon: 'none'
										})
									}
								}
							})
						} else {
							uni.showToast({
								title: '叫车失败！系统异常！',
								icon: 'none'
							})
						}
					}
				})
			},
			openGopj() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com//serviceAcceptanceForm/setOrderNoToken?orderNo=' +
						this.orderData.billNo,
					method: 'GET',
					success: res => {
						if (res.status == 200 && res.data) {
							let param = {
								url: 'https://task.xiaoyujia.com/serviceTicket/serviceAutograph?token=' +
									res.data
							}
							let data = JSON.stringify(param);
							uni.navigateTo({
								url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
							})
						} else {
							return uni.showToast({
								title: '系统错误!',
								icon: 'none'
							})
						}
					},
				})
			},
			closeDialog() {
				if (this.checkType == 4) {
					this.showModal9 = true
				}
			},
			goPj() {
				uni.navigateTo({
					url: '/pages-work/business/order/orderSigning?billNo=' + this.orderData.billNo + '&id=' + this.id
				})
				// 获取订阅消息授权
				// // #ifdef  MP-WEIXIN
				// wx.requestSubscribeMessage({
				// 	tmplIds: ["x64aI96dYKraSdcHcCTSh7sS0Bdvx8rRfzo3TU2RXK0"],
				// 	success: res => {
				// this.openCheck(4, "请确认已允许消息通知哦！否则用户评价后将接收不到通知！")
				// 	},
				// 	fail: res => {}
				// })
				// // #endif
				// this.showModal9 = true
			},
			copyAddress() {
				uni.setClipboardData({
					data: this.orderData.street,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			openMap(item) {
				let name = this.orderData.street
				const longitude = Number(this.markers[0].longitude)
				const latitude = Number(this.markers[0].latitude)
				uni.openLocation({
					latitude: latitude, //纬度 - 目的地/坐标点
					longitude: longitude, //经度 - 目的地/坐标点
					name: name,
					address: "",
					success: function() {
						console.log('success');
					}
				});
			},
			lookMap() {
				if (!this.markers[0].longitude || !this.markers[0].latitude) {
					return uni.showToast({
						title: '客户服务地址经纬度获取失败!',
						icon: 'none'
					})
				} else {
					this.mapFlag = true
				}
			},
			closePopup() {
				this.getOrderData()
				this.showModal9 = false
			},
			delSound(val){
				this.http({
					url: 'delSound',
					data: {
						billNo: this.orderData.billNo,
						delType: val,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						
						if (res.code == 0) {
							if(val==1){
								this.soundData.src = ''
							}else{
								this.soundEndData.src = ''
							}
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							setTimeout(()=>{
								this.getOrderData()
							},1000)
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			uploadSingUrl(val) {
				this.http({
					url: 'uploadSingUrl',
					data: {
						billNo: this.orderData.billNo,
						signUrl: val,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.fileList1 = []
						this.fileList2 = []
						this.fileList3 = []
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						if (res.code == 0) {
							this.getOrderData()
						}
					}
				})
			},
			uploadImg(val) {
				this.uploadFlag = val
			},
			confirm4(time) {
				this.timeCath = time
				this.showModal4 = false
				this.openCheck(3, "是否确认该操作？");
			},
			delRepair(val) {
				if (this.current !== 2) {
					uni.showToast({
						title: "请在开始服务状态下操作!",
						icon: 'none'
					})
				} else {
					this.http({
						url: 'delRepairOrder',
						data: {
							billNo: val,
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						success: res => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							if (res.code == 0) {
								this.getOrderData()
							}
						}
					})
				}
			},
			callPhone(val) {
				if (!val) {
					return uni.showToast({
						title: "系统派单中!请等待!",
						icon: 'none'
					})
				}
				uni.makePhoneCall({
					phoneNumber: val, //电话号码
					success: function(e) {
						console.log(e);
					},
					fail: function(e) {
						console.log(e);
					}
				});
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					if (this.uploadFlag == 1) {
						this.updateMoveUrl(obj.data)
					}
					if (this.uploadFlag == 2) {
						this.updateOrderImg()
					}
					if (this.uploadFlag == 3) {
						this.uploadSingUrl(obj.data)
					}
					if (this.uploadFlag == 4) {
						this.uploadServiceToolsUrl()
					}
					if (this.uploadFlag == 5) {
						this.uploadServiceEndUrl()
					}
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				let obj = {}
				if (this.uploadFlag == 3) {
					obj = {
						route: 'zhiQianDan'
					}
				}
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: obj,
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			updateOrderState(val) {
				if (val == 30 && !this.refuseValue) {
					return uni.showToast({
						title: '请填写拒绝原因!',
						icon: 'none'
					})
				}
					uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					geocode:true,
					success: res => {
						console.log("定位调用成功")
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude)
						console.log("（高精度）当前的纬度：", res.latitude, "当前的经度", res.longitude)
						uni.setStorageSync("lat", res.latitude)
						uni.setStorageSync("lng", res.longitude)
					},
					fail: err => {
						return uni.showToast({
							title: '操作失败，请授权定位信息!',
							icon: 'none'
						})
					},
				})
				
				if (val == 40) {
					this.sendReceivingMsg()
				}
				
				//服务结束
				if (val == 70) {
					this.http({
						url: 'updateOrderState',
						data: {
							id: this.orderData.id,
							orderState: val,
							reason: this.refuseValue,
							billNo: this.orderData.billNo,
							realName: this.serverName,
							productName: this.orderData.productName,
							startTime: this.orderData.startTime,
							bindTel: this.orderData.bindTel,
							employeeNo: this.serverNo,
							lat: uni.getStorageSync('lat'),
							lng: uni.getStorageSync('lng'),
							employeeId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						success: res => {
							this.showModal5 = false
							if (res.code == 0) {
								let leader = {}
								for (var i = 0; i < this.serverEmployee.length; i++) {
									if (this.serverEmployee[i].isLeader == 1) {
										leader = this.serverEmployee[i]
										break;
									}
								}
								if (!leader.id) {
									leader = {
										"id": this.serverEmployee[0].id,
										"serviceName": this.serverEmployee[0].serviceName,
										"employeeNo": this.serverEmployee[0].employeeNo,
										"leader": this.serverEmployee[0].leader,
									}
								}
								let objParam = {
									"Id": leader.id,
									"Name": leader.serviceName,
									"ClientIP": "127.0.0.1",
									"No": leader.employeeNo,
									"IsSys": false,
									"StoreId": [leader.storeId],
									"RoleId": null,
									"ProductId": [this.orderData.productId]
								}
								let lat = this.orderData.latitude || '24.49559'
								let lng = this.orderData.longitude || '118.10824'
								this.http({
									outsideUrl: 'https://inside.xiaoyujia.com/api/order/Complete?BillNo=' +
										this.orderData.billNo + '&lng=' + lng + '&lat=' + lat,
									data: objParam,
									header: {
										"content-type": "application/json;charset=UTF-8"
									},
									method: 'POST',
									success: res => {
										this.showModal8 = false
										this.showModal2 = false
										this.showModal3 = false
										this.showModa20 = false
										this.showModal5 = false
										this.fileList1 = []
										this.fileList2 = []
										this.fileList3 = []
										this.getOrderData()
										if (res.Meta.State == 200) {
											uni.showToast({
												title: '操作成功!',
												icon: 'none'
											})
										} else {
											uni.showToast({
												title: res.Meta.Msg,
												icon: 'none'
											})
										}


									}
								})
								return

							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none',
									duration: 2500
								})
								return
							}
						},
						fail: err => {
							console.log('系统异常!')
							return
						}
					})
				}
				// if(val==80) {
				// 	return this.getOrderData()
				// }
				if (val != 70) {
					this.http({
						url: 'updateOrderState',
						data: {
							id: this.orderData.id,
							orderState: val,
							reason: this.refuseValue,
							billNo: this.orderData.billNo,
							realName: this.serverName,
							productName: this.orderData.productName,
							startTime: this.orderData.startTime,
							bindTel: this.orderData.bindTel,
							employeeNo: this.serverNo,
							lat: uni.getStorageSync('lat'),
							lng: uni.getStorageSync('lng'),
							employeeId: uni.getStorageSync("employeeId")
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						success: res => {
							if (val == 30) {
								uni.navigateTo({
									url: '/pages-work/business/order/orderIndex'
								})
								this.showModal1 = false
							}
							this.showModal8 = false
							this.showModal2 = false
							this.showModal3 = false
							this.showModa20 = false
							this.showModal5 = false
							this.fileList1 = []
							this.fileList2 = []
							this.fileList3 = []
							this.getOrderData()
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
				}
			},
			// 发送确认通知
			sendReceivingMsg() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/work/sendReceivingMsg',
					data: {
						billNo: this.orderData.billNo || '',
						employeeNo: uni.getStorageSync('employeeNo') || '',
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {}
				})
			},
			getUpdateTimeButtonText(){
				return `订单改时（剩余${this.orderData.updateTimeNum}次操作次数）`;
			},
			getUpdateAmountButtonText(){
				return `订单改价（剩余${this.orderData.updateAmountNum}次操作次数）`;
			},
			getOrderData() {
				if (!this.id) {
					uni.showToast({
						title: '未获取到订单编号信息！',
						icon: 'none',
						duration: 2000
					})
					setTimeout(() => {
						// uni.navigateBack();
						uni.navigateTo({
							url: '/pages-work/business/businessIndex'
						})
					}, 2000)
				} else {
					this.http({
						url: "getOrderData",
						data: {
							id: this.id,
							employeeNo: this.serverNo
						},
						method: 'GET',
						success: res => {
							if (res.code == 0) {
								this.orderData = res.data.order
								this.getUpdateTimeButtonText()
								this.getUpdateAmountButtonText()
								this.soundData.src = res.data.order.soundUrl
								this.soundEndData.src = res.data.order.soundEndUrl
								if(res.data.order.soundUrl){
									this.soundData.name = '已完成服务录音'
								}else{
									this.soundData.name = '未完成录音上传'
								}
								if(res.data.order.soundEndUrl){
									this.soundEndData.name = '已完成服务录音'
								}else{
									this.soundEndData.name = '未完成录音上传'
								}
								this.serverEmployee = res.data.serverEmployee
								this.collect = res.data.collect
								this.repairOrder = res.data.repairOrder
								this.serverLog = res.data.serverLog
								if (res.data.order.isLeader == 0) {
									this.phoneShowFlag = false
								}
								if (res.data.order.longitude && res.data.order.latitude) {
									this.markers[0].longitude = res.data.order.longitude
									this.markers[0].latitude = res.data.order.latitude
									this.mapConfig.longitude = res.data.order.longitude
									this.mapConfig.latitude = res.data.order.latitude
								}
								if (res.data.order.signUrl) {
									let obj = {
										url: res.data.order.signUrl
									}
									this.fileList3.push(obj)
								}
								if (res.data.order.moveUrl) {
									let obj = {
										url: res.data.order.moveUrl
									}
									this.fileList1.push(obj)
								}
								this.fileList4 = []
								if (res.data.order.serviceToolsUrl) {
									let arr = res.data.order.serviceToolsUrl.split(',');
									for (var i = 0; i < arr.length; i++) {
										let obj = {
											url: arr[i]
										}
										this.fileList4.push(obj)
									}
								}
								this.fileList5 = []
								if (res.data.order.serviceEndUrl) {
									let arr = res.data.order.serviceEndUrl.split(',');
									for (var i = 0; i < arr.length; i++) {
										let obj = {
											url: arr[i]
										}
										this.fileList5.push(obj)
									}
								}
								if (res.data.order.orderImgs) {
									let arr = res.data.order.orderImgs.split(',');
									for (var i = 0; i < arr.length; i++) {
										let obj = {
											url: arr[i]
										}
										this.fileList2.push(obj)
									}
								}
								let flow = res.data.order.orderState
								switch (flow) {
									case 20:
										this.current = 0
										break;
									case 40:
										this.current = 1
										break;
									case 50:
										this.current = 1
										break;
									case 60:
										this.current = 2
										break;
									case 70:
										this.current = 3
										break;
									case 80:
										this.current = 4
										break;
									case 99:
										this.current = 4
										break;
									case 90:
										this.current = 5
										break;
								}
								if (this.current == 1 || this.current == 2) {
									this.addCarFlag = true
								}
								if (flow == 20 || flow == 50 || flow == 40 || flow == 99 || res.data.collect) {
									this.collectFlag = false
								}
								if (res.data.collect) {
									if (res.data.collect.ktPz) {
										let arr = res.data.collect.ktPz.split(',');
										this.kt1 = arr[1].substring(0, 1);
										this.kt2 = arr[2].substring(0, 1);
										this.kt3 = arr[3].substring(0, 1);
										this.kt4 = arr[4].substring(0, 1);
										this.kt5 = arr[5];
									}
									if (res.data.collect.bxPz) {
										let arr = res.data.collect.bxPz.split(',');
										this.bx1 = arr[1].substring(0, 1);
										this.bx2 = arr[2];
									}
									if (res.data.collect.xyjPz) {
										let arr = res.data.collect.xyjPz.split(',');
										this.xyj1 = arr[1].substring(0, 1);
										this.xyj2 = arr[2];
									}
								}
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					})
				}
			},
			confirmMaintenance(e){
				this.http({
					url: 'updateMaintenance',
					data: {
						id: this.orderData.id,
						maintenance: e.value[0],
						employeeId: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.getOrderData()
						this.bxLabelFlag = false
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			changeSelect(e) {
				this.http({
					url: 'updateOrderLabel',
					data: {
						id: this.orderData.id,
						label: e.value[0],
						employeeId: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.getOrderData()
						this.labelFlag = false
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 7) {
					this.isDelFlagB = 2
					this[`fileList${this.event.name}`].splice(this.event.index, 1)
					this.uploadServiceEndUrl()
				}
				
				if (this.checkType == 6) {
					this.isDelFlag = 2
					this[`fileList${this.event.name}`].splice(this.event.index, 1)
					this.uploadServiceToolsUrl()
				}

				if (this.checkType == 1) {
					this.updateMoveUrl('')
				}
				if (this.checkType == 2) {
					this[`fileList${this.event.name}`].splice(this.event.index, 1)
					this.updateOrderImg()
				}
				if (this.checkType == 3) {
					// let endDate = this.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd') + " " + this.timeCath.value +
					// 	":00";
					let leader = {}
					for (var i = 0; i < this.serverEmployee.length; i++) {
						if (this.serverEmployee[i].isLeader == 1) {
							leader = this.serverEmployee[i]
							break;
						}
					}
					if (!leader.id) {
						leader = {
							"id": this.serverEmployee[0].id,
							"serviceName": this.serverEmployee[0].serviceName,
							"employeeNo": this.serverEmployee[0].employeeNo,
							"leader": this.serverEmployee[0].leader,
						}
					}
					let objParam = {
						"Id": leader.id,
						"Name": leader.serviceName,
						"ClientIP": "127.0.0.1",
						"No": leader.employeeNo,
						"IsSys": false,
						"StoreId": [leader.storeId],
						"RoleId": null,
						"ProductId": [this.orderData.productId]
					}
					this.http({
						outsideUrl: 'https://inside.xiaoyujia.com/api/order/updateTime?BillItemNo=' +
							this.orderData.billNo + '&id=' + leader.id +
							'&StartTime=' + this.timeCath.value + ":00",
						data: objParam,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						method: 'POST',
						success: res => {
							if (res.Meta.State == 200) {
								this.timeVlaue = this.timeCath.value;
								this.getOrderData()
								uni.showToast({
									title: '操作成功!',
									icon: 'none'
								})
							} else {
								uni.showToast({
									title: res.Meta.Msg,
									icon: 'none',
									duration: 2000
								})
							}
						}
					})


					// this.http({
					// 	url: 'updateEndTime',
					// 	data: {
					// 		billNo: this.orderData.billNo,
					// 		endDateTime: endDate
					// 	},
					// 	header: {
					// 		"content-type": "application/json;charset=UTF-8"
					// 	},
					// 	method: 'POST',
					// 	success: res => {
					// 		uni.showToast({
					// 			title: res.data,
					// 			icon: 'none'
					// 		})
					// 		if (res.code == 0) {
					// 			this.timeVlaue = this.timeCath.value;
					// 			this.getOrderData()
					// 		}
					// 	}
					// })
				}
				if (this.checkType == 4) {
					this.showModal9 = true
				}
				if (this.checkType == 5) {
					this.http({
						outsideUrl: 'https://inside.xiaoyujia.com/api/Order/OrderHts?empId=' +
							uni.getStorageSync('employeeId') +
							'&ip=0&BillNo=' + this.orderData.billNo,
						method: 'POST',
						success: res => {
							if (res.Meta.State == 200) {
								uni.showToast({
									title: '回退成功!',
									icon: 'none'
								})
								setTimeout(() => {
									uni.navigateTo({
										url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
									})
								}, 2000)
							} else {
								uni.showToast({
									title: res.Meta.Msg,
									icon: 'none'
								})
							}

						}
					})
				}
			},
			updateOrderImg() {
				let imgs = '';
				if (this.fileList2.length > 0) {
					for (var i = 0; i < this.fileList2.length; i++) {
						imgs += this.fileList2[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.http({
					url: 'updateOrderImg',
					data: {
						id: this.orderData.id,
						orderImg: imgs,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.fileList1 = []
						this.fileList2 = []
						this.fileList3 = []
						this.getOrderData()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			uploadServiceEndUrl() {
				let imgs = '';
				if (this.fileList5.length > 0) {
					for (var i = 0; i < this.fileList5.length; i++) {
						imgs += this.fileList5[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.http({
					url: 'uploadServiceEndUrl',
					data: {
						id: this.orderData.id,
						serviceEndUrl: imgs,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.fileList4 = []
						this.fileList5 = []
						this.fileList1 = []
						this.fileList2 = []
						this.fileList3 = []
						this.getOrderData()
						if (this.isDelFlagB == 2) {
							this.showModa22 = true
						}
						if (res.code == 0) {
							uni.showToast({
								title: '操作成功！',
								icon: 'none'
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			uploadServiceToolsUrl() {
				let imgs = '';
				if (this.fileList4.length > 0) {
					for (var i = 0; i < this.fileList4.length; i++) {
						imgs += this.fileList4[i].url + ","
					}
				}
				if (imgs) {
					imgs = imgs.substring(0, imgs.length - 1)
				}
				this.http({
					url: 'uploadServiceToolsUrl',
					data: {
						id: this.orderData.id,
						serviceToolsUrl: imgs,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.fileList4 = []
						this.fileList1 = []
						this.fileList2 = []
						this.fileList3 = []
						this.getOrderData()
						if (this.isDelFlag == 2) {
							this.showModa21 = true
						}
						if (res.code == 0) {
							uni.showToast({
								title: '操作成功！',
								icon: 'none'
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			updateMoveUrl(val) {
				this.http({
					url: 'updateMoveUrl',
					data: {
						id: this.orderData.id,
						moveUrl: val,
						employeeId: uni.getStorageSync("employeeId")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						this.fileList1 = []
						this.fileList2 = []
						this.fileList3 = []
						this.getOrderData()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			// 删除图片
			deletePic(event) {
				this.openCheck(1, "确认删除增值服务图片吗？")
			},
			// 删除图片
			deletePicD(event) {
				this.event = event
				this.showModa22 = false
				this.openCheck(7, "确认删除结束图片吗？")
			},
			// 删除图片
			deletePicC(event) {
				this.event = event
				this.showModa21 = false
				this.openCheck(6, "确认删除工作台照片吗？")
			},
			// 删除图片
			deletePicB(event) {
				this.event = event
				this.openCheck(2, "确认删除该附件图片吗？")
			},
			goto(url) {
				uni.navigateTo({
					url: url + "?id=" + this.id
				})
			},
			//微信扫码支付弹窗--确认按钮
			confirm7() {
				this.showModal7 = false //关闭优惠券弹窗
				this.showModal8 = true //打开二维码弹窗
			},
			clickShow1() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/getOrderPayCode',
					data: {
						billNo: this.orderData.billNo
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							this.imgCode1 = res.data
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			handelClose(data) {
				if (!data) {
					return this.showhTime = false;
				}
				this.timeParam.startTime = data.date + " 00:00:00";
				this.timeParam.startTimeSpan = data.start;
				this.timeParam.endTimeSpan = data.end;
				this.timeParam.billNo = this.orderData.billNo;
				uni.showModal({
					title: '提示',
					content: '是否改时到' + data.all,
					success: res => {
						if (res.confirm) {
							this.updateTime();
						} else if (res.cancel) {
							this.showhTime = false;
						}
					}
				});
				// this.showhTime = false;
				console.log(data)
			},
			saveUpdateOrderServiceTimeLog(){
				this.http({
					url: 'saveUpdateOrderServiceTimeLog',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						employeeId: uni.getStorageSync('employeeId'),
						billNo: this.orderData.billNo,
						updateType: 1,
						startTime: this.timeParam.startTime
					},
					success: res => {
					}
				})
			},
			updateTime() {
				this.http({
					url: 'generateAddCarToken',
					data: {
						id: uni.getStorageSync("employeeId"),
						no: uni.getStorageSync("employeeNo"),
						remark: uni.getStorageSync("employeeToken")
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {
						if (res.code == 0 && res.data) {
							uni.setStorageSync("employeeToken", res.data)
							this.http({
								outsideUrl: 'https://appapi.xiaoyujia.com/api/customer/UpdateServiceTime',
								data: this.timeParam,
								header: {
									"content-type": "application/json;charset=UTF-8",
									'Authorization': "Basic " + res.data,
								},
								method: 'POST',
								success: res => {
									if (res.Meta.State == 200) {
										this.showhTime = false;
										uni.showToast({
											title: '操作成功',
											icon: 'none'
										})
										setTimeout(() => {
											this.saveUpdateOrderServiceTimeLog()
											this.getOrderData();
										}, 1500)
									} else {
										uni.showToast({
											title: res.Meta.Msg,
											icon: 'none'
										})
									}

								}
							})
						} else {
							uni.showToast({
								title: '改时失败！系统异常！',
								icon: 'none'
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.scroll-Y {
		height: 800rpx;
	}
	
	// .page {
	//   width: 100%;
	//   height: 100vh;
	//   background-color: #f6f7fb !important;
	// }
	.border-top {
		border-top: 2rpx solid #eee;
	}

	.letter-spacing40 {
		letter-spacing: 40rpx;
	}

	.letter-spacing20 {
		letter-spacing: 20rpx;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		justify-content: space-around;
	}

	/deep/.u-steps-item__wrapper__dot {
		width: 30rpx !important;
		height: 30rpx !important;
	}

	/deep/.uni-section .uni-section-header__decoration.line {
		height: 36rpx;
	}

	/deep/.uni-section {
		background-color: #f6f7fb !important;
	}

	/deep/ .uni-section .uni-section-header__content .distraction {
		color: #666 !important;
		font-weight: bold;
	}

	/deep/.u-text__value {
		font-weight: bold !important;
	}

	/deep/ .uni-table-th {
		color: #555 !important;
		font-size: 1rem !important;
	}

	/deep/.uni-table-td {
		font-size: 1rem !important;
	}

	/deep/ .u-button {
		// border-radius: 0 !important;
		margin: 20rpx;
	}

	/deep/ .u-button__text {
		font-size: 1rem !important;
	}

	/deep/ .u-tag {
		margin-right: 20rpx !important;
	}
</style>