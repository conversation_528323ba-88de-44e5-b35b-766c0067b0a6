<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-picker :show="showPickerMine" @cancel="showPickerMine = false" :columns="pickerMineList"
			@confirm="confirmPickerMine" @change="changeHandler" :keyName="pickerMineName"
			:defaultIndex="defaultIndex"></u-picker>

		<!-- 门店选择栏目 -->
		<view class="boxStyle bacf flac-row-b lh40">
			<view class="flac-row">
				<view class="text-l f16 fb" style="margin-right: 10rpx;" @click="openPickerMine(0)">
					{{storeName || '未知门店'}}
				</view>
				<u-icon name="arrow-down-fill" size='12' />
			</view>
		</view>

		<!-- 评分明细 -->
		<view class="boxStyle bacf">
			<view class="flac-row-b lh50">
				<view class="f15 fb">用户评分</view>
				<view class="f14 c6">更新于{{updatetime || new Date().toISOString().slice(0, 10)}}</view>
			</view>
			<view class="flac-row-b">
				<view class="flex-col-c lh50">
					<view class="ce7405d fb f22">{{acceptanceScore}}</view>
					<u-rate style="margin: auto 20rpx;" size="20" activeIcon="heart-fill" inactiveIcon="heart" count="5"
						readonly :value="acceptanceScore"></u-rate>
					<view class="">{{acceptanceScore < 2 ? '较差' : ( acceptanceScore < 4 ? '一般' : '较好' )}}</view>
				</view>
				<view class="flex-col-c c6">
					<view class="c9">{{formatLevelTips(0,acceptanceScore)}}</view>
					<view class="flac-row-c"
						style="background-color: #f4f3f2;padding: 0 20rpx;border-radius: 10rpx;margin: 20rpx auto;">
						具体分值
						<view class="c0 fb t-indent">{{acceptanceScore}}</view>
						<view class="t-indent" :style="[formatCountColor(acceptanceScore,acceptanceScoreLast)]">
							{{formatScoreTips(acceptanceScoreList)}}
						</view>
					</view>
					<view class="c9">{{formatLevelTips(1,acceptanceScore)}}</view>
				</view>
			</view>
		</view>

		<!-- 提升维度 -->
		<view class="boxStyle bacf">
			<view class="flac-row-b lh50">
				<view class="f15 fb">提升维度</view>
				<view class="f14 c6">更新于{{updatetime || new Date().toISOString().slice(0, 10)}}</view>
			</view>
			<view class="f14 flac-row-b text-c lh35 c6" style="flex-wrap: wrap;">
				<view class="w5" v-for="(item,index) in acceptanceScoreList[0].countList" :key="index">
					<view class="">{{item.title}}</view>
					<view class="f18 fb c0">{{item.count || 0}}</view>
					<view class="flac-row-c">较前一天<view class="t-indent" :style="[formatCountColor(item.count,0)]">
							{{formatCountAddVal(index)}}
						</view>
					</view>
				</view>
			</view>
			<view class="w9 flac-row textBox">
				<view class="w25 f14 c53a04a fb">建议</view>
				<view class="f12">新鲜评价在算分的权重中远高于远期评价，所以建议持续积累真实、新鲜的评价</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 最大分值
				maxScore: 5,

				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				acceptanceScore: '',
				acceptanceScoreLast: '',
				acceptanceScoreList: [{
					countList: []
				}],
				updatetime: new Date().toISOString().slice(0, 10),
				blankHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',

				pickerIndex: 0,
				defaultIndex: [0],
				pickerMineName: "",
				choicePickerMineValue: 0,
				pickerMineList: [],
				showPickerMine: false,

				storeId: 0,
				storeName: "",
				storeList: [],
			};
		},
		methods: {
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "label"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}
				this.choicePickerMineValue = value
				this.showPickerMine = true
			},
			confirmPickerMine() {
				let value = this.choicePickerMineValue
				if (value == 0) {
					this.storeId = this.storeList[0][this.pickerIndex].key
					this.storeName = this.storeList[0][this.pickerIndex].label
					this.getServiceAcceptanceScore()
				} else if (value == 1) {

				}

				this.showPickerMine = false
			},
			formatLevelTips(value, score) {
				let result = ''
				let levelHign = Math.ceil(score)
				let level = Math.floor(score)
				let levelLow = level != 0 ? level - 1 : 0
				if (value == 0) {
					if (score == this.maxScore) {
						return '已达最高等级'
					} else if (score == 0) {
						return '再+1.00 将升档'
					} else {
						let val = levelHign - score
						result = '再+' + parseFloat(val).toFixed(2) + ' 将升档'
					}
				} else if (value == 1) {
					if (score == this.maxScore) {
						return '再-1.00 将掉档'
					} else if (score == 0) {
						return '已达最低等级'
					} else {
						let val = level - score
						result = '再' + parseFloat(val).toFixed(2) + ' 将掉档'
					}
				}
				return result
			},
			formatScoreTips(data) {
				if (data.length < 2) {
					return
				}
				let scoreNow = data[0].score
				let scoreLast = data[2].score
				let result = '较上周 -'
				if (scoreNow < scoreLast) {
					result = '较上周 ↓'
				}

				if (scoreNow > scoreLast) {
					result = '较上周 ↑'
				}
				return result
			},
			// 计算数量差值
			formatCountAddVal(index) {
				let count = this.acceptanceScoreList[0].countList[index].count
				let countLast = this.acceptanceScoreList[1].countList[index].count
				let addVal = count - countLast
				if (addVal == 0) {
					return "无变化"
				}
				if (addVal > 0) {
					return "+" + addVal
				}
				return addVal
			},
			// 字体颜色样式判断
			formatCountColor(count, count1) {
				if (count > count1) return {
					color: "green"
				}
				if (count < count1) return {
					color: "red"
				}
				if (!count) return {
					color: "#CDB5CD"
				}
			},
			// 获取用户评分
			getServiceAcceptanceScore() {
				this.http({
					url: "getServiceAcceptanceScore",
					method: "POST",
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						storeId: this.storeId,
					},
					success: res => {
						if (res.code == 0) {
							this.acceptanceScoreList = res.data
							this.acceptanceScore = this.acceptanceScoreList[0].score
							this.acceptanceScoreLast = this.acceptanceScoreList[2].score
						}
					}
				})
			},
			getStoreList() {
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/work/getStoreListData",
					data: {
						employeeId: this.employeeId,
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.storeList = []
							this.storeList.push(res.data)
							let id = this.storeId
							this.storeList[0].map((item, i) => {
								let checked = item.key == id ? true : false
								if (checked) {
									this.storeName = item.label
									this.defaultIndex = []
									this.defaultIndex.push(i)
									this.pickerIndex = i
								}
							})
							this.getServiceAcceptanceScore()
						}
					}
				})
			},
		},
		onLoad(options) {
			this.storeId = options.storeId || 0
			this.getStoreList()
		},
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #f8f9fb;
	}

	.ce7405d {
		color: #e7405d;
	}

	.c53a04a {
		color: #53a04a;
	}

	.boxStyle {
		padding: 20rpx 40rpx;
		margin-bottom: 30rpx;
	}

	.textBox {
		align-items: unset;
		background-color: #f8f9fb;
		padding: 20rpx;
		border-radius: 10rpx;
		margin: 20rpx auto;
	}
</style>