<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<uni-popup ref="popupCert">
			<view>
				<img :src="certPopup" class="popup-back" mode="widthFix" />
				<img :src="cert.certImg" class="popup-img" mode="widthFix" />
				<view class="popup-text">
					<text>{{cert.certTitle}}</text>
					<text style="font-size:32rpx;color: #909399;margin-top: 10rpx;">{{cert.certContent}}</text>
				</view>
				<button class="popup-btn" @click="openCertDetail()" v-if="cert.state == 1">
					查看证书</button>
				<button class="popup-btn" @click="openCertTips()" v-if="cert.state == 0">
					获取证书</button>
			</view>
		</uni-popup>

		<!-- 		<uni-popup ref="popupCert" background-color="#fff" customStyle="z-index: 999;">
			<view>
				<img :src="certPopup" class="popup-back" />
			</view>
		</uni-popup> -->


		<!-- 头部个人信息 -->
		<view>
			<img :src="certHead" class="cert-head">
			<view class="head-img">
				<!-- 				<img src="blankImg"> -->
				<img :src="headImg!=null&&headImg!=''?headImg:blankImg">
			</view>
			<view class="head-name">
				<text>{{ memberName!==''?memberName:"暂无用户名"}}</text>
			</view>
			<view class="head-title">
				<text>我的证书（{{certCount}}）</text>
			</view>
		</view>

		<u-gap height="20"></u-gap>

		<!-- 证书列表 -->
		<view>
			<view class="swiper-menu">
				<u-sticky customStyle="z-index: 1 !important;">
          <u-tabs :list="menuList" @click="choiceMenu" :current="menuIndex" lineWidth="22" lineHeight="8" :lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
                  color: '#1e1848',
                  fontWeight: 'bold',
                  transform: 'scale(1.2)'
              }" :inactiveStyle="{
                  color: '#333',
                  transform: 'scale(1.05)'
              }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
          </u-tabs>
				</u-sticky>
			</view>

			<uni-transition mode-class="slide-left" :show="menuIndex==0">
				<view class="cert-list" v-if="menuIndex==0">
					<view class="cert-item" v-for="(cert,index) in certRecordList" :key="index"
						@click="previewCert(index)">
						<img :src="cert.certImg!=null&&cert.certImg!=''?cert.certImg:blankImg" alt="">
						<text>{{cert.certTitle}}</text>
						<text style="font-size:28rpx;color: #909399;">{{formatDate(cert.creTime)}}</text>
					</view>

				</view>
				
				<u-empty text="暂未获得证书" v-if="certCount==0&&menuIndex==0" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</uni-transition>

			<uni-transition mode-class="slide-right" :show="menuIndex==1">
				<view class="cert-list" v-if="menuIndex==1">
					<view class="cert-item" @click="previewCert1()">
						<!-- <img :src="cert1.certImg!=null&&cert1.certImg!=''?cert1.certImg:blankImg" alt=""> -->
						<img :src="cert1.certImg" alt="">
						<text>{{cert1.certTitle}}</text>
						<text style="font-size:28rpx;color: #909399;">暂未获得</text>
					</view>
				</view>
				
				<u-empty text="暂未获得证书" v-if="false&&menuIndex==1" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</uni-transition>

		</view>

		

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				certHead: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1674109721519certHead.png",
				certPopup: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1674099363985certPopup.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				memberId: null,
				memberName: uni.getStorageSync("memberName"),
				headImg: uni.getStorageSync("memberHeadImg"),

				menuIndex: 0,
				certCount: 0,
				cert: {
					certTitle: '',
					certContent: ''
				},
				cert1: {
					certImg: "https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/null1674021776654cert.jpg",
					certTitle: "经纪人毕业证书",
					certContent: "报名经纪人课程结课后可获得毕业证书，是后续开设门店的凭证。",
					certType: 1,
					certPreview: "https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/null1674021782292cert1.jpg",
					state: 0,
				},
				certList: [],
				certRecordList: [],
        lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [
					// {
					// 	name: '全部',
					// },
					{
						name: '已获得',
					},
					{
						name: '未获得',
					}
				],
				searchCondition: {
					search: "",
					memberId: null,
					state: null,
					certState: null,
					typeList: null,
					orderBy: "u.CreTime ASC",
					current: 1,
					size: 99
				}
			}
		},
		methods: {
			// 点击切换菜单
			choiceMenu(e) {
				this.menuIndex = e.index
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				// return this.$moment().format('YYYY-MM-DD') + "获得"
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '.' + MM + '.' + d + "获得"
			},
			// 预览证书
			previewCert(index) {
				this.cert = this.certRecordList[index]
				this.$refs.popupCert.open()
			},
			previewCert1() {
				this.cert = this.cert1
				this.$refs.popupCert.open()
			},
			// 查看证书详情
			openCertDetail() {
				uni.navigateTo({
					url: "/pages-mine/certificate/certificate-detail?cert=" + JSON.stringify(this.cert)
				})
			},
			// 获取证书
			openCertTips() {
				this.$refs.uNotify.success("证书考试还未开始，敬请期待！")
			},
			// 查询已获得的证书列表
			getUnionCertRecord() {
				this.searchCondition.memberId = this.memberId
				this.http({
					url: 'getUnionCertRecord',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.certRecordList = res.data.records
							this.certCount = res.data.total
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {

				}
			}
		},
		onLoad(options) {
			this.memberId = uni.getStorageSync('memberId') ? uni.getStorageSync('memberId') : -1
			this.getUnionCertRecord()
		},
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
		// background-color: #FEFAE6;
	}

	.swiper-menu {
		display: block;
		margin: 20rpx auto 0 auto;
		width: 90%;
	}

	.cert-head {
		display: block;
		width: 100%;
		height: 440rpx;
	}

	.head-img {
		position: absolute;
		width: 100%;
		height: auto;
		top: 75rpx;
		left: calc(40%);

		img {
			display: block;
			width: 150rpx;
			height: 150rpx;
			border-radius: 50%;
		}
	}

	.head-name,
	.head-title {
		position: absolute;
		width: 100%;

		text {
			font-size: 36rpx;
			display: block;
			text-align: center;
		}
	}

	.head-name {
		top: 240rpx;
		color: #fff;
		font-size: 40rpx;
	}

	.head-title {
		top: 354rpx;
		color: #C89C65;
	}

	// 证书列表
	.cert-list {
		display: flex;
		flex-wrap: wrap;
		padding-bottom: 100rpx;
	}

	.cert-item {
		width: 44%;
		margin: 20rpx 3%;
		height: 580rpx;
		background-color: #fff;
		box-shadow: 0 4rpx 20rpx #dedede;
		border-radius: 40rpx;

		img {
			display: block;
			width: 256rpx;
			height: 360rpx;
			margin: 40rpx auto;
		}

		text {
			display: block;
			text-align: center;
			font-size: 32rpx;
			line-height: 50rpx;
		}
	}

	/deep/ .u-sticky {
		z-index: 1;
	}

	/deep/ .uni-popup__wrapper {
		border-radius: 250rpx;
		z-index: 999;
	}

	// 弹窗背景图
	.popup-back {
		display: block;
		width: 600rpx;
		height: auto;
	}


	.popup-img,
	.popup-title,
	.popup-text,
	.popup-btn {
		position: absolute;
		z-index: 1;
	}

	.popup-img {
		width: 76%;
		height: 319rpx;
		left: 12%;
		top: 18%;

		img {
			display: block;
		}
	}

	.popup-text {
		bottom: 20%;
		left: 0rpx;
		width: 100%;
		text-align: center;

		text {
			display: block;
			width: 80%;
			margin: 0 auto;
			font-size: 42rpx;
			color: #000000;
		}

		text:nth-child(2) {
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			/*! autoprefixer: ignore next */
			-webkit-box-orient: vertical;
			/* autoprefixer: on */
			-webkit-line-clamp: 2;
		}
	}


	// 签到按钮
	.popup-btn {
		bottom: 6%;
		left: 5%;
		width: 90%;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 60rpx;
		font-weight: bold;
		font-size: 44rpx;
		color: #fe5e2f;
		// background-color: #ffc83c;
		background: linear-gradient(to right, #fffb76, #ff9e0a);
	}
</style>
