<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 快捷输入 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title">
				<text>快捷输入</text>
			</view>

			<view class="filter-content" style="height: 1100rpx;">
				<view class="filter-tab">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="defaultRemark"
							placeholder="请输入技能回答备注（将展示给客户）" height="100" count @input="changeRemark"></u--textarea>
					</view>

					<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
						<view v-for="(item,index) in quickTabType" :key="index">
							<view class="tab">
								<view class="tab-title-swiper">
									<text>{{item.tabTitle}}</text>
								</view>
								<view class="tab-checkbox" v-for="(tabList,index1) in quickTabList" :key="index1"
									v-if="showBox(index,index1)">
									<view class="checkbox" :class="{activeBox: tabList.isCheck==true?true:false}">
										<text @click="choiceQuickBox(index1)">{{tabList.text}}</text>
									</view>
								</view>
							</view>
						</view>

						<u-gap height="60"></u-gap>
					</scroll-view>

				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="cleanInput()">
						<text>清空备注</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="popupShow=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" :cancelText="cancelText" :confirmText="confirmText" :title="checkTitle"
				:content="checkText" @confirm="popupCheck()" @close="popupClose()">
				<!-- 确定回答提示内容和备注 -->
				<view class="popupCheck-inputbox" v-if="checkType==0">
					<text>{{checkText}}</text>
					<view style="display: flex;text-align: center;width: 100%;margin-left: 22%;">
						<text>具体回答可以备注哦</text>
						<uni-icons type="plus" style="margin-left: 20rpx;" size="24" @click="popupShow=true">
						</uni-icons>
					</view>

					<view class="tab-inputbox-high">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="defaultRemark"
							placeholder="请输入技能回答备注" height="150" count @input="changeRemark"></u--textarea>
					</view>
				</view>


				<!-- 确定鉴定提示 -->
				<view class="popupCheck-inputbox" v-if="checkType==1">
					<text>{{checkText}}</text>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" type="text" v-model="salary" placeholder="可手动输入鉴定工资" />
						</view>
					</view>
				</view>
			</uni-popup-dialog>
		</uni-popup>

		<view class="resume-tab">
			<view class="tab">
				<view class="tips-box" v-if="rankTips">
					<text>{{rankTips}}</text>
				</view>
				<view class="tab-head">
					<text>请询问{{baomu.realName}}以下问题</text>
					<text style="color:#ff4d4b">符合越多越好！</text>
				</view>
				<view class="tab-head-smail flac-col">
					<text>小贴士：详细询问员工各类工作技能内容，提审通过后的匹配度会更高哦！</text>
					<text style="color:#ff4d4b;font-size: 32rpx;">* 长按选项可编辑详情</text>
				</view>
				<view class="tab-head-smail">
					<text style="color:#19be6b">意向工作类型：{{checkStr(baomu.workType)}}</text>
				</view>
				<view class="btn-group1" style="padding: 20rpx 0rpx">
					<button @click="openTab(0)" class="btnStyle">员工简历</button>
					<button @click="openTab(1)" class="btnStyle">员工详情</button>
				</view>
			</view>

		</view>


		<!-- 工作技能鉴定-多选栏目 -->
		<view class="resume-tab" v-for="(workCode,index) in workCodeList" :key="index">
			<view class="tab">
				<view class="tab-head">
					<text>{{workCode.name}}</text>
					<text style="color:#ff4d4b"></text>
				</view>
				<view class="tab-checkbox" v-for="(item,index1) in workSkillList" :key="index1"
					v-if="workCode.key==item.workCode">
					<view class="checkbox" :class="{activeBox: item.workType==-1?true:false}">
						<text @click="confirmSkill(index1)" @longpress="choiceBox(index1)">{{item.title}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 工作技能鉴定-工作类型 -->
		<view class="resume-tab">
			<view class="tab">
				<view class="tab-head">
					<text>工作类型</text>
					<!-- 					<text style="color:#ff4d4b">将在短信中通知员工</text> -->
				</view>
				<view class="tab-checkbox" v-for="(item,index) in workTypeList" :key="index">
					<view class="checkbox" :class="{activeBox: item.index==choiceWorkTypeIndex?true:false}">
						<text @click="chocieWorkType(index)">{{item.workTypeName}}</text>
					</view>
				</view>
			</view>
		</view>

		<view class="tab">
			<view class="tab-head">
				<text>预估工资</text>
				<text></text>
			</view>
			<view class="tab-inputbox">
				<view class="tab-input">
					<input class="single-input" type="number" v-model="salary" placeholder="暂无估值" />
				</view>
			</view>

			<view class="tab-head">
				<text>店长备注</text>
				<text></text>
			</view>
			<view class="tab-inputbox">
				<view class="tab-input">
					<input class="single-input" type="text" v-model="baomu.workRemark" placeholder="仅经纪人可查看" />
				</view>
			</view>

			<view class="tab-head">
				<text>推荐理由</text>
				<text></text>
			</view>
			<view class="tab-inputbox">
				<view class="tab-input">
					<input class="single-input" type="text" v-model="baomu.introduceRemark"
						placeholder="显示在员工详情，客户可见" />
				</view>
			</view>

			<view class="tab-head">
				<text>实操视频</text>
				<text>上传后更易通过哦</text>
				<uni-icons type="trash" size="20" @click="cleanVideo" v-if="baomuInfo.introduceVideo"></uni-icons>
			</view>
			<video :src="baomuInfo.introduceVideo" v-if="baomuInfo.introduceVideo"
				style="width: 600rpx;height: 400rpx;margin: 0 75rpx;"></video>
			<uni-file-picker limit="1" file-mediatype="video" @select="uploadFile" v-else>
				<view class="mg-at w10" style="margin: 20rpx 0;">
					<img style="width: 500rpx;height: 200rpx;margin: 0 125rpx;" :src="uploadFileImg">
					</img>
					<view style="text-align: center;">可上传不超过200MB的视频文件</view>
				</view>
			</uni-file-picker>

			<view class="tab-head-smail" v-if="isAuthBefore"><text>鉴定人：{{updaterName}}（{{updaterNo}}）</text></view>
			<view class="tab-head-smail" v-if="isAuthBefore"><text>鉴定等级：{{levelName}}</text></view>
			<view class="tab-head-smail" v-if="isAuthBefore"><text>鉴定时间：{{updateTime}}</text></view>
		</view>

		<!-- 按钮组 -->
		<view class="btn-group" style="padding: 80rpx 0 0 0;">
			<button @click="cleanWorkSkill()"
				:style="choiceCount!==0?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">清空选项</button>
			<button @click="getWorkSkillSalary()"
				:style="choiceCount!==0?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">{{isAuthBefore?'重新提交':'确认提交'}}</button>
		</view>
		<view class="btn-big" style="padding-bottom: 0;">
			<button @click="openAuthReport()"
				:style="isAuthBefore?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">查看报告</button>
		</view>

		<view class="tab" v-if="rejectList.length">
			<view class="tab-head">
				<text>驳回记录</text>
			</view>
			<view class="reject-tab" v-for="(item,index) in rejectList" :key="index">
				<view class="tab-head-smail"><text>序号：{{index+1}}</text></view>
				<view class="tab-head-smail">
					<text>驳回人：{{item.rejectEmployeeName}}（{{item.rejectEmployeeNo}}）</text>
				</view>
				<view class="tab-head-smail"><text>驳回原因：{{item.remark}}</text></view>
				<view class="tab-head-smail"><text>驳回时间：{{item.createTime}}</text></view>
			</view>
		</view>

		<!-- 页面底部定位处 -->
		<u-gap height="80" id="confirm"></u-gap>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				popupShow: false,
				isAuth: false,
				isAuthBefore: false,
				confirmText: "回答符合",
				cancelText: "回答不符合",
				baomuAuthId: 0,
				level: 0,
				levelName: '',
				updaterNo: '',
				updaterName: '',
				updateTime: '',
				salary: '',
				authWorkType: 0,
				defaultRemark: '',
				remark: '',
				scrollTop: 0,
				choiceCount: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},

				memberId: null,
				baomuId: null,
				employeeId: null,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadFileImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/uploadFileTab.png',
				openSkillIndex: 0,
				rankTips: '',
				baomu: {
					baomuId: null,
					realName: null,
					workType: null,
					workRemark: null,
					state: null
				},
				baomuInfo: {},
				employeeInfo: {},
				employeeSkill: [],
				workSkillList: [],
				workSkillListPost: [],
				workTypeList: [],
				choiceWorkTypeIndex: 0,
				authData: {
					employeeId: null,
					updater: parseInt(uni.getStorageSync("employeeId")),
					level: 0,
					salary: 0,
					remark: '',
					introduceRemark: '',
				},
				workCodeList: [{
					key: 'Q',
					name: "突出技能"
				}, {
					key: 'B',
					name: "做饭"
				}, {
					key: 'C',
					name: "做家务"
				}, {
					key: 'A',
					name: "洗涤"
				}, {
					key: 'D',
					name: "照顾小孩"
				}, {
					key: 'E',
					name: "照顾老人"
				}, {
					key: 'F',
					name: "宠物照料"
				}, {
					key: 'O',
					name: "其他"
				}],
				searchParam: [
					"月嫂能力",
					"育儿嫂能力",
					"护工能力",
					"保姆做饭技能",
					"家务技能",
					"其它技能"
				],
				quickTabType: [{
						tabTitle: '月嫂技能',
						typeValue: 196
					},
					{
						tabTitle: '育儿嫂技能',
						typeValue: 197
					},
					{
						tabTitle: '护工技能',
						typeValue: 198
					},
					{
						tabTitle: '厨艺',
						typeValue: 24
					},
					{
						tabTitle: '家务技能',
						typeValue: 210
					},
					{
						tabTitle: '其它技能',
						typeValue: 211
					},
				],
				quickTabList: [],
				rejectList: [],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 清空视频
			cleanVideo() {
				this.baomuInfo.introduceVideo = ''
				this.$refs.uNotify.success("已清空当前视频，可选择重新上传！")
			},
			// 上传文件
			uploadFile(e) {
				const tempFilePaths = e.tempFilePaths;
				uni.uploadFile({
					url: 'https://api2.xiaoyujia.com/system/uploadFile',
					filePath: tempFilePaths[0],
					name: 'file',
					success: res => {
						let data = JSON.parse(res.data)
						if (data.code == 0) {
							this.$refs.uNotify.success("视频上传成功！")
							this.$set(this.baomuInfo, 'introduceVideo',
								data.data)
						} else {
							this.$refs.uNotify.error("视频上传失败！" + data.msg)
						}
					},
					fail: res => {
						this.$refs.uNotify.error("视频上传失败！" + data.msg)
					}
				});
			},
			// 查看认证报告
			openAuthReport() {
				if (this.isAuthBefore) {
					uni.navigateTo({
						url: "/pages-mine/auth/auth-report?baomuId=" + this.baomuId +
							"&employeeState=" + this.baomu.state + "&isAdmin=1"
					})
				} else {
					this.$refs.uNotify.error("鉴定后再查看吧！")
				}
			},
			openTab(value) {
				let url = ''
				if (value == 0) {
					url = '/pages-mine/resume/resume?baomuId=' + this.baomuId
				} else {
					url = '/pages-mine/works/employee-detail?baomuId=' + this.baomuId
				}
				uni.navigateTo({
					url: url
				})
			},
			// 清空输入
			cleanInput() {
				this.defaultRemark = ""
				this.workSkillList[this.openSkillIndex].defaultRemark = this.defaultRemark
				for (let item of this.quickTabList) {
					item.isCheck = false
				}
				this.$refs.uNotify.success("备注已清空！")
			},
			// 回写备注
			changeRemark() {
				this.workSkillList[this.openSkillIndex].defaultRemark = this.defaultRemark
			},
			// 选中多选框
			choiceBox(index) {
				this.openSkillIndex = index
				// 获取技能小标题、标题、具体描述、单位和默认数值
				let title = this.workSkillList[index].title
				let employeeSkill = this.workSkillList[index].employeeSkill
				let workRemark = this.workSkillList[index].workRemark
				let workUnit = this.workSkillList[index].workUnit
				let defaultValue = this.workSkillList[index].defaultValue
				let concatRemark = ''
				let defaultRemark = ''
				employeeSkill = employeeSkill !== null && employeeSkill !== '' ? employeeSkill : '该项'
				workRemark = workRemark !== null ? workRemark : '其他举例'
				defaultValue = defaultValue !== '0' ? defaultValue : ''
				if (workUnit == null) {
					workUnit = '吗'
					defaultValue = ''
				}
				// 测试时候加上（快速选择）
				// this.workSkillList[index].workType = -1
				// this.choiceCount++
				// return
				concatRemark = employeeSkill
				defaultRemark = this.workSkillList[index].defaultRemark
				if (defaultRemark == null) {
					this.workSkillList[index].defaultRemark = concatRemark
					this.defaultRemark = concatRemark
				} else {
					this.defaultRemark = defaultRemark
				}
				this.openCheck(0, employeeSkill + "，可以接受" + defaultValue + workUnit + "?", "还可以问问：" + workRemark)
			},
			// 选框显示控制（二级细分选项）
			showBox(index, index1) {
				if (this.quickTabList[index1].typeValue == this.quickTabType[index].typeValue) {
					return true
				} else {
					return false
				}
			},
			// 选择快捷输入项
			choiceQuickBox(index) {
				if (this.quickTabList[index].isCheck == true) {
					this.quickTabList[index].isCheck = false
				} else {
					this.quickTabList[index].isCheck = true
					this.defaultRemark += "，" + this.quickTabList[index].text
					this.workSkillList[this.openSkillIndex].defaultRemark = this.defaultRemark
				}
			},
			// 选择保姆工作类型
			chocieWorkType(index) {
				this.choiceWorkTypeIndex = this.workTypeList[index].index
			},
			// 清空选项
			cleanWorkSkill() {
				if (this.choiceCount == 0) {
					this.$refs.uNotify.error("还未选择任何选项！")
					return
				}
				for (let item of this.workSkillList) {
					if (item.workType == -1) {
						item.workType = 0
					}
				}
				this.choiceCount = 0
				this.workSkillListPost = []
				this.$refs.uNotify.success("选项已清空，重新开始鉴定吧！")
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 格式化请求参数防止过长
			formatWorkSkillPost() {
				this.workSkillListPost = []
				for (let item of this.workSkillList) {
					if (item.workType == -1) {
						let data = {}
						this.$set(data, "id", item.id)
						this.$set(data, "workType", -1)
						this.$set(data, "title", item.title)
						this.$set(data, "workScore", item.workScore)
						this.$set(data, "remark", item.defaultRemark || item.title)
						this.workSkillListPost.push(data)
					}
				}
			},
			// 格式化技能鉴定记录
			formatEmployeeSkill() {
				for (let item of this.employeeSkill) {
					for (let item1 of this.workSkillList) {
						if (item.workSkillId == item1.id) {
							item1.workType = -1
							item1.defaultRemark = item.remark
							this.choiceCount++
						}
					}
				}
			},
			// 获取技能字典内容
			getDictionaryByText() {
				this.http({
					url: "getDictionaryByText",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchParam,
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.quickTabList = res.data
						} else {}
					}
				})
			},
			// 获取工作技能鉴定排名
			getWorkSkillAuthRank() {
				this.http({
					url: "getWorkSkillAuthRank",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						baomuId: this.baomuId
					},
					success: res => {
						if (res.code == 0) {
							let rate = res.data
							let resumeScore = this.baomuInfo.resumeScore
							this.rankTips = '本次提审分数' + resumeScore + '分，超过全国' + rate + '店长'
						}
					}
				})
			},
			listBaomuAuthReject() {
				this.http({
					url: 'listBaomuAuthReject',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: {
						baomuAuthId: this.baomuAuthId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.rejectList = res.data
						}
					}
				});
			},
			// 获取员工技能鉴定记录
			getEmployeeSkill() {
				this.http({
					url: 'getEmployeeSkill',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.employeeSkill = res.data.employeeSkill
							this.formatEmployeeSkill()
							this.levelName = res.data.levelName
							this.salary = res.data.salary
							this.updaterNo = res.data.updaterNo
							this.updaterName = res.data.updaterName
							this.updateTime = res.data.updateTime
							this.choiceWorkTypeIndex = res.data.authWorkType
							this.baomuAuthId = res.data.baomuAuthId
							// 若鉴定过则自动滚动到页面底部
							this.isAuthBefore = true
							uni.pageScrollTo({
								// selector: '#confirm',
								scrollTop: 2400,
								duration: 300
							})
							this.getWorkSkillAuthRank()
							this.listBaomuAuthReject()
						} else {
							console.log("鉴定记录获取失败！员工之前还未鉴定过！")
							// this.$refs.uNotify.error("鉴定记录获取失败！")
						}
					}
				})
			},
			// 获取工作技能工资估值
			getWorkSkillSalary() {
				if (this.choiceCount == 0) {
					this.$refs.uNotify.error("请至少选择一个鉴定选项哦！")
				} else {
					console.log("开始获取工资估值！")
					this.formatWorkSkillPost()
					this.http({
						url: 'getWorkSkillSalary',
						path: this.baomuId,
						method: 'POST',
						data: this.workSkillListPost,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.level = res.data.level
								this.levelName = res.data.levelName
								this.salary = res.data.salary
								let tips = "确认该鉴定结果吗？"
								if (this.isAuthBefore) {
									// tips = "重新鉴定后需员工再次确认结果！"
									tips = "将重新进行上架！"
								}
								this.openCheck(1, "工作技能评估的工资为" + this.salary + "元" + "(" + this.levelName +
									")", tips)
							}
						}
					})
				}
			},
			// 开始鉴定
			startWorkSkillAuth() {
				if (this.isAuth) {
					this.$refs.uNotify.error("该员工已经鉴定过了哦，返回列表选择其他员工吧！")
					return
				}

				if (!this.employeeInfo.idCard) {
					this.$refs.uNotify.error("员工未上传身份证，无法进行提审！")
					return
				}
				this.authData.employeeId = this.baomuId
				this.authData.level = this.level
				this.authData.salary = this.salary
				this.authData.workSkill = this.workSkillListPost
				this.authData.remark = this.baomu.workRemark || ""
				this.authData.introduceRemark = this.baomu.introduceRemark || ""
				this.authData.authWorkType = this.workTypeList[this.choiceWorkTypeIndex].value || 0

				this.http({
					url: 'startWorkSkillAuth',
					method: 'POST',
					data: this.authData,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("员工鉴定上架完成！")
							// this.openCheck(2, "员工鉴定完成", "待员工确认报告后即可自动上架！")
							this.isAuth = true
							this.isAuthBefore = true
							this.getEmployeeSkill()
						} else {
							this.$refs.uNotify.error("员工鉴定失败！" + res.msg)
						}
					}
				})

			},
			// 获取工作技能列表
			getWorkSkill() {
				this.http({
					url: 'getAllWorkSkill',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.workSkillList = res.data
							this.getEmployeeSkill()
						}
					}
				})
			},
			// 获取鉴定工作类型
			getAuthWorkType() {
				this.http({
					url: 'getAuthWorkType',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.workTypeList = res.data
						}
					}
				})
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							let employee = res.data.employee
							let baomuInfo = res.data.baomuInfo
							this.employeeInfo = res.data.employeeInfo
							this.baomuInfo = baomuInfo
							this.baomu.realName = employee.realName
							this.baomu.workType = baomuInfo.workType
							this.baomu.workRemark = employee.workRemark
							this.baomu.introduceRemark = employee.introduceRemark
							this.baomu.state = employee.state
							this.getWorkSkill()
						} else {}
					}
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				if (checkType == 0) {
					this.confirmText = "回答符合"
					this.cancelText = "回答不符合"
				} else if (checkType == 1) {
					this.confirmText = "确定"
					this.cancelText = "取消"
				}
				this.$refs.popupCheck.open()
			},
			confirmSkill(index) {
				this.openSkillIndex = index
				let type = this.workSkillList[index].workType
				if (type == -1) {
					this.workSkillList[this.openSkillIndex].workType = 0
					if (this.choiceCount != 0) {
						this.choiceCount--
					}
				} else {
					this.workSkillList[this.openSkillIndex].workType = -1
					this.choiceCount++
				}
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.workSkillList[this.openSkillIndex].workType = -1
					this.choiceCount++
				} else if (this.checkType == 1) {
					this.startWorkSkillAuth()
				}
			},
			popupClose() {
				if (this.checkType == 0) {
					this.workSkillList[this.openSkillIndex].workType = 0
					if (this.choiceCount != 0) {
						this.choiceCount--
					}
				}
			}
		},
		onLoad(options) {
			this.baomuId = parseInt(options.baomuId) || 0
			this.getBaomuDetail()
			this.getAuthWorkType()
			this.getDictionaryByText()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	/deep/ .uni-popup__wrapper {
		padding: 0;
	}


	.tab-title-swiper {
		width: 100%;
		height: 60rpx;
		margin: 0 0 10rpx 40rpx;

		text {
			display: bolck;
			width: 200rpx;
			height: 60rpx;
			line-height: 60rpx;
			font-size: 36rpx;
			font-weight: bold;
		}
	}

	// 工作技能栏目
	.tab-skill {
		display: flex;
		flex-direction: row;
		margin: 20rpx auto;
		width: 100%;
		height: 100rpx;
		font-size: 36rpx;
		line-height: 100rpx;
	}

	.skill-title {
		width: 30%;
		height: 100rpx;
		text-align: right;
		padding-right: 40rpx;
	}

	// 技能输入框样式
	.skill-input {
		width: 45%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;

		input {
			display: block;
			width: 80%;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			margin: 10rpx 0 0 20rpx;
			border-style: hidden;
		}
	}

	.skill-input-unit {
		width: 25%;

		text {
			display: block;
			font-size: 36rpx;
			padding-left: 40rpx;
		}
	}

	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 弹窗输入区域
	.popupCheck-inputbox {
		display: flex;
		flex-direction: column;
		width: 100%;
		line-height: 80rpx;

		text {
			display: block;
			margin: -10rpx;
			text-align: center;
			font-size: 32rpx;
		}
	}

	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}

	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}

	.btn-group1 {
		display: flex;
		align-items: center;

		button {
			width: 220rpx;
			height: 60rpx;
			line-height: 60rpx;
			padding: 0;
			border-radius: 30rpx;
		}
	}

	.reject-tab {
		width: 90%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
		padding: 20rpx 0;
		border-radius: 20rpx;
	}
</style>