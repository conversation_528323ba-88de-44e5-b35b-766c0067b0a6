<template>
	<view>
		<!-- 1三嫂保险 2中介保险 -->
		<uni-card v-if="form.id">
			<u-text v-if="form.status === 1" text="当前报险流程还未受理"></u-text>
			<u-text v-if="form.status === 2" type="success" text="当前报险流程受理中,请继续补充材料"></u-text>
			<view v-if="form.status === 3">
				<u-text type="success" text="当前受理已结案"></u-text>
				<u-text :text="form.statusRemark"></u-text>
				<u-gap height="10"></u-gap>
				<u-text text="受理结果反馈图:"></u-text>
				<u-upload :fileList="statusRemarkFileList" multiple :maxCount="1" :disabled="true"
					v-if="statusRemarkFileList"></u-upload>
			</view>

		</uni-card>
		<view v-if="form.type == 1">

			<uni-section type="line" title="保姆姓名">
				<u-input placeholder="保姆姓名" border="bottom" v-model="form.employeeName" :disabled="form.id !== null"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="保姆身份证号">
				<u-input placeholder="身份证号" border="bottom" v-model="form.employeeCarId" :disabled="form.id !== null"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="保姆手机号">
				<u-input placeholder="保姆手机号" border="bottom" v-model="form.employeePhone" :disabled="form.id !== null"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="客户姓名">
				<u-input placeholder="客户姓名" border="bottom" v-model="form.customer" :disabled="form.id !== null"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="客户手机号">
				<u-input placeholder="客户手机号" border="bottom" v-model="form.customerPhone" :disabled="form.id !== null"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="出险时间(点击更改)" @click="showTimeS">
				<u-input v-model="form.reportTime" :disabled="true"></u-input>
				<u-datetime-picker @confirm="confirmTime" :show="showTime" :minDate="1640966400000" mode="datetime"
					@cancel="showTime = false"></u-datetime-picker>
			</uni-section>
			<uni-section type="line" title="出险地址">
				<u-input placeholder="出险详细地址" border="bottom" :disabled="form.id !== null" v-model="form.insuranceAddr"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="出险时所做工作">
				<u-input placeholder="出险时所做工作(保洁/单餐保姆....)" :disabled="form.id !== null" border="bottom"
					v-model="form.reportWork" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="出险详细说明(造成伤害/物损损失费用等说明)">

				<u-textarea v-model="form.detail" :disabled="form.id !== null"
					placeholder="例子:做家务过程中因手没拿稳导致花盆摔落/在做晚餐路上不小心摔倒导致右肩膀骨折"></u-textarea>
			</uni-section>

			<uni-section type="line" title="报险金额">
				<u-input v-model="form.reportAmount" :disabled="form.id !== null" placeholder="需要理赔金额"></u-input>
			</uni-section>

			<uni-section type="line" title="报险类型">
				<u-radio-group v-model="form.insuranceType">
					<u-radio :customStyle="{marginBottom: '8px',marginLeft: '8px'}" v-for="(item, index) in radioList"
						:key="index" :label="item.name" :name="item.type" shape="square" :disabled="form.id !== null">
					</u-radio>
				</u-radio-group>
			</uni-section>
			<u-text type="warning" text="注意:以下上传照片必传,否则无法提交表单"></u-text>
			<uni-section type="line" :title="form.insuranceType === 1 ? '物损照片': '受伤照片'">
				<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple
					:maxCount="10" :disabled="form.id !== null"></u-upload>
			</uni-section>
			<uni-section type="line"
				:title="form.insuranceType === 1 ? '受损物品购买凭证:发票(没有发票提供支付凭证)': '医疗就诊记录(诊断报告/手术记录/住记录)'">
				<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="2" multiple
					:maxCount="10" :disabled="form.id !== null"></u-upload>
			</uni-section>
			<uni-section type="line" title="员工身份证正面">
				<u-upload :fileList="fileList3" @afterRead="afterRead" @delete="deletePic" name="3" multiple
					:maxCount="1" :disabled="form.id !== null"></u-upload>
			</uni-section>
			<uni-section type="line" title="员工身份证反面">
				<u-upload :fileList="fileList4" @afterRead="afterRead" @delete="deletePic" name="4" multiple
					:maxCount="1" :disabled="form.id !== null"></u-upload>
			</uni-section>
			<uni-section type="line"
				:title="form.insuranceType === 1 ? '手写并上传事故经过(事发时间日期，事发起因，导致的结果)保姆签名按手印': '手写并上传事故经过(若发生交通事故需要开具：交通事故认定书）'">
				<u-upload :fileList="fileList5" @afterRead="afterRead" @delete="deletePic" name="5" multiple
					:maxCount="1" :disabled="form.id !== null"></u-upload>
			</uni-section>

			<view v-if="form.status == 2">
				<view v-if="form.insuranceType == 1">

				</view>
				<view v-if="form.insuranceType == 2">

				</view>

			</view>

			<u-button v-if="!form.id" text="提交受理" color="#1e1848" customStyle="width:100%;color:#f6cc70"
				@click="submitReport"></u-button>
		</view>
		<view v-if="form.status >= 2">
			<u-gap height="20" bgColor="#bbb"></u-gap>
			<u-text type="success" v-if="form.status == 2" text="报险流程已受理,请继续补充以下材料" align="center"></u-text>
			<uni-section type="line" title="事故地址照片:小区大门照片、雇主家大门照片(要有门牌号)" v-if="form.insuranceType === 1">
				<u-upload :fileList="fileList10" @afterRead="afterRead" @delete="deletePic1" name="10" multiple
					:maxCount="10" :disabled="form.status == 3"></u-upload>
			</uni-section>
			<uni-section type="line" :title="form.insuranceType === 1 ? '客户身份证正反面及银行卡正反面': '伤者身份证正反面及授权银行卡正反面'">
				<u-upload :fileList="fileList6" @afterRead="afterRead" @delete="deletePic1" name="6" multiple :disabled="form.status == 3"
					:maxCount="4"></u-upload>
			</uni-section>
			<uni-section type="line" :title="form.insuranceType === 1 ? '受损物品购买凭证,发票或支付凭证': '所有医疗就诊单据及票据原件'">
				<u-upload :fileList="fileList7" @afterRead="afterRead" @delete="deletePic1" name="7" multiple :disabled="form.status == 3"
					:maxCount="10"></u-upload>
			</uni-section>
			<uni-section type="line" title="保险结案材料:理赔协议或和解协议(盖章)、赔偿协议(现场拍照)">
				<u-upload :fileList="fileList8" @afterRead="afterRead" @delete="deletePic1" name="8" multiple :disabled="form.status == 3"
					:maxCount="10"></u-upload>
			</uni-section>
			<uni-section type="line" title="结案说明(三方确认)">
				<u-upload :fileList="fileList9" @afterRead="afterRead" @delete="deletePic1" name="9" multiple :disabled="form.status == 3"
					:maxCount="10"></u-upload>
			</uni-section>
			<u-button v-if="form.status == 2 && !form.fileList6" text="补充受理材料" color="#1e1848" customStyle="width:100%;color:#f6cc70" @click="updateReport"></u-button>	
		</view>







	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化
	import form from '../../uni_modules/uview-ui/libs/config/props/form';
	moment.locale('zh-cn');
	export default {
		data() {
			return {
				statusRemarkFileList: [], //备注图
				fileList1: [], //物损/受伤图
				fileList2: [], //物损发票/医疗保单记录
				fileList3: [], //员工身份证正面
				fileList4: [], //员工身份证反面
				fileList5: [], //手写事故鉴定书
				fileList6: [], //结案时的身份证正反面/银行卡正反面
				fileList7: [], //结案时物损单据/医疗就诊单票据
				fileList8: [], //结案材料
				fileList9: [], //结案说明
				fileList10: [], //事故地址照片：小区大门照片、雇主家大门照片（要有门牌号）
				showTime: false,
				radioList: [{
					type: 1,
					name: '物损'
				}, {
					type: 2,
					name: '人伤'
				}],
				form: {
					id:null,
					type: '',
					billNo: '',
					insuranceType: 1,
					status: 1,
					fileList1: '',
					fileList2: '',
					fileList3: '',
					fileList4: '',
					fileList5: '',
					fileList6: '',
					fileList7: '',
					fileList8: '',
					fileList9: '',
					fileList10: '',
					storeId: uni.getStorageSync('storeId'),
					opName: uni.getStorageSync('employeeName'),
					opPhone: uni.getStorageSync('bindTel')
				}
			};
		},
		onLoad({
			type,
			billNo
		}) {
			this.form.type = type
			this.form.billNo = billNo
			this.getInsurance(billNo)
		},
		methods: {
			showTimeS() {
				if (!this.form.id) {
					this.showTime = true
				}
			},
			getInsurance() {
				this.http({
					url: 'getReportInsurance',
					method: 'GET',
					data: {
						billNo: this.form.billNo
					},
					success: res => {
						if (res.data) {
							this.form = res.data
							for (var i = 1; i < 11; i++) {
								if(res.data[`fileList${i}`]) {
									const urlArray = res.data[`fileList${i}`].split(',');
									this[`fileList${i}`] = urlArray.map(url => ({
										url
									}));
								}
								
							}
							if (res.data.statusRemarkFileList) {
								const urlArray = res.data.statusRemarkFileList.split(',');
								this.statusRemarkFileList = urlArray.map(url => ({
									url
								}));
							}
							
							
						}
					}
				})
			},
			confirmTime(e) {
				this.form.reportTime = moment(e.value).format('YYYY-MM-DD HH:mm:ss');
				this.showTime = false;
			},
			// 删除图片
			deletePic(event) {
				if (this.form.id) {
					return;
				}
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 删除图片
			deletePic1(event) {
				if (this.form.fileList6) {
					return;
				}
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				console.log(event)
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							route: 'reportInsurance'
						},
						success: (res) => {
							setTimeout(() => {
								let data = JSON.parse(res.data);
								resolve(data.data)
							}, 1000)
						}
					});
				})
			},
			submitReport() {
				for (var i = 1; i < 6; i++) {
					if (!this[`fileList${i}`].length) {
						return uni.showToast({
							title: '图片提交不能为空',
							icon: 'none'
						})
					} else {
						this.form[`fileList${i}`] = this[`fileList${i}`].map(item => item.url).join(',');
					}
				}

				this.http({
					url: 'submitReportInsurance',
					method: 'POST',
					data: this.form,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: '提交成功,等待受理',
								icon: 'none'
							})
							setTimeout(() => {
								this.getInsurance();
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			updateReport() {
				for (var i = 6; i < 11; i++) {
					if (!this[`fileList${i}`].length) {
						return uni.showToast({
							title: '图片提交不能为空',
							icon: 'none'
						})
					} else {
						this.form[`fileList${i}`] = this[`fileList${i}`].map(item => item.url).join(',');
					}
				}
				//数组10单独处理
				if(this.form.insuranceType == 1) {
					if (!this.fileList10.length) {
						return uni.showToast({
							title: '图片提交不能为空',
							icon: 'none'
						})
					} else {
						this.form.fileList10 = this.fileList10.map(item => item.url).join(',');
					}
				}
				
				this.http({
					url: 'updateReportInsurance',
					method: 'POST',
					data: this.form,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: '更新成功,等待受理',
								icon: 'none'
							})
							setTimeout(() => {
								this.getInsurance();
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			}
		}
	}
</script>

<style>
</style>