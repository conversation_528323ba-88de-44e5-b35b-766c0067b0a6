<template>
  <view class="container">
    <view class="info-card">
      <view class="info-row">
        <text class="label">姓名：</text>
        <text class="value">{{ employeeName }}</text>
      </view>
      <view class="info-row">
        <text class="label">工号：</text>
        <text class="value">{{ employeeNo }}</text>
      </view>
	  <view class="info-row">
	    <text class="label">当前签署状态：</text>
	    <text class="value red" v-if="!handbookSignDate">未签署</text>
	    <text class="value green" v-else>已签署</text>
	  </view>
	  <view class="info-row">
	    <text class="label">签署时间：</text>
	    <text class="value">{{ handbookSignDate || '' }}</text>
	  </view>
    </view>
    <view class="action">
      <button v-if="!handbookSignDate" class="btnStyle" @click="signHandbook">签署员工手册</button>
      <button v-else class="btnStyle" @click="viewHandbook">查看员工手册</button>
	  <button class="btnStyle" @click="getEmployeeHandleSign">刷新状态</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      employeeNo: uni.getStorageSync('employeeNo'),
      employeeName: uni.getStorageSync('employeeName'),
      flowId: '',
	  handbookSignDate:''
    };
  },
  onLoad(option) {
    // 如果 flowId 需要从接口获取，可以在这里请求
    // this.getSignStatus();
	if(!uni.getStorageSync('employeeId')){
		uni.showModal({
			title: '提示',
			content: '登录失效,或该登录账号非在系统端中',
			showCancel:false,
			success: function (res) {
				if (res.confirm) {
					uni.clearStorageSync();
					uni.setStorageSync('redirectUrl', '/pages-mine/signIn/employeeHandbookSign');
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
				} 
			}
		});
	} else {
		this.getEmployeeHandleSign();
	}
  },
  methods: {
    signHandbook() {
      // 跳转到签署页面或调用签署接口
	  let source = null;
	  // #ifdef MP-WEIXIN
		source = 'xcx';
	  // #endif
      this.http({
		  url:'signHandbook',
		  method:'GET',
		  data:{employeeId:uni.getStorageSync('employeeId'),source:source},
		  success: res =>{
			  if (res.code == 0) {
			  	// #ifdef H5
			  	location.href = res.data.signUrl;
			  	// #endif
			  	// #ifdef MP-WEIXIN
			  	wx.navigateToMiniProgram({
			  		appId: 'wxa023b292fd19d41d', 
			  		path: res.data.signUrl, 
			  		extraData: null,
			  		envVersion: 'release'
			  	})
			  	// #endif
			  
			  } else {
			  	let msg = res.msg;
			  	uni.showToast({
			  		title: msg,
			  		icon:'none'
			  	})
			  }
		  }
	  })
    },
    viewHandbook() {
      this.http({
		  url:'viewSignPdfByFlowId',
		  data:{flowId:this.flowId},
		  success: res=>{
			  uni.downloadFile({
			  	url: res.data.url,
			  	success: function(res) {
			  		var filePath = res.tempFilePath;
			  		uni.openDocument({
			  			filePath: filePath,
			  			showMenu: true,
			  			success: function(res) {
			  				console.log('打开文档成功');
			  			}
			  		});
			  	}
			  });
		  }
	  })
    },
	getEmployeeHandleSign() {
		this.http({
			url:'getEmployeeHandleSign',
			method:'GET',
			data:{employeeId: uni.getStorageSync('employeeId')},
			success: res=>{
				if(res.code === 0) {
					this.flowId = res.data.handbookFlowId;
					this.handbookSignDate = res.data.handbookSignDate;
				} else {
					uni.showToast({
						title:res.msg,
						icon:'none'
					})
				}
			}
		})
	}
  }
}
</script>

<style>
.container {
  padding: 40rpx;
  background: #f7f8fa;
  min-height: 100vh;
}
.info-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.08);
  padding: 40rpx 32rpx;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.info-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 18rpx;
}
.info-row:last-child {
  margin-bottom: 0;
}
.label {
  color: #888;
  font-size: 30rpx;
  font-weight: 500;
  margin-right: 12rpx;
}
.value {
  color: #222;
  font-size: 32rpx;
  font-weight: bold;
}
.action button {
  width: 100%;
  margin-top: 20rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  letter-spacing: 2rpx;
}
.red {
	color: red;
}
.green {
	color: green;
}
.btnStyle {
	color: '#f6cc70';
	background-color: '#1e1848';
}
</style>