<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>员工日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index" v-if="current==0">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{checkStr(item.title)}}</text>
					</view>
					<text v-if="checkStr(item.workContent)!='暂无'">{{item.workContent}}</text>
					<text>时间：{{item.creatTime}}</text>
					<text>操作人：{{checkStr(item.crePerson)}}</text>
				</view>

				<view class="log-list" v-for="(item,index) of logList" :key="index" v-if="current==1">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{item.operationName||'-'}}</text>
					</view>
					<text>时间：{{item.creatDate}}</text>
					<text>操作人：{{item.operatorName||'-'}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<!-- 筛选弹窗 -->
		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="可输入员工姓名、手机号和工号等"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
							<view class="tab">
								<view class="tab-title">
									<text>排序方式</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in orderByList" :key="index"
									v-if="item.show">
									<view class="checkbox" :class="{activeBox: item.index==choiceOrderByIndex}">
										<text v-model="item.value" @click="choiceBox(10,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<u-gap height="60"></u-gap>
						</scroll-view>

					</view>
				</view>

				<view class="filter-button" style="width: 82%;">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="startFilter()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>



		<!-- 邀请有奖-头部信息 -->
		<view class="header bgc5">
			<view class="head-left">
				<img :src="headImg!==''?headImg:blankImg">
			</view>
			<view class="head-center f16">
				<text>{{ memberName!==''?memberName:"暂无用户名"}}</text>
				<text>邀请码：{{invitationCode }}</text>
				<text>邀请人数：{{total }}</text>
			</view>
			<view class="head-right">
				<button @click="openInvitation()">去邀请</button>
				<!-- <button @click="openProfit()">我的收益</button> -->
				<button @click="openWorkReward()">去绑定</button>
			</view>
		</view>

		<!-- 菜单栏 -->
		<view class="flac-col">
			<view class="w10" style="margin: 0rpx auto;">
				<u-sticky>
					<u-tabs :list="menuList" @click="choiceMenu" :current="current" lineWidth="22" lineHeight="8"
						:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
				  color: '#1e1848',
				  fontWeight: 'bold',
				  transform: 'scale(1.1)'
			  }" :inactiveStyle="{
				  color: '#333',
				  transform: 'scale(1.05)'
			  }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
					</u-tabs>
				</u-sticky>
			</view>

			<view class="flac-row-b">
				<view class="choice-menu" style="margin: 30rpx 0;">
					<view class="choice-item" v-for="(choiceList, index) in choiceList" :key="index"
						@click="choiceTab(index)" style="width: 140rpx;">
						<text :class="{activeChoice: choiceIndex == index}"
							class="choice-title">{{choiceList.choiceTitle}}</text>
					</view>
				</view>
				<view class="w2 flac-row" @click="popupShow=true">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
						size="15"></u-icon>
					<text class="f16 t-indent">筛选</text>
				</view>
			</view>
		</view>

		<!-- 消息气泡 -->
		<view style="height: 60rpx;" v-if="nearPutList.length!=0">
			<uni-transition mode-class="fade" :duration="tipsBoxDuration" :show="tipsBoxShow&&nearPutList.length!=0">
				<view class="tips-box" style="width: 86%;margin: 0 7%;">
					<text>{{formatTipsBox(tipsBoxIndex)}}</text>
				</view>
			</uni-transition>
		</view>

		<view class="swiper-tab" v-for="(item, index) in list" :key="index" v-if="current==0">
			<view class="swiper-head">
				<text class="swiper-title">{{formatWorkType(0,index)}}</text>
				<text class="swiper-tips"
					:style="item.state==1?'background-color: #1e1848;color: #f6cc70':''">{{item.state==1?"已上架":"未上架"}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left">
					<img :src="item.headPortrait||blankImg" alt="" @click="openImgPreview(item.headPortrait)"
						style="height: 200rpx;margin-top: 10rpx;">
					<view class="tab-left-btn" @click="urgeExamine(index)" v-if="showUrgeExamine(index)">
						<text>催审</text>
					</view>
				</view>
				<view class="content-right">
					<view class="content-title" @click="openTab(1,index)">
						<text>{{item.realName}}</text>
						<text
							style="font-weight: 100;font-size: 28rpx;">（简历：{{checkStr(item.resumeScore)!='暂无'?item.resumeScore:0}}分）</text>
						<text v-if="hasLevel(item.levelId)"
							style="color:#ff4d4b;font-weight: 100;font-size: 28rpx;">等级：{{formatLevel(item.levelId)}}</text>
					</view>
					<view class="content-text" @click="openTab(1,index)">
						<text>入驻时间：{{item.createDate}}</text>
					</view>
					<view class="flex-row lh30 f16" @click="openContractLog(item.id)">
						<text :style="item.contractId?'color:#ff4d4b':'color:#000'">状态：{{formatStatus(index)}}</text>
						<uni-icons type="eye" style="margin-left: 5rpx;display: inline-block;" size="18"
							color="#909399">
						</uni-icons>
					</view>
					<view class="content-text" @click="openTab(1,index)">
						<text>接单范围：{{siteFlagList[item.siteFlag+1].showText}}</text>
					</view>
					<view class="content-text" @click="openOrderNeeds(index)" style="display: flex;">
						<text>找一找Ta的工作</text>
						<uni-icons type="redo" size="20" color="#1e1848"></uni-icons>
					</view>

					<view style="display: flex; flex-direction: row;width: 100%;height: 100rpx;">
						<view style="width: 50%;height: 60rpx;" @click="openTab(0,index)">
							<view class="button-left">
								<text>简历</text>
							</view>
						</view>
						<view style="width: 50%;height: 60rpx;" @click="openLog(index)">
							<view class="button-right">
								<text>日志</text>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>

		<view class="swiper-tab" v-for="(item, index) in list" :key="index" v-if="current==1">
			<view class="swiper-head">
				<text class="swiper-title">{{item.post||'家政员工'}}</text>
				<text class="swiper-tips"
					:style="item.state==1?'background-color: #1e1848;color: #f6cc70':''">{{item.state==1?"已上架":"未上架"}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left">
					<img :src="item.headPortrait!==null?item.headPortrait:blankImg" alt=""
						@click="openImgPreview(item.headPortrait)" style="height: 200rpx;margin-top: 10rpx;">
				</view>
				<view class="content-right">
					<view class="content-title" @click="openTab(2,index)">
						<text>{{item.name}}</text>
					</view>
					<view class="content-text" @click="openTab(2,index)">
						<text>入驻时间：{{item.creatDate}}</text>
					</view>
					<view class="content-text" v-if="item.workingProperty!=0">
						<text>工作性质：{{workingPropertyList[item.workingProperty].showText}}</text>
					</view>
					<view class="content-text" v-if="item.departName">
						<text>部门：{{item.departName}}</text>
					</view>
					<view class="content-text" v-if="item.areaName">
						<text>区域：{{item.areaName}}</text>
					</view>

					<view style="display: flex; flex-direction: row;width: 100%;height: 100rpx;">
						<view style="width: 50%;height: 60rpx;" @click="openTab(2,index)">
							<view class="button-left">
								<text>简历</text>
							</view>
						</view>
						<view style="width: 50%;height: 60rpx;" @click="openLog(index)">
							<view class="button-right">
								<text>日志</text>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>


		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 是否显示消息气泡
				tipsBoxShow: true,
				// 气泡消息轮播数量
				tipsBoxLimit: 30,
				// 气泡消息轮播间隔（单位：s）
				tipsBoxInterval: 5,
				// 动画速度（单位：ms）
				tipsBoxDuration: 800,
				// 气泡消息显示天数
				tipsBoxDateRange: 20,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				tipsBoxPlayTime: 0,
				tipsBoxIndex: 0,
				loadMore: 0,
				popupShow: false,
				popupShowLog: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},

				current: 0,
				choiceIndex: 0,
				choiceOrderByIndex: 0,
				memberId: uni.getStorageSync('memberId') || null,
				employeeId: uni.getStorageSync('employeeId') || null,
				memberName: '',
				headImg: '',
				invitationCode: uni.getStorageSync("memberId"),
				tabNum: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				total: 0,
				searchText: '',
				headPortrait: '',
				invitationImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				list: [],
				nearPutList: [],
				logList: [],
				menuList: [{
						index: 0,
						name: '保姆月嫂',
					},
					{
						index: 1,
						name: '标准员工',
					}
				],
				choiceList: [{
						choiceTitle: "全部的",
						value: 0,
						show: true
					},
					{
						choiceTitle: "未上户",
						value: 1,
						show: true
					},
					{
						choiceTitle: "已上户",
						value: 2,
						show: true
					},
				],
				statusList: [{
						text: '不限',
						value: null
					}, {
						text: '正在找工作',
						value: 0
					},
					{
						text: '已经有工作',
						value: 1
					}
				],
				workingPropertyList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null,
				}, {
					index: 1,
					text: '',
					showText: "全职",
					value: 1,
				}, {
					index: 2,
					text: '',
					showText: "兼职",
					value: 2,
				}, {
					index: 3,
					text: '',
					showText: "行政",
					value: 0,
				}],
				orderByList: [{
						index: 0,
						value: "remindTime DESC,createDate DESC",
						showText: "默认",
						show: true,
					}, {
						index: 1,
						value: "createDate DESC",
						showText: "入驻时间",
						show: true,
					},
					{
						index: 2,
						value: "idtTime DESC",
						showText: "鉴定时间",
						show: false,
					}, {
						index: 3,
						value: "putTime DESC",
						showText: "上架时间",
						show: false,
					}, {
						index: 4,
						value: "siteFlag DESC",
						showText: "接单范围",
						show: false,
					}
				],
				siteFlagList: [{
					index: 0,
					value: null,
					showText: "不限",
				}, {
					index: 1,
					value: 0,
					showText: "本地",
				}, {
					index: 2,
					value: 1,
					showText: "省内",
				}, {
					index: 3,
					value: 2,
					showText: "省外",
				}, {
					index: 4,
					value: 3,
					showText: "全国",
				}, {
					index: 5,
					value: -1,
					showText: "非全国",
				}],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
				// 查询条件
				searchCondition: {
					education: null,
					otherSkills: null,
					agentName: null,
					baomuLevel: null,
					minAge: null,
					maxAge: null,
					storeId: null,
					address: null,
					phone: null,
					showTime: null,
					startTime: null,
					endTime: null,
					source: null,
					time: null,
					hometown: null,
					age: null,
					workYear: null,
					health: null,
					serverContent: null,
					workType: null,
					state: null,
					sites: null,
					id: null,
					no: null,
					realName: "",
					baomuWorkType: null,
					// 开启查阅权限
					introducer: uni.getStorageSync("employeeNo") || "-1",
					merchantCode: uni.getStorageSync('merchantCode') || null,
					shimingState: null,
					level: null,
					search: "",
					lat: null,
					lng: null,
					district: null,
					dateRange: null,
					creTimeRange: null,
					updateTimeRange: null,
					salaryLow: null,
					salaryHigh: null,
					scoreLow: null,
					scoreHigh: null,
					ageLow: null,
					ageHigh: null,
					status: null,
					siteFlag: null,
					orderId: null,
					orderBy: "remindTime DESC,createDate DESC",
					current: 1,
					size: 10
				},
				searchCondition1: {
					search: "",
					noAuth: 1,
					isBaomu: 0,
					state: null,
					storeId: null,
					processId: null,
					processIdRange: null,
					employeeType: 10,
					isAppraisal: null,
					introducer: uni.getStorageSync("employeeNo") || "-1",
					orderBy: "creatDate DESC,entryTime DESC",
					current: 1,
					size: 10
				},
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选择菜单（一级）
			choiceMenu(e) {
				this.current = e.index
				this.choiceTab(0)
			},
			// 选择菜单（二级）
			choiceTab(index) {
				this.choiceIndex = index
				this.refreshList()
			},
			// 选中单选框
			choiceBox(value, index) {
				if (value == 1) {

				} else if (value == 10) {
					this.choiceOrderByIndex = index
					this.searchCondition.orderBy = this.orderByList[index].value
				}
			},
			// 刷新列表
			refreshList() {
				let index = this.choiceIndex
				this.clearTabChoice()
				// 默认检索参数
				if (index == 0) {
					this.searchCondition.state = null
					this.searchCondition.orderId = null

					this.searchCondition1.isAppraisal = null
				} else if (index == 1) {
					this.searchCondition.state = null
					this.searchCondition.orderId = 0

					this.searchCondition1.isAppraisal = 0
				} else if (index == 2) {
					this.searchCondition.state = null
					this.searchCondition.orderId = 1

					this.searchCondition1.isAppraisal = 1
				}
				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {
				// 清空各个菜单特有的筛选条件
				this.searchCondition.state = 1
				this.searchCondition.orderId = null
			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
				this.searchCondition1.current = 1
			},
			// 清空筛选条件（搜索框和单选框）
			cleanFilter() {
				this.searchText = ""
				this.choiceOrderByIndex = 0
			},
			// 开始筛选
			startFilter() {
				this.list = []
				this.total = 0
				this.searchCondition.current = 1
				this.searchCondition1.current = 1
				this.searchCondition.search = this.searchText

				this.searchCondition.orderBy = this.orderByList[this.choiceOrderByIndex].value

				// 排序规则调整（默认排序下才生效）
				if (this.choiceOrderByIndex == 0) {
					if (this.choiceIndex == 0) {
						this.searchCondition.orderBy = "remindTime DESC,createDate DESC"
					} else {
						this.searchCondition.orderBy = "createDate DESC"
					}
				}

				this.getList()
				this.popupShow = false
			},
			// 打开详情
			openTab(index, index1) {
				// 打开保姆简历
				if (index == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.list[index1].baomuId
					})
				}

				// 打开员工详情
				else if (index == 1) {
					this.addBaomuWorkLog(index1)
					uni.navigateTo({
						url: "/pages-mine/works/employee-detail?baomuId=" + this.list[index1].baomuId
					})
				}

				// 打开标准员工简历
				if (index == 2) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-resume?id=' + this.list[index1].trialId + "&isAdmin=true"
					})
				}
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true

				if (this.current == 0) {
					let id = this.list[index].id
					this.http({
						url: 'getBaomuWorkLog',
						method: 'POST',
						hideLoading: true,
						data: {
							employeeId: id
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.logList = res.data
							} else {
								this.logList = []
							}
						}
					})
				} else if (this.current == 1) {
					let id = this.list[index].trialId
					this.http({
						url: 'listTrialStaffLog',
						method: 'POST',
						hideLoading: true,
						data: {
							trialId: id
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.logList = res.data
							} else {
								this.logList = []
							}
						}
					})
				}
			},
			// 打开合同日志
			openContractLog(id) {
				if (!id) {
					return
				}
				uni.navigateTo({
					url: "/pages-mine/contract/contract-history?id=" + id
				})
			},
			// 跳转到邀请页面
			openInvitation() {
				uni.navigateTo({
					url: '/pages-mine/invitation/invitation-detail'
				})
			},
			// 跳转到我的收益
			openProfit() {
				uni.navigateTo({
					url: '/pages-mine/invitation/profit'
				})
			},
			// 上下户打卡
			openWorkReward() {
				uni.navigateTo({
					url: '/pages-mine/invitation/invitation-detail?shareType=1'
				})
			},
			// 跳转线索广场
			openOrderNeeds(index) {
				let search = this.formatWorkType(1, index)
				search = search == '暂无工作类型' ? '' : search
				uni.navigateTo({
					url: '/pages-mine/works/find-works?show=true&isAdmin=true&isMine=true&search=' + search
				})
			},
			// 打开头像预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			showUrgeExamine(index) {
				let baomu = this.list[index]
				let result = true
				if (baomu.state == 1) {
					result = false
				}
				let authId = uni.getStorageSync('authId')
				let employeeId = uni.getStorageSync('employeeId') || null
				// 合伙人才有催审权限
				if (authId != 6 || employeeId) {
					result = false
				}
				return result
			},
			// 催审
			urgeExamine(index) {
				uni.showModal({
					title: '员工催审',
					content: '是否催审？将会给鉴定师发送消息',
					success: res => {
						if (res.confirm) {
							let id = this.list[index].id
							this.http({
								outsideUrl: "https://api2.xiaoyujia.com/acn/urgeExamine",
								method: 'POST',
								header: {
									'content-type': "application/json;charset=UTF-8"
								},
								data: {
									employeeId: id,
									operator: uni.getStorageSync('employeeName') || uni.getStorageSync(
										'memberName') || (
										'会员：' +
										uni.getStorageSync('account'))
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('催审成功！请等待鉴定师操作！')
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								}
							})
						}
					}
				});
			},
			addBaomuWorkLog(index) {
				let id = this.list[index].baomuId
				let name = uni.getStorageSync("employeeName") || uni.getStorageSync("memberName")
				this.http({
					url: "addBaomuWorkLog",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: id,
						title: "资料查看",
						workContent: "员工详情资料被查看",
						crePerson: name,
						type: 1
					},
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 格式化字符
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 判断是否已经鉴定
			hasLevel(level) {
				if (level != null) {
					if (level != 0) {
						return true
					}
				} else {
					return false
				}
			},
			formatWorkType(value, index) {
				let workType = this.list[index].workType || ""
				let workTypeList = ['住家', '不住家', '单餐', '育儿嫂', '月嫂', '护工', '陪读师', '管家', '钟点晚餐', '钟点保洁']
				let type = '暂无工作类型'
				let typeIndex = 0
				// 格式化工种
				for (let item of workTypeList) {
					typeIndex++
					if (workType.includes(item)) {
						type = item
						break
					}
				}
				if (typeIndex <= 3 && value == 0) {
					type += '保姆'
				}
				return type
			},
			formatTipsBox(index) {
				let data = this.nearPutList[this.tipsBoxIndex]
				if (!data) {
					return
				}

				let empName = data.realName
				let result = '恭喜' + empName + '已提审通过，期待早日上户分佣哦！'
				return result
			},
			// 格式化保姆等级
			formatLevel(level) {
				let result = ""
				if (level != null) {
					if (level > 1 && level < 6) {
						result = this.levelList[level - 1].text
					} else {
						result = "暂无"
					}
				}
				return result
			},
			formatStatus(index) {
				let item = this.list[index]
				let str = item.contractId != null ? "已开单" : "未开单"
				let str1 = ""
				let statusList = ['-正在找工作', '-已有工作', '-暂不找工作']
				let statusIndex = item.status != null ? parseInt(item.status) : 0
				str1 = statusList[statusIndex]
				return str + str1
			},
			// 获取最近上架的列表
			getNearPutList() {
				this.http({
					url: 'getBaomuPage',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						introducer: uni.getStorageSync("employeeNo") || "-1",
						merchantCode: uni.getStorageSync('merchantCode') || null,
						orderId: 0,
						state: 1,
						dateRange: this.tipsBoxDateRange,
						orderBy: "putTime DESC",
						current: 1,
						size: this.tipsBoxLimit
					},
					success: res => {
						if (res.code == 0) {
							this.nearPutList = res.data.records
							if (res.data.pages <= 1) {
								this.tipsBoxLimit = res.data.total
							}
						}
					}
				})
			},
			// 获取邀请人列表
			getList() {
				if (this.current == 0) {
					this.http({
						url: 'getBaomuPage',
						method: 'POST',
						hideLoading: true,
						data: this.searchCondition,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.total = res.data.total
								this.list = this.list.concat(res.data.records)
							}
						}
					})
				} else {

					this.http({
						url: 'getTrialStaffPage',
						method: 'POST',
						hideLoading: true,
						data: this.searchCondition1,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.total = res.data.total
								this.list = this.list.concat(res.data.records)
							}
						}
					})
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {

				}
			},
		},
		watch: {
			// employee页面开启
			loadMore: {
				handler(newValue, oldVal) {
					this.searchCondition.current++
					this.searchCondition1.current++
					this.getList()
				},
				deep: true
			},
			tipsBoxPlayTime: {
				handler(newValue, oldVal) {
					let speed = this.tipsBoxInterval
					if (this.tipsBoxPlayTime > 1) {
						// 控制消息气泡轮播
						if (this.tipsBoxPlayTime % speed > 0) {
							if (this.tipsBoxPlayTime % speed == 1) {
								this.tipsBoxIndex++
								if (this.tipsBoxIndex == this.tipsBoxLimit) {
									this.tipsBoxIndex = 0
								}
							}
							this.tipsBoxShow = true
						} else if (this.tipsBoxPlayTime % speed == 0) {
							this.tipsBoxShow = false
						}
					}
				},
				deep: true
			},
		},
		onReachBottom() {
			this.loadMore++
		},
		// 页面载入之后
		mounted() {
			this.memberName = uni.getStorageSync("memberName") || ''
			this.headImg = uni.getStorageSync("memberHeadImg") || ''
			this.getList()
			this.getNearPutList()
			let time = setInterval(() => {
				this.tipsBoxPlayTime += 1
			}, 1000);
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
		width: 100%;
	}

	// 邀请有奖-个人信息部分
	.header {
		width: 100%;
		height: 240rpx;
		// background-color: #FFE102;
	}

	// 头像部分
	.head-left {
		float: left;
		width: 30%;
		height: 100%;
	}

	.head-left img {
		display: block;
		margin: 40rpx auto;
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
	}

	// 个人信息部分
	.head-center {
		float: left;
		width: 50%;
		height: 80%;
		padding-top: 30rpx;
		display: flex;
		flex-direction: column;

		text {
			display: block;
			float: left;
			width: 80%;
			height: 60rpx;
			line-height: 60rpx;

			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			color: #fff;
		}
	}


	.head-right {
		float: left;
		width: 20%;
		height: 100%;
	}

	// 邀请按钮
	.head-right button {
		display: block;
		padding: 0 0;
		float: right;
		text-align: center;
		height: 50rpx;
		width: 120rpx;
		line-height: 50rpx;
		margin: 50rpx 40rpx 0 0;
		font-size: 26rpx;
		background-color: #fff;
		color: #1e1848;
	}

	// 按钮选项
	.button-row {
		// 垂直居中布局
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 0rpx;
		text-align: center;
		height: 120rpx;
		line-height: 40rpx;
	}

	.button-left,
	.button-right {
		width: 70%;
		height: 50rpx;
		line-height: 50rpx;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		color: #f6cc70;
		background-color: #1e1848;

		text {
			height: 50rpx;
			font-size: 32rpx;
		}
	}

	.log-list {
		width: 100%;
		height: auto;
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.tab-left-btn {
		text-align: center;
		margin: 20rpx auto;
		width: 100rpx;
		height: 50rpx;
		line-height: 50rpx;
		color: #fdd472;
		background-color: #1e1848;
		border-radius: 30rpx;
	}
</style>