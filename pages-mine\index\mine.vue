<template>
	<view class="page">

		<!-- #ifdef  MP-WEIXIN -->
		<!-- <u-gap height="90"></u-gap> -->
		<!-- #endif -->

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 头部 用户信息 -->
		<view class="user-head cf flac-row">
			<view class="w3">
				<img :src="headImg!=null&&headImg!=''?headImg:blankImg" @click="openAccount()" alt=""
					style="display: block;width: 120rpx;height: 120rpx;border-radius: 50%;margin: 0 auto;">
			</view>

			<view class="f18 w6">
				<view class="flac-row" v-if="isLogin">
					<text class="cfdd472">{{ employeeName||memberName||'暂无用户名'}}</text>
					<img v-if="memberLevel" @click="goPage('/pages-other/member/index')"
						:src="levelIconList[memberLevel-1]" alt=""
						style="display: block;width: 120rpx;height: 60rpx;margin-left: 10rpx;" />
				</view>
				<view @click="openLogin()" v-else>立即登录</view>
			</view>

			<view class="w2">
				<img :src="settingImg" alt="" @click="openSetting()" style="display: block;width: 50rpx;height: 50rpx;">
			</view>
		</view>
		<view class="w88 mg-at bacf c0 boxStyle flac-row-a" v-if="storeType!=3">
			<view class="flac-row-a" v-for="(item,i) in navList" :key="i">
				<image style="width: 60rpx;" :src="item.icon" mode="widthFix"></image>
				<view class="t-indent" @click="goPage(item.url)">
					<view class="f14 lh20">{{item.title}} ></view>
					<view class="c9 f12">{{item.text}}</view>
				</view>
				<view class="line" v-if="i != 2"></view>
			</view>
		</view>

		<!-- 菜单栏目列表 -->
		<view class="w88 mg-at menu-list">
			<view class="list-column" v-for="(menuList, index) in menuList" :key="index" @click="throttledHandleClick(index)"
				v-if="menuList.show">
				<image style="width: 30px;height: 30px;" :src="menuList.listImage"></image>
				<text class="column-text">{{menuList.listTitle}}</text>
				<text class="column-text-right">&ensp;❯</text>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout" v-if="isLogin">
			<text @click="tryLogout()">退出登录</text>
		</view>

		<view style="width: 100%;color: #909399;margin: 10rpx 0 0 40rpx;font-size: 32rpx;" v-if="introducerIdExisted">
			<text>推荐人：{{introducerIdExisted}}</text>
		</view>

		<u-gap height="80"></u-gap>

	</view>
</template>

<script>
	export default {
		name: 'mine',
		props: {
			refresh: {
				type: Boolean
			}
		},
		data() {
			return {
				// 可设置
				// 兔小巢产品id
				productId: 632126,
				unionMerchant: {},
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				storeType: uni.getStorageSync('storeType'),
				roleId: uni.getStorageSync("roleId") || 0,
				isLogin: false,
				//银行卡信息标识
				flagB: null,
				checkText: "",
				agent: '',
				introducerIdExisted: null,
				isEmployee: false,
				isBaomu: false,
				memberId: uni.getStorageSync('memberId') || null,
				employeeId: uni.getStorageSync('employeeId') || null,
				memberName: uni.getStorageSync('memberName') || "",
				employeeName: uni.getStorageSync('employeeName') || "",
				headImg: uni.getStorageSync("memberHeadImg"),
				memberLevel: null,
				index: null,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				settingImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-set.png",
				menuList: [{
						index: 0,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E5%90%88%E4%BC%99%E4%BA%BA.png',
						listTitle: '合伙人',
						listUrl: '/pages-mine/franchise/enter/topUpMoney',
						show: true,
					}, {
						index: 1,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jl.png',
						listTitle: '我的简历',
						listUrl: '/pages-mine/resume/resume',
						show: true,
					},
					{
						index: 2,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ms.png',
						listTitle: '我的面试',
						listUrl: '/pages-mine/interview/interview',
						show: true,
					},
					{
						index: 3,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-myjn.png',
						listTitle: '我的技能',
						listUrl: '/pages-mine/resume/ability',
						show: false,
					},
					{
						index: 4,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95.png',
						listTitle: '我的订单',
						listUrl: '/pages-mine/orders/orders',
						show: false,
					}, {
						index: 5,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E5%90%88%E5%90%8C.png',
						listTitle: '我的合同',
						listUrl: '/pages-mine/contract/contract',
						show: true,
					}, {
						index: 6,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%AF%81%E4%B9%A6.png',
						listTitle: '我的证书',
						listUrl: '/pages-mine/certificate/certificate',
						show: true,
					}, {
						index: 7,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E4%BD%B3%E5%B8%81.png',
						listTitle: '我的积分',
						listUrl: '/pages-mine/signIn/signin',
						show: true,
					},
					{
						index: 8,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E9%82%80%E8%AF%B7%E6%9C%89%E7%A4%BC.png',
						listTitle: '邀请有礼',
						listUrl: '/pages-mine/invitation/invitation',
						show: true,
					}, {
						index: 9,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-study.png',
						listTitle: '学习中心',
						listUrl: '/pages-mine/studyCenter/index',
						show: true,
					}, {
						index: 10,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%80%83%E8%AF%95.png',
						listTitle: '考试中心',
						listUrl: '/pages-mine/exam/exam-center',
						show: true,
					}, {
						index: 11,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-hd.png',
						listTitle: '活动中心',
						listUrl: '/pages-mine/activity/bobingshare',
						show: false,
					}, {
						index: 12,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E4%B8%9A%E5%8A%A1%E7%AE%A1%E7%90%86.png',
						listTitle: '业务管理',
						listUrl: '/pages-work/business/businessIndex',
						show: false,
					}, {
						index: 13,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E5%91%98%E5%B7%A5%E7%AE%A1%E7%90%86.png',
						listTitle: '员工管理',
						listUrl: '/pages-other/hr/index',
						show: false,
					},
					{
						index: 14,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_help_center.png',
						listTitle: '帮助中心',
						listUrl: '/pages-mine/helpCenter/helpIndex',
						show: true,
					},
					{
						index: 15,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E4%B8%9A%E5%8A%A1%E7%AE%A1%E7%90%86.png',
						listTitle: '直播中心',
						listUrl: '/pages-mine/room/liveCenter',
						show: false,
					},
					{
						index: 16,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_feedback_center.png',
						listTitle: '用户中心',
						listUrl: '/pages-mine/feedback/feedback',
						show: true,
					},
					{
						index: 17,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1710226356062抖音.png',
						listTitle: '数据中心',
						listUrl: '/pages-mine/feedback/feedback',
						show: true,
					},
					{
						index: 18,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_excitation.png',
						listTitle: '激励宝',
						listUrl: '/pages-other/excitation/index',
						show: false,
					},
					{
						index: 19,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E4%B8%9A%E5%8A%A1%E7%AE%A1%E7%90%86.png',
						listTitle: '工单',
						listUrl: '/pages-other/smartPartner/workOrder',
						show: false,
					},
					{
						index: 20,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1720423571871手机 (1).png',
						listTitle: '设备管理',
						listUrl: '/pages-other/device/customer',
						show: false,
					},
					{
						index: 21,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1724056089292潜在客户.png',
						listTitle: '潜客管理',
						listUrl: '/pages-other/potential/customer',
						show: false,
					},
					{
						index: 22,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1724832959712体检报告.png',
						listTitle: '体检中心',
						listUrl: '/pages-other/physical/index',
						show: true,
					},
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443817950029.png',
					// 	listTitle: '我的培训',
					// 	listUrl: '/pages-mine/train/train',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '工作手册',
					// 	listUrl: '/pages-mine/handbook/handbook',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '帮助中心',
					// 	listUrl: '/pages-mine/help/help',
					//  show: true,
					// }, {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443916814885.png',
					// 	listTitle: '建议反馈',
					// 	listUrl: '/pages-mine/feedback/feedback',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '小羽佳课堂',
					// 	listUrl: '/pages-mine/signIn/index',
					//  show: true,
					// },
				],
				navList: [{
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_order.png',
					title: '做任务',
					text: '签到有礼',
					url: '/pages-mine/signIn/taskPage'
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_xz.png',
					title: '领勋章',
					text: '家政新星',
					url: '/pages-mine/signIn/medalPage'
				}, {
					icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_jb.png',
					title: '去商城',
					text: '积分兑换',
					url: '/pages-mine/integralMall/productIndex'
				}],
				levelIconList: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag01.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag02.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag03.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag04.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/hy_tag05.png',
				],
				isAuth: 0,
				throttleTimeout: null, // 用于存储setTimeout的ID  
			}
		},
		methods: {
			 // 包裹了节流逻辑的点击事件处理函数  
			    throttledHandleClick(index) {
					this.index = index
			      // 使用箭头函数来保持this的上下文  
			      const throttledFunc = this.throttle(this.handleClick, 100); // 1000毫秒内只执行一次  
			      throttledFunc();  
			    },
			// 节流函数  
			    throttle(func, limit) {  
			      let lastFunc;  
			      let lastRan;  
			      return function() {  
			        const context = this;  
			        const args = arguments;  
			        if (!lastRan) {  
			          func.apply(context, args);  
			          lastRan = Date.now();  
			        } else {  
			          clearTimeout(lastFunc);  
			          lastFunc = setTimeout(function() {  
			            if ((Date.now() - lastRan) >= limit) {  
			              func.apply(context, args);  
			              lastRan = Date.now();  
			            }  
			          }, limit - (Date.now() - lastRan));  
			        }  
			      }  
			    },  
				 // 需要被节流的点击事件处理函数  
				    handleClick() {  
					  let  index = this.index 
				      let url = this.menuList[index].listUrl
				      // 未登录情况-可允许在不登录情况下查看的菜单索引
				      let allowNoLoginMenu = [4, 6, 7, 10, 11, 14]
				      if (!this.isLogin) {
				      	if (allowNoLoginMenu.findIndex(item => item == index) == -1) {
				      		return uni.navigateTo({
				      			url: "/pages-mine/login/notlogin?url=" + url
				      		})
				      	}
				      }
				      if (index == 0) {
				      	this.goToWork()
				      }
				      if (index == 12) {
				      	let tmpIds = [];
				      	// if (uni.getStorageSync("employeeType") == 10 || uni.getStorageSync("roleId") == 1) {
				      	// 	取消订单
				      	// 	tmpIds.push("YRSv7oviwbjl0U-fo-ghtq6OUG2Zn4u-pqd6Y9ums90")
				      	// 	接单
				      	// 	tmpIds.push("U2NPysqnHSDt_1oVl1Nvbwy_vivhiRfrlHqwwEI3xEQ")
				      	// 	服务评价
				      	// 	tmpIds.push("x64aI96dYKraSdcHcCTSh7sS0Bdvx8rRfzo3TU2RXK0")
				      	// }
				      
				      	if (uni.getStorageSync("employeeType") == 20 || uni.getStorageSync("roleId") == 1) {
				      		// 活动通知
				      		tmpIds.push("ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI")
				      		// 获取订阅消息授权
				      		// #ifdef  MP-WEIXIN
				      		wx.requestSubscribeMessage({
				      			tmplIds: tmpIds,
				      			success: res => {
				      				console.log("用户同意进行小程序消息订阅！")
				      			},
				      			fail: res => {}
				      		})
				      		// #endif
				      	}
				      }
				      // 直接跳转到兔小巢（若注释掉则先跳转到用户中心页面）
				      if (index == 16) {
				      	return this.jumpToTxc(1, '', '')
				      }
				      // 跳转谷雨有数
				      if (index == 17) {
				      	let path = ""
				      	if (uni.getStorageSync('employeeNo')) {
				      		path = 'pages/dateIndex?employeeNo=' + uni.getStorageSync('employeeNo')
				      	} else {
				      		path = 'pages/dateIndex'
				      	}
				      	return wx.navigateToMiniProgram({
				      		appId: 'wx0bacb3edec2f4cbd', // pord
				      		path: path,
				      		envVersion: 'release',
				      		success(res) {
				      			// 打开成功
				      		}
				      	})
				      }
				      if (index != 0) {
				      	uni.navigateTo({
				      		url: url
				      	});
				      }
				      // 在这里编写你的点击处理逻辑  
				    },  
			getMemberData() {
				this.http({
					url: "getMemberById",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.isAuth = res.data.isAuth
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 打开登录
			openLogin() {
				uni.reLaunch({
					url: '/pages-mine/login/login'
				});
			},
			// 打开个人信息
			openAccount() {
				// 未登录的情况
				if (!this.isLogin) {
					return uni.navigateTo({
						url: "/pages-mine/login/notlogin"
					})
				}

				// uni.navigateTo({
				// 	url: '/pages-mine/resume/account'
				// });
				uni.navigateTo({
					url: '/pages-mine/mine-detail/setting'
				});
			},
			// 打开设置页面
			openSetting() {
				// 未登录的情况
				if (!this.isLogin) {
					return uni.navigateTo({
						url: "/pages-mine/login/notlogin"
					})
				}

				uni.navigateTo({
					url: "/pages-mine/mine-detail/setting"
				});
			},
			// 打开对应的详细页面
			openMineDetail(index) {
				
			},
			goToWork() {
				//商户信息标识  1：不存在 2:存在
				let ifMerchant = 1
				let franchiseMsg = {}
				//如果是员工并开通体验权限就生成商户信息
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/unionMerchant/getIfEmployeeByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						agent: this.agent,
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							//返回值为2  则生成商户信息成功或存在商户信息
							ifMerchant = res.data
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})

				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							franchiseMsg = res.data
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})

				setTimeout(() => {

					let now = new Date();
					let validity;
					if (this.unionMerchant) {
						validity = new Date(this.unionMerchant.validity)
					}
					let url = '/pages-mine/franchise/enter/topUpMoney'
					//生成商户信息成功或存在商户信息 直接进入工作台
					if (!uni.getStorageSync("employeeNo") && !franchiseMsg.bankReservedPhone && franchiseMsg
						.franchiseId && ifMerchant != 2) {
						url = "/pages-mine/franchise/enter/platformAudit"
					} else if (ifMerchant == 2) {
						url = "/pages-work/index"
					} else
						//已付款且不存在银行卡信息、存在商户信息 则跳转平台审核页
						if ((franchiseMsg.orderState == 1 && franchiseMsg.franchiseId) ||
							franchiseMsg.orderState == 1 && franchiseMsg.authenticationType == 1) {
							url = "/pages-mine/franchise/enter/platformAudit"
						} else
							//已付款不存在商户信息跳转主体页面
							if ((franchiseMsg.orderState == 1 && !franchiseMsg.franchiseId) || this.isAuth == 1) {
								url = "/pages-mine/franchise/enter/bodyMsg?authFlag=" + this.isAuth
							}

					uni.navigateTo({
						url: url
					});
				}, 700)
			},
			// 跳转到兔小巢
			jumpToTxc(value, path, url) {
				if (value == 0) {
					return
				}
				// #ifndef MP-WEIXIN
				url = url || 'https://txc.qq.com/embed/phone/PRODUCT_ID'
				let data = JSON.stringify({
					url: url.replace('PRODUCT_ID', this.productId)
				})
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
				// #ifdef MP-WEIXIN
				let data = {
					appId: "wx8abaf00ee8c3202e",
					extraData: {
						id: this.productId,
						customData: {
							customInfo: 'memberId/' + this.memberId + '*phone/' + uni.getStorageSync('account') +
								'*employeeNo/' + this.employeeNo || '-' +
								'*realName/' + (this.employeeName || this.memberName),
						}
					},
				}
				if (path) {
					this.$set(data, 'path', path)
				}
				wx.openEmbeddedMiniProgram(data)
				// #endif
			},
			// 退出登录
			tryLogout() {
				this.openCheck(0, "确定退出登录吗？", "下次需要重新登录哦～")
			},
			// 退出登录
			logout() {
				// 清除用户数据并返回到登录页
				this.$toast.toast('退出成功！')
				if (uni.getStorageSync('employeeId')) {
					//更新员工token
					this.http({
						url: 'updateEmployeeToken',
						method: 'POST',
						data: {
							employeeId: uni.getStorageSync('employeeId')
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						hideLoading: true,
						success: res => {

						}
					})
				}
				uni.clearStorage()
				let timer = setTimeout(() => {
					uni.redirectTo({
						url: "/pages-mine/login/login"
					})
				}, 500);
			},
			// 检查员工状态
			checkState() {
				if (uni.getStorageSync("isInvited") !== null) {
					if (uni.getStorageSync("isInvited") == true) {
						return true
					}
				}
				let employeeState = uni.getStorageSync("employeeState")
				if (employeeState != -1) {
					return true
				} else {
					return false
				}
			},
			// 检查员工身份
			checkEmployee() {
				let roleId = uni.getStorageSync('roleId')
				// 测试时加上
				// return
				this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
				this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				let employeeType = uni.getStorageSync("employeeType")
				console.log("是否员工：", this.isEmployee, "是否保姆：", this.isBaomu)
				if (this.isEmployee) {
					if (!this.isBaomu) {
						this.menuList[1].show = false
						this.menuList[2].show = false
						this.menuList[3].show = false
					}
				}
				if (uni.getStorageSync('storeType') == 3) {
					this.menuList[9].show = false
					this.menuList[6].show = false
					this.menuList[5].show = false
					this.menuList[7].show = false
					this.menuList[10].show = false
					this.menuList[14].show = false
				}

				// 标准单员工
				if (employeeType == 10 && !this.isBaomu) {
					// 不显示合伙人
					this.menuList[0].show = false
				}

				// 标准单员工或管理员
				if ((employeeType == 10 && !this.isBaomu) || this.roleId == 1) {
					// 显示业务管理
					this.menuList[12].show = true
				}

				// 行政员工
				if (employeeType == 20) {
					// 显示激励宝
					this.menuList[18].show = true
				}

				// 客服开启设备管理
				if (roleId == 1 || roleId == 3 || roleId == 12) {
					this.menuList[20].show = true
					this.menuList[21].show = true
				}

				if (roleId == 1 || roleId == 42 ||
					roleId == 66 || roleId == 100 ||
					roleId == 106 || roleId == 78 ||
					roleId == 12 || roleId == 67 ||
					roleId == 4 || roleId == 2 ||
					roleId == 9 || roleId == 57) {
					this.menuList[19].show = true
				}

				// 规避审核人员
				if (!this.checkState()) {
					this.menuList[1].show = false
					this.menuList[2].show = false
					this.menuList[3].show = false
				}

				this.checkHRBPPower()
			},
			// 校验员工是否符合HRBP权限（员工管理）
			checkHRBPPower() {
				return
				let showHRBPP = uni.getStorageSync("showHRBPP") || false
				// 不缓存
				showHRBPP = false
				if (showHRBPP) {
					this.menuList[13].show = true
				} else {
					this.http({
						url: "checkHRBPPower",
						method: 'POST',
						data: {
							id: this.employeeId
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								if (res.data) {
									this.menuList[13].show = true
									uni.setStorageSync("showHRBPP", true)
								} else {
									uni.setStorageSync("showHRBPP", false)
								}
							} else {
								uni.setStorageSync("showHRBPP", false)
							}
						},
					})
				}
			},
			// 尝试从登录后的缓存中获取用户信息
			getMemberInfor() {
				this.introducerIdExisted = uni.getStorageSync("introducerIdExisted") || ''
				this.memberName = uni.getStorageSync("memberName")
				this.memberHeadImg = uni.getStorageSync("memberHeadImg")
				this.checkEmployee()
				this.checkAuthIdPower()
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：确认退出
				if (this.checkType == 0) {
					this.logout()
				}
			},
			getUnionMerchantData() {
				this.http({
					url: "getUnionMerchantData",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId") || 0
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.unionMerchant = res.data
							this.memberLevel = res.data.memberLevel || null
							uni.setStorageSync("unionMerchantId", res.data.id)
							uni.setStorageSync("merchantCode", res.data.merchantCode)
						}
					}
				})
			},
			checkAuthIdPower() {
				this.http({
					url: 'checkAuthIdPower',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.employeeId,
						memberId: this.memberId,
						merchantCode: uni.getStorageSync('merchantCode') || ''
					},
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync("authId", res.data || 0)
						}
					}
				});
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast("您还未进行登录哦，登录后可体验更多功能！")
					uni.setStorageSync('redirectUrl', '/pages/index/index')
					this.menuList[1].show = false
					this.menuList[2].show = false
					// setTimeout(() => {
					// 	uni.redirectTo({
					// 		url: '/pages-mine/login/login'
					// 	})
					// }, 2000)
					return false
				} else {
					this.isLogin = true
					this.getMemberInfor()
					this.getMemberData()
					return true
				}
			}
		},
		watch: {
			// 监听返回操作并刷新页面数据
			refresh: {
				handler(newValue, oldVal) {
					this.getMemberInfor()
				},
				deep: true
			}
		},
		onShow() {
			this.getMemberInfor()
		},
		mounted() {
			this.checkLogin()
			this.getUnionMerchantData()
		}
	}
</script>

<style lang="scss">
	.boxStyle {
		border-radius: 20rpx;
		// border-top-right-radius: 30rpx;
		padding: 40rpx 20rpx;
		margin: auto auto 30rpx;
	}

	.line {
		width: 2rpx;
		height: 100rpx;
		background-color: #333;
		margin-left: 20rpx;
	}

	.page {
		width: 100%;
		min-height: 100vh;
		background-color: #f0f0f0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_bg2.png') no-repeat center;
		background-size: 100% 105%;
	}

	// .header {
	// 	width: 100%;
	// 	height: 360rpx;
	// 	box-shadow: 0 4rpx 20rpx #dedede;
	// 	margin-bottom: 20rpx;
	// 	padding: 20rpx 0;
	// 	border-bottom-left-radius: 30%;
	// 	border-bottom-right-radius: 30%;
	// }

	// 用户信息
	.user-head {
		display: flex;
		width: 100%;
		height: 200rpx;
	}

	.user-info {
		width: 60%;
		height: 100%;

		text {
			position: relative;
			left: 40rpx;
			top: 80rpx;
			display: block;
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			font-size: 36rpx;
		}
	}

	.user-name {
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 10rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	// 课程资料
	.course-info {
		display: flex;
		width: 90%;
		height: 180rpx;
		margin: 20rpx auto;
		border-radius: 20rpx;
		border: #f4f4f5 2px solid;
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.course-left,
	.course-right {
		width: 50%;
		height: 100%;
	}

	.left-content,
	.right-content {
		width: 100%;
		height: 50%;
		margin: 12% auto;
	}

	.course-left view {
		border-right: #dedede 1px solid;
	}

	.text-head {
		display: block;
		width: 100%;
		height: 40rpx;
		text-align: center;
		margin: 0;
	}

	.course-time {
		font-size: 40rpx;
		font-weight: bold;
	}

	.course-unit {
		font-size: 32rpx;
		margin-left: 10rpx;
	}

	.course-text {
		display: block;
		font-size: 36rpx;
		text-align: center;
		margin-top: 10rpx;
	}

	// 菜单列表
	.menu-list {
		display: block;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		background-color: #fff;
		padding: 0 20rpx;
		border-radius: 20rpx;
	}

	.list-column {
		width: 100%;
		height: 120rpx;
	}

	.list-column image {
		float: left;
		margin: 32rpx 0 0 40rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.column-text {
		line-height: 120rpx;
		margin: 0 0 0 20rpx;
		font-size: 32rpx;
	}

	.column-text-right {
		float: right;
		margin-right: 40rpx;
		line-height: 120rpx;
		color: #909399;
	}

	.logout {
		width: 100%;
		height: 100rpx;
		border: #909399;
		box-shadow: 0 4rpx 20rpx #dedede;

		text {
			display: block;
			text-align: center;
			width: 40%;
			height: 100rpx;
			margin: 0 auto;
			line-height: 100rpx;
			color: #1e1848;
			font-size: 36rpx;
		}
	}
</style>