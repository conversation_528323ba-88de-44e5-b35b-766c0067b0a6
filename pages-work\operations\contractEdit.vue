<template>
	<view class="w10 h10">
		<u-notify ref="uNotify"></u-notify>
		<view v-if="dom.contractType == 1">
			<u-steps :current="active" dot activeColor="#1e1848">
				<u-steps-item title="关联月嫂"></u-steps-item>
				<u-steps-item title="关联客户"></u-steps-item>
				<u-steps-item title="合同信息"></u-steps-item>
			</u-steps>
		</view>
		<view v-else-if="dom.contractType == 6">
			<u-steps :current="active" dot activeColor="#1e1848">
				<u-steps-item title="关联保姆"></u-steps-item>
				<u-steps-item title="关联客户"></u-steps-item>
				<u-steps-item title="合同信息"></u-steps-item>
				<u-steps-item title="基本信息"></u-steps-item>
			</u-steps>
		</view>
		<view v-else>
			<u-steps :current="active" dot activeColor="#1e1848">
				<u-steps-item title="关联保姆"></u-steps-item>
				<u-steps-item title="关联客户"></u-steps-item>
				<u-steps-item title="合同信息"></u-steps-item>
				<u-steps-item title="基本信息"></u-steps-item>
				<u-steps-item title="放假规则"></u-steps-item>
			</u-steps>
		</view>

		<u-gap height="10"></u-gap>

		<view class="cell-body" v-show="active==0">
			<uni-section type="line" title="关联保姆" sub-title="输入保姆姓名即可关联保姆"></uni-section>
			<u-search placeholder="请输入保姆姓名" v-model="keywordbaomu" @custom="searchbm"></u-search>
			<uni-section type="line" title="乙方姓名">
				<u-input placeholder="乙方(保姆)姓名" v-model="dom.employeeName" border="bottom" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="乙方身份证">
				<u-input placeholder="乙方(保姆)身份证" border="bottom" v-model="dom.employeeCarId" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="乙方电话">
				<u-input placeholder="乙方(保姆)电话" border="bottom" v-model="dom.employeePhone" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="乙方地址">
				<u-input placeholder="乙方(保姆)地址" border="bottom" v-model="dom.employeeAdress" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
		<u-text type="error"   text="注:更换阿姨请用搜索框搜索对应阿姨数据"></u-text>
		</view>

		<view class="cell-body" v-show="active==1">
			<uni-section type="line" title="关联客户" sub-title="输入客户手机号即可关联以下信息"></uni-section>
			<u-search placeholder="请输入客户手机号" v-model="keywordcustomer" @custom="searchcs"></u-search>
			<uni-section type="line" title="客户名称">
				<u-input placeholder="甲方(客户)姓名" v-model="dom.memberName" border="bottom" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="甲方身份证">
				<u-input placeholder="甲方(客户)身份证" v-model="dom.memberCardId" border="bottom" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="甲方电话">
				<u-input placeholder="甲方(客户)电话" border="bottom" v-model="dom.memberPhone" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="甲方地址">
				<u-input placeholder="甲方(客户)地址" border="bottom" v-model="dom.memberAdress" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<u-text type="error"   text="注:更换客户请用搜索框搜索客户数据,搜不到请让客户去小羽佳小程序授权登录一下"></u-text>
		</view>

		<view class="cell-body" v-show="active==2">
			<uni-section type="line" title="劳务报酬(元)">
				<u-input placeholder="劳务报酬" border="bottom" type="number" v-model="dom.servicePay"
					suffixIcon="arrow-right"></u-input>
			</uni-section>
			<view v-if="dom.contractType == 1">
				<uni-section type="line" title="定金(元)">
					<u-input placeholder="输入金额自动转换大写" border="bottom" v-model="dom.earnestMoney"
						suffixIcon="arrow-right" @blur="rmbToggleCase(1)"></u-input>
				</uni-section>
				<uni-section type="line" title="尾款支付时间" sub-title="点击标题修改时间" @click="showpayEndDateTime = true">
					<u-input v-model="dom.payEndDate" placeholder="尾款支付时间" border="bottom" suffixIcon="arrow-right"
						:disabled="true"></u-input>
					<u-datetime-picker :show="showpayEndDateTime" mode="date" @cancel="showpayEndDateTime = false"
						:closeOnClickOverlay="true" @close="showpayEndDateTime = false" :minDate="1577808000000"
						@confirm="confirmPayEndTime"></u-datetime-picker>
				</uni-section>
			</view>
			<view v-if="dom.contractType != 1">
				<uni-section type="line" title="中介费(元)">
					<u-input placeholder="中介费(输入金额自动转换大写)" border="bottom" v-model="dom.inPay"
						suffixIcon="arrow-right" @blur="rmbToggleCase(2)"></u-input>
				</uni-section>
				<uni-section type="line" title="服务内容">
					<u-checkbox-group v-model="serviceTypes" placement="column" @change="changeServiceContent"
						activeColor="#ffdd0b">
						<u-checkbox :customStyle="{marginBottom: '8px',marginLeft: '10px'}"
							v-for="(item, index) in serviceTypeOptions" :key="index" :label="item.name"
							:name="item.name">
						</u-checkbox>
					</u-checkbox-group>
				</uni-section>
				<uni-section type="line" title="服务内容(其他)">
					<u-input placeholder="服务内容" border="bottom" v-model="dom.serviceContentRemark"
						suffixIcon="arrow-right">
					</u-input>
				</uni-section>
				<uni-section type="line" title="服务方式">
					<u-input placeholder="住家/不住家...." border="bottom" v-model="dom.serviceType"
						suffixIcon="arrow-right">
					</u-input>
				</uni-section>
				<uni-section type="line" title="服务次数(/年)">
					<u-input placeholder="不限/365天..." border="bottom" v-model="dom.serviceNum" suffixIcon="arrow-right">
					</u-input>
				</uni-section>
			</view>
			<uni-section type="line" title="服务最大天数">
				<u-input placeholder="服务最大天数(无请填写/)" border="bottom" v-model="dom.serviceMax" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="服务开始时间" sub-title="点击标题修改时间" @click="showServiceStartTime = true">
				<u-input v-model="dom.serviceStarDate" placeholder="服务开始时间" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showServiceStartTime" mode="date" @cancel="showServiceStartTime = false"
					:closeOnClickOverlay="true" @close="showServiceStartTime = false" :minDate="minDate"
					@confirm="confirmServiceStartTime"></u-datetime-picker>
			</uni-section>
			<uni-section type="line" title="服务结束时间" sub-title="点击标题修改时间" @click="showServiceEndTime = true">
				<u-input v-model="dom.serviceEndDate" placeholder="服务结束时间" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showServiceEndTime" mode="date" @cancel="showServiceEndTime = false"
					:minDate="minDate" :closeOnClickOverlay="true" @close="showServiceEndTime = false"
					@confirm="confirmServiceEndTime">
				</u-datetime-picker>
			</uni-section>
			<uni-section type="line" title="发放日期" v-if="dom.contractType == 2">
				<u-input placeholder="每月分期支付日期" border="bottom" suffixIcon="arrow-right" v-model="dom.specifyDate"
					type="number">
				</u-input>
			</uni-section>
			<uni-section type="line" title="支付日期" v-if="dom.contractType == 4">
				<u-input placeholder="每月劳务费发放日期" border="bottom" suffixIcon="arrow-right" v-model="dom.specifyDate"
					type="number">
				</u-input>
			</uni-section>
			<uni-section type="line" title="核算日期" v-if="dom.contractType == 2">
				<u-input placeholder="每月劳务费核算日期" v-model="dom.auditDate" border="bottom" suffixIcon="arrow-right"
					type="number"></u-input>
			</uni-section>
			<!-- <view v-if="dom.contractType != 1">
				<uni-section type="line" title="保险">
					<u-checkbox-group v-model="insures" placement="column" @change="changeInsures"
						activeColor="#ffdd0b">
						<u-checkbox :customStyle="{marginBottom: '8px',marginLeft: '10px'}"
							v-for="(item, index) in insureOptions" :key="index" :label="item.name" :name="item.name">
						</u-checkbox>
					</u-checkbox-group>
				</uni-section>
				<uni-section type="line" title="保险(其他)">
					<u-input placeholder="" border="bottom" v-model="dom.insureRemark"
						suffixIcon="arrow-right"></u-input>
				</uni-section>
			</view> -->
			<uni-section type="line" title="合同补充条款">
				<u-textarea v-model="dom.addendum" placeholder="请输入内容" autoHeight maxlength="200"></u-textarea>
			</uni-section>
			<u-gap height="40"></u-gap>
			<view v-if="dom.contractType === 1">
				<uni-section type="line" title="服务内容">
					<u-checkbox-group v-model="serviceTypes" placement="column" @change="changeServiceContent"
						activeColor="#ffdd0b">
						<u-checkbox :customStyle="{marginBottom: '8px',marginLeft: '10px'}"
							v-for="(item, index) in yuesaoOptions" :key="index" :label="item.name"
							:name="item.name">
						</u-checkbox>
					</u-checkbox-group>
				</uni-section>
				<uni-section type="line" title="服务内容(其他)">
					<u-input placeholder="服务内容" border="bottom" v-model="dom.serviceContentRemark"
						suffixIcon="arrow-right">
					</u-input>
				</uni-section>
			</view>
		</view>


		<view class="cell-body" v-show="active==3">
			<uni-section type="line" title="服务对象户型">
				<u-input placeholder="平层/高层..." border="bottom" v-model="dom.homeDetail" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="总家庭成员人数">
				<u-input  border="bottom" type="number" v-model="dom.homeAllPerson" suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="家庭成员(成人人数)">
				<u-input border="bottom" type="number" v-model="dom.homePerson" suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="家庭成员(孩子人数)">
				<u-input border="bottom" type="number" v-model="dom.homeChildren" suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="面积(m²)" v-if="dom.contractType !== 6">
				<u-input  border="bottom" type="number" v-model="dom.homeArea" suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="宠物(数量)" v-if="dom.contractType !== 6">
				<u-input  border="bottom" type="number" v-model="dom.homePets"  suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="健康状况" v-if="dom.contractType == 6">
				<u-input border="bottom" v-model="dom.homeHealth" suffixIcon="arrow-right"></u-input>
			</uni-section>
			<uni-section type="line" title="其他">
				<u-input border="bottom" suffixIcon="arrow-right" v-model="dom.homeRemark"></u-input>
			</uni-section>
		</view>

		<view class="cell-body" v-show="active==4">
			<view v-for="(h,index) in holidayList" style="color: #969799" :key="index"
				:class="dom.holidayId==h.id?'text-box-act':'text-box'" @click="dom.holidayId=h.id">
				<view>周休息天数：<text class="text-show">{{h.weekday}}天</text></view>
				<view>月工资天数：<text class="text-show">{{h.mouthday}}天</text></view>
				<view>节假日休息天数：<text class="text-show">{{h.holiday}}天</text></view>
				<view>国庆休息天数：<text class="text-show">{{h.nationalday==-1?'依法定':h.nationalday+'天'}}</text></view>
				<view>春节休息天数：<text class="text-show">{{h.springday==-1?'依法定':h.springday+'天'}}</text></view>

			</view>
		</view>

		<u-popup :show="showPopupbm" @close="showPopupbm = false">
					<u-cell-group v-for="(item,index) in searchList" :key="index">
						<u-cell :title="item.realName+' | '+item.no" isLink @click="changeBaoMu(item)"></u-cell>
					</u-cell-group>
				</u-popup>
				<u-popup :show="showPopupmb" @close="showPopupmb = false">
					<u-cell-group v-for="(item,index) in searchList" :key="index">
						<u-cell :title="item.name+'|'+item.bindTel" isLink @click="changeMember(item)"></u-cell>
					</u-cell-group>
				</u-popup>
		<u-gap height="80"></u-gap>
		<u-button text="上一步" :customStyle="btnStyle1" @click="beforeStep" v-if="active !== 0"></u-button>
		<u-gap height="5"></u-gap>
		<u-button :text="nextText" :customStyle="btnStyle" @click="nextStep"></u-button>
		<u-modal @confirm="showModal = false" :show="showModal" title="操作须知" content='依据合同具有法律效应,填写客户信息和阿姨信息必须提供真实姓名丶身份证丶地址,否则签署会有异常! \n 合同服务时间必须大于今天,且合同状态必须属于生效中或客户当天签署才会生效上保险' confirmText="我已知晓"></u-modal>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化
	moment.locale('zh-cn');
	export default {
		data() {
			return {
				btnStyle1: {
					color: '#f6cc70',
					backgroundColor: '#1e1848',
					borderRadius: '0!important',
					borderColor: '#1e1848',
					position: 'fixed',
					bottom: '110rpx',
					left: 0
				},
				btnStyle: {
					color: '#f6cc70',
					backgroundColor: '#1e1848',
					borderRadius: '0!important',
					borderColor: '#1e1848',
					position: 'fixed',
					bottom: 0,
					left: 0
				},
				minDate:'1577808000000',
				showModal:true,
				showpayEndDateTime: false,
				showPopupbm: false,
				showPopupmb: false,
				showServiceStartTime: false,
				nowDate: Number(new Date()),
				showServiceEndTime: false,
				value1: '',
				value2: '',
				active: 0,
				keywordbaomu: '',
				keywordcustomer: '',
				searchList: [],
				nextText: '下一步',
				yuesaoOptions:[{
					name: '服务26天'
				},{
					name: '服务28天'
				},{
					name: '服务30天'
				},{
					name: '服务35天'
				},{
					name: '服务42天'
				},{
					name: '服务52天'
				}],
				serviceTypeOptions: [{
					name: '家庭卫生打扫'
				}, {
					name: '婴、幼儿照护'
				}, {
					name: '婴幼儿教育'
				}, {
					name: '产妇与新生儿护理'
				}, {
					name: '老人照护'
				}, {
					name: '病人陪护'
				}, {
					name: '计时服务'
				}, {
					name: '家庭餐制作'
				}],
				insureOptions: [{
					name: '《家政人员雇主责任险》'
				}, {
					name: '《家政服务公众责任险》'
				}, ],
				serviceTypes: [],
				insures: [],
				holidayList: [],
				billNo: '',
				employeeNo: '',
				orderNeedId: '',
				dom: {
					no: null,
					id: null,
					memberId: null,
					employeeId: null,
					memberName: null,
					employeeName: null,
					memberCardId: null,
					employeeCarId: null,
					memberPhone: null,
					employeePhone: null,
					memberAdress: null,
					employeeAdress: null,
					agentId: uni.getStorageSync('employeeId'),
					agentName: uni.getStorageSync('employeeName'),
					createDate: null,
					serviceStarDate: null,
					serviceEndDate: null,
					serviceType: null,
					servicePay: null,
					serviceContent: null,
					serviceContentRemark: '无',
					homeDetail: null,
					homeArea: 0,
					homeAllPerson: 0,
					homePerson: 0,
					homeChildren: 0,
					homeHealth: null,
					homePets: 0,
					homeRemark: '无',
					inPay: null,
					serviceNum: null,
					serviceMax: null,
					insures: '《家政人员团体意外险》,《家政服务公众责任险》',
					insureRemark: '',
					insureBy: '丙方',
					memberSignDate: null,
					employeeSignDate: null,
					updateDate: null,
					status: '0',
					client: null,
					clientDate: null,
					orderId: null,
					holidayId: null,
					contractType: null,
					specifyDate: null,
					auditDate: null,
					addendum: null
				}
			};
		},
		onLoad(option) {
			// this.flowStatus = option.flowStatus
			// this.orderNeedId = option.orderNeedId
			// this.orderName = option.orderName
			this.employeeNo = option.employeeNo
			// this.agentName = option.agentName
			// this.phone = option.phone
			// this.billNo = option.billNo
			// this.orderPhone = option.orderPhone
			// this.dom.memberAdress = option.street
			// this.dom.memberPhone = option.orderPhone
			if (option.orderNeedId) {
				this.getOrderNeedsInfo(option.orderNeedId);
			}

			if (option.id) {
				this.getContractById(option.id);
			}
			//创建的合同时间默认
			// this.dom.serviceEndDate = moment().format('YYYY-MM-DD');
			this.dom.serviceStarDate = moment().format('YYYY-MM-DD');
			this.dom.serviceEndDate = moment().add(1, 'year').format('YYYY-MM-DD');
			this.minDate = moment().subtract(1,"month").valueOf();
			// console.log(moment().add(1,'year').format('YYYY-MM-DD'))
			this.getListContract();
			this.setBaoMuMsg();
			const type = option.contractType;
			if (type) {
				this.dom.contractType = type;
				if (type == 2 || type == 1) {
					this.dom.insureBy = '乙方';
				}

				if(type == 6) {
					let arr = [{name:'语文'},{name:'数学'},{name:'英语'},{name:'小学全科'},{name:'初中全科'},{name:'高中全科'}]
					this.serviceTypeOptions = arr;
				}
				if(type == 7) {
					let arr = [{name:'陪伴3-5岁'},{name:'陪伴5-8岁'},{name:'陪伴8-12岁'}]
					this.serviceTypeOptions = arr;
				}
			}
		},
		methods: {
			rmbToggleCase(type){
				let money = this.dom.inPay
				if(type==1){
					money = this.dom.earnestMoney
				}
				this.http({
					url: "rmbToggleCase",
					data: {
						money: money
					},
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.dom.earnestMoney = res.data
							if(type==1){
								this.dom.earnestMoney = res.data
							}else{
								this.dom.inPay = res.data
							}
						}
					}
				})
			},
			getOrderNeedsInfo(needId) {
				this.http({
					url: "getOrderNeedsById",
					data: {
						orderNeedsId :needId
					},
					method: 'get',
					success: res => {
						if (res.code == 0 && res.data.id) {
							let result = res.data.orderNeeds;
							this.flowStatus = result.flowStatus
							this.orderNeedId = result.id
							this.dom.memberName = result.name
							this.dom.billNo = result.billNo
							this.orderPhone = result.orderPhone
							this.dom.memberAdress = result.street
							this.dom.memberPhone = result.phone
							this.dom.servicePay = result.salary
						} else {
							return uni.showToast({
								title: '找不到对应线索信息',
								icon: 'none'
							})
						}
					}
				})
			},
			setBaoMuMsg() {
				if (this.employeeNo) {
					this.dom.billNo = this.billNo
					this.http({
						url: 'searchBaomuList',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: {
							no: this.employeeNo,
							current: 1,
							size: 10
						},
						success: (res) => {
							if (res.data.records.length > 0) {
								this.dom.employeeName = res.data.records[0].realName
								this.dom.employeeCarId = res.data.records[0].idcard
								this.dom.employeePhone = res.data.records[0].phone
								this.dom.employeeAdress = res.data.records[0].hometown
								this.dom.employeeId = res.data.records[0].id
							} else {
								return uni.showToast({
									title: '找不到对应保姆信息',
									icon: 'none'
								})
							}

						}
					})
				}
			},
			searchbm(e) {
				this.http({
					url: 'searchBaomuList',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						realName: e,
						current: 1,
						size: 10
					},
					success: (res) => {
						if (res.data.records.length > 0) {
							this.searchList = res.data.records;
							this.showPopupbm = true;
						} else {
							return uni.showToast({
								title: '找不到对应保姆信息',
								icon: 'none'
							})
						}

					}
				})
				console.log(e)

			},
			searchcs(e) {
				this.searchList = [];
				this.http({
					url: 'selectMemberList',
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						bindTel: e,
					},
					success: (res) => {
						if (res.data.length > 0) {
							this.searchList = res.data;
							this.showPopupmb = true;
						} else {
							return uni.showToast({
								title: '找不到对应会员信息,请让用户登录小羽佳小程序授权登录后重试',
								icon: 'none'
							})
						}

					}
				})
			},
			beforeStep() {
				this.active --;
				this.nextText = '下一步';
			},
			validateIdCard(idCard) {
			    const reg = /^([1-9]\d{5}[12]\d{3}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx])$/;

			    if (!reg.test(idCard)) {
			        return false; // 初步过滤不通过
			    }

			    // 检查校验码
			    if (!this.checkLastDigit(idCard)) {
			        return false;
			    }

			    // 简单的地区码和日期有效性检查（这里只做格式上的检查）
			    const birthYear = idCard.substring(6, 10);
			    const birthMonth = idCard.substring(10, 12);
			    const birthDay = idCard.substring(12, 14);

			    const birthday = new Date(`${birthYear}-${birthMonth}-${birthDay}`);
			    if (birthday.getFullYear() != parseInt(birthYear) || birthday.getMonth() + 1 != parseInt(birthMonth) || birthday.getDate() != parseInt(birthDay)) {
			        return false; // 出生日期无效
			    }

			    return true;
			},
			checkLastDigit(idCard) {
			    const parityBit = idCard[17]; // 最后一位可能是数字或字母X
			    const power = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
			    const verifyNumberString = '10X98765432'; // 校验码对应的值
			    let sum = 0;

			    for (let i = 0; i < 17; i++) {
			        sum += parseInt(idCard[i]) * power[i];
			    }
			    const result = verifyNumberString[sum % 11];

			    return result === (parityBit.toUpperCase());
			},
			nextStep() {
				// 步骤1验证
				if (this.active == 0) {
					if (!this.dom.employeeId || !this.dom.employeeCarId ||
						!this.dom.employeeName || !this.dom.employeeAdress ||
						!this.dom.employeePhone) {
						return uni.showToast({
							title: '请将数据填写完整',
							icon: 'none'
						})
					}
				}
				// if(!this.validateIdCard(this.dom.employeeCarId)) {
				// 	return uni.showToast({
				// 		title:'员工身份证校验不正确',
				// 		icon:'none'
				// 	})
				// }



				// 步骤2验证
				if (this.active == 1) {
					if (!this.dom.memberPhone) {
						return uni.showToast({
							title: '请将客户手机号填写完整',
							icon: 'none'
						})
					}
					if (!this.dom.memberName || !this.dom.memberAdress || !this.dom.memberCardId) {
						return uni.showToast({
							title: '客户三要素信息不能为空',
							icon: 'none'
						})
					}
					
					if (this.dom.contractType == 1) {
						this.nextText = '生成合同';
					}
				}

				// 步骤3验证
				if (this.active == 2) {

					if(this.dom.contractType == 1){
						if(!this.dom.payEndDate){
							return uni.showToast({
								title:'尾款时间不能为空',
								icon:'none'
							})
						}
					}
					if (this.dom.contractType == 2) {
						const sd = this.dom.specifyDate;
						if (!sd || sd < 1 || sd > 31) {
							return uni.showToast({
								title: '每月劳务费发放日期填写错误',
								icon: 'none'
							})
						}
						const ad = this.dom.specifyDate;
						if (!ad || ad < 1 || ad > 31) {
							return uni.showToast({
								title: '每月劳务费核算日期填写错误',
								icon: 'none'
							})
						}
					} else if (this.dom.contractType == 4) {
						const sd = this.dom.specifyDate;
						if (!sd || sd < 1 || sd > 31) {
							return uni.showToast({
								title: '每月分期支付日期填写错误',
								icon: 'none'
							})
						}
					}
					if (!this.dom.serviceEndDate || !this.dom.serviceStarDate) {
						return uni.showToast({
							title: '请将服务起始与结束时间填写完整',
							icon: 'none'
						})
					}
					let st = moment(this.dom.serviceStarDate, 'YYYY-MM-DD')
					let et = moment(this.dom.serviceEndDate, 'YYYY-MM-DD')
					// 检查结束日期不早于开始日期
					if (et.isBefore(st)) {
					    return uni.showToast({
					        title: '结束日期不能早于开始日期',
					        icon: 'none'
					    });
					}

					if (et.diff(st, 'days') > 366) {
					    return uni.showToast({
					        title: '合同日期不可超过1年',
					        icon: 'none'
					    });
					}



					if (this.dom.contractType != 1) {
						if (!this.dom.servicePay || !this.dom.inPay ||
							this.dom.servicePay < 1 || this.dom.inPay < 1) {
							return uni.showToast({
								title: '请将劳务报酬与中介费填写完整',
								icon: 'none'
							})
						}
						if (!this.dom.serviceContent) {
							return uni.showToast({
								title: '请将服务内容填写完整',
								icon: 'none'
							})
						}
						if (!this.dom.serviceContentRemark || !this.dom.serviceType) {
							return uni.showToast({
								title: '请将其他服务内容与服务类型填写完整',
								icon: 'none'
							})
						}
						if (!this.dom.serviceNum || !this.dom.serviceMax) {
							return uni.showToast({
								title: '请将服务次数与服务最大天数填写完整',
								icon: 'none'
							})
						}



					} else {
						this.active = 5;
					}


				}

				//步骤4
				if (this.active == 3 && this.dom.contractType != 1) {
					if (!this.dom.homeDetail) {
						return uni.showToast({
							title: '服务对象户型为空',
							icon: 'none'
						})
					}
					if (!this.dom.homeAllPerson || this.dom.homeAllPerson < 1) {
						return uni.showToast({
							title: '总家庭人数为空',
							icon: 'none'
						})
					}
					this.nextText = '生成合同';
				}



				if (this.active < 4) {
					this.active = this.active + 1;
					return
				} else {
					this.save();
					console.log(JSON.stringify(this.dom))
				}
			},
			changeServiceContent(model) {
				this.dom.serviceContent = model.join(',');
				console.log(model.join(','))
			},
			changeInsures(model) {
				this.dom.insure = model.join(',');
				console.log(this.dom.insure)
			},
			confirmServiceEndTime(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.serviceEndDate = moment(e.value).format('YYYY-MM-DD');
				this.showServiceEndTime = false;
			},
			confirmServiceStartTime(e) {
				// if(e.value > moment().date(new Date))
				if (moment(e.value).format('YYYY-MM-DD') < moment(moment().valueOf()).format('YYYY-MM-DD')) {
					return uni.showToast({
						title: '开始时间不能少于今天',
						icon: 'none'
					})
				}
				this.dom.serviceStarDate = moment(e.value).format('YYYY-MM-DD');
				this.showServiceStartTime = false;
			},
			confirmPayEndTime(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.payEndDate = moment(e.value).format('YYYY-MM-DD');
				this.showpayEndDateTime = false;
			},
			getListContract() {
				this.http({
					url: 'listContractHoliday',
					method: 'GET',
					success: (res) => {
						this.holidayList = res.data;
					}

				})
			},
			changeBaoMu(baomu) {
				this.dom.employeeId = baomu.id;
				this.dom.employeeCarId = baomu.idcard;
				this.dom.employeeName = baomu.realName;
				this.dom.employeeAdress = baomu.hometown;
				this.dom.employeePhone = baomu.phone;
				this.showPopupbm = false;
			},
			changeMember(member) {
				this.dom.memberId = member.id;
				this.dom.memberPhone = member.bindTel;
				this.showPopupmb = false;
			},
			updateOrderNeedState() {
				this.http({
					url: 'updateOrderNeedState',
					data: {
						orderNeedsId: this.orderNeedId,
						flowStatus: 6
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					success: res => {
						if (res.code == 0) {}
					}
				})
			},
			save() {
				this.http({
					url: 'addContract',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.dom,
					success: (res) => {
						if (res.code == 0) {
							//线索管理合同签署
							if (this.orderNeedId) {
								this.$refs.uNotify.success(res.data)
								setTimeout(() => {
									uni.redirectTo({
										url: "/pages-work/operations/clew/clewPage?id=" + this
											.orderNeedId

									}, 10)
								}, 1000)
								return;
							}
							setTimeout(() => {
								uni.redirectTo({
									url: '/pages-work/operations/contract'
								})
							}, 1000)
						} else {
							this.$refs.uNotify.error(res.msg)

						}

					}
				})
			},
			getContractById(id) {
				this.http({
					url: 'getContractById',
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						id: id
					},
					success: (res) => {
						this.dom = res.data;
						if (res.data.serviceContent) {
							this.serviceTypes = this.dom.serviceContent.split(',')
						}
						if (res.data.contractType == 2 || res.data.contractType == 1) {
							this.dom.insureBy = '乙方';
						}

						if(res.data.contractType == 6) {
							let arr = [{name:'语文'},{name:'数学'},{name:'英语'},{name:'小学全科'},{name:'初中全科'},{name:'高中全科'}]
							this.serviceTypeOptions = arr;
						}
					}

				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cell-body {
		padding: 10rpx;
	}

	/deep/ .uni-section .uni-section-header__decoration {
		background-color: #ffdd0b;
	}

	.text-box-act {
		border: 2rpx solid #ffdd0b;
		background: #ffdd0b;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.text-box {
		border: 2rpx solid #ddd;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.text-show {
		float: right;
		text-align: right;
		color: #323233;
		font-weight: 600;
	}
</style>
