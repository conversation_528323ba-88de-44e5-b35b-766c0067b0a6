<template>
	<view style="padding-bottom: 100rpx;">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 消息气泡 -->
		<uni-transition mode-class="fade" :duration="tipsBoxDuration" :show="tipsBoxShow&&orderDeliveryList.length!=0">
			<view class="tips-box-fixed">
				<text>{{formatTipsBox(tipsBoxIndex)}}</text>
			</view>
		</uni-transition>

		<u-gap height="30" v-if="orderDeliveryList.length!=0"></u-gap>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 海报分享 -->
		<uni-popup ref="popupPostImg" background-color="#fff" class="popup">
			<view @click="saveToPhone()">
				<img :src="postShareImg" class="post-img" mode="widthFix" />
			</view>
		</uni-popup>

		<!-- 分享方式选择 -->
		<u-popup :show="popupShareWay" mode="bottom" @close="popupShareWay = false">
			<view class="filter-title">
				<text>线索分享</text>
			</view>
			<view style="display: flex;flex-direction: column;">
				<view class="btn-share">
					<button plain="true" @click="openShare(0)" open-type="share">链接分享</button>
				</view>
				<!-- #ifdef H5 -->
				<!-- #endif -->
				<view class="btn-share">
					<button plain="true" @click="openShare(1)">生成海报</button>
				</view>
			</view>

			<view class="btn-bottom-center" @click="popupShareWay = false">
				<button>取消</button>
			</view>
		</u-popup>

		<!-- 分享按钮 -->
		<view style="position: fixed;bottom: 25%;right:40rpx;z-index:999" @click="popupShareWay = true"
			v-if="allowShare">
			<u-icon size="40" name="https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/icon-share.png"></u-icon>
		</view>
		<!-- 感兴趣 -->
		<view style="position: fixed;bottom: 19%;right:40rpx;z-index:999" @click="interested">
			<u-icon size="40"
				:name="isInterested?'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/icon-Interested1.png':'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/icon-Interested.png'"></u-icon>
		</view>

		<view class="f16 fb lh35" style="padding: 30rpx 30rpx;margin: 0 20rpx;">
			<view class="f20 c1e1848 flex-row">
				<text>{{order.productName || ''}}</text>
				<text
					style="font-size: 32rpx;font-weight: 100;">{{order.productRemark?'（'+order.productRemark+'）':''}}</text>
				<!-- 				<uni-icons @click="playAudio" type="sound-filled" style="margin-left: 10rpx;" size="20" color="#1e1848"
					v-if="formatRemarkStr(order.introduceVoice)!='暂无'">
				</uni-icons> -->
			</view>
			<!-- 		<img v-if="order.flowStatus==6" :src="finishImg"
				style="width: 120rpx;width:110rpx;position:absolute;top: 100rpx;right: 40rpx">
			</img> -->
			<view @click="saveText(3)">
				<text class="c6 fb4">工作地址：</text>
				{{formatAddress(order.street,0)}}
			</view>
			<view>
				<text class="c6 fb4">薪资待遇：</text>
				{{formatSalary(order.salary)}}
				<text
					style="font-size: 32rpx;font-weight: 100;">{{order.salaryRemark?'（'+order.salaryRemark+'）':''}}</text>
			</view>
			<view v-if="order.workTime">
				<text class="c6 fb4">
					{{(order.productId == 66|| order.productId==67)?'预产日期：':'上户日期：'}}
				</text>
				{{formatDate(order.workTime)}}
			</view>
			<view>
				<text class="c6 fb4">
					客户情况：
				</text>
				{{formatRemarkStr(order.remark)}}
			</view>
			<view>
				<text class="c6 fb4">
					工作内容：
				</text>
				{{formatRemarkStr(order.workContent)}}
			</view>
			<view>
				<text class="c6 fb4">
					工作要求：
				</text>
				{{formatRemarkStr(order.workRequire)}}
			</view>
			<view @click="saveText(3)">
				<text class="c6 fb4">线索编号：</text>
				{{orderNeedsId || '-'}}
			</view>
		</view>
		<view class="btn-center flac-row-c" @click="playAudio" v-if="formatRemarkStr(order.introduceVoice)!='暂无'"
			style="margin-top: -20rpx;">
			<uni-icons type="sound-filled" style="margin-right: 10rpx;" size="20" color="f6cc70">
			</uni-icons><text class="f14">播放工作详情</text>
		</view>

		<!-- 音频播放组件 -->
		<freeAudio startPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_play.png'
			endPic='https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/audio_stop.png' audioId='audio1'
			:url='order.introduceVoice' v-if="order.introduceVoice&&isShowAudio" activeColor="#1e1848"
			:isPlay="isShowAudio" />

		<view class="w8 flac-row" style="margin: 40rpx 60rpx;">
			<u-avatar
				:src="order.agentHeadImg || 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png'"
				size="40" @click="openStore" />
			<view class="w8 f16 t-indent2" @click="openStore">
				{{formatStr(0,1,order.realName)}}老师
				<view class="fb4 c9">{{formatDate(order.startTime)}}发布</view>
			</view>
			<view class="w2 text-c" style="display: flex;flex-direction: column;margin-right: 15rpx;"
				v-if="order.weChatCodeImgUrl" @click="weChatModalFalg=true">
				<uni-icons type="weixin" style="margin-left: 10rpx;" size="24" color="#1e1848">
				</uni-icons>
				<text>微信聊</text>
			</view>
			<!-- <view class="w2 text-c" style="display: flex;flex-direction: column;"
				v-if="checkStr(order.agentPhone)!='暂无'" @click="openCallPhone()">
				<uni-icons type="phone-filled" style="margin-left: 10rpx;" size="24" color="#1e1848">
				</uni-icons>
				<text>打电话</text>
			</view> -->
		</view>
		<view class="btn-center flac-row-c" v-if="order.agentPhone" @click="openCallPhone()"><uni-icons
				type="phone-filled" style="margin-right: 10rpx;" size="20" color="#f6cc70">
			</uni-icons><text class="f14">拨打电话</text>
		</view>

		<!-- 		<view class="boxStyle">
			<view class="lineBg c1e1848 f18 fb">客户要求</view>
			<view class="w85 mg-at lh30">{{order.workRequire||'暂无'}}</view>
		</view> -->

		<view class="boxStyle">
			<view class="lineBg c1e1848 f18 fb">地址预览</view>
			<view class="w85 mg-at flac-row-b">
				<view class="w8">{{formatAddress(order.street,0)}}</view>
				<view class="red" @click="saveText(0)">复制</view>
			</view>
			<map style="width: 90%; height: 25vh;margin: 20rpx auto;" :show-location="false" ref="map" id="map"
				:scale="scale" :latitude="lat" :longitude="lng" @tap="openMap" />
		</view>
		<view class="boxStyle">
			<view class="lineBg c1e1848 f18 fb">签约流程</view>
			<view class="w9 mg-at flac-row-b">
				<img style="width: 45px;height: 55px"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-wsjl.png" />
				<text class="c9 f16 fb">--</text>
				<img style="width: 45px;height: 55px"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-xzdz.png" />
				<text class="c9 f16 fb">--</text>
				<img style="width: 45px;height: 55px"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ljgt.png" />
				<text class="c9 f16 fb">--</text>
				<img style="width: 45px;height: 55px"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-qysh.png" />
			</view>
		</view>
		<view class="boxStyle f16" style="text-indent: 50rpx" v-if="checkStr(order.billNo)!='暂无'" @click="saveText(2)">
			单号：{{order.billNo||'暂无'}}</view>
		<view class="btn-bottom-fixed">
			<button name="share" @click="sendResume()"
				:style="order.flowStatus<3?'background-color:#909399':''">报名面试</button>
		</view>

		<view class="push-tips" v-if="orderNeedsList.length!=0">
			<text>-----为您推荐更多合适订单-----</text>
		</view>

		<!-- 推荐线索 -->
		<view class="need-tab" v-for="(order, index) in orderNeedsList" :key="index">
			<view class="tab-head-bar">
				<view class="bar-title">
					<text>{{order.productName}}</text>
					<text v-if="order.productRemark">（{{order.productRemark}}）</text>
				</view>

				<text class="bar-price">{{formatSalary(order.salary,order.afterSalesFee)}}</text>
			</view>
			<view class="tab-content">
				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作地址：</text>
					</view>
					<view class="item-text">
						<text>{{formatAddress(order.street,1)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作内容：</text>
					</view>
					<view class="item-text">
						<text>{{checkStr(order.workContent)}}</text>
					</view>
				</view>

				<view class="tab-item" @click="openWorksDetail(index)">
					<view class="item-title">
						<text>工作要求：</text>
					</view>
					<view class="item-text">
						<text>{{checkStr(order.workRequire)}}</text>
					</view>
				</view>

			</view>

			<view class="tab-bottom">
				<view class="bottom-img">
					<img :src="order.agentHeadImg||blankHeadImg">
				</view>
				<view class="f14 bottom-title">
					<text class="lh30">{{formatStr(0,1,order.realName)}}老师</text>
					<text class="f12 c9">{{formatDate(order.startTime)}}发布</text>
				</view>
				<view class="bottom-button">
					<button @click="openWorksDetail(index)">我要接单</button>
				</view>
			</view>
		</view>

		<view class="lh40 text-c f16" v-if="orderNeedsList.length">
			<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
			<view v-else @click="searchCondition.current++;getOrderNeedsPage()">下滑查看更多...</view>
		</view>

		<u-gap height="60"></u-gap>

		<u-modal :show="weChatModalFalg" title="企业微信码" @confirm="weChatModalFalg = false">
			<view>
				<image :show-menu-by-longpress="true" style="height: 1000rpx;width: 500rpx;"
					:src="order.weChatCodeImgUrl"></u-image>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import freeAudio from '@/pages-mine/common/components/free-audio.vue'
	export default {
		components: {
			freeAudio
		},
		data() {
			return {
				weChatModalFalg: false,
				// 可设置
				// 推荐人提醒-订阅消息模板
				templateId: "ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI",
				// 是否显示音频播放
				isShowAudio: false,
				// 是否显示消息气泡
				tipsBoxShow: true,
				// 气泡消息轮播数量
				tipsBoxLimit: 30,
				// 气泡消息轮播间隔（单位：s）
				tipsBoxInterval: 5,
				// 动画速度（单位：ms）
				tipsBoxDuration: 800,
				// 是否允许分享
				allowShare: true,
				// 是否店长分享
				isAdminShare: false,


				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				tipsBoxPlayTime: 0,
				tipsBoxIndex: 0,
				popupPost: false,
				popupShareWay: false,
				flagShare: 0,
				shareType: 0,
				show: false,
				needId: 0,
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				baomuId: null,
				resumeScore: 0,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankHeadPortrait: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_head_portrait.png",
				needImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666857777990need-img.png",
				finishImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/orderneeds_finished.png',
				postShareImg: '',
				shareImg: '',
				shareImg1: '',
				orderNeedsId: null,
				orderNeedsList: [],
				orderDeliveryList: [],
				pageCount: 0,
				order: {},
				orderDelivery: {
					id: null,
					list: []
				},
				addressDeal: '',
				addressDealShort: '',
				headImg: '',

				lat: uni.getStorageSync('lat'),
				lng: uni.getStorageSync('lng'),
				scale: 16,
				shareContent: {
					title: '',
					path: '',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: ''
				},
				searchCondition: {
					productName: '',
					productId: null,
					isPush: 1,
					flowStatus: -1,
					isSalary: 1,
					isStoreName: 1,
					monthRange: 2,
					orderBy: 'ons.startTime DESC',
					current: 1,
					size: 4
				},
				shareErr: false,
				isExcludePush: false,
				sourceMemberId: null,
				isInterested: false,
				interestedList: [], // 感兴趣列表
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openStore() {
				uni.navigateTo({
					url: "/pages-other/store/index?id=" + this.order.storeId
				})
			},
			// 打开地图
			openMap() {
				let name = this.addressDeal
				if (name.length < 2) {
					name = this.order.housingEstate
				}
				uni.openLocation({
					latitude: this.order.lat,
					longitude: this.order.lng,
					name: name,
					success: function() {
						console.log('success')
					}
				})
			},
			openCallPhone() {
				this.openCheck(2, '确定拨打吗？', '将为您接通客户专属经纪人')
			},
			// 拨打电话
			callPhone() {
				// 判断一下线索状态，若为取消则不能拨打电话
				if (this.order.cancelTime) {
					this.$refs.uNotify.error('该线索已取消，暂时无法联系经纪人哦!')
					return
				}

				// 改为直接拨打，不使用虚拟号
				uni.makePhoneCall({
					phoneNumber: this.order.agentPhone,
					success: res => {
						this.addOrderNeedLog(1)
					},
					fail: res => {}
				})
				return

				this.http({
					url: 'callPrivatePhone',
					data: {
						employeeId: this.order.agentId
					},
					method: 'GET',
					hideLoading: true,
					success: (res) => {
						if (res.code == 0) {
							let that = this
							uni.makePhoneCall({
								phoneNumber: res.data,
								success: res => {
									console.log('成功拨打电话！')
									that.addOrderNeedLog(1)
								},
								fail: res => {}
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})

				// let phone = this.order.agentPhone
				// uni.makePhoneCall({
				// 	phoneNumber: phone,
				// 	success: function(e) {

				// 	},
				// 	fail: function(e) {
				// 		console.log(e)
				// 	}
				// });
			},
			// 复制文本
			saveText(value) {
				let text = ''
				let name = ''
				let nameData = ['服务地址', '电话号码', '单号', '线索编号']
				switch (value) {
					case 0:
						text = this.addressDeal
						break
					case 1:
						text = this.order.agentPhone
						break
					case 2:
						text = this.order.billNo
						break
					case 3:
						text = this.orderNeedsId
						break
				}
				name = nameData[value]
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success(name + '复制成功!')
					}
				})
			},

			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				if (index == -1) {
					result = str
				}
				return result
			},
			// 格式化备注字段（隐藏隐私信息）
			formatRemarkStr(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					result = result.replace(/^([^s]*)元,/, " ")
				}
				return result
			},
			// 格式化地址
			formatAddress(str, value) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
						if (value == 0) {
							this.addressDealShort = result
							// 添加小区
							result += this.order.housingEstate || ""
							this.addressDeal = result
						}
					}
				}

				return result
			},
			// 格式化期望薪资
			formatSalary(salary) {
				// 售后薪资存在，则返回售后薪资
				let afterSalesFee = this.order.afterSalesFee || 0
				if (afterSalesFee != 0) {
					salary = afterSalesFee
				}

				// 如果工资字段值存在，则直接返回值
				if (salary != null && salary != 0) {
					return salary + "元/月"
				} else {
					return "工资面议"
				}
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 时间格式化
			formatDate(value) {
				if (!value) {
					return "/"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatTipsBox(index) {
				let data = this.orderDeliveryList[this.tipsBoxIndex]
				if (!data) {
					return
				}

				let empName = data.empName ? data.empName.substring(0, 1) : ''
				let result = '恭喜' + empName + '**成功报名了'
				let salary = (data.orderNeeds.salary != null && data.orderNeeds.salary != 0) ? data.orderNeeds.salary +
					'元' : ''
				result = result + salary + data.orderNeeds.productName + '单'
				return result
			},
			openWorksDetail(index) {
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + this.orderNeedsList[index].id + "&flagShare=" +
						this.flagShare + "&sourceMemberId=" + this
						.memberId
				})
			},
			// 获取简历分
			getResumeScore() {
				let baomuId = this.employeeId !== null ? this.employeeId : -1
				this.http({
					url: 'getResumeScore',
					path: baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.resumeScore = res.data.scoreSum
							console.log("当前简历分为：", this.resumeScore)
						}
					}
				});
			},
			// 发送简历
			sendResume() {
				if (!this.checkLogin()) {
					return
				}

				// 获取订阅消息授权
				// #ifdef  MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: [
						"h8NonHTTvPPiRw7mWNcduoJAqQ1CT5JQexpRveeBDbs",
					],
					success: res => {
						console.log("用户同意进行小程序消息订阅！")
					},
					fail: res => {}
				})
				// #endif

				let isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				let employeeId = uni.getStorageSync("employeeId") || 0

				if (employeeId == 0) {
					this.openCheck(3, "您还没有入驻哦！", "请先完善资料后再来面试吧！")
					return
				}

				// 如果是管理员或店长邀约，则直接进简历推荐列表
				if (this.isAdminShare) {
					this.addInterviewRecommendOption()
					return
				}

				if (this.order.flowStatus < 3) {
					this.$refs.uNotify.warning('您好，已联系店长，请等待回复！')
					return
				}

				// if (!isBaomu) {
				// 	this.openCheck(-1, "只有一线员工可以报名哦", "请转发给您邀请的员工吧！")
				// 	return
				// }
				if (this.resumeScore < 80) {
					// this.openCheck(0, "简历分80分以上才可报名哦～", "当前只有" + this.resumeScore + "分，请先完善个人简历吧～")
					this.openCheck(0, "简历分80分以上才可报名哦～", "请先完善个人简历吧～")
				} else {
					this.orderDelivery.id = this.orderNeedsId + ""
					let list = []
					list.push(this.employeeId + "")
					this.orderDelivery.list = list
					this.$set(this.orderDelivery, "grouping", this.getRandomNum())
					this.$set(this.orderDelivery, "interviewAddr", this.order.street)
					this.http({
						url: 'addOrderDelivery1',
						method: 'POST',
						hideLoading: true,
						data: this.orderDelivery,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.openCheck(1, "恭喜您，报名成功!", "要去我的面试栏目查看吗？")
								this.sendSubscribeMsg()
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					})
				}
			},
			// 将员工添加至线索推荐可选列表
			addInterviewRecommendOption() {
				this.http({
					url: 'addInterviewRecommendOption',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.employeeId,
						orderNeedsId: this.orderNeedsId,
						introduceMemberId: this.sourceMemberId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('报名成功!')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			//生成四位随机数
			getRandomNum() {
				let randomNum = ''
				for (var i = 0; i < 4; i++) {
					let num = Math.floor(Math.random() * 10);
					randomNum += num
				}
				return randomNum
			},
			// 发送订阅消息给经纪人
			sendSubscribeMsg() {
				let realName = uni.getStorageSync("employeeName") || "员工"
				let phone = uni.getStorageSync("account") || 0
				if (realName == '员工' || phone == 0) {
					return
				}
				this.http({
					url: 'sendSubscribeMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						source: "xyjacn",
						memberId: null,
						employeeId: this.order.agentId,
						templateId: this.templateId,
						page: "/pages-work/operations/clew/clewPage?id=" + this.orderNeedsId,
						data: {
							"thing6": {
								"value": realName + "-面试通知"
							},
							"thing1": {
								"value": "该线索有新的面试者啦"
							},
							"thing4": {
								"value": "请尽快安排Ta进行面试吧"
							}
						}
					},
					success: res => {
						if (res.code == 0) {

						}
					}
				})

				this.http({
					url: 'sendTemplateMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						source: "xyjacnoa",
						memberId: null,
						employeeId: this.order.agentId,
						templateId: "u2WOn2WQqHe-JgV_xvYGmGu1deCRCnmaGYQaiBsuwq0",
						page: "/pages-work/operations/clew/clewPage?id=" + this.orderNeedsId,
						data: {
							"thing1": {
								"value": this.order.productName
							},
							"thing5": {
								"value": realName + " " + phone
							},
							"thing6": {
								"value": "线上面试，请您尽快安排吧"
							}
						}
					},
				})
			},
			// 检查分享员工
			checkShareMember() {
				this.http({
					url: 'getEmployeeByMemberId',
					method: 'GET',
					hideLoading: true,
					path: this.sourceMemberId || 0,
					success: res => {
						if (res.code == 0) {
							let roleId = res.data.roleId
							if (roleId == 1 || roleId == 42 || roleId == 66 ||
								roleId == 77 || roleId == 95) {
								this.isAdminShare = true
							}
						}
					}
				})
			},
			// 点击感兴趣
			interested() {
				if (this.isInterested) {
					this.$refs.uNotify.warning('已经添加过感兴趣啦！')
					return
				}
				this.openCheck(4, '对该线索很感兴趣', '确定添加为感兴趣吗？将通知线索发布者！')
			},
			listInterested() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/order/listInterested',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId || null,
						type: 0,
						orderNeedsId: this.orderNeedsId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.interestedList = res.data
							this.isInterested = true
						}
					}
				})
			},
			insertInterested() {
				if (!this.memberId) {
					this.$refs.uNotify.warning('登录后体验更多功能！')
					return
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/order/insertInterested',
					method: 'POST',
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId || null,
						type: 0,
						orderNeedsId: this.orderNeedsId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('已添加至感兴趣！')
							this.isInterested = true
						}
					}
				})
			},
			getOrderDeliveryPage() {
				this.http({
					url: 'getOrderDeliveryPage',
					method: 'POST',
					hideLoading: true,
					data: {
						isSalary: 1,
						current: 1,
						size: this.tipsBoxLimit,
						orderBy: "del.CreTime DESC"
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.orderDeliveryList = this.orderDeliveryList.concat(res.data.records)
						}
					}
				})
			},
			// 获取线索列表
			getOrderNeedsById() {
				this.http({
					url: 'getOrderNeedsDetail',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.orderNeedsId || 0
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.order = res.data
							this.getOrderNeedsVoice()
							this.lat = this.order.lat
							this.lng = this.order.lng

							// 根据当前线索查询推荐线索
							let productName = this.order.productName
							let productList = ['不住家', '住家', '单餐', '育儿嫂', '月嫂', '护工', '陪读师', '管家', '晚餐',
								'保洁'
							]
							productList.forEach(item => {
								if (productName.includes(item)) {
									this.searchCondition.productName = item
									this.getOrderNeedsPage()
								}
							})

							// 判断线索状态，禁用分享
							if (this.order.flowStatus < 3) {
								// this.allowShare = false
								// // #ifdef MP-WEIXIN
								// wx.hideShareMenu({})
								// // #endif
							}

						}
					}
				})
			},
			// 获取线索语音介绍
			getOrderNeedsVoice() {
				if (this.checkStr(this.order.introduceVoice) !== '暂无') {
					return
				}

				this.http({
					url: 'getOrderNeedsVoice',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.orderNeedsId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.$set(this.order, "introduceVoice", res.data)
						}
					}
				})
			},
			// 登录状态检查
			checkLogin() {
				console.log('开始检查登录状态！')
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，登录后可体验更多功能！')
					uni.setStorageSync('redirectUrl', '/pages-mine/works/works-detail?id=' + this.orderNeedsId +
						"&sourceMemberId=" + this
						.memberId)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
			getOrderNeedsPage() {
				this.http({
					url: 'getOrderNeedsPage',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let data = res.data.records

							if (!this.isExcludePush) {
								for (let i = 0; i < data.length; i++) {
									if (data[i].id == this.orderNeedsId) {
										this.$delete(data, i)
										this.isExcludePush = true
										break
									}
								}
							}
							this.orderNeedsList = this.orderNeedsList.concat(data)
							this.pageCount = res.data.pages
							if (this.orderNeedsList.length && data.length == 0) {
								// this.$refs.uNotify.error("没有更多推荐了哦！")
							}
						}
					}
				})
			},
			// 添加线索分享日志
			addOrderNeedsShareLog() {
				let data = []
				data.push(parseInt(this.orderNeedsId))
				this.http({
					url: 'addOrderNeedsShareLog',
					method: 'POST',
					hideLoading: true,
					data: {
						operatorId: uni.getStorageSync("employeeId") || 0,
						memberId: uni.getStorageSync("memberId") || null,
						typeDetail: parseInt(this.shareType) + 1,
						orderNeedsIdList: data
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						console.log(res.msg)
					}
				})
			},
			// 添加线索日志
			addOrderNeedLog(value) {
				let data = {}
				if (value == 0) {
					data = {
						orderNeedsId: this.orderNeedsId,
						title: "查看线索",
						message: "线索详情被查看",
						type: 5,
						typeDetail: this.flagShare,
						sourceMemberId: this.sourceMemberId
					}
				} else if (value == 1) {
					data = {
						orderNeedsId: this.orderNeedsId,
						title: "联系线索经纪人",
						message: "拨打线索经纪人电话",
						type: 6
					}
				}

				let operatorId = uni.getStorageSync("employeeId") || null
				let memberId = this.memberId || null
				this.$set(data, "memberId", memberId)
				if (operatorId) {
					this.$set(data, "operatorId", operatorId)
				}

				this.http({
					url: 'addOrderNeedLog',
					method: 'POST',
					data: data,
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						console.log(res.msg)
					}
				})
			},
			// 选择分享方式
			openShare(value) {
				this.popupShareWay = false
				this.shareType = value
				if (value == 0 && !this.shareContent.imageUrl) {
					this.$refs.uNotify.warning('分享前可以检查一下分享内容哦！')
				}

				if (value == 1) {
					this.openPostShare()
				}

				// #ifdef H5
				if (value == 0) {
					this.openPostShare()
				}
				// #endif
			},
			// 分享海报
			async openPostShare() {
				await this.getWxShareImg()
				this.$refs.uNotify.success("点击海报即可保存！")
				this.$refs.popupPostImg.open()
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.postShareImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			getShareTitle() {
				// let title = this.checkStr(this.order.productRemark) != '暂无' ? '【' + this.order
				// 	.productName +
				// 	'-' + this.order.productRemark + '】' : '【' + this.order.productName + '】'
				// title += this.checkStr(this.order.salaryRemark) != '暂无' ? this.formatSalary(
				// 	this
				// 	.order.salary) + '-' + this.order.salaryRemark : this.formatSalary(
				// 	this
				// 	.order.salary)
				// let addressDeal = this.addressDealShort || this.order.street
				// title += '，' + addressDeal + '，高薪急聘'
				let title = this.checkStr(this.order.productRemark) != '暂无' ? '' + this.order
					.productName +
					'-' + this.order.productRemark + '' : '' + this.order.productName + ''
				let addressDeal = this.addressDealShort || this.order.street
				let time = this.order.workTime ? this.formatDate(this.order.workTime) + '上户' : '尽快上户'
				title = title + '|' + addressDeal + '|' + time
				return title
			},
			getWxShareImg() {
				let value = this.shareType
				let sname = this.order.sname || ''
				let productName = this.order.productName
				let addressDeal = this.addressDealShort || this.order.street
				let salary = this.formatSalary(this.order.salary)
				let shareBack =
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_orderneed_img1.png'
				// 添加备注
				productName = this.checkStr(this.order.productRemark) != '暂无' ? productName + '(' + this.order
					.productRemark +
					')' : productName
				salary = this.checkStr(this.order.salaryRemark) != '暂无' ? salary + '(' + this.order.salaryRemark + ')' :
					salary

				let data = {}
				// 分享类型0：图文分享 1：海报分享 2：文本分享
				if (value == 0) {
					if (this.shareImg) {
						return
					}

					// 文字大小
					const fontSize = 45
					// 文字间隔
					const fontInterval = 15
					// 行间隔
					const rowInterval = 5
					// 文本初始x轴坐标
					let originX = 30
					// 文本初始y轴坐标
					let originY = 30
					// 工作地点高度
					let salaryRow = (salary.length > 10) || (salary.includes('面议') && salary.length > 7) ? 2 : 1

					let textList = [{
							"text": '',
							// "text": productName + '|' + addressDeal,
							"fontSize": fontSize,
							"isCenter": false,
							"maxLineCount": 1,
							// "isBold": true,
							"x": originX,
							"y": originY
						},
						{
							"text": "薪资待遇：" + salary,
							"fontSize": fontSize + 5,
							"color": "0x1e1848",
							"isCenter": false,
							"isBold": true,
							"x": originX,
							// "y": originY + fontSize + fontInterval + rowInterval - 5,
							"y": originY
						},
					]
					originY = originY + (fontSize + fontInterval) * salaryRow + rowInterval + 5
					// 格式化后续文本（客户情况，工作内容，工作要求）
					let content = []
					let timeTitle = (this.order.productId == 66 || this.order.productId == 67) ? '预产日期：' : '上户日期：'
					let time = this.order.workTime ? this.formatDate(this.order.workTime) : '尽快'
					content.push(timeTitle + time)
					content.push("客户情况：" + this.formatRemarkStr(this.order.remark))
					// content.push("工作内容：" + this.formatRemarkStr(this.order.workContent))
					content.push("工作要求：" + this.formatRemarkStr(this.order.workRequire))
					for (let i = 0; i < content.length; i++) {
						let long = 1
						if (i > 0 && content[i].length > 10) {
							long = 2
						}
						textList.push({
							text: content[i],
							fontSize: fontSize,
							isCenter: false,
							// isBold: true,
							maxLineCount: long,
							x: originX,
							y: originY
						})
						originY += (fontSize + fontInterval) * long + rowInterval
					}

					data = {
						textList: textList,
						maxWidth: 720,
						img: shareBack,
					}
				} else if (value == 1) {
					if (this.postShareImg) {
						return
					}

					// 文字大小
					const fontSize = 80
					// 文字间隔
					const fontInterval = 15
					// 行间隔
					const rowInterval = 25
					// 最大文本宽度
					const maxWidth = 1240
					// 文本初始x轴坐标
					let originX = 100
					// 文本初始y轴坐标
					let originY = 390
					originY = this.order.productRemark ? originY : (originY - fontSize - fontInterval)
					// 工作地点高度
					let salaryRow = (salary.length > 15) || (salary.includes('面议') && salary.length > 12) ? 2 : 1

					let textList = [{
							"text": this.order.productName,
							"fontSize": fontSize + 50,
							"color": "0xFFFFFFFF",
							"isCenter": true,
							"isBold": true,
							"maxLineCount": 1,
							"x": originX,
							"y": 130
						}, {
							"text": this.order.productRemark || "",
							"fontSize": fontSize + 10,
							"isCenter": false,
							"isBold": true,
							"maxLineCount": 1,
							"x": originX,
							"y": originY
						},
						{
							"text": salary,
							"fontSize": fontSize + 15,
							"color": "0xe30202",
							"isCenter": false,
							"isBold": true,
							"x": originX,
							"y": originY + fontSize + fontInterval
						},
						{
							"text": "工作地点：" + addressDeal,
							"fontSize": fontSize,
							"isCenter": false,
							"isBold": true,
							"maxLineCount": 1,
							"x": originX,
							"y": originY + (fontSize + fontInterval) * (salaryRow + 1) + rowInterval
						}
					]
					originY = originY + (fontSize + fontInterval) * (salaryRow + 3) + rowInterval - 10
					// 格式化后续文本（客户情况，工作内容，工作要求）
					let content = []
					content.push("客户情况：" + this.formatRemarkStr(this.order.remark))
					content.push("工作内容：" + this.formatRemarkStr(this.order.workContent))
					content.push("工作要求：" + this.formatRemarkStr(this.order.workRequire))
					content.forEach(item => {
						textList.push({
							"text": item,
							"fontSize": fontSize,
							"isCenter": false,
							"isBold": true,
							"maxLineCount": 2,
							"x": originX,
							"y": originY
						})
						originY += (fontSize + fontInterval) * 2 + rowInterval
					})

					let name = this.formatStr(0, 1, this.order.realName) + '老师'
					sname = sname.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
					// 经纪人名字
					textList.push({
						"text": name,
						"fontSize": fontSize - 10,
						"isCenter": false,
						"isBold": true,
						"x": name.length == 2 ? 390 : 340,
						"y": 1980
					})
					// 门店名字
					textList.push({
						"text": sname,
						"fontSize": fontSize - 10,
						"isCenter": false,
						"isBold": true,
						"x": 384 - (sname.length * 24),
						"y": 1980 + fontSize
					})
					data = {
						textList: textList,
						// 二维码
						qrCodeStyle: {
							width: 340,
							height: 340,
							x: 860,
							y: 1640
						},
						// 图片合成
						imgList: [{
							url: this.order.agentHeadImg || this.blankHeadPortrait,
							width: 300,
							height: 300,
							roundCorner: 300,
							x: 306,
							y: 1660
						}],
						maxWidth: maxWidth,
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_orderneed_post.png",
						path: "pages-mine/works/works-detail",
						scene: "id/" + this.orderNeedsId + "*flag/2" + "*sid/" + this.memberId,
						source: "xyjacn",
						type: 1
					}
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							let shareImg = res.data
							if (value == 0) {
								// this.$set(this.shareContent, "title", sname + '高薪急招' + productName + '，优秀阿姨快来报名吧')
								let title = this.getShareTitle()
								this.$set(this.shareContent, "title", title)
								this.$set(this.shareContent, "path", '/pages-mine/works/works-detail?id=' +
									this
									.orderNeedsId + "&flagShare=1" + "&sourceMemberId=" + this
									.memberId)
								this.shareImg = shareImg
								this.$set(this.shareContent, "imageUrl", shareImg)
								// #ifdef H5
								this.postShareImg = shareImg
								// #endif
							} else if (value == 1) {
								this.postShareImg = shareImg
							}
							this.addOrderNeedsShareLog()
							this.shareErr = false
						} else {
							this.$refs.uNotify.error('分享失败！获取分享图片异常！' + res.msg)
							this.shareErr = true
						}
					}
				})
			},
			// 分享
			onShareAppMessage(res) {
				if (this.order.flowStatus < 3) {
					this.$refs.uNotify.error('该线索未到达匹配阶段，分享后阿姨将无法报名！')
					// return
				}

				return new Promise((resolve, reject) => {
					if (this.shareContent.imageUrl) {
						resolve(this.shareContent)
					} else {
						this.getWxShareImg()
						wx.showLoading({
							title: '正在获取分享内容...',
							icon: 'none'
						})

						setTimeout(() => {
							wx.hideLoading()
							if (!this.shareErr) {
								resolve(this.shareContent)
							}
						}, 1600)
					}
				})
			},
			// 朋友圈
			onShareTimeline(res) {
				if (this.order.flowStatus < 3) {
					this.$refs.uNotify.error('该线索未到达匹配阶段，分享后阿姨将无法报名！')
					// return
				}
				let title = this.getShareTitle()
				this.shareType = 3
				this.addOrderNeedsShareLog()
				return {
					title: title,
					query: 'id=' + this.orderNeedsId +
						'&flagShare' + this.flagShare + "&sourceMemberId=" + this
						.memberId,
					path: '/pages-mine/works/works-detail'
				}
			},
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			popupCheck() {
				// Tpye的值可以控制，0：确认去填写简历
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume"
					});
				} else if (this.checkType == 1) {
					uni.navigateTo({
						url: "/pages-mine/interview/interview"
					});
				} else if (this.checkType == 2) {
					this.callPhone()
				} else if (this.checkType == 3) {
					uni.reLaunch({
						url: '/pages-mine/resume/resume-simplify'
					})
				} else if (this.checkType == 4) {
					this.insertInterested()
				}
			},
			// 播放音频
			playAudio() {
				if (this.isShowAudio) {
					uni.$emit('stop')
				} else {
					uni.$emit('play')
				}
				this.isShowAudio = !this.isShowAudio
			},
			// 登录状态检查
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', '/pages-mine/works/works-detail?id=' + this.orderNeedsId +
						'&flagShare' + this.flagShare + "&sourceMemberId=" + this
						.memberId)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
					return false
				} else {
					return true
				}
			}
		},
		onUnload() {
			uni.$emit('stop')
		},
		onReachBottom() {
			this.searchCondition.current++
			this.getOrderNeedsPage()
		},
		onLoad(options) {
			if (options.id !== undefined) {
				this.orderNeedsId = parseInt(options.id)
			}
			this.flagShare = options.flagShare || 0
			this.sourceMemberId = options.sourceMemberId || null

			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.orderNeedsId = obj.id || this.orderNeedsId
				this.flagShare = obj.flag || this.flagShare
				this.sourceMemberId = obj.sid || this.sourceMemberId
			}

			this.getOrderNeedsById()
			this.memberId = uni.getStorageSync('memberId') || null
			this.employeeId = uni.getStorageSync('employeeId') || 0
			console.log("员工id为：", this.employeeId)
			this.getResumeScore()
			this.addOrderNeedLog(0)
			this.getOrderDeliveryPage()
			this.listInterested()
			this.checkShareMember()
			this.checkLogin()
		},
		watch: {
			tipsBoxPlayTime: {
				handler(newValue, oldVal) {
					let speed = this.tipsBoxInterval
					if (this.tipsBoxPlayTime > 1) {
						// 控制消息气泡轮播
						if (this.tipsBoxPlayTime % speed > 0) {
							if (this.tipsBoxPlayTime % speed == 1) {
								this.tipsBoxIndex++
								if (this.tipsBoxIndex == this.tipsBoxLimit) {
									this.tipsBoxIndex = 0
								}
							}
							this.tipsBoxShow = true
						} else if (this.tipsBoxPlayTime % speed == 0) {
							this.tipsBoxShow = false
						}
					}
				},
				deep: true
			},
		},
		mounted() {
			// 开始线索报名轮播
			let time = setInterval(() => {
				this.tipsBoxPlayTime += 1
			}, 1000);
			this.checkLogin()
		}
	}
</script>


<style lang="scss" scoped>
	@import '@/static/css/themeStyle.scss';
	@import "@/pages-mine/common/css/work-tab.scss";

	.boxStyle {
		width: 90%;
		margin: 50rpx auto;
		background-color: #fff;
		box-shadow: 0rpx 0rpx 10rpx 10rpx #e8ecf2;
		padding: 40rpx 10rpx;
		border-radius: 20rpx;
	}

	.lineBg {
		width: 28%;
		height: 80rpx;
		margin-left: 50rpx;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-bg.png') no-repeat center;
		background-size: 100% 20%;
	}

	.btn-bottom-fixed {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 30rpx 5%;
			width: 90%;
			height: 70rpx;
			line-height: 70rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 32rpx;
		}
	}

	.btn-bottom-center {
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 30rpx 5%;
			width: 90%;
			height: 70rpx;
			line-height: 70rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 32rpx;
		}
	}

	.swiper {
		width: 100%;
		height: 320rpx;
	}

	.post-img {
		display: block;
		width: 600rpx;
		margin: auto;
		height: auto;
	}

	.filter-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	.btn-share {
		button {
			border: none;
		}
	}

	button[plain] {
		border: 0
	}

	// 需求栏目
	.need-tab {
		width: 92%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
		padding: 20rpx 0;
		border-radius: 20rpx;
	}

	// 栏目头部
	.tab-head-bar {
		display: block;
		width: 100%;
		height: 80rpx;
		display: flex;
	}

	.bar-title {
		width: 74%;
		height: 80rpx;
		font-size: 36rpx;
		line-height: 80rpx;
		margin-left: 30rpx;
		color: #1e1848;
		font-weight: bold;

		text:nth-child(2) {
			font-size: 28rpx;
		}
	}

	.bar-price {
		width: 29%;
		margin: 20rpx 30rpx 0 0;
		text-align: right;
		height: 40rpx;
		line-height: 40rpx;
		color: #ff4d4b;
		font-weight: bold;
		font-size: 32rpx;
	}

	// 栏目底部
	.tab-bottom {
		width: 100%;
		height: 120rpx;
		font-size: 36rpx;
		display: flex;
	}

	.bottom-img {
		width: 20%;
		height: 100rpx;

		img {
			display: block;
			margin: 15rpx auto;
			width: 90rpx;
			height: 90rpx;
			border-radius: 50%;
		}
	}

	.bottom-title {
		margin: 20rpx 0;
		width: 52%;
		height: 80rpx;
		display: flex;
		flex-direction: column;

		text {
			height: 40rpx;
			line-height: 40rpx;
		}
	}

	.bottom-button {
		width: 18%;
		margin: 30rpx 0;

		button {
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 20rpx;
			font-size: 32rpx;
			padding: 0 0;
		}
	}

	.push-tips {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		color: #1e1848;
		text-align: center;
		font-size: 36rpx;
	}

	.btn-center {
		margin: 10rpx auto;
		width: 60%;
		height: 70rpx;
		line-height: 70rpx;
		background-color: #1e1848;
		color: #f6cc70;
		border-radius: 50rpx;
		box-shadow: 4rpx 4rpx 10rpx #909399;
	}
</style>