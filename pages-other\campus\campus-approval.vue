<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<img :src="postImg" mode="widthFix" style="width: 100%;" />

		<!-- 选择器 -->
		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<view class="w9 mg-at">
			<u--form :model="dom" labelPosition="top" labelWidth="330" :labelStyle="labelStyle">
				<u-form-item label="校区选择" required @click="openPickerMine(0)">
					<view class="flac-col">
						<u-search bgColor="#fff" v-model="storeName" placeholder="默认为当前门店，可进行选择" @clear="storeName='';"
							:disabled="true" />
						<view class="f16 lh40" style="color: #ff4d4b;">
							选择对应的校区提交审核
						</view>
					</view>
				</u-form-item>
				<u-form-item label="开通类型" required>
				</u-form-item>
				<view class="w10 mg-at flac-row f16">
					<view class="w5 flac-col-c choice-tab" v-for="(item,index) in campusTypeList" :key="index"
						@click="choiceType(index)" :style="choiceIndex==index?'border: 6rpx #19be6b solid;':''">
						<text class="lh40 fb">{{item.title}}</text>
						<view class="flac-col">
							<view class="" v-for="(item1,index1) in item.contentList" :key="index1">
								{{item1}}
							</view>
						</view>
						<view class="lh20 fb" style="color:#ff4d4b">{{item.remark}}</view>
					</view>
				</view>
				</u-form-item>
				<u-form-item label="学校培训地址" prop="campusAddress" required>
					<u-textarea placeholder="请填写学校培训地址" v-model="dom.campusAddress" />
				</u-form-item>
				<u-form-item label="校长姓名" prop="principalName" required>
					<u-input placeholder="请填写校区校长姓名" v-model="dom.principalName" />
				</u-form-item>
				<u-form-item label="校长联系方式" prop="principalPhone" required>
					<u-input placeholder="请填写校长手机号" v-model="dom.principalPhone" type="number" />
				</u-form-item>
				<u-form-item label="招生/教务负责人" prop="directorName" required>
					<u-input placeholder="请填写校区负责人姓名" v-model="dom.directorName" />
				</u-form-item>
				<u-form-item label="负责人联系方式" prop="directorPhone" required>
					<u-input placeholder="请填写负责人手机号" v-model="dom.directorPhone" type="number" />
				</u-form-item>
				<u-form-item label="讲师姓名" prop="lecturerName" required>
					<u-input placeholder="请填写校区讲师姓名" v-model="dom.lecturerName" />
				</u-form-item>
				<u-form-item label="讲师联系方式" prop="lecturerPhone" required>
					<u-input placeholder="请填写讲师手机号" v-model="dom.lecturerPhone" type="number" />
				</u-form-item>
				<u-form-item label="校区图片" required v-if="choiceIndex==1">
				</u-form-item>
			</u--form>
			<shmily-drag-image v-model="imgList" keyName="imgUrl" class="w10 mg-at" :addImage="uploadImg"
				:delImage="delImage" @delDragImg="delDragImg" v-if="choiceIndex==1"></shmily-drag-image>
		</view>

		<view class="btn-bottom-fixed" @click="pageType==0?save():update()">
			<button style="background-color:#1e1848;">
				{{pageType==0?'提交申请':'保存修改'}}
			</button>
		</view>
		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 最大显示图片数
				maxShowPhoto: 3,
				// 页面类型（0：发布 1：修改）
				pageType: 0,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				labelStyle: {
					'font-weight': 'bold',
					'font-size': '32rpx',
					'letter-spacing': '5rpx',
				},
				dom: {
					storeId: null,
					employeeId: uni.getStorageSync("employeeId") || null,
					approvalor: '',
					campusType: 0,
					campusAddress: '',
					principalName: '',
					principalPhone: '',
					directorName: '',
					directorPhone: '',
					lecturerName: '',
					lecturerPhone: ''
				},
				campusTypeList: [{
					id: 0,
					title: '灵活',
					contentList: [
						'1、 授权半年。',
						'2、授权期始至授权期止， 招生(不含 退费) 低于90人， 将自动取消授权。',
						'3、符合招生指标将重新续约。'
					],
					remark: '* 场地面积不限，可灵活调整上课场所。'
				}, {
					id: 1,
					title: '固定',
					contentList: [
						'1、授权一年。',
						'2、场地面积不得低于25m2，配备多媒体。',
						'3、提供场地照片及租赁合同。'
					],
					remark: '* 需提交场地照片以供审核。'
				}],
				searchText: '',
				storeId: uni.getStorageSync("storeId") || null,
				storeName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				showPopup: false,
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/campus-approval.png',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				defaultPost: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/vote_share.png",

				choiceIndex: 0,
				choiceImgIndex: 0,
				imgList: [],
				campusApprovalId: null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 选择奖励类型
			choiceType(index) {
				if (this.pageType == 0) {
					this.choiceIndex = index
					this.dom.campusType = this.campusTypeList[index].id || 0
				} else {
					this.$refs.uNotify.warning('审批已提交，不可进行修改！')
				}
			},
			// 打开选择器
			openPickerMine(value) {
				let id = 1
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
					id = this.storeId || id
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				// 初始化选择器位置
				for (let i = 0; i < this.pickerMineList.length; i++) {
					if (id == this.pickerMineList[i].id) {
						this.pickerIndex = i
						break
					}
				}
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				let name = this.pickerMineList[index][this.pickerMineName]
				let id = this.pickerMineList[index].id
				if (value == 0) {
					this.storeName = name
					this.dom.storeId = id
				} else if (value == 1) {

				}
				this.showPickerMine = false
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 初始化选择器
			initPickerMine(id, name, list) {
				for (let i = 0; i < list.length; i++) {
					if (id == list[i].id) {
						let param = 'storeName'
						this[param] = list[i][name]
						this.pickerIndex = i
						break
					}
				}
			},
			// 上传图片
			uploadImg() {
				let url = "https://api2.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					count: 9,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						for (let i = 0; i < tempFilePaths.length; i++) {
							let item = tempFilePaths[i]
							uni.uploadFile({
								url: url,
								filePath: item,
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: res => {
									let result = JSON.parse(res.data)
									let imgUrl = result.data
									let data = {
										'id': 0,
										'imgUrl': imgUrl
									}
									this.imgList.push(data)
								}
							});
						}
					}
				});
			},
			delDragImg(index) {
				this.choiceImgIndex = index
			},
			// 删除个人照片
			delImage(done) {
				uni.showModal({
					content: '确定删除该照片吗，删除后不可恢复！?',
					success: res => {
						if (res.confirm) {
							this.delete(done)
						}
					}
				})
			},
			// 删除照片
			delete(done) {
				let id = this.imgList[this.choiceImgIndex].id
				if (id == 0) {
					this.$delete(this.imgList, this.choiceImgIndex)
					this.$refs.uNotify.success("照片删除成功！")
					return
				}

				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/course/deleteCampusApprovalImg',
					method: 'GET',
					path: id,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("照片删除成功！")
							done()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			//输入校验
			checkInput() {
				if (!this.dom.employeeId) {
					this.$refs.uNotify.warning('非员工账号，暂时无法提交申请！')
					return false
				}
				if (!this.dom.storeId) {
					this.$refs.uNotify.warning('请选择需要开通的校区！')
					return false
				}
				if (!this.dom.campusAddress) {
					this.$refs.uNotify.warning('请填写校区地址！')
					return false
				}
				if (!this.dom.principalName) {
					this.$refs.uNotify.warning('请填写校长姓名！')
					return false
				}
				if (!this.dom.principalPhone) {
					this.$refs.uNotify.warning('请填写校长联系方式！')
					return false
				}
				if (!this.dom.directorName) {
					this.$refs.uNotify.warning('请填写负责人姓名！')
					return false
				}
				if (!this.dom.directorPhone) {
					this.$refs.uNotify.warning('请填写负责人联系方式！')
					return false
				}
				if (!this.dom.lecturerName) {
					this.$refs.uNotify.warning('请填写讲师姓名！')
					return false
				}
				if (!this.dom.lecturerPhone) {
					this.$refs.uNotify.warning('请填写讲师联系方式！')
					return false
				}
				if (this.choiceIndex == 1 && !this.imgList.length) {
					this.$refs.uNotify.warning('请上传校区图片！')
					return false
				}
				return true
			},
			// 添加优秀员工
			save() {
				if (!this.checkInput()) {
					return
				}
				this.dom.campusType = this.campusTypeList[this.choiceIndex].id
				uni.showModal({
					title: '确定提交校区申请吗？',
					content: '提交后将由评审员进行评审，需耐心等待结果！',
					success: (res) => {
						if (res.confirm) {
							this.http({
								outsideUrl: 'https://api2.xiaoyujia.com/course/insertCampusApproval',
								method: 'POST',
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								data: this.dom,
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('提交成功！')
										this.pageType = 1
										this.dom = res.data
										this.campusApprovalId = this.dom.id
										this.updateCampusApprovalImgList()
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				});
			},
			// 更新
			update() {
				if (!this.checkInput()) {
					return
				}
				if (this.dom.approvalState != 0) {
					this.$refs.uNotify.warning('当前未处于审批状态，无法修改！')
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/course/updateCampusApproval',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.dom,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('修改成功！')
							this.updateCampusApprovalImgList()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 更新图片
			updateCampusApprovalImgList() {
				if (!this.imgList.length) {
					return
				}
				this.imgList.forEach(item => {
					this.$set(item, 'campusApprovalId', this.campusApprovalId)
				})
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/course/updateCampusApprovalImgList',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.imgList,
					success: res => {
						if (res.code == 0) {

						}
					},
				})
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/store/getByList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.storeList = res.data
							let id = this.storeId
							this.initPickerMine(id, 'storeName', this.storeList)
						}
					}
				});
			},
			
			getCampusApprovalById() {
				if (!this.campusApprovalId) {
					return
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/course/getCampusApprovalById',
					path: this.campusApprovalId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.dom = res.data
							this.imgList = res.data.campusApprovalImgList
							this.choiceIndex = this.dom.campusType
						}
					}
				});
			}
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.campusApprovalId = obj.t || this.campusApprovalId
			}
			this.campusApprovalId = options.id || this.campusApprovalId
			if (this.campusApprovalId) {
				this.pageType = 1
			}
			this.getCampusApprovalById()
			this.getStoreList()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.choice-tab {
		padding: 0 20rpx;
	}
</style>