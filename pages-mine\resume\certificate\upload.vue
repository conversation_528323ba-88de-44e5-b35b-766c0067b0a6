<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 日期选择器 -->
		<u-datetime-picker :show="show" v-model="certificateDate" mode="date" @cancel="show = false"
			@confirm="confirmState" :minDate="minDate"></u-datetime-picker>

		<u-datetime-picker :show="show1" v-model="certificateDateEnd" mode="date" @cancel="show1 = false"
			@confirm="confirmState" :minDate="minDate"></u-datetime-picker>

		<!-- 是否上传-小提示 -->
		<view class="tab-tips" :style="isUpload?'background-color:#19be6b;':'background-color:#ff7373;'">
			<text>{{isUpload?"该证件已上传":"该证件还未上传"}}</text>
		</view>

		<!-- 上传提示标题 -->
		<view class="head">
			<text @click="openImgPreview(imgUpload)">请拍摄/上传您本人的{{certificateTitle}}</text>
		</view>

		<!-- 上传图片 -->
		<view class="upload-img" v-if="!isMultiple">
			<img :src="imgUpload" alt="" @click="uploadImg" @longpress="openImgPreview(imgUpload)">
		</view>

		<!-- 上传图片列表 -->
		<view class="upload-img-list" v-if="isMultiple">
			<view v-for="(item,index) in certList" :key="index" class="upload-img-item">
				<img :src="item.certificateImg" @click="openImgPreview(item.certificateImg)">
				<view class="upload-img-delete">
					<uni-icons type="clear" size="24" color="#000000" @click="deleteImg(index)"></uni-icons>
				</view>
			</view>

			<view class="upload-img-item">
				<img :src="imgUpload" alt="" @click="uploadImg">
			</view>
		</view>



		<view style="font-size: 30rpx;margin: 0 60rpx;line-height: 40rpx;">
			<text>*点击可预览照片</text>
		</view>
		<view style="font-size: 30rpx;margin: 0 60rpx;line-height: 40rpx;" v-if="isMultiple">
			<text>{{certificateType == 8?'*体检报告每一页都要清晰上传':''}}</text>
		</view>

		<!-- 证件名称输入 -->
		<view class="upload-tab" v-if="certificateIndex==12">
			<text>证件名称</text>
			<text v-if="certificateName==''" style="color: #909399;">点击填写</text>
			<input class="upload-input" type="text" v-model="certificateName"></input>
		</view>

		<!-- 证件号码信息输入 -->
		<!-- 		<view class="upload-tab">
			<text>证件号码</text>
			<text v-if="certificateNum==''" style="color: #909399;">点击填写</text>
			<input class="upload-input" type="text" v-model="certificateNum"></input>
		</view>

		<view class="upload-tab">
			<text>证件有效期开始</text>
			<text v-if="nowDate==certificateDate" style="color: #909399;" @click="show = true">请选择&ensp;❯</text>
			<text v-if="nowDate!==certificateDate" @click="show = true">{{ formatDate(certificateDate) }}</text>
		</view> -->

		<view class="upload-tab">
			<text>{{certificateType==3||certificateType==8?'体检日期':'发证日期'}}</text>
			<text v-if="nowDate==certificateDateEnd" style="color: #909399;" @click="show1 = true">请选择&ensp;❯</text>
			<text v-if="nowDate!==certificateDateEnd" @click="show1 = true">{{ formatDate(certificateDateEnd) }}</text>
		</view>

		<!-- 文字提示 -->
		<view class="upload-tips">
			<text>信息仅用于身份验证，小羽佳保障您的信息安全</text>
		</view>

		<!-- 保存按钮 -->
		<view class="btn-bottom">
			<button @click="trySave()">确 认</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 日期可选择最小值
				minDate: Number(new Date().setYear(new Date().getFullYear() - 20)),

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				certificateIndex: 0,
				certificateTitle: "",
				certificateType: 0,
				isUpload: false,
				certificateNum: "",
				certificateName: "",
				certificateImg: "",
				show: false,
				show1: false,
				nowDate: Number(new Date()),
				certificateDate: Number(new Date()),
				certificateDateEnd: Number(new Date()),
				memberId: null,
				baomuId: null,
				employeeId: null,
				changeNum: 0,

				choiceImgIndex: 0,
				isMultiple: false,
				certList: [],
				certUploadList: [],
				certificate: {
					title: null,
					employeeId: this.baomuId,
					certificateType: this.certificateType,
					certificateImg: null
				},
				// 员工信息
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				// 证件照片
				certificate: {
					title: null,
					employeeId: this.baomuId,
					certificateImg: null,
					certificateType: null,
				},
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				imgUploadUrl: "",
				otherCertificateList: [],
				otherCertificateList1: [{
						index: 0,
						tabTitle: '健康证',
						tabTips: '10分',
						value: 3,
						isUpload: false,
					}, {
						index: 1,
						tabTitle: '体检表',
						tabTips: '',
						value: 8,
						isUpload: false,
					},
					{
						index: 2,
						tabTitle: '技能证书',
						tabTips: '10分',
						value: 23,
						isUpload: false,
					},
					{
						index: 3,
						tabTitle: '月嫂证',
						tabTips: '',
						value: 4,
						isUpload: false,
					}, {
						index: 4,
						tabTitle: '育婴师证',
						tabTips: '',
						value: 6,
					},
					{
						index: 5,
						tabTitle: '催乳师证',
						tabTips: '',
						value: 7,
						isUpload: false,
					}, {
						index: 6,
						tabTitle: '护工证',
						tabTips: '',
						value: 9,
						isUpload: false,
					}, {
						index: 7,
						tabTitle: '家政员证',
						tabTips: '',
						value: 10,
						isUpload: false,
					},
					{
						index: 8,
						tabTitle: '毕业证',
						tabTips: '',
						value: 20,
						isUpload: false,
					}, {
						index: 9,
						tabTitle: '教师证',
						tabTips: '',
						value: 21,
						isUpload: false,
					}, {
						index: 10,
						tabTitle: '驾驶证',
						tabTips: '',
						value: 22,
						isUpload: false,
					},
					{
						index: 11,
						tabTitle: '赛事证书',
						tabTips: '',
						value: 24,
						isUpload: false,
					},
					{
						index: 12,
						tabTitle: '其它证件',
						tabTips: '',
						value: 99,
						isUpload: false,
					}
				]
			}
		},
		methods: {
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			// 上传图片
			uploadImg() {
				const url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					count: 9,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						tempFilePaths.forEach(item => {
							uni.uploadFile({
								url: url,
								filePath: item,
								name: 'file',
								formData: {
									route: 'certificate',
									watermark: 'ture'
								},
								dataType: 'json',
								success: res => {
									console.log('上传图片的临时地址:', item)
									let result = JSON.parse(res.data)
									this.imgUploadUrl = result.data
									console.log('上传图片后返回文件地址:', this.imgUploadUrl)

									// 多选时，将选项添加到图片列表
									if (this.isMultiple) {
										let data = {
											id: 0,
											employeeId: this.baomuId,
											title: this.certificateTitle,
											certificateType: this.certificateType,
											certificateImg: this.imgUploadUrl,
											validity: this.formatDate(this
												.certificateDateEnd)
										}
										this.certList.push(data)
									} else {
										this.imgUpload = this.imgUploadUrl
									}
								},
								fail: res => {
									this.$refs.uNotify.error("上传失败！" + res.msg)
								}
							});
						})
					}
				});
			},
			checkDate() {
				let d1 = this.certificateDateEnd
				let d2 = this.certificateDate
				let d3 = this.nowDate
				let result = false

				// 原方法弃用，改为简单形式
				if (d1 == d3) {
					this.$refs.uNotify.error("请将日期补充完整哦～")
					return false
				} else {
					return true
				}

				if (new Date(d1).getTime() - new Date(d2).getTime() > 0) {
					result = true
				} else {
					this.$refs.uNotify.error("结束时间应该在开始时间之后哦～")
					result = false
				}
				if (new Date(d1).getTime() - new Date(d3).getTime() > 0 || new Date(d2).getTime() - new Date(d3)
					.getTime() > 0) {
					this.$refs.uNotify.error("选择的时间不能超过当前时间哦～")
					result = false
				}
				if (d1 == d2 || d2 == d3) {
					this.$refs.uNotify.error("请将日期补充完整哦～")
					result = false
				}
				return result
			},
			// 尝试保存
			trySave() {
				this.openCheck(0, "确定保存我的证件吗？", "需要真实证件哦～")
			},
			save() {
				if (this.certificateType == 99) {
					this.certificate.title = this.certificateName
					if (this.certificateName == "") {
						this.$refs.uNotify.error("请手动输入证件名称～")
						return
					}
				}
				// if (this.certificateNum == "") {
				// 	this.$refs.uNotify.error("证件号码不能为空～")
				// } else 
				if (this.imgUploadUrl == "" && !this.isMultiple) {
					this.$refs.uNotify.error("证件还未上传哦～")
				} else if (this.checkDate()) {
					this.checkBaomuAndInit()
					if (this.baomuId == null) {
						let timer = setTimeout(() => {
							if (this.baomuId !== null) {
								this.addCertificate()
							}
						}, 1500);
					} else {
						this.addCertificate()
					}
				}
			},
			// 删除证件图片
			delete() {
				let id = this.certList[this.choiceImgIndex].id
				if (id == 0) {
					this.$delete(this.certList, this.choiceImgIndex)
					this.$refs.uNotify.success("证件图片删除成功！")
					return
				}

				this.http({
					url: 'deleteCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: id
					},
					success: res => {
						if (res.code == 0) {
							this.$delete(this.certList, this.choiceImgIndex)
							this.$refs.uNotify.success("证件图片删除成功！")
						} else {
							this.$refs.uNotify.error("证件图片删除失败！" + res.msg)
						}
					}
				})
			},
			// 删除图片
			deleteImg(index) {
				this.choiceImgIndex = index
				this.openCheck(1, "确定删除该证件吗？", "删除后不可恢复！")
			},
			// 时间格式化
			formatDate(value) {
				if (value.length < 11) {
					if (value == "3000/01/01") {
						return '长期有效'
					} else {
						return value
					}
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '/' + MM + '/' + d
			},
			// 确认日期选择
			confirmState() {
				this.show = false
				this.show1 = false
			},
			// 更新证件图片
			addCertificate() {
				if (this.isMultiple) {
					this.addCertificateList()
					return
				}

				this.$set(this.certificate, 'employeeId', this.baomuId)
				this.certificate.certificateType = this.certificateType
				this.certificate.certificateImg = this.imgUploadUrl
				this.certificate.validity = this.formatDate(this.certificateDateEnd)
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.certificate,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("证件图片更新成功！")
							this.isUpload = true
							let timer = setTimeout(() => {
								return uni.navigateBack()
							}, 1500);
						} else {
							this.$refs.uNotify.error("图片上传失败！" + res.msg)
						}
					}
				})
			},
			// 添加证件图片列表
			addCertificateList() {
				// 更新证件图片列表的有效期
				for (let item of this.certList) {
					if (item.id != 0) {
						item.validity = this.formatDate(this.certificateDateEnd)
						console.log("同步有效期！" + item.validity)
					}
				}

				this.http({
					url: 'addCertificateList',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.certList,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("证件图片更新成功！")
							this.isUpload = true
							let timer = setTimeout(() => {
								return uni.navigateBack()
							}, 1500);
						} else {
							this.$refs.uNotify.error("图片上传失败！" + res.msg)
						}
					}
				})
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				// 请求：通过会员ID获取保姆关联信息
				if (this.baomuId === null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync("baomuId", this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log("初始化的保姆ID为" + this.baomuId)
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 获取证件图片列表
			getCertList() {
				if (!this.isMultiple) {
					return
				}

				this.http({
					url: 'getCertificateByCertType',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId,
						certificateType: this.certificateType
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.certList = res.data
						} else {

						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：保存证件
				if (this.checkType == 0) {
					this.save()
				} else if (this.checkType == 1) {
					this.delete()
				}
			},
			// 获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let baomuId = uni.getStorageSync('baomuId')
				baomuId = baomuId == '' ? null : baomuId;
				this.memberId = memberId
				// 如果在上一个页面没有获取参数，则从缓存中取值
				if (this.baomuId == undefined || this.baomuId == null) {
					this.baomuId = baomuId
				}
				console.log("用户ID：" + memberId + "保姆ID：" + baomuId)
			},
			// 发送部分请求，初始化页面信息
			initInfo() {
				this.getMemberInfor()
			},
		},
		// 获取上个页面回传的数据
		onLoad(options) {
			this.baomuId = JSON.parse(options.baomuId)

			let index = options.certificateIndex
			let isUpload = ""
			this.certificateIndex = index
			if (options.otherCertificateList !== undefined) {
				this.otherCertificateList = JSON.parse(options.otherCertificateList)
				this.certificateTitle = this.otherCertificateList[index].tabTitle
				this.certificateType = this.otherCertificateList[index].value
				this.isUpload = this.otherCertificateList[index].isUpload

				// 判断是否为多选（体检表可多选）
				if (this.certificateType == 8) {
					this.isMultiple = true
				}

				if (this.otherCertificateList[index].validity != null) {
					this.certificateDateEnd = this.otherCertificateList[index].validity
					console.log("输出", this.certificateDateEnd)
				}
				if (options.certificateImg != "" && options.certificateImg != null && !this.isMultiple) {
					this.imgUpload = options.certificateImg
					this.imgUploadUrl = this.imgUpload
					this.certificateName = options.certificateTitle
				}
				console.log("输出：", this.imgUpload)
			}



			// 测试时加上
			// index = 2
			// this.certificateIndex = index
			// this.certificateTitle = this.otherCertificateList1[index].tabTitle
			// this.certificateType = this.otherCertificateList1[index].value
			// this.isUpload = false

			this.getCertList()
			uni.setNavigationBarTitle({
				title: this.certificateTitle
			})
		},
		mounted() {
			this.initInfo()
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #ffffff;
	}

	.head {
		width: 100%;
	}

	.head text {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 40rpx;
		font-weight: bold;
		padding-left: 40rpx;
	}

	.tab-tips {
		width: 100%;
		line-height: 80rpx;
		height: 80rpx;
		font-size: 32rpx;
		color: #fff;

		text {
			margin: 0 40rpx;
		}
	}

	.upload-img {
		width: 100%;
		height: 300rpx;
		padding-left: 40rpx;
		margin-bottom: 60rpx;

		img {
			display: block;
			width: 300rpx;
			height: 300rpx;
		}
	}

	// 图片上传列表
	.upload-img-list {
		display: flex;
		flex-wrap: wrap;
		width: 92%;
		height: auto;
		padding: 20rpx 4%;
	}

	.upload-img-delete {
		position: absolute;
		margin: -216rpx 0 0 166rpx;
	}

	.upload-img-item {
		display: inline-block;
		width: 33%;

		img {
			display: block;
			margin: 20rpx auto;
			width: 200rpx;
			height: 200rpx;
		}
	}

	// 上传选择栏目
	.upload-tab {
		height: 120rpx;
		width: 100%;
		border-bottom: #f4f4f5 2px solid;
		background-color: #ffffff;

		text {
			display: block;
			line-height: 120rpx;
			height: 100%;
			font-size: 36rpx;
		}

		text:first-child {
			float: left;
			width: 300rpx;
			padding-left: 40rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			float: right;
			padding-right: 40rpx;
		}
	}

	// 证件号码输入框
	.upload-input {
		position: absolute;
		display: block;
		width: 60%;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 20rpx;
		font-size: 36rpx;
		text-align: right;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
		right: 20rpx;
	}

	.upload-tips {
		position: absolute;
		height: 100rpx;
		width: 100%;
		bottom: 180rpx;

		text {
			display: block;
			font-size: 32rpx;
			color: #909399;
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
		}
	}

	// 保存按钮
	.btn-bottom {
		button {
			position: absolute;
			bottom: 0rpx;
			left: calc(10%);
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>