<template>
	<view class="">
		<view class="u-page w10 h10">
			<view class="bacf f16 flex-col-c" style="margin: 40rpx auto;padding-bottom: 50rpx;">
				<u-image :src="headImg  ? headImg : src1" width="80" height="80" radius="20"
					@click="uploadImg"></u-image>
				<view class="lh35 f15">{{realName}}</view>
				<view class="f14 text-c">工号：{{no}}</view>
				<view class="f14 text-c">门店：{{storeName||'小羽佳家政'}}</view>
			</view>
			<!-- 业务管理 -->
			<u-notice-bar customStyle="width:93%;" speed="45" color="red" bgColor="#FFFFFF" :text="promptMsg"
				mode="closable"></u-notice-bar>
			<view class="u-page-part2 bacf">
				<view class="u-page-part2-title f18 fb">业务管理</view>
				<view class="u-page-part2-con flex-col" style="flex-direction: row;flex-wrap: wrap;">
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon1" mode=""
							@click="goto('/pages-work/business/order/orderIndex')">
						</image>
						<text class="u-page-part2-text f14 text-c">接单</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon2" mode=""
							@click="goto('/pages-work/business/time/timeIndex')">
						</image>
						<text class="u-page-part2-text f14 text-c">时间</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon3" mode=""
							@click="goto('/pages-work/business/salary')"></image>
						<text class="u-page-part2-text f14 text-c">工资</text>
					</view>
					<!-- </view> -->
					<!-- <view class="u-page-part2-con flex-col" style="flex-direction: row;"> -->
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon4" mode=""
							@click="goto('/pages-work/business/evaluate')">
						</image>
						<text class="u-page-part2-text f14 text-c">评价</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon5" mode=""
							@click="goto('/pages-work/business/point/pointsIndex')">
						</image>
						<text class="u-page-part2-text f14 text-c">积分</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon6" mode=""
							@click="goto('/pages-work/business/teacherPupil/apprentice')">
						</image>
						<text class="u-page-part2-text f14 text-c">徒弟</text>
					</view>
					<!-- </view> -->
					<!-- <view class="u-page-part2-con flex-col" style="flex-direction: row;"> -->
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon7" mode=""
							@click="goto('/pages-work/business/leave/leaveIndex')">
						</image>
						<text class="u-page-part2-text f14 text-c">请假</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon8" mode=""
							@click="goto('/pages-work/business/fund/fundIndex')">
						</image>
						<text class="u-page-part2-text f14 text-c">资金</text>
					</view>
					<view class="u-page-part2-con2 flex-col">
						<image class="u-page-part2-icon" :src="icon9" mode=""
							@click="goto('/pages-work/business/developOrder')">
						</image>
						<text class="u-page-part2-text f14 text-c">开发</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>
		
		<!-- 提现信息--弹窗 -->
		<u-popup :show="withdrawPopup" :round="10" mode="bottom" @close="withdrawPopup=false" :closeable="true">
		  <view class="f18 fb text-c lh50">提现认证信息</view>
		  <u--form labelPosition="left" labelWidth="100px" ref="form" class="w85 mg-at">
		  	<u-form-item label="姓名:" prop="userInfo.name" borderBottom ref="item">
		  		<u--input v-model="withdrawName" border="none" placeholder="请填写姓名"></u--input>
		  	</u-form-item>
		  	<u-form-item label="手机号:" prop="userInfo.tags" borderBottom ref="item">
		  		<u--input v-model="mobile" border="none" placeholder="请填写手机号"></u--input>
		  	</u-form-item>
		  	<u-form-item label="身份证号码:" prop="userInfo.tags" borderBottom ref="item">
		  		<u--input v-model="identityCard" border="none" placeholder="请填写身份证号码"></u--input>
		  	</u-form-item>
		  </u--form>
		  <u-button text="提交" @click="authentication()" color="#1e1848" shape="circle"
		  	customStyle="width:80%;margin: 30rpx auto"></u-button>
		</u-popup>
		
	</view>
</template>
<script>
	export default {
		data() {
			return {
				checkTitle: "",
				checkText: "",
				promptMsg: "",
				checkType: 0,
				withdrawName: uni.getStorageSync("employeeName"),
				identityCard: uni.getStorageSync("idCard"),
				mobile: uni.getStorageSync("phone"),
				realName: uni.getStorageSync("employeeName"),
				storeName: uni.getStorageSync('storeName'),
				no: '',
				withdrawPopup: false,
				headImg: '',
				src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1668683870133tx.png',
				icon1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jd.png',
				icon2: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-time.png',
				icon3: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-gz.png',
				icon4: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-pj2.png',
				icon5: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jf.png',
				icon6: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-td.png',
				icon7: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-qj.png',
				icon8: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zj.png',
				icon9: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1723531470538分销2.png',
			}
		},
		onLoad() {
			this.getEmployee()
			this.checkLogin()
			this.getPromptData()
		},
		methods: {
			getPromptData() {
				let roleId = 0
				if (uni.getStorageSync('roleId') && uni.getStorageSync('roleId') !== null) {
					roleId = uni.getStorageSync('roleId')
				}
				this.http({
					url: 'getPromptData',
					method: 'GET',
					hideLoading: true,
					data: {
						roleId: roleId,
						promptType: 3,
						storeId: uni.getStorageSync('storeId'),
						employeeNo: uni.getStorageSync('employeeNo')
					},
					success: res => {
						if (res.code == 0) {
							if (res.data.idCard) {
								uni.setStorageSync('idCard', res.data.idCard)
							}
							this.promptMsg = '家人好，副业一定超过主业，您还要帮助' + res.data.employeeSum + '人找家政工作，就可以用副业发财了！'
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			authentication(){
				if(!this.withdrawName){
					return uni.showToast({
						title: "请填写姓名!",
						icon: 'none'
					})
				}
				if(!this.mobile){
					return uni.showToast({
						title: "请填写手机号!",
						icon: 'none'
					})
				} else {
					// let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
					let reg = /^[1][3-9][0-9]{9}$/;
					if (!reg.test(this.mobile)) {
					return uni.showToast({
						title: "请填写正确的手机号!",
						icon: 'none'
					})
					}
				}
				if(!this.identityCard){
					return uni.showToast({
						title: "请填写身份证号码!",
						icon: 'none'
					})
				}
				//提现认证
				this.http({
					url: "getFranchiseIfAuth",
					method: 'GET',
					data: {
						employeeId: uni.getStorageSync("employeeId"),
						mobile: this.mobile,
						name: this.withdrawName,
						identityCard: this.identityCard,
					},
					success: res => {
						if (res.code == 0) {
							if (res.data.flag == 0) {
								this.withdrawPopup = false
								return uni.showToast({
									title: '查询到已完成签约灵工平台，快去接单吧！',
									icon: 'none'
								})
							}
							if (res.data.checkRemark) {
								uni.showToast({
									title: res.data.checkRemark + ",即将跳转认证签约平台...",
									icon: 'none'
								})
							}
							this.withdrawPopup = false
							setTimeout(() => {
								if (res.data.flag == 1) {
									let param = {
										url: 'https://lingong-h5-sign-prod.renliwo.com/#/login?vendorId=1745&invitationCode=BUIAOE2E4T&contractConfigId=89&channelChildrenCode=xiaoyujia'
									}
									let data = JSON.stringify(param);
									uni.navigateTo({
										url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
									})
								}
				
							}, 2000)
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			uploadImg() {
				this.openCheck(1, "是否重新上传员工头像")
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					uni.chooseImage({
						success: (chooseImageRes) => {
							const tempFilePaths = chooseImageRes.tempFilePaths;
							uni.uploadFile({
								url: 'https://api2.xiaoyujia.com/system/imageUpload',
								filePath: tempFilePaths[0],
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: (uploadFileRes) => {
									let result = JSON.parse(uploadFileRes.data)
									this.http({
										url: 'uploadHeadImg',
										data: {
											id: uni.getStorageSync("employeeId"),
											remark: result.data
										},
										header: {
											"content-type": "application/json;charset=UTF-8"
										},
										method: 'POST',
										success: res => {
											uni.showToast({
												title: res.msg,
												icon: 'none'
											})
											if (res.code == 0) {
												this.headImg = result.data
											}
										}
									})
								}
							});
						}
					});

				}
			},
			goto(url) {
				console.log(url);
				// // 获取订阅消息授权
				// // #ifdef  MP-WEIXIN
				// wx.requestSubscribeMessage({
				// 	tmplIds: ["YRSv7oviwbjl0U-fo-ghtq6OUG2Zn4u-pqd6Y9ums90",
				// 		"U2NPysqnHSDt_1oVl1Nvbwy_vivhiRfrlHqwwEI3xEQ"
				// 	],
				// 	success: res => {
				// 		console.log("用户同意进行小程序消息订阅！")
				// 	},
				// 	fail: res => {}
				// })
				// // #endif
				if(url=='/pages-work/business/order/orderIndex'&&uni.getStorageSync('storeId')==1038){
					this.getRlwLogData()
				}else{
						uni.navigateTo({
							url: url + "?no=" + this.no
						})
				}
			},
			getRlwLogData() {
				this.http({
					url: 'getRlwLogData',
					method: 'GET',
					data: {
						employeeId: uni.getStorageSync('employeeId')
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								 uni.navigateTo({
									url: '/pages-work/business/order/orderIndex' + "?no=" + this.no
								})
							} else {
								uni.showModal({
									title: '提示',
									content: '检测到您还未进行用工签约，请签约通过后接单!',
									success: res => {
										if (res.confirm) {
											this.withdrawPopup = true
										}
									}
								});
							}
						}
					}
				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-work/index',
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast('还未登录哦，请重新登录')
					uni.setStorageSync('redirectUrl', '/pages-work/index')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
				} else {
					this.getEmployee()
				}
			},
			// 获取员工信息
			getEmployee() {
				// 请求：获取员工信息
				if (uni.getStorageSync("employeeId") !== null) {
					this.http({
						url: 'getEmployeeById',
						method: 'GET',
						hideLoading: true,
						path: uni.getStorageSync("employeeId"),
						success: res => {
							if (res.code == 0) {
								this.no = res.data.no
								this.headImg = res.data.headPortrait;
								uni.setStorageSync('employeeHeadImg', res.data.headPortrait);
								uni.setStorageSync('storeId', res.data.storeId);
								uni.setStorageSync('entryTime', res.data.entryTime);
								if (res.data.isDriver) {
									uni.setStorageSync('isDriver', 1);
								} else {
									uni.setStorageSync("isDriver", 0)
								}
								uni.setStorageSync('score', res.data.score);
								uni.setStorageSync('phone', res.data.phone);
								uni.setStorageSync('workingProperty', res.data.workingProperty);
							}
						},
						fail: err => {}
					});
				}
			},
		},

	}
</script>
<style lang="scss" scoped>
	.u-page {
		background-color: #f4f2f3;
		padding-bottom: 10rpx;
		// padding-bottom: 220rpx;
	}

	.u-page-part2 {
		width: 90%;
		height: 100%;
		margin: 40rpx auto;
		border-radius: 20rpx;
	}

	.u-page-part2-title {
		width: 80%;
		padding: 30rpx 0;
		margin: auto;
		color: #1e1848;
	}

	.u-page-part2-con {
		width: 95%;
		margin: 0 auto;
	}

	.u-page-part2-con2 {
		margin: 20rpx 60rpx;
	}

	.u-page-part2-icon {
		width: 60rpx;
		height: 60rpx;
		border: 2rpx solid #1e1848;
		border-radius: 60rpx;
		padding: 10rpx;
	}

	.u-page-part2-text {
		line-height: 60rpx;
	}
</style>