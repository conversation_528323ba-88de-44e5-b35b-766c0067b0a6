<template>
	<view class="page">
		<view class="w9 mg-at flac-row t-indent">
			<image style="width: 50rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_jinbi2.png"
				mode="widthFix"></image>
			<view class="">积分</view>
			<view class="f18 fb">{{jiaBiAmount||0}}</view>
		</view>
		<view class="w85 lh50 mg-at flac-row f14">
			<view class="" @click="goToUrl('./exchangeLog')">兑换记录 ></view>
			<view class="t-indent2" @click="goToUrl('/pages-mine/signIn/IntegralPage')">积分详情 ></view>
		</view>
		<!-- <view class="w9 mg-at flac-row-a">
			<view class="btnStyle" :class="changeColor == 1 ? 'AbtnStyle' : ''" @click="changeColor = 1">兑换记录</view>
			<view class="btnStyle" :class="changeColor == 2 ? 'AbtnStyle' : ''" @click="changeColor = 2">积分明细</view>
		</view> -->
		<view class="w9 mg-at flac-row" style="flex-wrap: wrap;">
			<view class="cardBox" v-for="(item,i) in productList" :key="i" @click="goPage(item)">
				<view class="tipStyle1" v-if="item.ifPopular == 1">热门</view>
				<view class="tipStyle2" v-if="item.inventory<=0">已抢光</view>
				<image class="imgBox" :src="item.productPicture" mode="aspectFit"></image>
				<view class="w85 mg-at">{{item.productName || '商品名称'}}</view>
				<view class="w85 mg-at f14 lh25 fb">{{item.exchangeIntegral || '未知'}}
					积分
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jiaBiAmount: {},
				changeColor: 0,
				size: 10,
				loadMore: 0,
				current: 1,
				productList: []
			};
		},
		onLoad() {
			this.checkLogin()
			this.getJiaBiAmount()
			this.getAllIntegralProduct()
		},
		onReachBottom() {
			// 滑动到底部刷新
			// console.log("滑动到底部加载更多...")
			this.loadMore++
		},
		watch: {
			loadMore: {
				handler(newValue, oldVal) {
					this.current++
					this.getAllIntegralProduct()
				},
				deep: true
			}
		},
		methods: {
			goToUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					let url = '/pages-mine/intergralMall/productIndex'
					uni.setStorageSync('redirectUrl', url)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				}
			},
			// 获取积分数量
			getJiaBiAmount() {
				// 请求：获取积分数量
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: uni.getStorageSync('memberId')
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
						} else {}
					},
					fail: err => {}
				})
			},
			getAllIntegralProduct() {
				this.http({
					url: 'getAllIntegralProduct',
					method: 'GET',
					hideLoading: true,
					data: {
						'productExchangeType': 1,
						'size': this.size,
						'current': this.current,
					},
					success: res => {
						if (res.code == 0) {
							let dataList = res.data.records
							this.productList = this.productList.concat(dataList)
						}
					}
				})
			},
			goPage(item) {
				uni.removeStorageSync('userAddress')
				uni.setStorageSync('productData', encodeURIComponent(JSON.stringify(item)))
				uni.navigateTo({
					url: './productDetails?id=' + item.id
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		min-height: 100vh;
		margin: auto;
		padding: 50rpx 0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jfStore_bg.png') no-repeat;
		background-size: 100% 100%;
	}

	.btnStyle {
		width: 45%;
		margin: 20rpx auto;
		border: 2rpx solid #eee;
		border-radius: 8rpx;
		text-align: center;
		line-height: 50rpx;
	}

	.cardBox {
		position: relative;
		width: 45%;
		height: 30vh;
		margin: 20rpx 15rpx;
		border: 2rpx solid #eee;
		border-radius: 20rpx;
		background-color: #fff;
	}

	.imgBox {
		width: 87%;
		margin: auto;
		height: 200rpx;
		padding: 20rpx;
		background-color: #fff;
		border-top-left-radius: 18rpx;
		border-top-right-radius: 18rpx;
		border-bottom: 2rpx solid #eee;
	}

	.AbtnStyle {
		color: #fff;
		background-color: #a4adb3;
	}

	.tipStyle1 {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
		width: 30%;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		background: red;
		line-height: 40rpx;
		border-top-left-radius: 18rpx;
		border-bottom-right-radius: 18rpx;
	}

	.tipStyle2 {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
		width: 30%;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		background: #f1c36a;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-top-left-radius: 18rpx;
		border-bottom-right-radius: 18rpx;
	}
</style>