<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="resume-tab">
			<!-- 身份证上传部分 -->
			<view v-if="isShowUpload">
				<view class="head">
					<text>拍摄/上传您本人的二代身份证</text>
				</view>

				<view class="upload">
					<view class="upload-view" @longpress="openImgPreview(0)">
						<img class="upload-img" :src="identityFront" @click="uploadImg(0)">
					</view>
				</view>

				<view class="upload">
					<view class="upload-view" @longpress="openImgPreview(1)">
						<img class="upload-img" :src="identityBack" @click="uploadImg(1)">
					</view>
				</view>
			</view>

			<view v-if="!isShowUpload">
				<view class="head">
					<text @click="isShowUpload=true" style="color: #ff4d4b;">已经上传过啦，点击后可重新上传!</text>
				</view>
			</view>

			<view style="font-size: 30rpx;margin: 0 60rpx;line-height: 40rpx;" v-if="isShowUpload">
				<text>*长按可预览照片</text>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>身份证信息</text>
				</view>

				<view class="tab-head-smail">
					<text>姓名</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="employee.realName" :placeholder="uploadTips"
							:disabled="isAllowInput?false:true">
						</input>
					</view>
				</view>

				<view class="tab-head-smail">
					<text>身份证号</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="employeeInfo.idCard" :placeholder="uploadTips"
							disabled="true">
						</input>
					</view>
				</view>

				<view class="tab-head-smail">
					<text>身份证地址</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="employeeInfo.hometown" :placeholder="uploadTips"
							disabled="true">
						</input>
					</view>
				</view>

				<view class="tab-head-smail">
					<text>有效期</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="baomuInfo.idCardTime" :placeholder="uploadTips"
							disabled="true">
						</input>
					</view>
				</view>

				<view class="tab-head-smail">
					<text>生日</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="employeeInfo.birthTime" :placeholder="uploadTips"
							disabled="true">
						</input>
					</view>
				</view>

			</view>
		</view>


		<!-- 文字提示和保存按钮 -->
		<view style="margin-top: 200rpx;">
			<view class="upload-tips" style="padding-bottom: 100rpx;">
				<text>信息仅用于身份验证，小羽佳保障您的信息安全</text>
			</view>
		</view>

		<view class="btn-bottom-fixed">
			<button @click="trySave()">确 认</button>
		</view>


	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				// 可设置
				// 最大入驻年龄
				maxAge: 65,
				// 最小入驻年龄
				minAge: 18,

				identityFront: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBack: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				fileFront: [],
				fileBack: [],
				memberId: null,
				baomuId: null,
				employeeId: null,
				isAllowInput: false,
				isAuthenticated: false,
				shimingState: false,
				isShowUpload: true,
				uploadTips: "上传身份证自动识别",
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				birthTimeYear: 2023,
				idcard1: [{
					content: "",
					certificate: {
						title: "身份证正面",
						employeeId: this.baomuId,
						certificateType: 1,
						certificateImg: null,
						validity: null
					}
				}],
				idcard2: [{
					content: "",
					certificate: {
						title: "身份证反面",
						employeeId: this.baomuId,
						certificateType: 2,
						certificateImg: null,
						validity: null
					}
				}],
				certificateList: [],
				// 员工
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				// 员工详细信息
				employeeInfo: {
					employeeId: this.employeeId,
					sex: null,
					zodiac: 0,
					workingState: 0,
					religion: 0,
					languagenum: null,
					educationnum: null,
					birthTime: null,
					idCard: null,
					hometown: null,
					nation: null,
					education: null,
					married: null,
					tomarried: null,
					language: null,
					workYear: null,
					family: null,
				},
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null,
					constellation: null,
				}
			}
		},
		methods: {
			// 打开图片预览
			openImgPreview(index) {
				let data = []
				let img = ""
				if (index == 0) {
					data.push(this.identityFront)
					data.push(this.identityBack)
					img = this.identityFront
				} else {
					data.push(this.identityBack)
					data.push(this.identityFront)
					img = this.identityBack
				}
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			uploadImg(value) {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						// 将图片上传至后台
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'idCard'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.idcard1[0].certificate.certificateImg = result.data
								} else if (value == 1) {
									this.idcard2[0].certificate.certificateImg = result.data
								}
							}
						})
						// #ifdef  H5
						pathToBase64(tempFilePaths)
							.then(base64 => {
								if (value == 0) {
									this.identityFront = tempFilePaths[0]
									this.idcard1[0].content = base64
									this.vidcardNext()
								} else if (value == 1) {
									this.identityBack = tempFilePaths[0]
									this.idcard2[0].content = base64
									this.vidcard1Next()
								}
							})
							.catch(error => {
								console.error("转化失败！")
							})
						// #endif
						// #ifdef  MP-WEIXIN
						const base64 = this.urlTobase64(tempFilePaths[0])
						if (value == 0) {
							this.identityFront = tempFilePaths[0]
							this.idcard1[0].content = base64
							this.vidcardNext()
						} else if (value == 1) {
							this.identityBack = tempFilePaths[0]
							this.idcard2[0].content = base64
							this.vidcard1Next()
						}
						// #endif
					}
				});
			},
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			// 尝试保存
			trySave() {
				if (!this.isShowUpload) {
					this.$refs.uNotify.error("身份信息已认证，可点击上方提示重新上传哦!")
				}
				// else if (this.idcard1[0].content == "") {
				// 	this.$refs.uNotify.error("身份证正面还未上传或无法识别！")
				// } else if (this.idcard2[0].content == "") {
				// 	this.$refs.uNotify.error("身份背面还未上传或无法识别！")
				// } else if (this.isIdCardInfoError()) {
				// 	this.$refs.uNotify.error("身份证信息识别不全，请重新上传！")
				// } 
				else if (this.employeeInfo.idCard == null) {
					this.$refs.uNotify.error("身份证正面还未上传或无法识别！")
				} else if (this.baomuInfo.idCardTime == null) {
					this.$refs.uNotify.error("身份背面还未上传或无法识别！")
				} else if (this.overAge()) {

				} else {
					this.checkBaomuAndInit()
					let timer = setTimeout(() => {
						if (this.baomuId !== null) {
							console.log("开始上传！")
							this.addCertificate(this.idcard1[0].certificate)
							this.addCertificate(this.idcard2[0].certificate)
							this.shimingState = true
							this.updateEmployee()
							this.updateEmployeeInfo()
							this.updateBaomuInfo()
							this.$refs.uNotify.success("身份证信息认证完成！")
						}
					}, 1000);
				}
			},
			// 检查识别的信息，若信息不全则无法更新身份证信息
			isIdCardInfoError() {
				let result = false
				if (this.employee.realName == "" || this.employeeInfo.idCard == "" || this.employeeInfo.birthTime == "" ||
					this
					.employeeInfo.hometown == "" || this.baomuInfo.zodiac == "") {
					result = true
				}
				return result
			},
			// 识别身份证正面
			vidcardNext() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				console.log("读取图片后进行请求！")
				// 请求：识别身份证
				let data = {
					file: this.idcard1[0].content,
					cardTpe: 0
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证正面上传成功！")
							this.employee.realName = res.data.words_result.姓名.words
							this.employeeInfo.idCard = res.data.words_result.公民身份号码.words
							this.employeeInfo.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeInfo.hometown = res.data.words_result.住址.words
							this.baomuInfo.zodiac = this.getshengxiao(res.data.words_result.出生.words.substring(
								0, 4))
							this.baomuInfo.constellation = this.getConstellation(res.data.words_result.出生
								.words)
							this.employeeInfo.nation = res.data.words_result.民族.words
							this.employeeInfo.sex = this.getSex(res.data.words_result.性别.words)
						} else if (status === 'unknown' && res.data.idcard_number_type === 0) {
							this.employee.realName = res.data.words_result.姓名.words
							this.employeeInfo.idCard = res.data.words_result.公民身份号码.words
							this.employeeInfo.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeInfo.hometown = res.data.words_result.住址.words
							this.baomuInfo.zodiac = this.getshengxiao(res.data.words_result.出生.words.substring(
								0, 4))
							this.baomuInfo.constellation = this.getConstellation(res.data.words_result.出生
								.words)
							this.employeeInfo.nation = res.data.words_result.民族.words
							this.employeeInfo.sex = this.getSex(res.data.words_result.性别.words)
							this.isAllowInput = true
							this.$refs.uNotify.error("因第三方接口不识别，请自行输入身份证号码！")
						} else {
							let msg = '身份证认证失败，请重新上传';
							if (status != null) {
								if (status === 'reversed_side') {
									msg = '身份证应上传照片面'
								}
								if (status === 'non_idcard') {
									msg = '上传的图片中不包含身份证'
								}
								if (status === 'blurred') {
									msg = '身份证模糊'
								}
								if (status === 'other_type_card') {
									msg = '其他类型证照'
								}
								if (status === 'over_exposure') {
									msg = '身份证关键字段反光或过曝'
								}
								if (status === 'over_dark') {
									msg = '身份证欠曝（亮度过低）'
								}
							}
							this.idcard1[0].content = ""
							this.$refs.uNotify.error(msg)
						}
						if (this.employeeInfo.birthTime && this.employeeInfo.birthTime.length > 8) {
							this.birthTimeYear = this.employeeInfo.birthTime.substring(0, 4) || 2023
							this.birthTimeYear = parseInt(this.birthTimeYear)
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			// 识别身份证背面
			vidcard1Next() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				// 请求：识别身份证
				let data = {
					file: this.idcard2[0].content,
					cardTpe: 1
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证背面上传成功！")
							this.baomuInfo.idCardTime = this.getMyTime(res.data.words_result.失效日期.words)
						} else {
							this.idcard2[0].content = ""
							this.$refs.uNotify.error("身份证背面认证失败，请检查有效期限后请重新上传")
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			useImg(base64) {
				this.fileFront[0].content = base64;
				// console.log(base64.length);
				this.vidcardNext();
			},
			// 图片压缩处理
			dealImage(base64, w, callback) {
				var newImage = new Image();
				var quality = 0.6; //压缩系数0-1之间
				newImage.src = base64;
				newImage.setAttribute("crossOrigin", 'Anonymous'); //url为外域时需要
				var imgWidth, imgHeight;
				newImage.onload = function() {
					imgWidth = this.width;
					imgHeight = this.height;
					var canvas = document.createElement("canvas");
					var ctx = canvas.getContext("2d");
					if (Math.max(imgWidth, imgHeight) > w) {
						if (imgWidth > imgHeight) {
							canvas.width = w;
							canvas.height = w * imgHeight / imgWidth;
						} else {
							canvas.height = w;
							canvas.width = w * imgWidth / imgHeight;
						}
					} else {
						canvas.width = imgWidth;
						canvas.height = imgHeight;
						quality = 0.6;
					}
					ctx.clearRect(0, 0, canvas.width, canvas.height);
					ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
					var base64 = canvas.toDataURL("image/jpeg", quality); //压缩语句
					callback(base64); //必须通过回调函数返回，否则无法及时拿到该值
				}
			},
			// 获取星座
			getConstellation(dateStr) {
				let constellation = ''
				// 使用正则表达式检查日期格式
				if (!dateStr || !/^\d{4}\d{2}\d{2}$/.test(dateStr)) {
					return constellation
				}
				// 解析年月日
				const year = parseInt(dateStr.substring(0, 4));
				const month = parseInt(dateStr.substring(4, 6));
				const day = parseInt(dateStr.substring(6, 8));
				// 根据日期计算星座
				if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) {
					constellation = '水瓶座';
				} else if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) {
					constellation = '双鱼座';
				} else if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) {
					constellation = '白羊座';
				} else if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) {
					constellation = '金牛座';
				} else if ((month === 5 && day >= 21) || (month === 6 && day <= 21)) {
					constellation = '双子座';
				} else if ((month === 6 && day >= 22) || (month === 7 && day <= 22)) {
					constellation = '巨蟹座';
				} else if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) {
					constellation = '狮子座';
				} else if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) {
					constellation = '处女座';
				} else if ((month === 9 && day >= 23) || (month === 10 && day <= 23)) {
					constellation = '天秤座';
				} else if ((month === 10 && day >= 24) || (month === 11 && day <= 22)) {
					constellation = '天蝎座';
				} else if ((month === 11 && day >= 23) || (month === 12 && day <= 21)) {
					constellation = '射手座';
				} else if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) {
					constellation = '摩羯座';
				} else {
					constellation = '';
				}
				return constellation
			},
			getshengxiao(yyyy) {
				var arr = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
				return /^\d{4}$/.test(yyyy) ? arr[yyyy % 12] : null
			},
			getMyTime(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8)
				if (time.includes("长期")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime1(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(5, 7) + "-" + time.substring(8, 10)
				if (time.includes("3000-01-01")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime2(time) {
				if (time.includes("长期")) {
					return "3000/01/01"
				} else {
					let dateTime = time.substring(0, 4) + "/" + time.substring(5, 7) + "/" + time.substring(8, 10)
					return dateTime
				}
			},
			getSex(sex) {
				let result = 2
				if (sex == "男") {
					result = 1
				} else if (sex == "女") {
					result = 2
				}
				return result
			},
			// 年龄超出
			overAge() {
				let nowDateYear = new Date().getFullYear()
				let age = nowDateYear - (this.birthTimeYear || 2023)
				console.log('入驻员工年龄：', age)
				if (age < this.minAge && this.birthTimeYear != 2023) {
					this.$toast.toast("抱歉，您的年龄低于" + this.minAge + "岁，依据平台规定，暂不符合入驻条件！")
					return true
				}
				return false
			},
			// 获取员工已经上传的证件
			getCertificateByEmployeeId() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getCertificateByEmployeeId',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								this.certificateList = res.data
								// 格式化证件数据，显示已上传的身份证正反面
								for (let item of this.certificateList) {
									let certificateType = item.certificateType
									if (certificateType == 1) {
										this.identityFront = item.certificateImg
									} else if (certificateType == 2) {
										this.identityBack = item.certificateImg
									}
								}
							}
						},
					});
				}
			},
			// 更新证件图片
			addCertificate(data) {
				this.$set(this.idcard1[0].certificate, 'employeeId', this.baomuId)
				this.$set(this.idcard2[0].certificate, 'employeeId', this.baomuId)

				this.idcard1[0].certificate.validity = this.getMyTime2(this.baomuInfo.idCardTime)
				this.idcard2[0].certificate.validity = this.getMyTime2(this.baomuInfo.idCardTime)
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: data,
					success: res => {
						if (res.code == 0) {
							console.log('身份证照片上传成功！')
						} else {
							console.log('身份证照片上传失败！')
						}
					}
				})
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				if (this.baomuId === null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync("baomuId", this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log("初始化的保姆ID为" + this.baomuId)
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 更新员工信息
			updateEmployee() {
				this.$set(this.employee, 'id', this.baomuId)
				// 同步更新实名认证状态
				if (this.shimingState) {
					this.employee.shimingState = 1
				}
				this.http({
					url: 'updateBaomu',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(this.employee),
					success: res => {
						if (res.code == 0) {
							console.log("更新员工信息-成功！")
						} else {
							console.log("更新员工信息-返回错误！")
						}
					},
					fail: err => {
						console.log('更新员工信息-请求失败！' + res.code)
					}
				})
			},
			// 更新员工详细信息
			updateEmployeeInfo() {
				this.$set(this.employeeInfo, 'employeeId', this.baomuId)
				this.http({
					url: 'updateEmployeeInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.employeeInfo,
					success: res => {
						if (res.code == 0) {
							if (this.employeeInfo.idCard != null) {
								this.isAuthenticated = true
								this.isShowUpload = false
								uni.setStorageSync("isAuthenticated", true)
							}
						} else {
							console.log("更新员工详细信息-返回错误！")
						}
					},
					fail: err => {
						console.log('更新员工信息-请求失败！' + res.code)
					}
				})
			},
			// 更新保姆信息
			updateBaomuInfo() {
				this.$set(this.baomuInfo, 'baomuId', this.baomuId)
				let idCardTime = this.baomuInfo.idCardTime
				if (idCardTime.includes("长期")) {
					this.baomuInfo.idCardTime = "3000-01-01" + " 00:00:00.000"
				} else {
					this.baomuInfo.idCardTime = idCardTime + " 00:00:00.000"
				}
				this.http({
					url: 'updateBaomuInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: this.baomuInfo,
					success: res => {
						this.baomuInfo.idCardTime = idCardTime
						if (res.code == 0) {
							console.log("更新保姆信息-成功！")
						} else {
							console.log("更新保姆信息-返回错误！" + res.msg)
						}
					},
					fail: err => {
						console.log('更新保姆信息失败-请求错误！' + res.code)
					}
				})
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								// 获取员工信息
								this.employee = baomuDetail.employee

								// 获取员工详细信息
								this.employeeInfo = baomuDetail.employeeInfo
								if (this.employeeInfo.idCard && this.employeeInfo.birthTime) {
									this.isAuthenticated = true
									this.isShowUpload = false
									// 尝试取出身份证信息
									this.getCertificateByEmployeeId()
								}

								// 获取保姆详细
								this.baomuInfo = baomuDetail.baomuInfo
								this.baomuInfo.idCardTime = this.getMyTime1(this.baomuInfo.idCardTime)

								console.log('获取保姆详细信息成功-请求成功！')
							} else {
								console.log('获取保姆详细信息成功-请求失败！')
							}
						}
					});
				}
			},
			// 尝试从登录后的缓存中获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let baomuId = uni.getStorageSync('baomuId')
				baomuId = baomuId == '' ? null : baomuId
				this.memberId = memberId
				// 如果在上一个页面没有获取参数，则从缓存中取值
				if (this.baomuId == undefined || this.baomuId == null) {
					this.baomuId = baomuId
				}
				console.log("会员ID：" + memberId + "保姆ID：" + baomuId)
			},
			// 用户信息初始化
			orginInfo() {
				this.getMemberInfor()
				this.getBaomuDetail()
			}
		},
		// 获取上个页面回传的数据
		onLoad(options) {
			this.baomuId = JSON.parse(options.baomuId)
			this.orginInfo()
		},
		// 页面载入后
		mounted() {

		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.head {
		width: 100%;
	}

	.head text {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 40rpx;
		font-weight: bold;
		padding-left: 40rpx;
	}

	.upload {
		margin-bottom: 60rpx;
		width: 100%;
	}

	.upload-view {
		width: 620rpx;
		height: 400rpx;
		margin: 0rpx auto;
	}

	.upload-img {
		width: 620rpx;
		height: 400rpx;
		border-radius: 20rpx;
	}

	.upload-tips {
		height: 100rpx;
		width: 100%;
		bottom: 200rpx;

		text {
			display: block;
			font-size: 32rpx;
			color: #909399;
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
		}
	}
</style>