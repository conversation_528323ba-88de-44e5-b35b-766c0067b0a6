<template>
	<view class="page">
		<u-notify ref="uNotify"></u-notify>

		<view class="w9 mg-at flac-row" style="flex-wrap: wrap;">
			<view class="cardBox" v-for="(item,index) in list" :key="index">
				<view class="tipStyle1" v-if="item.isHot == 1">热门</view>
				<image class="imgBox" :src="item.img" mode="aspectFit"></image>
				<view class="w85 mg-at">{{item.name || '商品名称'}}</view>
				<view class="w85 mg-at f14 lh25 fb">￥{{item.amount || '0'}} 起
				</view>
				<view class="w85 mg-at f14 lh25">{{item.content || ''}}
				</view>
				<view>
					<button class="btn" @click="goPage(item)">购买</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jiaBiAmount: {},
				changeColor: 0,
				size: 10,
				loadMore: 0,
				current: 1,
				list: []
			};
		},
		onLoad() {
			this.checkLogin()
			this.listEmployeeMaterial()
		},
		methods: {
			goToUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					let url = '/pages-other/employee/employeeMaterial'
					uni.setStorageSync('redirectUrl', url)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				}
			},
			listEmployeeMaterial() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/listEmployeeMaterial',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {},
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						}
					}
				})
			},
			goPage(item) {
				uni.setClipboardData({
					data: item.url,
					success: () => {
						this.$refs.uNotify.success('购买链接已复制!')
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		min-height: 100vh;
		margin: auto;
		padding: 50rpx 0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jfStore_bg.png') no-repeat;
		background-size: 100% 100%;
	}

	.btnStyle {
		width: 45%;
		margin: 20rpx auto;
		border: 2rpx solid #eee;
		border-radius: 8rpx;
		text-align: center;
		line-height: 50rpx;
	}

	.cardBox {
		position: relative;
		width: 45%;
		height: auto;
		margin: 20rpx 15rpx;
		border: 2rpx solid #eee;
		border-radius: 20rpx;
		background-color: #fff;
		padding-bottom: 20rpx;
	}

	.imgBox {
		width: 87%;
		margin: auto;
		height: 200rpx;
		padding: 20rpx;
		background-color: #fff;
		border-top-left-radius: 18rpx;
		border-top-right-radius: 18rpx;
		border-bottom: 2rpx solid #eee;
	}

	.AbtnStyle {
		color: #fff;
		background-color: #a4adb3;
	}

	.tipStyle1 {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
		width: 30%;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		background: red;
		line-height: 40rpx;
		border-top-left-radius: 18rpx;
		border-bottom-right-radius: 18rpx;
	}

	.tipStyle2 {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
		width: 30%;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		background: #f1c36a;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-top-left-radius: 18rpx;
		border-bottom-right-radius: 18rpx;
	}

	.btn {
		display: block;
		padding: 0;
		margin: 10rpx 20rpx 10rpx 160rpx;
		width: 40%;
		height: 50rpx;
		line-height: 50rpx;
		color: #f6cc70;
		background-color: #1e1848;
		border-radius: 50rpx;
		font-size: 28rpx;
	}
</style>