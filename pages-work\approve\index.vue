<template>
	<view>
		<u-notify ref="uNotify"></u-notify>
		<!-- 导航栏 -->
		<u-sticky>
			<u-tabs :list="tabsList" style="background-color: white;" :current="pageIndex" :scrollable="false" lineWidth="100" lineHeight="2"
				lineColor="#4a456b" :activeStyle="{fontSize:'36rpx',color: '#4a456b',fontWeight: 'bold',}"
				:inactiveStyle="{fontSize:'36rpx',color: '#666',}" itemStyle="width:50%;margin: 20rpx auto"
				@click="changePage" />
			<view v-if="roleId === 100 || roleId === 1">
				<view class="lh35 f15 text-c selectBox" @click="pickerShow = true">选择操作的门店</view>
				<view class="f14 text-c lh30">当前选择门店：<text class="red f16">{{changeRoleStoreName}}</text></view>
			</view>
			<u-tabs v-if="pageIndex == 0" :list="menuList" @click="choiceMenu" :current="menuIndex" :scrollable="false"
				lineWidth="20" lineHeight="8" :lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
				        color: '#1e1848',
				        fontWeight: 'bold',
				        transform: 'scale(1.1)'
				    }" :inactiveStyle="{
				        color: '#333',
				        transform: 'scale(1)'
				    }" itemStyle="width:20%;height: 100rpx;" />
		</u-sticky>
		<view class="w100 mg-at container flac-row" v-if="pageIndex == 0">
			<view class="btn text-c flac-row-c" v-for="(item,i) in btnList" :key="i" @click="clickBtn(i)"
				v-if="menuIndex == 0">
				<u-icon :name="item.url" size="20" />
				<view class="t-indent">{{item.name}}</view>
			</view>
			<view class="btn text-c flac-row-c" v-for="(item,i) in btnList" :key="i" @click="clickBtn(i)"
				v-if="item.typeId == menuIndex">
				<u-icon :name="item.url" size="20" />
				<view class="t-indent">{{item.name}}</view>
			</view>
		</view>
		<view class="w95 mg-at" v-if="pageIndex == 1">
			<u-empty v-if="!list.length" text="暂无审批内容" icon="http://cdn.uviewui.com/uview/empty/list.png" />
			<view class="w9 flac-row-b" style="margin: 40rpx auto;border-bottom: 2rpx solid #ccc;padding-bottom: 20rpx;"
				v-for="(item,i) in list" :key="i">
				<view class="">
					<view v-if="item.type === 4">
						<view class="f16" v-if="type === 4">合同转移</view>
						<u-text :text="'合同号：'+item.contractNo"></u-text>
						<u-text :text="'申请时间：'+item.creatDate"></u-text>
					</view>
					<view v-if="item.type === 5">
						<view class="f16">线索(合同)集团单改价</view>
						<u-text :text="'订单号：'+item.billNo"></u-text>
						<u-text :text="'订单金额：'+item.realTotalAmount"></u-text>
						<u-text :text="'更改金额：'+ (item.realTotalAmount - item.discountAmount)"></u-text>
					</view>
					<view v-if="item.type === 6">
						<view class="f16">标准单开通</view>
						<u-text :text="item.openStoreName"></u-text>
						<u-text :text="'开通区域：'+item.openCityName+'-'+item.openAreaName"></u-text>
						<u-text :text="'开通产品：'+item.openProductNameList"></u-text>
					</view>
					<view v-if="item.type === 9">
						<view class="f16">订单开发人异常</view>
						<u-text :text="'订单编号：'+item.billNo"></u-text>
						<u-text :text="'期望开发人：'+item.modifyEmployeeNo"></u-text>
						<u-text :text="'申请说明：'+item.remark"></u-text>
						<u-text text="开发人证明："></u-text>
						<u-upload maxCount="1" :fileList="item.fileList1" :previewFullImage="true" name="1" multiple></u-upload>
					</view>
					<view v-if="item.type === 10">
						<view class="f16">订单营业额扣减</view>
						<u-text :text="'订单编号：'+item.billNo"></u-text>
						<u-text :text="'扣减金额：'+item.deductionAmount"></u-text>
						<u-text :text="'申请说明：'+item.remark"></u-text>
					</view>
					<view v-if="item.type === 11">
						<view class="f16">订单开发奖扣减</view>
						<u-text :text="'订单编号：'+item.billNo"></u-text>
						<u-text :text="'申请说明：'+item.remark"></u-text>
					</view>
					<view v-if="item.type === 13">
						<view class="f16">线索类改价</view>
						<u-text :text="'线索号：'+item.orderNeedsId"></u-text>
						<u-text :text="'原线索薪资：'+item.oldSalary"></u-text>
						<u-text :text="'改价线索薪资：'+item.newSalary"></u-text>
						<u-text :text="'原中介费：'+item.oldAgencyFee"></u-text>
						<u-text :text="'改价中介费：'+item.newAgencyFee"></u-text>
					</view>
					<view v-if="item.type === 14">
						<view class="f16">线索流转</view>
						<u-text :text="'线索号：'+item.orderNeedsId"></u-text>
						<u-text :text="'原线索门店：'+item.oldStoreName"></u-text>
						<u-text :text="'原线索经纪人：'+item.oldAgentName"></u-text>
					</view>
					<view>申请时间：{{item.creatDate}}</view>
					<view>当前审批人：{{item.directorName}}</view>
					<view v-if="item.approvalRemark">审批备注：{{item.approvalRemark}}</view>
					<u-button
						customStyle="text-align: center;border-radius: 40rpx;color: white;color:#f6cc70;background-color: #1e1848;"
						@click="urgeApprove(item.id)" shape="circle" text="催办"></u-button>
				</view>

				<view class="f14 stateStyle primary" v-if="item.state == 1 || item.state == 2">审批中</view>
				<view class="f14 stateStyle success" v-if="item.state >= 3">审批成功</view>
				<view class="f14 stateStyle fail" v-if="item.state == 0">拒绝申请</view>
			</view>
		</view>
		<u-popup :show="show" @close="show=false" :round="10" :closeable="true" :closeOnClickOverlay="false">
			<view class="f18 fb text-c" style="margin-top: 50rpx;">{{popupTitle}}</view>
			<view class="formStyle" v-if="number == 0">
				<view class="w95 mg-at flac-row f16 lh80">
					<view class="w35 fb">开通服务产品</view>
					<view class="text-c c9" @click="showPicker">{{remind || '请选择需要的服务产品'}}</view>
					<baTreePicker ref="treePicker" :multiple="true" @select-change="selectChange" title="选择服务产品"
						:localdata="listData" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">所属门店</view>
					<view>{{productData.openStoreName}}</view>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">开通区域</view>
					<view @click="showPickerCity = true">{{this.productData.openAreaName || '点击选择区域'}}</view>
					 <u-picker :show="showPickerCity" :columns="cityAreaList" keyName="name" @cancel="showPickerCity = false" @confirm="changeTag"></u-picker>
					<!-- <scroll-view scroll-y="true" style="padding: 25rpx;height: calc(25vh - 50px);">
						<view v-for="(item,index) in cityAreaList" :key="item.id" style="height: 75rpx;">
							<uni-tag
								custom-style=" display:inline-block;width:220rpx;height:43rpx;line-height:43rpx;text-align:center;font-weight:500;overflow: hidden;text-overflow: ellipsis;"
								type="warning" :inverted="item.flag" :text="item.name" @click="changeTag(index)" />
						</view>

					</scroll-view> -->
				</view>

				<button class="btnStyle" @click="SubmitOpen">提交</button>
			</view>
			<view class="formStyle" v-if="number == 1">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">订单号</view>
					<input class="uni-input" v-model="priceData.billNo" placeholder="请输入" @blur="getOrderDetail" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">服务项目</view>
					<input class="uni-input" v-model="priceData.productName" :disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">原订单金额</view>
					<input class="uni-input" type="number" v-model="priceData.realTotalAmount" :disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">改价后的金额</view>
					<input class="uni-input" type="number" v-model="priceData.discountAmount" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">改价原因</view>
					<input class="uni-input" v-model="priceData.remark" placeholder="请输入" />
				</view>
				<button class="btnStyle" @click="SubmitPrice">提交</button>
			</view>
			<view class="formStyle" v-if="number == 2">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">合同号</view>
					<input class="uni-input" v-model="contractData.contractNo" placeholder="请输入" />
					<uni-icons type="search" size="30" @click="selectContractByNo"></uni-icons>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">手机号</view>
					<input class="uni-input" v-model="contractData.phone" placeholder="请输入" />
					<uni-icons type="search" size="30" @click="selectContractPhone"></uni-icons>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">当前经纪人</view>
					<input class="uni-input" v-model="contractData.agentName" :disabled="true" />
				</view>
				<button class="btnStyle" @click="SubmitContract">提交</button>
			</view>
			<view class="formStyle" v-if="number == 3">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">姓名</view>
					<input class="uni-input" v-model="accountData.name" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">手机号</view>
					<input class="uni-input" v-model="accountData.phone" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">身份证号</view>
					<input class="uni-input" type="number" v-model="accountData.identity" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">所属角色</view>
					<uni-data-select :clear="false" placeholder="请选择账号角色" v-model="accountData.roleVal"
						:localdata="roleRange" @change="selectStore" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">所属门店</view>
					<uni-data-select :clear="false" placeholder="请选择提交人所在门店" v-model="accountData.storeVal"
						:localdata="storeRange" @change="selectStore" />
				</view>
				<button class="btnStyle" @click="SubmitAccount">提交</button>
			</view>
			<view class="formStyle" v-if="number == 4">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">员工工号</view>
					<input class="uni-input" v-model="cancelData.logoutNo" placeholder="请输入" @blur="selectEmployee" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">员工姓名</view>
					<input class="uni-input" v-model="cancelData.realName" :disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">注销原因</view>
					<input class="uni-input" v-model="cancelData.remark" placeholder="请输入" />
				</view>
				<button class="btnStyle" @click="SubmitCancel">提交</button>
			</view>
			<view class="formStyle" v-if="number == 5">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">绑定员工工号</view>
					<input class="uni-input" v-model="introducerData.modifyEmployeeNo" placeholder="请输入"
						@blur="selectEmployee" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">员工姓名</view>
					<input class="uni-input" v-model="introducerData.realName" :disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">原开发人</view>
					<input class="uni-input" v-model="introducerData.introducer" :disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">申请原因</view>
					<input class="uni-input" v-model="introducerData.remark" placeholder="请输入" />
				</view>
				<view class="w95 mg-at f16 lh30">
					<view class="w10 ">*若通过邀请码入驻后未能成功绑定为你开发的员工，则可在此进行申请</view>
				</view>
				<button class="btnStyle" @click="submitIntroducer">提交</button>
			</view>
			<view class="formStyle" v-if="number == 6">
				<view style="margin-left: 15rpx;">
				<view style="display: flex;">
				<view class="fb" style="margin-top: 20rpx;">我的门店代码：{{storeSource}}</view>
				 <u-icon name="file-text" @click="copyText(storeSource)"></u-icon>
				 </view>
				 <view style="display: flex;">
				<view class="fb" style="margin-top: 20rpx;">我的工号：{{myEmployeeNo}}</view> 
				<u-icon name="file-text" @click="copyText(storeSource)"></u-icon>
				 </view>
				 </view>
				<view class="w95 mg-at flac-row-a f16 lh80" @click="selectBillNo(1)">
					<view class="w3 fb">订单编号</view>
					<input class="uni-input" disabled v-model="orderData.billNo" placeholder="请选择订单" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">期望开发人</view>
					<input class="uni-input" v-model="orderData.modifyEmployeeNo" placeholder="三嫂单：工号，标准单：门店代码" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">申请说明</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">开发人证明</view>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" :previewFullImage="true"
						name="1" multiple :maxCount="5">
					</u-upload>
				</view>
				<view style="color: red;margin-left: 450rpx;margin-top: -120rpx;" @click="lookEx">点击查看证明示例</view>
				<view style="height: 150rpx;"></view>
				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
			<view class="formStyle" v-if="number == 7">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">订单编号</view>
					<input class="uni-input" v-model="orderData.billNo" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">扣减金额</view>
					<input type="number" class="uni-input" v-model="orderData.deductionAmount" placeholder="请输入" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">申请说明</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入" />
				</view>

				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
			<view class="formStyle" v-if="number == 8">
				<view class="w95 mg-at flac-row-a f16 lh80" @click="selectBillNo(2)">
					<view class="w3 fb">订单编号</view>
					<input class="uni-input" v-model="orderData.billNo" disabled placeholder="请选择订单" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">申请说明</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入" />
				</view>

				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
			<view class="formStyle" v-if="number == 9">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">线索号</view>
					<input class="uni-input" v-model="orderData.orderNeedsId" placeholder="请输入线索号" @blur="getOrderNeedsById" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">原薪资金额</view>
					<input class="uni-input" type="number" v-model="orderData.oldSalary" disabled="true"/>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">改价后薪资</view>
					<input class="uni-input" type="number" v-model="orderData.newSalary" placeholder="请输入改价金额" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">原中介费金额</view>
					<input class="uni-input" type="number" v-model="orderData.oldAgencyFee" disabled="true" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">改价后中介费</view>
					<input class="uni-input" type="number" v-model="orderData.newAgencyFee" placeholder="请输入改价金额" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">改价原因</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入改价原因" />
				</view>
				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
			<view class="formStyle" v-if="number == 10">
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">线索号</view>
					<input class="uni-input" v-model="orderData.orderNeedsId" placeholder="线索号/手机号填写其一即可" @blur="getOrderNeedsById" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80" v-if="phoneShow">
					<view class="w35 fb">手机号</view>
					<input class="uni-input" v-model="orderData.phone" placeholder="手机号/线索号填写其一即可" @blur="getOrderNeedsByPhone" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">原门店</view>
					<input class="uni-input" type="text" v-model="orderData.oldStoreName" disabled="true" placeholder="自动获取"/>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">原经纪人</view>
					<input class="uni-input" type="text" v-model="orderData.oldAgentName" disabled="true" placeholder="自动获取"/>
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">流转原因</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入流转原因" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">凭证</view>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" :previewFullImage="true"
						name="1" multiple :maxCount="5">
					</u-upload>
				</view>
				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
			<view class="formStyle" v-if="number == 11">
				<view style="color: red;">提示：暂存后奖金将停止扣减，同时也会将保险下户。</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">合同编号</view>
					<input class="uni-input" v-model="orderData.contractNo" placeholder="请输入合同编号" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w35 fb">暂存原因</view>
					<input class="uni-input" v-model="orderData.remark" placeholder="请输入暂存原因" />
				</view>
				<view class="w95 mg-at flac-row-a f16 lh80">
					<view class="w3 fb">凭证</view>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" :previewFullImage="true"
						name="1" multiple :maxCount="5">
					</u-upload>
				</view>
				<button class="btnStyle" @click="submitApprove">提交</button>
			</view>
		</u-popup>

		<u-popup :show="showContractPhonePopup" @close="showContractPhonePopup=false" :round="10" :closeable="true"
			:closeOnClickOverlay="true" :safeAreaInsetTop="true" mode="top" overlayOpacity="0">
			<view class="w10" style="padding:40rpx" v-for="(item,i) in contractPhoneList" :key="i">
				<view class="lh35" @click="confineContractList(item)">
					<u-text :text="'合同编号：'+item.no"></u-text>
					<u-text :text="'服务开始时间：'+item.serviceStarDate"></u-text>
					<u-text :text="'服务结束时间：'+item.serviceEndDate"></u-text>
					<u-text :text="'当前经纪人：'+item.agentName"></u-text>
				</view>
			</view>
		</u-popup>
		<u-popup :show="pickerShow" @close="pickerShow=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerText" cancelButton="none" @input="searchPicker">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="changeRoleStoreId" placement="column" borderBottom>
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, i) in selectColumns" :key="i"
						:label="item.text" :name="item.value" @change="changePicker(item)" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>
		<u-modal :show="showModal" title="证明示例" @confirm="showModal = false">
			<view>
				<u-image  src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/169942903177722cb1a95275c862debcda30ad0f5169.png" mode="aspectFit"
				width="700rpx" height="900rpx"></u-image>
			</view>
		</u-modal>
		
		<u-popup :show="abnormalFlag" @close="abnormalFlag = false,show = true">
			<view>
				<uni-section :title="abnormalType==1?'开发人异常订单信息':'开发/线索奖扣减异常订单信息'" type="line" padding>
					<scroll-view scroll-y="true" class="scroll-Y" style="height: 41vh">
						 <u-empty text="暂无需要审批的异常订单!" width="120" textSize="18" v-if="!abnormalList.length" customStyle="padding-top:200rpx">
						          </u-empty>
								  <view class="bacf flac-row" style="margin-left: 20rpx;"  @click="showDate = true">
								  	<view class="f16 fb" style="color: red;">点击选择操作月份:</view>
								    <u-input style="text-align: center;" border border="bottom" :disabled="true" disabledColor="#fff" v-model="logDate">
								    </u-input>
								  </view>
								
						<radio-group @change="radioChange">
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in abnormalList" :key="i">
								<view>
									<radio :value="item.billNo" :checked="i === current" />
								</view>
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="lh36">订单编号：{{item.billNo}}</view>
										<view class="lh36">订单类型：{{item.orderType}}</view>
										<view class="lh36">服务名称：{{item.productName}}</view>
										<view class="lh36">订单金额：￥{{item.realTotalAmount}}</view>
										<view class="lh36">结算金额：￥{{item.amount}}</view>
										<view class="lh36">扣减金额：￥{{item.developDeduction}}</view>
										<view class="lh36">扣减说明：{{item.remark}}</view>
									</view>
								</view>
							</view>
						</radio-group>
					</scroll-view>
				</uni-section>
			</view>
			<view style="margin-left: 45rpx;">
				<view style="display: flex;">
			<view class="fb f15" style="padding-bottom: 15rpx;">
				异常订单总数：{{abnormalCount}}</view>
			<view class="fb f15" style="padding-bottom: 15rpx;margin-left: 20%">异常订单扣减总金额：￥{{abnormalSumMoney}}</view>
			</view>
			</view>
			<u-button color="#1e1848" v-if="" text="确认选择" shape="circle" @click="confirmSelect()"
				customStyle="width: 60%;height: 70rpx;margin:40rpx auto ;"></u-button>
		</u-popup>
		<u-datetime-picker :show="showDate" :maxDate="nowDate" v-model="valueDate" mode="year-month"
		    @confirm="formatDateA" @cancel="showDate = false" ></u-datetime-picker>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化 
	import baTreePicker from "@/pages-work/components/ba-tree-picker/ba-tree-picker.vue"
	export default {
		components: {
			baTreePicker
		},
		data() {
			return {
				showPickerCity:false,
				logDate: '',
				nowDate: null,
				valueDate: null,
				selectColumns: [],
				current: 0,
				phoneShow: true,
				abnormalCount: 0,
				abnormalList: [],
				abnormalSumMoney: 0.00,
				abnormalFlag: false,
				showDate:false,
				abnormalType: null,
				myEmployeeNo: uni.getStorageSync('employeeNo'),
				fileList1: [],
				pickerShow: false,
				storeSource: 'store_'+uni.getStorageSync('storeId'),
				changeRoleStoreId: '',
				searchPickerText: '',
				showModal: false,
				orderData: {
					remark: '',
					contractNo: '',
					billNo: '',
					phone: '',
					orderNeedsId: null,
					oldSalary: 0.00,
					oldAgencyFee: 0.00,
					newAgencyFee: 0.00,
					newSalary: 0.00,
					realTotalAmount: null,
					fileList: '',
					type: null,
					deductionAmount: 0.00,
					modifyEmployeeNo: '',
					oldStoreName: '',
					oldStoreId: null,
					newStoreId: null,
					oldAgentId: null,
					oldAgentName: '',
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName')
				},
				changeRoleStoreName: '',
				roleId: uni.getStorageSync('roleId'),
				showContractPhonePopup: false,
				pageIndex: 0, //模块切换
				tabsList: [{ //模块菜单
					name: '审批事项'
				}, {
					name: '审批进度'
				}],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuIndex: 0,
				menuList: [{
					"name": "全部"
				}, {
					"name": "产品",
				}, {
					"name": "人事",
				}, {
					"name": "财务",
				}, {
					"name": "营销",
				}],
				btnList: [{
					index: 0,
					typeId: 1,
					name: '标准单开通',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-sktbzd.png'
				}, {
					index: 1,
					typeId: 1,
					name: '订单改价',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-scpgj.png'
				}, {
					index: 2,
					typeId: 1,
					name: '合同转移',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shtzy.png'
				}, {
					index: 3,
					typeId: 2,
					name: '账号开通',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-szhkt.png'
				}, {
					index: 4,
					typeId: 2,
					name: '账号注销',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-szhzx.png'
				}, {
					index: 5,
					typeId: 2,
					name: '员工开发异常',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-szhkt.png'
				}, {
					index: 6,
					typeId: 1,
					name: '开发人异常',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1697078840377订单异常.png'
				}, {
					index: 7,
					typeId: 1,
					name: '营业额扣减',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1697106150788付费填充-01.png'
				}, {
					index: 8,
					typeId: 1,
					name: '开发/线索扣减',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1697106207352app-matchBudget.png'
				},{
					index: 9,
					typeId: 1,
					name: '线索类改价',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1697106207352app-matchBudget.png'
				}, {
					index: 10,
					typeId: 1,
					name: '线索流转',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1730879030948流转.png'
				}, {
					index: 11,
					typeId: 1,
					name: '合同暂存',
					url: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1739256879141暂停.png'
				}, ],
				list: [],
				show: false,
				selectTime: false,
				subType: null,
				number: '',
				popupTitle: '',
				remind: '',
				listData: [],
				productData: {
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName'),
					openProductIdList: [],
					openProductNameList: '',
					openCityId: '',
					openAreaId: '',
					openAreaName: '',
					openStoreId: '',
					openStoreName: '',
					type: 6,
				},
				priceData: {
					billNo: null,
					productName: null,
					realTotalAmount: null,
					type: 5,
					discountAmount: '',
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName'),
					remark: ''

				},
				contractData: {
					type: 4,
					contractNo: '',
					agentName: '',
					phone: '',
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName'),
				},
				accountData: {},
				cancelData: {
					logoutNo: '',
					realName: '',
					remark: '',
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName'),
				},
				introducerData: {
					type: 8,
					introducer: '',
					modifyEmployeeNo: '',
					realName: '',
					remark: '',
					operationId: uni.getStorageSync('employeeId'),
					operationName: uni.getStorageSync('employeeName'),
				},
				storeRange: [],
				roleRange: [],
				contractPhoneList: [],
				cityAreaList: [],

			};
		},
		onLoad() {
			let roleId = uni.getStorageSync('roleId');
			if (roleId === 100 || roleId === 1) {
				this.getStoreList();
			}
			let date = new Date();  
			// date.setMonth(date.getMonth() - 1);  
			this.nowDate = Number(date)
			this.valueDate = Number(date)
			this.logDate = this.$u.timeFormat(date, 'yyyy-mm');
		},
		methods: {
			urgeApprove(id){
				this.http({
					url: "urgeApprove",
					method: 'POST',
					data: {
								operationId: uni.getStorageSync('employeeId'),
								id: id
							},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
					}
				})
			},
			confirmSelect(){
				if(this.abnormalList.length<=0){
					uni.showToast({
						icon: 'none',
						title: '暂无需要审批的异常订单!'
					})
				}
				this.orderData.billNo = this.abnormalList[this.current].billNo
				if(this.abnormalList[this.current].orderType =='三嫂单'){
					this.orderData.modifyEmployeeNo = this.myEmployeeNo
				}else {
					this.orderData.modifyEmployeeNo = this.storeSource
				}
				this.abnormalFlag = false
				this.show = true
			},
			radioChange(evt) {
				for (let i = 0; i < this.abnormalList.length; i++) {
					if (this.abnormalList[i].billNo === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			formatDateA(time) {
				var date = new Date();
				date.setTime(time.value);
				var month = date.getMonth() + 1;
				var time = date.getFullYear() + "-" + month + "-" + date.getDate();
				this.showDate = false
				this.logDate = 	this.$u.timeFormat(time, 'yyyy-mm');
				this.valueDate = time
				this.getCanWithdrawalAbnormalOrder()
			},
			selectBillNo(type){
				this.abnormalType = type
				this.show = false
				this.getCanWithdrawalAbnormalOrder()
				setTimeout(()=>{
					this.abnormalFlag = true
				},2000)
			},
			getCanWithdrawalAbnormalOrder(){
				let storeId = null
				if (this.changeRoleStoreId) {
					storeId = this.changeRoleStoreId;
				} else {
					storeId = uni.getStorageSync('storeId');
				}
				this.http({
				  url: 'getCanWithdrawalAbnormalOrder',
				  method: 'GET',
				  data: {
				    storeId: storeId,
					stateTime: this.logDate,
					abnormalType: this.abnormalType
				  },
				  success: res => {
				    if (res.code == 0) {
					  this.abnormalSumMoney = res.data.abnormalSumMoney
					  this.abnormalList = res.data.abnormalList
					  this.abnormalCount = res.data.abnormalCount
				    }else {
						uni.showToast({
							icon: 'none',
							title: '获取异常订单失败!'
						})
					}
				  }
				})
			},
			lookEx(){
				this.show = false
				this.showModal = true
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: function() {
						console.log('复制成功');
					}
				});
			},
			submitApprove() {
				if (this.subType == 6 || this.subType == 7 || this.subType == 8) {
					if (!this.orderData.billNo) {
						return this.$refs.uNotify.error('请输入订单编号')
					}
					if (!this.orderData.remark) {
						return this.$refs.uNotify.error('请输入申请说明')
					}
				}
				if (this.subType == 6) {
					if (!this.orderData.modifyEmployeeNo) {
						return this.$refs.uNotify.error('请输入期望开发人')
					}
					this.orderData.type = 9
					let imgUrl = ''
					for (var i = 0; i < this.fileList1.length; i++) {
						imgUrl += this.fileList1[i].url + ","
					}
					this.orderData.fileList = imgUrl
					if (!this.orderData.fileList) {
						return this.$refs.uNotify.error('请上传图片证明')
					}
				}
				if (this.subType == 7) {
					if (!this.orderData.deductionAmount) {
						return this.$refs.uNotify.error('扣减金额不能为空或小于零')
					}
					this.orderData.type = 10
				}
				if (this.subType == 8) {
					this.orderData.type = 11
				}
				if (this.subType == 9) {
					if (!this.orderData.orderNeedsId) {
						return this.$refs.uNotify.error('请输入线索号！')
					}
					if (!(!isNaN(parseFloat(this.orderData.newSalary)) && isFinite(this.orderData.newSalary))) {
						return this.$refs.uNotify.error('请输入正确的薪资改价金额！')
					}
					if (!(!isNaN(parseFloat(this.orderData.newAgencyFee)) && isFinite(this.orderData.newAgencyFee))) {
						return this.$refs.uNotify.error('请输入正确的中介费改价金额！')
					}
					if (this.orderData.newSalary<=0) {
						return this.$refs.uNotify.error('薪资改价金额不能小于或等于0！')
					}
					if (this.orderData.newAgencyFee<=0) {
						return this.$refs.uNotify.error('中介费改价金额不能小于或等于0！')
					}
					if (!this.orderData.remark) {
						return this.$refs.uNotify.error('请输入改价原因！')
					}
					this.orderData.type = 13
				}
				if(this.subType==10){
					if (!this.orderData.orderNeedsId&&!this.orderData.phone) {
						return this.$refs.uNotify.error('请输入线索号/手机号！')
					}
					if (!this.orderData.remark) {
						return this.$refs.uNotify.error('请输入流转原因！')
					}
					this.orderData.type = 14
					let imgUrl = ''
					for (var i = 0; i < this.fileList1.length; i++) {
						imgUrl += this.fileList1[i].url + ","
					}
					this.orderData.fileList = imgUrl
					if (!this.orderData.fileList) {
						return this.$refs.uNotify.error('请上传凭证')
					}
				}
				if(this.subType==11){
					if (!this.orderData.contractNo) {
						return this.$refs.uNotify.error('请输入合同编号！')
					}
					if (!this.orderData.remark) {
						return this.$refs.uNotify.error('请输入暂存原因！')
					}
					this.orderData.type = 16
					let imgUrl = ''
					for (var i = 0; i < this.fileList1.length; i++) {
						imgUrl += this.fileList1[i].url + ","
					}
					this.orderData.fileList = imgUrl
					if (!this.orderData.fileList) {
						return this.$refs.uNotify.error('请上传凭证')
					}
				}
				this.http({
					url: 'applyApprove',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.orderData,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('提交成功，请等待审核')
							this.orderData.deductionAmount = 0.00
							this.orderData.fileList = ''
							this.orderData.billNo = ''
							this.orderData.contractNo = ''
							this.orderData.remark = ''
							this.orderData.modifyEmployeeNo = ''
							this.orderData.type = null
							this.orderData.orderNeedsId = null
							this.orderData.phone = null
							this.orderData.realTotalAmount = null
							this.orderData.oldSalary = 0.00
							this.orderData.newSalary = 0.00
							this.orderData.oldAgencyFee = 0.00
							this.orderData.newAgencyFee = 0.00
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}

				})
				this.show = false
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					fileListLen++
				}
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api2.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			getStoreList() {
				this.http({
					url: "getStoreNameList",
					method: "GET",
					hideLoading: true,
					data: {},
					success: res => {
						if (res.code == 0) {
							this.selectColumns = res.data
							this.selectColumns.forEach(item => {
								this.$set(item, "show", true)
							})
						}
					}
				})
			},
			changePicker(e) {
				this.changeRoleStoreId = e.value
				this.changeRoleStoreName = e.text
				this.pickerShow = false
			},
			searchPicker(e) {
				if (e) {
					this.selectColumns.forEach(item => {
						if (item.text.includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.selectColumns.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			selectEmployee(e) {
				this.http({
					url: 'getEmployeeByNo',
					methods: 'GET',
					hideLoading: true,
					path: e.detail.value,
					success: res => {
						if (res.code == 0) {
							if (this.number == 4) {
								this.cancelData.logoutNo = res.data.no
								this.cancelData.realName = res.data.realName
							} else if (this.number == 5) {
								this.introducerData.modifyEmployeeNo = res.data.no
								this.introducerData.realName = res.data.realName
								this.introducerData.introducer = res.data.introducer
							}
						} else {
							return this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			selectContractByNo() {
				if (!this.contractData.contractNo) {
					return this.$refs.uNotify.error('请输入合同号')
				}
				this.http({
					url: 'getContractByNo',
					path: this.contractData.contractNo,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.contractData.contractNo = res.data.no
							this.contractData.agentName = res.data.agentName
							this.contractData.phone = res.data.memberPhone
						} else {
							return this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			confineContractList(item) {
				this.contractData.contractNo = item.no
				this.contractData.agentName = item.agentName
				this.contractData.phone = item.memberPhone
				this.showContractPhonePopup = false;
			},
			selectContractPhone() {
				if (!this.contractData.phone) {
					return this.$refs.uNotify.error('请输入手机号')
				}
				this.http({
					url: 'getContractByMenberPhone',
					path: this.contractData.phone,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.contractPhoneList = res.data;
							this.showContractPhonePopup = true;
						} else {
							return this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			getOrderDetail() {
				if (this.priceData.billNo) {
					this.http({
						url: 'orderDetails',
						method: 'GET',
						hideLoading: true,
						data: {
							billNo: this.priceData.billNo
						},
						success: res => {
							if (res.code == 0) {
								this.priceData.productName = res.data.productName
								this.priceData.realTotalAmount = res.data.realTotalAmount
							} else {
								return this.$refs.uNotify.error(res.msg)
							}
						}
					})
				}

			},
			getOrderNeedsByPhone() {
				if (this.orderData.phone) {
					this.http({
						url: 'getOrderNeedsByPhone',
						data: {
								phone: this.orderData.phone
						},
						method: 'GET',
						success: res => {
							if (res.code == 0) {
								this.orderData.orderNeedsId = res.data.id
								this.orderData.oldStoreName = res.data.storeName
								this.orderData.oldStoreId = res.data.needStoreId
								this.orderData.oldAgentId = res.data.agentId
								this.orderData.oldAgentName = res.data.agentRemark
							} else {
								return this.$refs.uNotify.error(res.msg)
							}
						}
					})
				}
			
			},
			getOrderNeedsById() {
				if (this.orderData.orderNeedsId) {
					this.http({
						url: 'getOrderNeedsById',
						method: 'GET',
						data:{
							orderNeedsId: this.orderData.orderNeedsId
						},
						success: res => {
							if (res.code == 0) {
								if(res.data.orderNeeds.status==='0'){
									this.orderData.orderNeedsId = null
									return this.$refs.uNotify.error('操作失败，此线索已取消！');
								}
								if(res.data.orderNeeds.billNo){
									this.orderData.orderNeedsId = null
									return this.$refs.uNotify.error('操作失败，此线索已创建订单！');
								}
								this.phoneShow = false
								this.orderData.oldAgencyFee = res.data.orderNeeds.agencyFee
								this.orderData.phone = res.data.orderNeeds.phone
								this.orderData.oldSalary = res.data.orderNeeds.salary
								this.orderData.oldStoreName = res.data.map.needsStoreName
								this.orderData.oldStoreId = res.data.orderNeeds.needStoreId
								this.orderData.oldAgentId = res.data.orderNeeds.agentId
								this.orderData.oldAgentName = res.data.name
							} else {
								return this.$refs.uNotify.error("未获取到线索信息!")
							}
						}
					})
				}
			
			},
			getApplyLog() {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/yiYeToolLog/selectYiYeToolLogByOperationId?operationId=' +
						uni.getStorageSync('employeeId'),
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'GET',
					success: res => {
						if (res.status == 200) {
							for (var i = 0; i < res.data.length; i++) {
								if(res.data[i].fileList){
									let obj = {
										url: res.data[i].fileList
									}
									res.data[i]['fileList1']= [obj]
								}
							}
							this.list = res.data;
						} else {
							return this.$refs.uNotify.error(res.msg);
						}
					},
					fail: err => {
						console.log(res)
					}
				})
			},
			changePage(e) { //模块切换
				this.pageIndex = e.index
				console.log(this.pageIndex)
				if (this.pageIndex === 1) {
					this.getApplyLog();
				}
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					value = new Date()
				}
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? '0' + MM : MM;
				let d = date.getDate();
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d;
			},
			clickBtn(i) {
				this.subType = i
				i == 0 && (this.popupTitle = '标准单上架')
				i == 1 && (this.popupTitle = '订单改价', this.show = true)
				i == 2 && (this.popupTitle = '合同转移', this.show = true)
				i == 3 && (this.popupTitle = '账号权限开通')
				i == 4 && (this.popupTitle = '账号注销')
				i == 5 && (this.popupTitle = '绑定开发人', this.show = true)
				i == 6 && (this.popupTitle = '订单开发人异常', this.show = true)
				i == 7 && (this.popupTitle = '订单营业额扣减', this.show = true)
				i == 8 && (this.popupTitle = '订单开发/线索奖扣减', this.show = true)
				i == 9 && (this.popupTitle = '线索薪资/中介费改价', this.show = true)
				i == 10 && (this.popupTitle = '线索流转', this.show = true)
				i == 11 && (this.popupTitle = '合同暂存', this.show = true)
				console.log(this.number)
				this.number = i
				if (i == 3 || i == 4) {
					return uni.showToast({
						icon: 'none',
						title: '该功能正在开发，敬请期待!'
					})
				}
				if (this.number == 0) {
					//判断保证金
					this.getStoreAmount();
				}
				this.orderData.fileList = ''
				this.orderData.billNo = ''
				this.orderData.remark = ''
				this.orderData.modifyEmployeeNo = ''
			},
			getAreaByCityId(cityId) {
				this.http({
					url: 'getAreaByCityId',
					methods: 'GET',
					hideLoading: true,
					path: cityId,
					success: res => {
						if (res.data) {
							//得赋值flag用于选择
							let arr = [];
							arr.push(res.data)
							this.cityAreaList = arr;
							// console.log(JSON.stringify(this.cityAreaList))
							// this.cityAreaList.forEach(v => {
							// 	v.flag = true;
							// })
						}
					}
				})
			},
			getStoreAmount() {
				let storeId;
				if (this.changeRoleStoreId) {
					storeId = this.changeRoleStoreId;
				} else {
					storeId = uni.getStorageSync('storeId');
				}
				this.http({
					url: 'getStoreById',
					methods: 'GET',
					hideLoading: true,
					path: storeId,
					success: res => {
						if (res.data) {
							if (res.data.storeType !== 2) {
								return this.$refs.uNotify.error('非加盟店不允许使用')
							}
							let amount = res.data.amount;
							if (res.data && amount && amount >= 5000) {
								this.productData.openStoreName = res.data.storeName;
								this.productData.openStoreId = res.data.id;
								this.productData.openCityId = res.data.cityId;
								this.getOpenProductSelectList(res.data.id);
								this.getAreaByCityId(res.data.cityId);
								this.show = true;
							} else {
								uni.showModal({
									title: '开通保证金',
									content: '开通产品服务必须提供5000元保证金',
									confirmText: '去缴费',
									success: function(res) {
										if (res.confirm) {
											uni.navigateTo({
												url: '/pages-other/income/incomeIndex'
											})
										} else if (res.cancel) {
											console.log('用户点击取消');
										}
									}
								});
							}
						} else {
							return this.$refs.uNotify.error('未找到门店信息')
						}

					}
				})
			},
			// 点击切换菜单（一级）
			choiceMenu(e) {
				this.menuIndex = e.index
			},
			// 显示选择器
			showPicker() {
				this.$refs.treePicker._show();
			},
			//监听选择（ids为数组）
			selectChange(ids, names) {
				// console.log(ids,name)
				this.remind = "已选完（点击可重选）"
				this.productData.openProductIdList = ids;
				console.log(this.productData.openProductIdList);
			},

			SubmitPrice() {
				if (!this.priceData.productName || !this.priceData.billNo) {
					return this.$refs.uNotify.error('订单不存在')
				}
				this.http({
					url: 'applyApprove',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.priceData,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('提交成功，请等待审核')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}

				})
				this.show = false
			},
			SubmitContract() {
				if (!this.contractData.contractNo || !this.contractData.agentName) {
					return this.$refs.uNotify.error('合同不存在或者经纪人不存在')
				}
				this.http({
					url: 'applyApprove',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.contractData,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('提交成功，请等待审核')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}

				})
				this.show = false
			},
			SubmitAccount() {
				console.log(this.priceData)
				this.show = false
				this.priceData = {}
			},
			SubmitCancel() {
				console.log(this.priceData)
				this.show = false
				this.priceData = {}
			},
			SubmitOpen() {
				console.log(JSON.stringify(this.productData))
				if (!this.productData.openAreaId) {
					return this.$refs.uNotify.error('请选择开通的区域')
				}

				if (this.productData.openProductIdList.length <= 0) {
					return this.$refs.uNotify.error('请选择开通的产品')
				}
				this.http({
					url: 'applyApprove',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.productData,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('提交成功，请等待审核')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}

				})
				this.show = false
			},
			// 提交绑定开发人申请
			submitIntroducer() {
				if (!this.introducerData.modifyEmployeeNo) {
					return this.$refs.uNotify.error('请输入想要绑定的员工工号！')
				}
				if (!this.introducerData.realName) {
					return this.$refs.uNotify.error('查询不到该员工！无法申请！')
				}
				if (this.introducerData.introducer) {
					return this.$refs.uNotify.error('该员工已被绑定！无法申请！')
				}
				if (!this.introducerData.remark) {
					return this.$refs.uNotify.error('请输入申请原因！')
				}
				this.http({
					url: 'applyApprove',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.introducerData,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('提交成功，请等待审核')
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}

				})
				this.show = false
			},
			changeTag(option) {
				console.log(option.value[0])
				let result = option.value[0];
				// //先把所有的flag全部变为true,把选中的变为false即可
				// const newArr = this.cityAreaList.map(item => {
				// 	return {
				// 		...item,
				// 		flag: true
				// 	};
				// });
				// this.cityAreaList = newArr;
				// this.cityAreaList[index].flag = !this.cityAreaList[index].flag;
				// this.$set(this.cityAreaList, index, this.cityAreaList[index])
				this.productData.openAreaId = result.id;
				this.productData.openAreaName = result.name;
				this.showPickerCity = false;
			},
			getOpenProductSelectList(id) {
				this.http({
					url: 'getOpenProductSelectList',
					data: {
						storeId: id
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.data) {
							this.listData = res.data;
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		flex-wrap: wrap;
		margin: 40rpx auto;
	}

	.btn {
		width: 35%;
		height: 60rpx;
		line-height: 60rpx;
		margin: 20rpx 30rpx;
		border: 1rpx solid #1e1848;
		border-radius: 8rpx;
		padding: 5rpx 20rpx;

	}

	.btn:active {
		background-color: #1e1848;
		color: #f6cc70;
	}

	.stateStyle {
		background: #fff;
		padding: 0 20rpx;
		border-radius: 10rpx;
	}

	.primary {
		border: 2rpx solid #55aaff;
		color: #55aaff;
	}

	.success {
		border: 2rpx solid green;
		color: green;
	}

	.fail {
		border: 2rpx solid #999;
		color: #999;
	}

	.formStyle {
		padding: 20rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}

	.uni-input {
		width: 74%;
		border-bottom: 2rpx solid #eee;
	}

	.btnStyle {
		width: 60%;
		margin: auto;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #1d1848;
		color: #fdd472;
	}

	.selectBox {
		border: 2rpx dashed #1d1848;
		margin: 20rpx;
		border-radius: 10rpx;
	}
</style>