<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-popup :show="showPickerMine" @close="showPickerMine=false;popupShowFilter=true">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<u-datetime-picker :show="popupShowDate" mode="date" @confirm="bindDateChange"
			@cancel="popupShowDate=false;popupShowFilter=true" :minDate="minDate"
			:maxDate="maxDate"></u-datetime-picker>
		<u-datetime-picker :show="popupShowDate1" mode="date" @confirm="bindDateChange1"
			@cancel="popupShowDate1=false;popupShowFilter=true" :minDate="minDate"
			:maxDate="maxDate"></u-datetime-picker>

		<u-popup :show="popupShowFilter" @close="popupShowFilter=false" round="10">
			<view class="f20 fb text-c lh60">筛选</view>

			<scroll-view :scroll-top="scrollTop1" scroll-y style="height: 1100rpx;">
				<view class="tab-title" style="margin-top: 0;">
					<text>订单编号</text>
					<text style="color: #ff4d4b;"></text>
				</view>
				<view class="tab-picker">
					<input class="single-input" type="text" v-model="searchCondition.billNo" placeholder="请输入订单编号" />
				</view>

				<view class="tab-title" style="margin-top: 0;">
					<text>客户电话</text>
					<text style="color: #ff4d4b;"></text>
				</view>
				<view class="tab-picker">
					<input class="single-input" type="text" v-model="searchCondition.tel" placeholder="请输入客户电话" />
				</view>

				<view class="tab-title" style="margin-top: 0;">
					<text>开发人</text>
					<text style="color: #ff4d4b;"></text>
				</view>
				<view class="tab-picker">
					<input class="single-input" type="text" v-model="searchCondition.channel" placeholder="请输入开发人工号" />
				</view>


				<view class="tab-title" style="margin-top: 0;">
					<text>开单人</text>
					<text style="color: #ff4d4b;"></text>
				</view>
				<view class="tab-picker">
					<input class="single-input" type="text" v-model="searchCondition.crePerson"
						placeholder="请输入开单人工号" />
				</view>

				<view class="tab-title" style="margin-top: 0;">
					<text>所属门店</text>
					<text style="color: #ff4d4b;"></text>
				</view>
				<view class="tab-picker" @click="openPickerMine(0)">
					<text class="picker-text">{{ storeName||'点击选择门店' }}</text>
					<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
				</view>
				<view>
					<view class="tab-title">
						<text>服务时间</text>
					</view>
					<view class="flac-row-c" style="margin: 0 40rpx;">
						<input class="date-input" type="text" v-model="searchCondition.startTime" placeholder="请选择开始时间"
							@click="popupShowFilter=false;popupShowDate=true" />
						<view style="margin: 0 40rpx;"> — </view>
						<input class="date-input" type="text" v-model="searchCondition.endTime" placeholder="请选择结束时间"
							@click="popupShowFilter=false;popupShowDate1=true" />
					</view>
				</view>

				<u-gap height="120"></u-gap>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="popupShowFilter=false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="reset()">
					<view class="filter-button-right">
						<text>查询</text>
					</view>
				</view>
			</view>
		</u-popup>


		<view class="t-indent2 lh30 f18 flac-col">
			<view class="">
				所选时段：{{searchCondition.startTime}}---{{searchCondition.endTime}}
			</view>
			<view class="">
				欠款单数：{{total || 0}}
			</view>
		</view>

		<view class="swiper-tab" v-for="(item, index1) in list" :key="index1"
			@click="openOrderDetail(item.billNo,item.productName)">
			<view class="swiper-head" @click="copyText(item.billNo,'订单编号')">
				<text class="swiper-title">单号：{{item.billNo}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left">
					<img :src="item.productImg||blankImg" alt="" @click="openImgPreview(item.productImg||blankImg)">
				</view>
				<view class="content-right">
					<view class="content-title" @click="openDetail(item.billNo)">
						<text>{{item.productName}}</text>
					</view>
					<view class="content-text flac-row">
						<text>订单金额：￥{{item.realTotalAmount}}</text>
					</view>
					<view class="content-text flac-row">
						<text>所属门店：{{item.storeName}}</text>
					</view>

					<view class="content-text flac-row">
						<text>开发人：{{item.channel||'-'}}</text>
					</view>
					<view class="content-text flac-row">
						<text>开单人：{{item.crePersonName?item.crePersonName+'-'+item.crePerson:'-'}}</text>
					</view>
					<view class="content-text flac-row">
						<text>服务人员：{{item.serviceNo}}</text>
					</view>
					<view class="content-text flac-row" @click="copyText(item.tel,'客户电话')">
						<text>客户电话：{{item.tel}}</text>
					</view>
					<view class="content-text flac-row">
						<text>客户地址：{{item.street}}</text>
					</view>
					<view class="content-text flac-row">
						<text>订单备注：{{item.remark|| '-'}}</text>
					</view>
					<view class="content-text flac-row">
						<text>服务时间：{{item.startTime}}</text>
					</view>

					<view class="content-text-right flac-row">
						<text>欠款：</text>
						<text style="color: #ff4d4b;">￥{{item.qk}}</text>
					</view>
				</view>
			</view>

		</view>

		<view class="lh40 text-c" v-if="list.length">
			<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
			<view v-else @click="searchCondition.current++;getList()">下滑查看更多...</view>
		</view>
		<u-empty v-if="!list.length" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="bacf" style="position: fixed;bottom: 18%;right:34rpx;z-index:999;border-radius: 50%;
		padding: 20rpx;border: #1e1848 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;color:#f6cc70;
		background-color: #1e1848;line-height: 25rpx;font-size: 24rpx;" @click="popupShowFilter=true">
			<view>
				订单
			</view>
			<view>
				筛选
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 日期可选择最小值
				minDate: Number(new Date().setYear(new Date().getFullYear() - 3)),
				// 日期可选择最大值
				maxDate: Number(new Date().setYear(new Date().getFullYear() + 0)),

				scrollTop: 0,
				scrollTop1: 1,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				popupShowFilter: false,
				popupShowDate: false,
				popupShowDate1: false,

				storeName: "",
				pickerIndex: -1,
				openPickerMineValue: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				list: [],
				pageCount: 0,
				total: 0,
				searchCondition: {
					size: 20,
					current: 1,
					// orderBy: 't.startTime DESC'
				}
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			openDetail(billNo) {
				uni.navigateTo({
					url: '/pages-mine/orders/orders-detail?billNo=' + billNo
				})
			},
			bindDateChange(e) {
				let date = new Date(e.value)
				this.searchCondition.startTime = this.formatDate(e.value)
				this.popupShowDate = false
				this.popupShowFilter = true
			},
			bindDateChange1(e) {
				let date = new Date(e.value)
				this.searchCondition.endTime = this.formatDate(e.value)
				this.popupShowDate1 = false
				this.popupShowFilter = true
			},
			// 打开选择器
			openPickerMine(value, index) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.openPickerMineValue = index
				this.showPickerMine = true
				this.popupShowFilter = false
				if (index == 1) {
					this.popupShare = false
				}
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.storeIndex = index
					this.searchCondition.storeId = this.storeList[index].id
					this.storeName = this.storeList[index].storeName
				}
				this.showPickerMine = false
				this.popupShowFilter = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			copyText(text, tips) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success(tips + '已复制！')
					}
				}, true);
			},
			// 获取所有门店列表
			getAllStoreList() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/acn/getAllStoreList',
					method: 'POST',
					hideLoading: true,
					data: {
						isOpen: false
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
						}
					}
				})
			},
			reset() {
				this.searchCondition.current = 1
				this.list = []
				this.getList()
			},
			getList() {
				this.popupShowFilter = false
				this.http({
					url: 'pageDebtOrder',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.list = this.list.concat(res.data.records)
							this.pageCount = res.data.pages
							this.total = res.data.total
						} else if (this.searchCondition.current > 1) {
							this.$refs.uNotify.warning('暂无更多内容了哦')
						}
					},
				});
			},
			onReachBottom() {
				this.searchCondition.current++
				this.getList()
			},
		},
		onLoad(options) {
			let time = options.endTime || this.formatDate(new Date())
			let nowDate = new Date(time)
			this.searchCondition.endTime = time
			let lastDate = nowDate.setDate(nowDate.getDate() - 7);
			this.searchCondition.startTime = this.formatDate(lastDate)
			this.getList()
			this.getAllStoreList()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.date-input {
		width: 42%;
		height: 90rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		text-align: center;
		margin: 12rpx 0 0 0rpx;
		border-style: hidden;
		background-color: #f4f4f5;
		border-radius: 20rpx;
	}
</style>