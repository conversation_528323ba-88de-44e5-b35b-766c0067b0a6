//服务器地址
// const service = "http://localhost:9999/"; 
// const service = "http://**************:9999/";
const service = "https://api.xiaoyujia.com/";


import api from "@/api/api.js"

module.exports = (params) => {
	let url = params.url;
	let method = params.method;
	let header = params.header || {};
	let data = params.data || {};
	let token = uni.getStorageSync("token");
	let path = "";
	let urlResult = "";
	// 刷新动画自定义：请求时传入true值可以取消显示动画（使用参数：hideLoading）
	let hideLoading = params.hideLoading || params.hideLoading;
	if (token) {
		console.log(token)
		const tokenValue = {
			"token": token
		}
		header = Object.assign({}, header, tokenValue);
	}
	//	请求方式 GET POST
	if (method) {
		method = method.toUpperCase(); //	小写转大写
		if (method == "POST") {
			// header: {"content-type":"application/json;charset=UTF-8"},
			// const contentType = {"content-type":"application/json;charset=UTF-8"}
			const contentType = {
				"content-type": "application/x-www-form-urlencoded;charset=UTF-8"
			}
			header = Object.assign({}, header, contentType);
		} else {
			const contentType = {
				"content-type": "application/x-www-form-urlencoded;charset=UTF-8"
			}
			header = Object.assign({}, header, contentType);
		}
	}
	// 请求头自定义：请求时可指定content-type类型，否则直接采用上面定义的默认header（使用参数：header）
	header = params.header !== undefined ? Object.assign({}, header, params.header) : header
	// 请求参数自定义：请求时可在url末尾追加Pathvariable（使用参数：path）
	path = params.path !== undefined && params.path !== "" ? "/" + params.path : ""
	// 请求地址自定义：请求时可使用外部项目url地址（使用参数：outsideUrl）
	urlResult = params.outsideUrl !== undefined ? params.outsideUrl + path : service + api[url] + path

	//	发起请求 加载动画（可选不加载）
	// if (!params.hideLoading) {
	if (!hideLoading) {
		uni.showLoading({
			title: "加载中",
			mask: true
		})
	}
	//	发起网络请求
	uni.request({
		// 不启用接口api，直接根据url请求即可
		// url: service + url,
		// 启用接口api，请求地址需在api.js配置
		url: urlResult,
		method: method || "GET",
		header: header,
		dataType: "json",
		data: data,
		sslVerify: false, //	是否验证ssl证书
		success: res => {
			typeof params.success == "function" && params.success(res.data);
		},
		fail: err => {
			console.log(err)
			uni.showToast({
				title: err.errMsg,
				icon: 'none'
			})
			typeof params.fail == "function" && params.fail(err.data);
		},
		complete: (e) => {
			console.log("请求完成");
			// if (!params.hideLoading) {
			if (!hideLoading) {
				setTimeout(() => {
					uni.hideLoading()
				}, 1000)
			}
			typeof params.complete == "function" && params.complete(e.data);
			return;
		}
	})
}