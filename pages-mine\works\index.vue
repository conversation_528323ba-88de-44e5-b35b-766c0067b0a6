<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="找家政工作" confirmText="成为合伙人" :title="checkTitle"
				:content="checkText" @confirm="popupCheck(0)" @close="popupCheck(1)"></uni-popup-dialog>
		</uni-popup>

		<!-- #ifdef  MP-WEIXIN -->
		<official-account></official-account>
		<!-- #endif -->

		<view class="w9 bacf flac-row" style="border-radius: 80rpx;margin: 30rpx auto;">
			<uni-data-select v-model="value" :localdata="range" :clear="false" @change="change" />
			<view class="w8">
				<u-search bgColor="#fff" :clearabled="true" :showAction="true" :animation="true" margin="0 20rpx"
					v-model="searchText" placeholder="你想找什么人？" @search="searchPage" @custom="searchPage" />
			</view>
		</view>
		<!-- 		<view class="bgStyle text-c fb lh45 c2f2e71" style="font-style: italic;">
			xxxx
		</view> -->

		<view class="tips-box" v-if="popupShowTips" @click="openTab(3,0)">
			<view class="box-triangle"></view>
			<view class="box-cont">
				<text>{{popupTips}}</text>
			</view>
		</view>

		<view class="w9 mg-at home-btn">
			<view class="btn-item" v-for="(item,index) in homeBtnList" :key="index" v-if="item.show"
				@click="openTab(0,index)">
				<img :src="item.img" style="width: 100rpx;height: 100rpx;" />
				<text>{{item.showText}}</text>
			</view>
		</view>
		<view class="w9 mg-at bacf flac-row-a" style="padding: 40rpx 0;" v-if="roleId!=110">
			<view class="flex-col-c" v-for="(item,index) in homeBtnList2" :key="index" v-if="item.show"
				@click="openTab(1,index)">
				<img :src="item.img" style="width: 55rpx;height: 60rpx;" />
				<text class="lh40">{{item.showText}}</text>
			</view>
		</view>
		<view class="w9 bacf" style="margin: 40rpx auto;">
			<view class="w9 mg-at flac-row-b" style="padding: 20rpx 0;">
				<view>
					<view class="fb">豪宅管家</view>
					<view class="f12 lh20">一对一专属豪宅客户管家</view>
					<u-button text="立即报名" color="#9e0b0f" size="mini" shape="circle"
						customStyle="width:150rpx;margin: 20rpx 0" @click="openPage(0)" />
				</view>
				<u-image src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-hzgj.png"
					width="130" height="90" @click="openPage(0)" />
			</view>
		</view>

		<!-- 排行榜 -->
		<view class="post-img" v-if="roleId!=110">
			<img @click="openPage(1)" mode="widthFix" :src="rankingImg" />
		</view>

		<view style="padding: 20rpx 30rpx;display: flex;">
			<view style="width: 80%;">
				<!-- 选项栏 -->
				<u-tabs :list="tabsList" lineWidth="35" lineHeight="5" :lineColor="`url(${lineBg}) 100% 100%`"
					:activeStyle="{
						color: '#303133',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#606266',
						transform: 'scale(1)'
					}" itemStyle="padding: 0 15px; height: 30px;" @click="choiceTab">
				</u-tabs>
			</view>
			<view style="width: 30%;margin: auto 40rpx;line-height: 60rpx;text-align: right;" @click="refresh()">
				<u-icon label="换一批" labelPos="left" name="reload" labelColor="#000" :bold="true">
				</u-icon>
			</view>
		</view>
		<view class="list">
			<view class="list-item flex-col-c" v-for="(item,index) in list" :key="index" @click="openTab(2,index)">
				<view class="list-img">
					<u-image :src="item.headPortrait!=null?item.headPortrait:blankImg" width="100" height="120"
						radius="10" />
				</view>
				<view class="f14 lh30 fb">
					<text>{{formatRealName(index)}}</text>
					<text style="font-size: 28rpx;font-weight: 100;">{{formatWorkType(index)}}</text>
				</view>
				<!-- <view class="f14">工资：{{item.salaryExpectation!=0?item.salaryExpectation + '元/月':'-'}}</view> -->
				<view class="flac-row f14">星级：<u-rate size="14" :value="formatLevel(index)" activeColor="#1c1c76"
						readonly />
				</view>
			</view>
		</view>

		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />


	</view>
</template>

<script>
	export default {
		name: "index",
		components: {},
		data() {
			return {
				// 可设置
				// 查询时间范围（单位：天）
				dateRange: 7,
				// 查询位置范围（单位：公里）
				districtRange: 5,
				// 是否登录
				isLogin: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",
				roleId: uni.getStorageSync('roleId'),
				searchText: '',
				value: 0,
				range: [{
						value: 0,
						text: "全部"
					},
					// {
					// 	value: 1,
					// 	text: "漳州"
					// },
					// {
					// 	value: 2,
					// 	text: "泉州"
					// },
				],
				tabsList: [{
					name: '推荐'
				}, {
					name: '附近'
				}, {
					name: '最新'
				}],
				list: [],
				total: 0,
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-bg.png',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				logo: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/home/<USER>",
				rankingImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/bg-lmphb.png',
				homeBtnList: [{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-bm.png",
						showText: "住家保姆",
						text: "住家",
						url: '/pages-other/employee/employee-search?search=住家',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-bm.png",
						showText: "白班保姆",
						text: "不住家",
						url: '/pages-other/employee/employee-search?search=不住家',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-dc.png",
						showText: "单餐保姆",
						text: "单餐",
						url: '/pages-other/employee/employee-search?search=单餐',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yrs.png",
						showText: "育儿嫂",
						text: "育儿嫂",
						url: '/pages-other/employee/employee-search?search=育儿嫂',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ys.png",
						showText: "月嫂",
						text: "月嫂",
						url: '/pages-other/employee/employee-search?search=月嫂',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-hg.png",
						showText: "护工",
						text: "护工",
						url: '/pages-other/employee/employee-search?search=护工',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-pd.png",
						showText: "陪读师",
						text: "陪读师",
						url: '/pages-other/employee/employee-search?search=陪读师',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-guanjia.png",
						showText: "管家",
						text: "管家",
						url: '/pages-other/employee/employee-search?search=管家',
						show: true
					},
				],
				homeBtnList2: [{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-daihuo.png",
						showText: "去推广",
						url: '/pages-work/distribution/index?index=0',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zuojiazheng.png",
						showText: "找合作",
						url: '/pages-work/distribution/index?index=1',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-fenxiao.png",
						showText: "去赚钱",
						url: '/pages-work/promote/share/shareIndex?curNow=2',
						show: true
					},
					{
						img: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jiabi.png",
						showText: "积分",
						url: '/pages-mine/signIn/signin',
						show: true
					}
				],
				roleId: uni.getStorageSync("roleId") || null,

				popupShow: false,
				popupShowTips: false,
				popupTips: '',
				popupTipsId: 0,
				memberId: null,
				choiceIndex: 0,
				choiceIndexOld: 0,
				choiceIndex: 0,
				choiceOrderByIndex: 0,

				// 查询条件
				searchCondition: {
					introducer: null,
					introducerName: null,
					// 开启查阅权限
					// introducer: uni.getStorageSync("employeeNo") || "-1",
					updaterNo: null,
					updaterName: null,
					shimingState: 1,
					employeeSource: null,
					level: null,
					levelId: null,
					state: 1,
					isAppraisal: 1,
					isPush: 1,
					search: "",
					lat: null,
					lng: null,
					district: null,
					dateRange: null,
					creTimeRange: null,
					updateTimeRange: null,
					putTimeRange: null,
					scoreLow: null,
					scoreHigh: null,
					ageLow: null,
					ageHigh: null,
					status: null,
					siteFlag: null,
					orderId: null,
					orderBy: "putTime DESC,createDate DESC",
					current: 1,
					size: 6
				},
			}
		},
		methods: {
			openTab(value, index) {
				// #ifndef MP-TOUTIAO
				if (!this.isLogin) {
					// return this.reLogin()
				}
				// #endif
				let url = ""
				if (value == 0) {
					url = this.homeBtnList[index].url
				} else if (value == 1) {
					url = this.homeBtnList2[index].url
				} else if (value == 2) {
					url = "/pages-mine/works/employee-detail?baomuId=" + this.list[index].baomuId
				} else if (value == 3) {
					url = "/pages-mine/inform/inform-detail?id=" + this.popupTipsId
					this.popupShowTips = false
				}

				// 选择管家-跳转到小程序
				if (url.includes("管家")) {
					// #ifdef MP-WEIXIN
					wx.navigateToMiniProgram({
						appId: 'wxc33730c9e09594f8', // pord
						path: 'pages/product/houseKeep',
						envVersion: 'release',
						success(res) {

						}
					})
					return
					// #endif
				}

				// 未注册合伙人和员工
				let notEmployee = !uni.getStorageSync("merchantCode") && !uni.getStorageSync("employeeId")
				// 询问是找工作还是合伙人
				if (value == 1 && index < 3 && notEmployee) {
					// if (!uni.getStorageSync('memberId')) {
					// 	this.$toast.toast('还未登录哦，登录后体验更多功能！')
					// 	uni.setStorageSync('redirectUrl', '/pages-mine/index/index')
					// 	return setTimeout(() => {
					// 		uni.redirectTo({
					// 			url: '/pages-mine/login/login'
					// 		})
					// 	}, 2000)
					// }
					this.openCheck(0, "您想用家姐联盟做什么?", "选择后即可开启更多功能哦")
					return
				}

				uni.navigateTo({
					url: url
				})
			},
			openPage(value) {
				console.log("打开其他页面！")
				if (value == 0) {
					// #ifdef MP-WEIXIN
					wx.navigateToMiniProgram({
						appId: 'wxc33730c9e09594f8', // pord
						path: 'pages/product/houseKeep',
						envVersion: 'release',
						success(res) {

						}
					})
					// #endif
				} else if (value == 1) {
					uni.navigateTo({
						url: '/pages-other/serviceScore/scoreRank'
					})
					// this.$toast.toast('功能已在开发啦，敬请期待！')
				}
			},
			// 打开头像预览
			openImgPreview(img) {
				if (!this.isLogin) {
					return this.reLogin()
				}
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			searchPage(value) {
				let url = "/pages-other/employee/employee-search?search=" + value
				uni.navigateTo({
					url: url
				})
			},
			formatRealName(index) {
				let name = this.list[index].realName
				let sex = this.list[index].sex
				let workType = this.list[index].workType
				let xin = name.substring(0, 1)
				let data = ['', '师傅', '阿姨', '', '']
				let call = data[sex]
				if (call == '') {
					call = '某某'
				}

				return xin + call
			},
			formatWorkType(index) {
				let workType = this.list[index].workType || ""
				let workTypeList = ['住家', '不住家', '单餐', '育儿嫂', '月嫂', '护工', '陪读师', '管家', '钟点晚餐', '钟点保洁']
				let type = ''
				// 格式化工种
				for (let item of workTypeList) {
					if (workType.includes(item)) {
						type = '（' + item + '）'
						break
					}
				}
				// 优先显示搜索词
				if (workType.includes(this.choiceWorType) && this.choiceWorType != '') {
					type = '（' + this.choiceWorType + '）'
				}
				return type
			},
			formatLevel(index) {
				let levelId = this.list[index].levelId
				let level = 0
				level = levelId + 1
				return level
			},
			// 选择菜单
			choiceTab(e) {
				if (!this.isLogin) {
					// return this.reLogin()
				}
				this.choiceIndex = e.index
				this.refreshList()
			},
			// 刷新列表
			refreshList() {
				let index = this.choiceIndex
				this.clearTabChoice()
				// 默认检索参数
				if (index == 0) {
					this.searchCondition.isPush = 1
				} else if (index == 1) {
					this.searchCondition.district = this.districtRange
					this.searchCondition.lat = uni.getStorageSync("lat")
					this.searchCondition.lng = uni.getStorageSync("lng")
				} else if (index == 2) {
					this.searchCondition.dateRange = this.dateRange
				}
				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {
				// 清空各个菜单特有的筛选条件

				// 每个菜单都需要被推荐员工，因此注释
				// this.searchCondition.isPush = null
				this.searchCondition.district = null
				this.searchCondition.lat = null
				this.searchCondition.lng = null
				this.searchCondition.dateRange = null
				this.searchCondition.state = 1
			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
				this.searchCondition.agentName = ""
			},
			// 清空筛选条件（搜索框和单选框）
			cleanFilter() {
				this.searchText = ""

				this.searchCondition.creTimeRange = null
				this.searchCondition.levelId = null
				this.searchCondition.salaryLow = null
				this.searchCondition.salaryHigh = null
				this.searchCondition.scoreLow = null
				this.searchCondition.scoreHigh = null
				this.searchCondition.ageLow = null
				this.searchCondition.ageHigh = null
				this.searchCondition.address = ""
				this.searchCondition.storeId = null
			},
			// 开始筛选
			startFilter() {
				// this.list = []
				this.total = 0
				this.searchCondition.current = 1
				this.searchCondition.search = this.searchText
				this.searchCondition.workType = this.choiceWorType
				this.searchCondition.serverContent = this.choiceServer


				this.searchCondition.married = this.choiceMarried
				this.searchCondition.education = this.choiceEducation
				this.searchCondition.shimingState = 1

				if (this.choiceLevelIndex == 0) {
					// 不限保姆等级
					this.searchCondition.levelId = null
				}

				// 排序规则调整（默认排序下才生效）
				if (this.choiceOrderByIndex == 0) {
					if (this.choiceIndex == 0) {
						this.searchCondition.orderBy = "putTime DESC,createDate DESC"
					} else if (this.choiceIndex == 1) {
						this.searchCondition.orderBy = "putTime DESC"
					} else if (this.choiceIndex == 2) {
						this.searchCondition.orderBy = "createDate DESC"
					}
				}

				this.getList()
				this.popupShow = false
			},
			// 刷新列表
			refresh() {
				this.searchCondition.current++
				this.getList()
			},
			getList() {
				// 陪跑店只能看自己门店保姆
				let storeId = uni.getStorageSync("storeId") || null
				if (this.roleId == 110 && storeId) {
					this.$set(this.searchCondition, "storeId", storeId)
				}
				this.http({
					url: 'getBaomuPage',
					method: 'POST',
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							// this.list = this.list.concat(res.data.records)
							this.list = res.data.records
						}
					}
				})
			},
			// 检查是否推送消息
			checkUnionInformPopup() {
				this.http({
					url: 'checkUnionInformPopup',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync("memberId") || 0,
						employeeId: uni.getStorageSync("employeeId") || 0,
						authId: uni.getStorageSync('authId') || 0,
						popupTimeLimit: 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.popupShowTips = true
							this.popupTipsId = res.data.id
							this.popupTips = res.data.informTitle
						} else {
							// this.popupShowTips = true
							// this.popupTips = "添加到【我的小程序】，查看更快捷>"
						}
					},
				})
			},
			getEmployee() {
				this.http({
					url: 'getEmployeeById',
					method: 'GET',
					path: uni.getStorageSync("employeeId") || 0,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							let isBaomu = res.data.isBaomu
							uni.setStorageSync("isBaomu", isBaomu)
							uni.setStorageSync("employeeType", res.data.employeeType)
							uni.setStorageSync("employeeName", res.data.realName)
						}
					},
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck(value) {
				// 确定
				if (value == 0) {
					if (this.checkType == 0) {
						uni.navigateTo({
							url: "/pages-mine/franchise/enter/topUpMoney"
						})
					}
				}
				// 取消
				else if (value == 1) {
					if (this.checkType == 0) {
						uni.navigateTo({
							url: "/pages-mine/resume/resume-simplify"
						})
					}
				}
			},
			reLogin() {
				// this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
				this.$toast.toast('您还未进行登录哦，先去登录吧！')
				uni.setStorageSync('redirectUrl', '/pages-mine/index/index')
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					});
				}, 2000);
			},
			// 登录状态检查
			checkLogin() {
				this.getList()
				console.log('开始检查登录状态！')
				if (!uni.getStorageSync('memberId')) {
					this.isLogin = false
					return false
				} else {
					this.isLogin = true
					this.checkUnionInformPopup()
					this.getEmployee()
					return true
				}
			}
		},
		onReady() {
			this.checkLogin()
		},
		mounted() {
			this.checkLogin()
		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.c2f2e71 {
		color: #2f2e71;
	}

	/deep/.uni-select {
		text-indent: 1rem;
		border: none !important;
	}

	.bgStyle {
		width: 85%;
		height: 75rpx;
		margin: auto;
		background-size: 100%;
		background-image: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-titlebg.png');
	}

	/deep/.u-list {
		width: 90%;
		margin: 10rpx auto;
	}

	/deep/.u-list-item {
		flex-direction: row;
		flex-shrink: 1;
		flex-wrap: wrap;
		justify-content: space-between;
		flex: 0;
	}

	.itemBox {
		width: 330rpx;
		min-width: 330rpx;
		overflow: hidden;
		margin: 20rpx 0;
	}

	// .glodYBox {
	//   display: flex !important;
	//   flex-direction: row !important;
	//   flex-wrap: wrap !important;
	//   justify-content: space-between !important;

	//   .setListItem {
	//     flex: 0 !important;
	//     .itemBox {
	//       width: 330rpx !important;
	//       min-width: 330rpx !important;
	//       overflow: hidden !important;
	//       margin: 20rpx 0 !important;
	//     }
	//   }
	// }
</style>
<style lang="scss">
	page {
		height: auto;
		background-color: #f5f5f5 !important;
		padding: 0;
	}

	.img-view {
		width: 100%;
		height: auto;

		img {
			display: block;
			padding-top: 160rpx;
			width: 100%;
			height: auto;
		}
	}

	.home-btn {
		display: flex;
		flex-wrap: wrap;
		margin-top: 50rpx;
	}

	.btn-item {
		display: flex;
		flex-direction: column;
		width: 25%;
		height: 250rpx;

		img {
			display: block;
			width: 100rpx;
			height: 100rpx;
			margin: 20rpx auto;
		}

		text {
			display: block;
			line-height: 50rpx;
			color: #201b51;
			text-align: center;
			font-size: 32rpx;
		}
	}

	.list {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		margin: 0 auto;
	}

	.list-item {
		width: 45%;
		height: auto;
		padding: 20rpx 1% 20rpx 3%;
	}

	.list-img {
		img {
			display: block;
			width: 200rpx;
			height: 260rpx;
			margin: auto;
			border-radius: 15rpx;
		}
	}

	.post-img {
		width: 100%;

		img {
			display: block;
			width: 90%;
			height: auto;
			margin: 0 auto;
		}
	}

	.tips-box {
		position: fixed;
		/* #ifdef H5 || APP-PLUS */
		top: 5.4%;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		top: 0%;
		/* #endif */
		right: 20rpx;
		z-index: 999;
		min-width: 200rpx;
		height: auto;
		padding: 0 0 0 0;
	}

	.box-triangle {
		width: 0;
		height: 0;
		border: 15rpx solid transparent;
		border-bottom-color: #f6dda7;
		margin-left: 75%;
	}

	.box-cont {
		width: auto;
		height: 70rpx;
		line-height: 70rpx;
		background-color: #f6dda7;
		border-radius: 15rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 0 20rpx;

		text {
			color: #612d06;
			display: block;
		}
	}
</style>