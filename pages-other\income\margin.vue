<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>


		<view class="shadow">
			<view style="margin-left: 70%;" @click="gotoDetail">保证金明细</view>
			<view class="lh30"
				style="display: block; width: 15%;border-radius: 5px;margin: 0 auto;padding-top: 100rpx;">
				<u-icon size="55"
					name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1688540394759保证金.png">
				</u-icon>
			</view>
			<view class="flac-row-c lh50">
				<view class="" style="text-align: center;">保证金当前余额</view>
				<u-icon @click="updateStoreDeposit()" size="25" name="reload"></u-icon>
			</view>
			<view style="text-align: center;padding-top: 50rpx;font-weight: bold;font-size: 80rpx;">
				￥{{nowBalance||'0.00'}}</view>
			<view class="button-state">
				<u-button color="#1e1848" customStyle="color:#f6cc70" @click="gopdf">查看标准单保证金管理条款</u-button>
				<u-gap height="20"></u-gap>
				<u-button color="#1e1848" customStyle="color:#f6cc70" @click="gosign">签署保证金协议</u-button>
				<u-gap height="20"></u-gap>
				<u-button color="#1e1848" customStyle="color:#f6cc70" @click="popupShow = true">充值</u-button>
				<u-gap height="20"></u-gap>
				<u-button color="#1e1848" customStyle="color:#f6cc70" v-if="roleId==1" @click="tiXian">提现</u-button>

			</view>
		</view>


		<u-popup mode="center" :show="popupShow" @close="popupShow=false">
			<view class="bacf" style="width: 600rpx;height: 340rpx;">
				<uni-table class="tableStyle" style="margin: 0;">
					<uni-tr>
						<uni-td align="center" style="font-weight: bold;">
							是否确认充值金额为：
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-input prefixIcon="rmb" type="number" v-model="totalAmount"></u-input>
						</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td>
							<u-button text="确认" @click="determine" color="#1e1848">
							</u-button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</u-popup>

		<!-- 打开小程序二维码 -->
		<u-modal :show="showModal" title="付款码" @confirm="closeModal">
			<view>
				<u-image :src="imgCode" mode="aspectFit"></u-image>
			</view>

		</u-modal>

		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>
	</view>
</template>
<script>
	export default {

		data() {
			return {
				checkTitle: "",
				checkText: "",
				storeData: {},
				checkType: 0,
				msgText: "",
				msgType: "success",
				imgCode: '',
				showPopu: false,
				popupShow: false,
				moeny: '',
				nowBalance: 0.00,
				totalAmount: 5000,
				showModal: false,
				roleId: uni.getStorageSync('roleId'),
				orderNo: '',
				payInfo: {},
				price: "",
				address: [1648265],
				address: [1648265],
				addressList: [],
				addressInfo: {
					"memberId": uni.getStorageSync("memberId"),
					"id": "",
					"name": uni.getStorageSync("memberName") || "用户" + uni.getStorageSync("memberId"),
					"phone": uni.getStorageSync("account"),
					"street": "厦门小羽佳家政股份有限公司",
					"city": "厦门市",
					"area": "湖里区",
					"cityId": 1,
					"areaId": 2,
					"lng": "118.101760",
					"lat": "24.489437",
					"isDefault": false
				},
				productDetail: {},
				skuDes: [],
				memberId: '',
				flag: false
			}
		},
		onLoad(option) {
			uni.setStorageSync('cathOrderNo', '')
			this.getStoreData()
			this.updateStoreDeposit()
			this.getStoreById()
			this.getPlaceOrder()
			this.memberId = option.id
		},
		methods: {
			getStoreData() {
				this.http({
					url: "getStoreData",
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
					},
					method: "GET",
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.storeData = res.data
						}
					}
				})
			},
			tiXian() {
				if (this.nowBalance <= 0) {
					return uni.showToast({
						title: '暂无可提现的保证金！',
						icon: 'none'
					})
				}
				if (!this.storeData.bankCode || !this.storeData.bankType || !this.storeData.accountName) {
					uni.showModal({
						title: '提示',
						content: '检测到银行卡等信息不完整，请填写完整后后操作!',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: './cardBinding'
								})
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}.bind(this)
					});
				} else {
					this.openCheck(1, "确认提现保证金吗？")
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					this.http({
						url: 'withdrawalDeposit',
						method: 'POST',
						data: {
							storeId: uni.getStorageSync('selectStoreId'),
							payType: 4,
							operator: uni.getStorageSync('employeeId')
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								uni.showToast({
									title: '提现成功！我们将会在1-7个工作日处理！',
									icon: 'none',
									duration: 1500
								})
								setTimeout(() => {
									this.getStoreById()
								}, 1500)
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					})
				}
			},
			gopdf() {
				uni.downloadFile({
					url: 'https://xyj-cereshop.oss-cn-shenzhen.aliyuncs.com/doc/jiameng01.pdf',
					success: function(res) {
						var filePath = res.tempFilePath;
						uni.openDocument({
				 		filePath: filePath,
				  	showMenu: true,
							success: function(res) {
								console.log('打开文档成功');
							}
						});
					}
				});
			},
			closeModal() {
				this.showModal = false
				this.updateStoreDeposit()
			},
			gosign() {
				let param = {
					url: "https://agent.xiaoyujia.com/franchiseEarnestMoney/" + uni.getStorageSync("selectStoreId")
				}
				let data = JSON.stringify(param);
				uni.redirectTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
			determine() {
				if (!this.totalAmount) {
				 return uni.showToast({
						title: '充值金额不能为空!',
						icon: 'none'
					})
				}
				if (this.totalAmount <= 0) {
					return uni.showToast({
						title: '金额不能小于或等于零元!',
						icon: 'none'
					})
				}
				this.popupShow = false
				this.getBillNoByMemberId()
				setTimeout(() => {
					this.getOrderPayCode()
					this.showPopu = true
				}, 1000)
			},
			getOrderPayCode() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/getOrderPayCode',
					data: {
						billNo: uni.getStorageSync('cathOrderNo')
					},
					method: 'GET',
					success: (res) => {
						if (res.code == 0) {
							this.showModal = true
							this.imgCode = res.data
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			getStoreById() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/system/getStoreById/" + uni.getStorageSync(
						"selectStoreId"),
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.nowBalance = res.data.amount
						}
					},
				})
			},
			gotoDetail() {
				uni.navigateTo({
					url: './marginDetail'
				})
			},
			getBillNoByMemberId() {
				this.http({
					url: "getBillNoByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						totalAmount: this.totalAmount,
						storeId: uni.getStorageSync("selectStoreId")
					},
					success: res => {
						if (res.code == 0 && res.data) {
							this.orderNo = res.data
							uni.setStorageSync('cathOrderNo', res.data)
						} else {
							this.createOrder()
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			getproductDetails() {
				let _this = this;
				let param = {
					areaId: 2,
					productId: "367"
				}
				uni.request({
					method: "post", //type可以为post也可以为get
					url: "https://api.xiaoyujia.com/product/productDetails",
					data: param, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
					success: function(data) {
						if (data.data.code == 0) {
							_this.moeny = data.data.data.productPrice
						}
					},
					error: function(data) {
						console.log("出现错误")
					}
				})

			},
			getPlaceOrder() {
				let _this = this;
				//只是读取产品信息，不作为下单依据
				let param = {
					areaId: 2,
					cityId: 1,
					memberId: 0,
					productId: "367"
				}
				uni.request({
					method: "post", //method可以为post也可以为get
					url: "https://yibanapi.xiaoyujia.com/product/placeOrder",
					data: param, //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
					success: function(data) {
						_this.productDetail = data.data.data;
						if (data.data.data.surcharges.length > 0) {
							let surcharges = data.data.data.surcharges;
							surcharges.forEach(i => {
								let pa = {
									id: null,
									value: null,
									price: null,
									unit: null,
									name: null
								}
								pa.id = i.id;
								pa.price = i.price;
								this.price = i.price
								pa.value = i.num;
								pa.unit = i.unit;
								pa.name = i.name;
								_this.selectMaterialList.push(pa);
							})
						}
						// _this.getProductRemark(data.data.data.productCategoryId);
						_this.productDetail.skulist.forEach(v => {
							let sku = {
								skuPropertylist: [],
								units: []
							};
							sku.skuPropertylist = v.skuPropertylist

							sku.units = [];
							v.units.forEach(vs => {
								let unit = {
									id: null,
									name: null,
									number: null,
								};
								unit.id = vs.id;
								unit.name = vs.name;
								unit.number = vs.minValue;

								sku.units.push(unit)
							});
							_this.skuDes.push(sku)
						})
						console.log(JSON.stringify(_this.skuDes))
					},
					error: function(data) {
						console.log("出现错误")
					}
				})
			},
			// 获取用户地址列表
			getAddressList() {
				if (uni.getStorageSync("memberId") == null) {
					return
				}
				this.http({
					url: 'selectMemberAddressAndInit',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.addressInfo,
					success: res => {
						if (res.code == 0) {
							this.address = []
							this.address.push(res.data[0].id)
							console.log("输出默认地址Id：", this.address[0])
						}
					}
				})
			},
			createOrder() {
				let param = {
					'cityId': 1,
					'areaId': 2,
					'productId': "367",
					'startTime': new Date(),
					'startTimeSpan': "08:00",
					'endTimeSpan': "18:00",
					'skuDes': this.skuDes,
					'address': this.address,
					'serviceRemark': null,
					'buChannel': 37,
					'source': '',
					'phone': uni.getStorageSync("account"),
					'memberId': uni.getStorageSync("memberId"),
					'totalAmount': this.totalAmount,
					'realTotalAmount': "0.01",
					'couponId': null,
					'selectMaterialList': [],

				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/addOrder',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: param,
					success: res => {
						uni.hideLoading();
						if (res.code === 0) {
							this.orderNo = res.data
							uni.setStorageSync('cathOrderNo', res.data)
							//  uni.redirectTo({
							// 	url: '/pages/order/orderdetail?billNo=' + res.data
							// })
						} else {
							this.$toast.toast(res.msg)
							this.flag = true
						}
					},
					fail: err => {
						uni.hideLoading();
						console.log(res)
					}
				})
			},
			updateStoreDeposit() {
				this.http({
					url: 'updateStoreDeposit',
					method: 'POST',
					data: {
						storeId: uni.getStorageSync('selectStoreId'),
						payType: 1,
						operator: uni.getStorageSync('employeeId')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.getStoreById()
						if (code == 0) {
							// uni.setStorageSync('cathOrderNo','')
						} else {

						}
					}
				})

			},
			getminiPay() {
				const openId = uni.getStorageSync('openId');
				if (!openId) {
					uni.showToast({
						title: '未进行微信授权登录'
					})
					reject(false);
				}
				const param = {
					openId: openId,
					orderNo: this.orderNo,
					channel: 'XYJACN'
				};
				return new Promise((resolve, reject) => {
					this.http({
						outsideUrl: 'https://api.xiaoyujia.com/pays/getWxMiniProgramPayInfo',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: param,
						success: res => {
							if (res.code === 0) {
								this.payInfo = res.data;
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
							resolve(true);
						},
						fail: err => {
							console.log(res)
							reject(false);
						}
					})

				})
			},
		},
		// 页面加载后
		mounted() {
			this.getproductDetails()
			this.getAddressList()
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}

	.textA {
		text-align: center;
		display: block;
	}

	.button-state {
		margin-top: 450rpx;
	}

	.steps {
		padding-bottom: 30rpx;
		padding-top: 25rpx;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.text-state {
		color: darkgrey;
		margin-top: 3%;
		margin-left: 30rpx;
	}

	.companyMsg {
		color: black;
		font-size: 35rpx;
		margin-left: 4%;
		line-height: 100rpx;
	}

	.input-state {
		margin-top: -2%;
		margin-left: 4%;
	}
</style>
