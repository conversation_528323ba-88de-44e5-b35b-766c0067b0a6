<template>
	<view style="padding-bottom: 160rpx;background-color: #fcfbfb;">
		<view @click="moreOperationShow = false">
			<view
				style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));height: 100rpx;display: flex;">
				<image
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1713773602507basicprofile.jpg"
					mode="widthFix" class="iconImage"></image>
				<view style="color: white;font-size: 30rpx;font-weight: bold;margin-left: 30rpx;margin-top: 25rpx;">
					{{smartPartnerName}}
				</view>
			</view>
			<uni-load-more :status="downStatus" :content-text="contentText" v-if="nowWorkOrder.no" />
			<view v-for="(item,i) in contentData" :key="i">
				<!--回复-->
				<view style="color: gray;margin-left: 20rpx;margin-top: 40rpx;font-size: 25rpx;"
					v-if="(!nowWorkOrder.no||item.messageData)&&!item.initiator">
					<!-- 工单客服-{{item.contacts}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.dateTime||item.createTime}} -->
					{{item.id?'工单客服-':''}}{{item.contacts}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.dateTime||item.createTime}}
				</view>
				<view class="w88 mg-at menu-list f14 interval replyStyle" style="margin-top: 20rpx;"
					v-if="!item.initiator&&item.msgType==1">
					<view class="div">{{item.messageData}}</view>
				</view>
				<view style="text-align: center" v-if="nowWorkOrder.no&&item.openingRemarks">
					已为您转接工单客服-{{nowWorkOrder.no}}
<!-- 					已为您转接{{nowWorkOrder.name}}-{{nowWorkOrder.no}} -->
				</view>
				<!--发送-->
				<view style="color: gray;margin-left: 60%;;margin-top: 40rpx;font-size: 25rpx;"
					v-if="item.initiator&&(item.msgType==1||item.msgType==2)">
					{{item.dateTime||item.createTime}}
				</view>
				<view class="w88 mg-at menu-list f14 interval sendStyle" style="margin-top: 20rpx;"
					v-if="item.initiator&&item.msgType==1">
					<view class="div1">{{item.messageData}}</view>
				</view>
				<u-image v-if="item.initiator&&item.msgType==2" customStyle="margin-left: 25%;" :src="item.messageData"
					mode="aspectFit" @click="openImgPreview(item.messageData)"></u-image>
				<u-image v-if="!item.initiator&&item.msgType==2" customStyle="margin-left: -10rpx" :src="item.messageData"
				    mode="aspectFit" @click="openImgPreview(item.messageData)"></u-image>
				<view class="w88 mg-at menu-list f14" style="margin-top: 20rpx;"
					v-if="item.msgType==8&&!nowWorkOrder.no">
					<view class="fb interval" style="font-size: 30rpx;">猜您想问~</view>
					<u-tabs @click="tabsClick" :activeStyle="tabsActiveStyle" :inactiveStyle="tabsInactiveStyle"
						:list="item.cardTypeList" :current="item.tabscurrent" :scrollable="true" lineColor="#1e1848"
						lineWidth="40">
					</u-tabs>
					<u-divider />
					<view v-for="(qaItem,qaI) in problemList" :key="qaIndex" @click="qaMethod(qaItem)">
						<view style="margin-left: 20rpx;font-size: 25rpx;margin-top: -15rpx;" class="flac-row-b">
							<view style="color: gray;" class="w9">{{qaItem.problem}}</view>
							<u-icon name="arrow-right" size="20" class="w1" @click="gotoDetail(qaItem)">
							</u-icon>
						</view>
						<u-divider />
					</view>
					<u-empty text="抱歉!暂无相关类型问题!" width="50" textSize="14" v-if="problemList.length<=0"
						customStyle="padding-bottom: 50rpx;">
					</u-empty>
				</view>
			</view>


			<scroll-view style="white-space:nowrap;padding-bottom: 20rpx;" scroll-x="true" v-if="!nowWorkOrder.no">
				<view class="flac-row-b">
					<view class="menu-list f14 interval" style="margin: 0 20rpx;" v-for="(item,index) in shortcutIconList"
						:key="index" @click="toGoUrl(item)">
						<image :src="item.iconUrl" mode="widthFix" class="iconImage"></image>
						<view style="margin-top: 20rpx;margin-left: 15rpx;">{{item.viewName}}</view>
					</view>
				</view>
			</scroll-view>

			<scroll-view style="" class="w88 mg-at" scroll-x="true" v-if="!nowWorkOrder.no&&chatType=='工单模式'">
			<!-- 	<view style="display: flex;">
					<view class="w3  typeBox" style="display: flex;" v-for="(item,i) in storeRegion" :key="i" @click="choiceWorkOrder(item)">
						<view>{{item.name}}</view>
						<view v-if="item.newMsgFlag==2" style="background: red;display: inline-block;width: 15rpx;height: 15rpx;border-radius: 50%;margin-top: -5rpx;"></view>
					</view>
				</view> -->
					<view style="display: flex;">
						<view class="w8  typeBox" style="display: flex;margin: auto" @click="choiceWorkOrder()">
							<view :style="isNewChatMsg?'margin-left: 25%':'margin-left: 40%'"></view>
							<view style="">{{isNewChatMsg?'查看最新工单消息':'发起工单'}}</view>
							<view v-if="isNewChatMsg" style="background: red;display: inline-block;width: 15rpx;height: 15rpx;border-radius: 50%;margin-top: -5rpx;"></view>
						</view>
					</view>
			</scroll-view>

		</view>

		<view class="chatFrame">
			<view class="w9 mg-at flac-row-b bacf">
				<view @click="pickerShow=true">{{chatType}}</view>
				<uni-icons type="down" size="20" @click="pickerShow=true"></uni-icons>
				<u-input class="w9" v-model="msgContent" placeholder="请描述您的问题" border="surround" clearable
					@confirm="sendMsg"></u-input>
				<u-icon class="w1" @click="switchMoreOperation" size="30" name="plus-circle"></u-icon>
			</view>
			<uni-grid v-if="moreOperationShow" :column="3" :highlight="true" :showBorder="false" :square="false">
				<uni-grid-item v-for="(item, i) in iconList" :index="i" :key="i">
					<view class="flex-col-c" style="margin-top: 30rpx;" @click="clickMore(item)">
						<image class="iconBox" :src="item.image" mode=""></image>
						<text class="f10">{{item.text}}</text>
					</view>

				</uni-grid-item>
			</uni-grid>
		</view>

		<u-picker @cancel="closePicker" :show="pickerShow" :columns="pickerColumns" @confirm="pickerConfirm"></u-picker>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化 
	export default {
		name: 'index',
		data() {
			return {
				isNewChatMsg: false,
				downStatus: 'more',
				contentText: {
					contentdown: '下拉加载历史记录',
					contentrefresh: '加载中',
					contentnomore: '没有更多'
				},
				iconUrl: '',
				openingRemarks: '',
				smartPartnerName: '',
				msgKey: '',
				msgContent: '',
				contentData: [],
				iconList: [
				// 	{
				// 	image: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1715071175142237拍照.png',
				// 	text: '拍照',
				// 	id: 1
				// }, 
				{
					image: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1715071150227219相册.png',
					text: '相册',
					id: 2
				}],
				pickerShow: false,
				moreOperationShow: false,
				chatType: '工单模式',
				pickerColumns: [
					['问答模式', '工单模式']
				],
				inputClearValue: "",
				tabsActiveStyle: {
					color: '#1e1848',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				},
				tabsInactiveStyle: {
					color: '#606266',
					transform: 'scale(1)'
				},
				unionMerchant: {},
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				storeType: uni.getStorageSync('storeType'),
				roleId: uni.getStorageSync("roleId") || 0,
				isLogin: false,
				checkText: "",
				moreCurrent: 0,
				nowWorkOrder: {},
				// nowWorkOrder: {},
				shortcutIconList: [],
				problemList: [],
				storeRegion: [],
			}
		},
		onLoad() {
			this.checkLogin()
		},
		onPullDownRefresh() {
			this.downStatus = 'loading'
			this.moreCurrent +=1;
			this.http({
				url: "getHistoryChatLog",
				method: 'GET',
				hideLoading: true,
				data: {
					contacts: this.nowWorkOrder.no,
					employeeNo: uni.getStorageSync("employeeNo"),
					current: this.moreCurrent 
				},
				success: res => {
					if (res.code == 0) {
						if(res.data){
						let arr = []
						if(this.moreCurrent==1){
							this.contentData=[]
						}
						for (var i = 0; i < res.data.length; i++) {
							arr.push(res.data[i])
						}
						if(this.moreCurrent==1){
						for (var i = 0; i < this.contentData.length; i++) {
							if(this.contentData[i].id){
							arr.push(this.contentData[i])
							}
						}
						}
						let sarr = arr.reverse()
						if(this.moreCurrent==1){
							for (var i = 0; i < sarr.length; i++) {
								this.contentData.push(sarr[i])
							}
						}
						if(this.moreCurrent>1){
							let darr = sarr.reverse()
							for (var i = 0; i < darr.length; i++) {
								this.contentData.unshift(darr[i])
							}
						}
						}
						this.downStatus = 'noMore'
						uni.stopPullDownRefresh();
					} else {
						uni.showToast({
							title: '获取失败，请稍后重试！',
							icon: 'none'
						})
						this.downStatus = 'noMore'
						uni.stopPullDownRefresh();
					}
				},
			})
		},
		methods: {
			getNewChatMsg() {
				this.http({
					url: "getNewChatMsg",
					method: 'GET',
					data: {
						employeeNo: uni.getStorageSync('employeeNo')
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if(res.data==2){
								this.isNewChatMsg = true
							}
						}
					},
				})
			},
			openImgPreview(val){
				let data = []
				data.push(val)
				uni.previewImage({
					urls: data,
					current: val
				})
			},
			qaMethod(item) {
				uni.showToast({
					title: '问答功能即将上线，请敬请期待！',
					icon: 'none'
				})
			},
			clickMore(item) {
				if (item.id == 2) {
					if (this.chatType == '工单模式' && !this.nowWorkOrder.no) {
						return uni.showToast({
							icon: 'none',
							title: '请先选择上方联系角色!'
						})
					} else {
						uni.chooseImage({
							success: (chooseImageRes) => {
								const tempFilePaths = chooseImageRes.tempFilePaths;
								uni.uploadFile({
									url: 'https://api2.xiaoyujia.com/system/imageUpload',
									filePath: tempFilePaths[0],
									name: 'file',
									formData: {
										route: 'userPhotos'
									},
									dataType: 'json',
									success: (uploadFileRes) => {
										let result = JSON.parse(uploadFileRes.data)
										let obj = {
											messageData: result.data,
											chatType: this.chatType == '工单模式' ? 2 : 1,
											initiator: uni.getStorageSync('memberId'),
											contacts: this.nowWorkOrder.no,
											msgType: 2,
											channel: this.msgKey,
											employeeNo: uni.getStorageSync('employeeNo'),
											dateTime: moment().format('yyyy-MM-DD HH:mm:ss'),
										};
										//保存聊天信息
										this.http({
											url: "saveSmartPartnerChatLog",
											method: 'POST',
											data: obj,
											header: {
												"content-type": "application/json;charset=UTF-8"
											},
											success: res => {
												if (res.code == 0) {
													obj.id = res.data
													this.goEasy.pubsub.publish({
														channel: this.msgKey,
														message: JSON
															.stringify(obj),
														onFailed: function(
															error) {
															console.log(
																"消息发送失败，错误编码：" +
																error
																.code +
																" 错误信息：" +
																error
																.content
															);
														}
													});
													this.contentData.push(obj)
													this.moreOperationShow = false
													this.msgContent = ''
												} else {
													uni.showToast({
														title: res.msg,
														icon: 'none'
													})
												}
											}
										})


									}
								});


							}
						});

					}
				} else {
					return uni.showToast({
						title: '该功能即将上线，请敬请期待！',
						icon: 'none'
					})
				}
			},
			getSmartPartnerChatLog() {
				this.http({
					url: "getSmartPartnerChatLog",
					method: 'GET',
					data: {
						channel: this.msgKey,
						employeeNo: uni.getStorageSync('employeeNo'),
						contacts: this.nowWorkOrder.no
					},
					success: res => {
						if (res.code == 0) {

						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					},
				})
			},
			toGoUrl(item) {
				console.log(item);
				if (item.linkType == 1) {
					uni.navigateTo({
						url: item.linkUrl
					})
				} else {
					console.log("1111");
				}
			},
			getStoreSmartPartnerData() {
				this.http({
					url: "getStoreSmartPartnerData",
					method: 'GET',
					data: {
						roleId: uni.getStorageSync('roleId'),
						employeeId: uni.getStorageSync('employeeId'),
						storeId: uni.getStorageSync('storeId')
					},
					success: res => {
						if (res.code == 0) {
							this.smartPartnerName = res.data.smartPartnerName
							this.iconUrl = res.data.iconUrl
							this.openingRemarks = res.data.openingRemarks
							this.shortcutIconList = res.data.shortcutIconList
							this.contentData = res.data.contentData
							this.storeRegion = res.data.storeRegion
							this.problemList = res.data.contentData[1].cardTypeList[0].cardListList
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					},
				})
			},
			recevice() {
				//订阅消息
				let _this = this;
				this.goEasy.pubsub.subscribe({
					channel: this.msgKey, //替换为您自己的channel
					onMessage: function(message) { //收到消息
						console.log("Channel:" + message.channel + " content:" + message.content);
						let content = JSON.parse(message.content);
						console.log("content", content);
						//自己发送的不回存
						if (content.hasOwnProperty('initiator')) {
							console.log('自己发送的不回存');
						} else {
							//如果已转工单  覆盖原联系人信息
							if(content.hasOwnProperty('ifTurnFlag')){
								_this.nowWorkOrder.no = content.turnContacts
								_this.smartPartnerName = content.turnContactsRole + "-" + content.turnContacts
								uni.setNavigationBarTitle({
									title: content.turnContactsRole + "-" + content.turnContacts
								});
							}
							let obj = {
								messageData: content.messageData,
								chatType: content.chatType,
								contacts: content.contacts,
								msgType: content.msgType,
								channel: content.channel,
								employeeNo: content.employeeNo,
								dateTime: content.dateTime,
								id: content.id,
							};
							_this.contentData.push(obj)
						}

					},
					onFailed: function(error) {
						console.log("Channel订阅失败, 错误编码：" + error.code + " 错误信息：" + error.content)
					}
				});
			},
			sendMsg() {
				if (!this.msgContent) {
					return uni.showToast({
						icon: 'none',
						title: '不能发送空白信息!'
					})
				}
				if (this.chatType == '工单模式' && !this.nowWorkOrder.no) {
					return uni.showToast({
						icon: 'none',
						title: '请先选择上方联系角色!'
					})
				}

				let obj = {
					messageData: this.msgContent,
					chatType: this.chatType == '工单模式' ? 2 : 1,
					initiator: uni.getStorageSync('memberId'),
					contacts: this.nowWorkOrder.no,
					msgType: 1,
					channel: this.msgKey,
					employeeNo: uni.getStorageSync('employeeNo'),
					dateTime: moment().format('yyyy-MM-DD HH:mm:ss'),
				};

				//保存聊天信息
				this.http({
					url: "saveSmartPartnerChatLog",
					method: 'POST',
					data: obj,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							obj.id = res.data
							this.goEasy.pubsub.publish({
								channel: this.msgKey,
								message: JSON.stringify(obj),
								onFailed: function(error) {
									console.log("消息发送失败，错误编码：" + error.code + " 错误信息：" + error
										.content);
								}
							});
							this.contentData.push(obj)
							this.msgContent = ''
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})

			},
			choiceWorkOrder(item) {
				if(!item){
					item = this.storeRegion[0]
				}
				const now = new Date();
				const year = now.getFullYear();
				const month = ('0' + (now.getMonth() + 1)).slice(-2);
				const day = ('0' + now.getDate()).slice(-2);
				const hours = ('0' + now.getHours()).slice(-2);
				const minutes = ('0' + now.getMinutes()).slice(-2);
				const seconds = ('0' + now.getSeconds()).slice(-2);
				const formattedTime = year + month + day + hours + minutes + seconds;

				this.msgKey = formattedTime + uni.getStorageSync('memberId')
				this.recevice()
				this.nowWorkOrder = item
				// this.smartPartnerName = item.name + "-" + item.no
				// uni.setNavigationBarTitle({
				// 	title: item.name + "-" + item.no
				// });
				this.smartPartnerName =  "工单客服-" + item.no
				uni.setNavigationBarTitle({
					title:  "工单客服-" + item.no
				});
			},
			switchMoreOperation() {
				if (this.moreOperationShow) {
					this.moreOperationShow = false
				} else {
					this.moreOperationShow = true
				}
			},
			closePicker() {
				this.pickerShow = false
			},
			pickerConfirm(e) {
				this.chatType = e.value[0]
				this.pickerShow = false
			},
			clearInput: function(event) {
				this.inputClearValue = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			tabsClick(val) {
				this.problemList = val.cardListList
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast("您还未进行登录哦，登录后可体验更多功能！")
					uni.setStorageSync('redirectUrl', '/pages-other/smartPartner/index')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				}else{
					this.getStoreSmartPartnerData()
					this.getNewChatMsg()
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-tabs__wrapper__nav__item__text {
		font-size: 25rpx !important;
	}

	.iconBox {
		width: 60rpx;
		height: 60rpx;
		background-color: #eeeff4;
		padding: 25rpx;
		margin: 20rpx;
	}

	.typeBox {
		white-space: nowrap;
		text-align: center;
		font-size: 28rpx;
		color: #1e1848;
		margin: 10rpx 20rpx 10rpx 0;
		background-color: #eee;
		padding: 15rpx;
		border-radius: 30rpx;
	}

	.iconImage {
		display: block;
		margin-top: 10rpx;
		width: 80rpx;
		height: 80rpx;
		margin-left: 30rpx;
		border-radius: 50%;
		// animation: iconImage 5s linear infinite;
	}

	.menu-list {
		display: block;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		background-color: #fff;
		padding: 0 20rpx;
	}

	.interval {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.replyStyle {
		background: #eee;
		/* 箭头靠左边 */
		clip-path: polygon(5% 0, 100% 0, 100% 100%, 5% 100%, 5% 65%, 0 50%, 5% 35%);

	}

	.sendStyle {
		background: #eee;
		/* 箭头靠左边 */
		clip-path: polygon(0 0, 88% 0, 88% 35%, 95% 50%, 88% 65%, 88% 100%, 0 100%);

	}

	.div {
		width: 90%;
		margin: auto 40rpx;
	}

	.div1 {
		width: 90%;
		margin: auto 10rpx;
	}

	.chatFrame {
		width: 100%;
		margin: auto;
		padding: 30rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		box-shadow: 0 4rpx 20rpx #dedede;
	}
</style>