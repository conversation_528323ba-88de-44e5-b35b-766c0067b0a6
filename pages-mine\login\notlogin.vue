<template>
	<view style="padding-top: 400rpx;">

		<u-empty text="当前未登录，请先登录" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		<view class="btn-big">
			<button class="btnStyle" @click="openLogin()">立即登录</button>
		</view>
	</view>
</template>

<script>
	export default {
		methods: {
			// 打开登录
			openLogin() {
				uni.reLaunch({
					url: '/pages-mine/login/login'
				});
			},
		},
		onLoad(options) {
			let url = options.url || ""
			if (url != "") {
				console.log("缓存重定向地址", url)
				uni.setStorageSync("redirectUrl", url)
			}
		}
	}
</script>

<style lang="scss">
	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			border: #1e1848 2rpx solid;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>