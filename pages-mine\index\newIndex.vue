<template>
  <view>
    
    <view class="main">
      <works v-if="current==0" :loadMore="loadMore" />
      <mine v-if="current==2" :refresh="refresh" />
    </view>
    <u-tabbar :value="current" @change="name => current = name" :fixed="true" :placeholder="true"
      :safeAreaInsetBottom="true" activeColor="#1c1c76" :border="false">
      <u-tabbar-item>
       <u-icon slot="active-icon" size="50" name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-logo.png" />
        <u-icon slot="inactive-icon" size="45" name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-logo.png" />
      </u-tabbar-item>
      <u-tabbar-item text="消息">
      		<image
      			class="slot-icon"
      			slot="active-icon"
      			src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-xx.png"
      		></image>
      		<image
      			class="slot-icon"
      			slot="inactive-icon"
      			src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-inxx.png"
      		></image>
      	</u-tabbar-item>
      <u-tabbar-item text="我的" @click="empower()">
        <image
        	class="slot-icon"
        	slot="active-icon"
        	src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-grzx.png"
        ></image>
        <image
        	class="slot-icon"
        	slot="inactive-icon"
        	src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ingrzx.png"
        ></image>
      </u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
  import works from "@/pages-mine/works/works.vue";
  import mine from "@/pages-mine/index/mine.vue";
  export default {
    components: {
      works,
      mine
    },
    data() {
      return {
        refresh: false,
        current: 0,
        loadMore: 0,
        memberId: null,
        baomuId: null,
        scrollTop: 0,
        backTopStyle: {
          iconStyle: {
            fontSize: '32rpx',
            color: '#f6cc70'
          },
          customStyle: {
            backgroundColor: '#1e1848'
          },
        },
      }
    },
    methods: {
      empower() {
        // 通用-接单提醒模板&活动进展模板
        let tmpIds = [
          "GD6KOYLLn0ZlmOZLQ0gSVm8ZUC78ex98C-gVNVMk4fk",
          "ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI"
        ]
        if (uni.getStorageSync("employeeType") == 10 || uni.getStorageSync("roleId") == 1) {
          //取消订单
          tmpIds.push("YRSv7oviwbjl0U-fo-ghtq6OUG2Zn4u-pqd6Y9ums90")
          //接单
          tmpIds.push("U2NPysqnHSDt_1oVl1Nvbwy_vivhiRfrlHqwwEI3xEQ")
          //服务评价
          tmpIds.push("x64aI96dYKraSdcHcCTSh7sS0Bdvx8rRfzo3TU2RXK0")
        }
        // 获取订阅消息授权
        // #ifdef  MP-WEIXIN
        wx.requestSubscribeMessage({
          tmplIds: tmpIds,
          success: res => {
            console.log("用户同意进行小程序消息订阅！")
          },
          fail: res => {}
        })
        // #endif
      },
      onPageScroll(e) {
        this.scrollTop = e.scrollTop
      },
      // 分享到好友或朋友圈
      onShareAppMessage(res) {
        return {
          title: '小羽佳-家姐联盟',
          path: '/pages/index/index',
          mpId: 'wx8342ef8b403dec4e'
        }
      },
      onShareTimeline(res) {
        return {
          title: '小羽佳-家姐联盟',
          type: 0,
          summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
        }
      },
      getLocation() {
        //#ifdef  MP-WEIXIN || APP-PLUS || H5
        let that = this;
        uni.getLocation({
          type: 'gcj02',
          isHighAccuracy: true,
          success: res => {
            console.log("定位调用成功")
            console.log('---', res)
            let {
              latitude,
              longitude
            } = res
            let x = longitude
            let y = latitude
            let x_pi = (3.14159265358979324 * 3000.0) / 180.0
            let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi)
            let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi)
            let lng = z * Math.cos(theta) + 0.0065
            let lat = z * Math.sin(theta) + 0.006
            console.log("（高精度）当前的纬度：", lat, "当前的经度", lng)
            uni.setStorageSync("lat", lat)
            uni.setStorageSync("lng", lng)
          },
          fail: err => {
            // that.hasLocation = false
          },
        })
        // #endif

      },
      checkBaomu() {
        this.http({
          url: 'checkBaomuCollectByMemberId',
          method: 'POST',
          data: {
            memberId: uni.getStorageSync("memberId")
          },
          success: res => {
            if (res.code == 0) {
              this.baomuId = res.data.baomuId
              uni.setStorageSync("baomuId", this.baomuId)
            }
          },
        })
      },
      checkEmployee() {
        this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
        this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
        console.log("是否员工：", this.isEmployee, "是否保姆：", this.isBaomu)
        console.log("员工id：", uni.getStorageSync("employeeId"))
        if (this.isEmployee) {
          if (this.isBaomu) {
            if (this.baomuId == null) {
              this.checkBaomu()
            }
          }
        }
      },
      getMemberInfor() {
        let baomuId = uni.getStorageSync('baomuId')
        baomuId = baomuId == "" ? null : baomuId
        this.checkEmployee()
      },
      checkLogin() {
        console.log("正在检查登录状态...")
        if (!uni.getStorageSync('memberId')) {
          uni.setStorageSync("employeeState", -1)
          return false
        } else {
          this.getMemberInfor()
          return true
        }
      }
    },
    onReachBottom() {
      // 滑动到底部刷新
      // console.log("滑动到底部加载更多...")
      this.loadMore++
    },
    async onLoad(option) {
      await this.getLocation()
    },
    onShow() {
      this.refresh = !this.refresh
    },
    mounted() {
      this.checkLogin()
    }
  }
</script>

<style lang="scss">
  page {
    height: auto;
    background-color: #ffffff;
  }
  .slot-icon {
    width: 40rpx;height: 40rpx;
  }
</style>