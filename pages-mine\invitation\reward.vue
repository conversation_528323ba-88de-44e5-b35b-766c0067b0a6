<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view>
			<view class="introduce-img">
				<img :src="shareImg" mode="widthFix" @click="openImgPreview()"
					@longpress="showAccountLogin=!showAccountLogin" />
			</view>
			<!-- 			<view class="btn-big">
				<u-button shape="circle" text="上户领积分" customStyle="color:#f6cc70" color="#1e1848"
					@click="openTab(0)"></u-button>
			</view>
			<view class="btn-big-plain">
				<u-button shape="circle" :plain="true" text="下户领积分" color="#1e1848" @click="openTab(1)"></u-button>
			</view> -->

			<view class="btn-big-plain">
				<u-button shape="circle" text="绑定家政经纪人" customStyle="color:#f6cc70" color="#1e1848"
					@click="openTab(0)"></u-button>
			</view>

			<view class="f16 lh30" style="margin: 0 40rpx;">
				* 绑定您的专属家政经纪人，邀请Ta帮助您完善简历，去寻找心仪的工作吧！
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可配置选项
				// 推荐人提醒-订阅消息模板
				templateId: "ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI",

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				memberId: null,
				baomuId: null,
				employeeId: null,
				privater: null,
				employee: {
					introducer: null
				},
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671158255939192x192.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				shareImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_bindreward.png",
			}
		},
		methods: {
			openTab(index) {
				let employeeType = uni.getStorageSync("employeeType") || 0

				if (!this.checkLogin()) {
					this.openCheck(2, "您还未登录哦", "登录后体验更多功能！")
					return
				}

				if (employeeType == 20) {
					this.openCheck(-1, "只有员工可以绑定哦", "请退出登录重试！")
					return
				}

				this.openCheck(0, "绑定家政经纪人", "绑定后可邀请Ta帮助您完善简历，早日找到工作哦！")
			},
			// 上下户领积分
			workReward(value) {
				this.http({
					url: 'bindReward',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId,
						type: value,
						privater: this.privater
					},
					success: res => {
						if (res.code == 0) {
							let str = "绑定成功！"
							this.$refs.uNotify.success(str)
							this.sendSubscribeMsg(value)
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 发送订阅消息给推荐人（提醒其帮助员工完善简历）
			sendSubscribeMsg(index) {
				let introducer = this.employee.introducer
				let realName = uni.getStorageSync('employeeName')
				let data = {
					source: "xyjacn",
					memberId: null,
					employeeId: null,
					employeeNo: introducer,
					templateId: this.templateId,
					page: "/pages-mine/resume/experience?baomuId=" + this.employeeId + "&showTips=1"
				}

				if (index == 0) {
					let dataDetail = {
						"thing6": {
							"value": realName + "-上户提醒"
						},
						"thing1": {
							"value": "该员工准备上户啦"
						},
						"thing4": {
							"value": "请帮助Ta完善之前的工作经历吧"
						}
					}
					this.$set(data, 'data', dataDetail)
				} else if (index == 1) {
					let dataDetail = {
						"thing6": {
							"value": realName + "-下户提醒"
						},
						"thing1": {
							"value": "该员工已经下户啦"
						},
						"thing4": {
							"value": "请帮助Ta完善之前的工作经历吧"
						}
					}
					this.$set(data, 'data', dataDetail)
				}
				this.http({
					url: 'sendSubscribeMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					path: this.employeeId,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							let baomuDetail = res.data
							this.employee = baomuDetail.employee
						} else {
							// this.$toast.toast('获取保姆详细信息失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 分享到好友
			onShareAppMessage(res) {
				return {
					title: '打卡领积分兑好礼',
					path: "/pages-mine/invitation/reward?privater=" + uni.getStorageSync("employeeNo"),
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: this.shareImg
				}
			},
			//分享到朋友圈
			onShareTimeline(res) {
				return {
					title: '打卡领积分兑好礼',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			// 获取用户信息
			getMemberInfor() {
				this.memberId = uni.getStorageSync('memberId') || 0
				this.employeeId = uni.getStorageSync('employeeId') || 0
				this.getBaomuDetail()
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.workReward(0)
				} else if (this.checkType == 1) {
					this.workReward(1)
				} else if (this.checkType == 2) {
					uni.reLaunch({
						url: '/pages-mine/login/login'
					});
				}
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					uni.setStorageSync('redirectUrl', '/pages-mine/invitation/reward?privater=' + this.privater)
					return false
				} else {
					return true
				}
			}
		},
		onLoad(options) {
			this.privater = options.privater || ""
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				let no = obj.id
				this.privater = no
			} else {
				console.log("未获取邀请人信息！")
			}
		},
		// 页面加载后
		mounted() {
			this.getMemberInfor()
		},
	}
</script>
<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.introduce-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 100%;
			height: auto;
			margin: 0 auto;
		}
	}

	.btn-big,
	.btn-big-plain {
		width: 80%;
		height: 90rpx;
		margin-left: 10%;
		margin: 40rpx auto;

		button {
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 50rpx;
			font-size: 36rpx;
			color: #f6cc70;
			background-color: #1e1848;
		}
	}

	.btn-big-plain {
		button {
			color: #1e1848;
			background-color: #ffffff;
		}
	}
</style>