<template>
	<view class="page">
		<uni-section class="" title="服务区域" type="line" titleFontSize="18px" style="border-bottom: 2rpx solid #eee;">
		</uni-section>
		<!-- 服务区域 -->
		<view class="bacf" style="padding: 30rpx 20rpx;">
			<uni-data-checkbox mode="button" selectedColor="#1e1848" multiple v-model="checkbox" :localdata="listData"
				min="1"></uni-data-checkbox>
		</view>
		<!-- 开始时间-结束时间-时间管理 -->
		<u-datetime-picker :show="showStart" mode="time" @cancel="showStart = false" confirmColor="#dd0000"
			@confirm="formatDate" v-model="startTime"></u-datetime-picker>
		<u-datetime-picker :show="showEnd" @confirm="formatDateB" v-model="endTime" mode="time"
			@cancel="showEnd = false" confirmColor="#dd0000">
		</u-datetime-picker>
		<view class="bacf" style="margin: 20rpx auto;">
			<view class="listStyle w9 mg-at flac-row f18 lh50" @click="showStart = true">
				<view class="c6">开始时间</view>
				<view class="flac-row">
					<view class="" style="margin: auto 20rpx;">{{startTime}}</view>
					<u-icon name="arrow-right" size="22"></u-icon>
				</view>
			</view>
			<view class="listStyle w9 mg-at flac-row f18 lh50 border-bottom-2se border-top-2se" @click="showEnd = true">
				<view class="c6">结束时间</view>
				<view class="flac-row">
					<view class="" style="margin: auto 20rpx;">{{endTime}}</view>
					<u-icon name="arrow-right" size="22"></u-icon>
				</view>
			</view>
		</view>
		<u-button text="确认提交" @click="submitData" :customStyle="btnStyle"></u-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				btnStyle: {
					color: '#f6cc70',
					backgroundColor: '#1e1848',
					borderRadius: 0,
					borderColor: '#1e1848',
					position: 'fixed',
					bottom: 0,
					left: 0
				},
				showStart: false,
				showEnd: false,
				startTime: '06:00',
				endTime: '18:00',
				id: '',
				checkbox: [],
				res: '公休',
				employeeId: uni.getStorageSync('employeeId') || 0,
				listData: [],
			};
		},
		onLoad(val) {
			this.employeeId = val.employeeId || this.employeeId
			this.getEmployeeTimeTemplate()
		},
		onShow: function() {
			uni.setStorageSync('timeDetailShow', true)
		},
		methods: {
			getEmployeeTimeTemplate() {
				this.http({
					url: 'getEmployeeTimeTemplate',
					data: {
						employeeId: this.employeeId
					},
					method: 'GET',
					success: res => {
						if (res.code == 0 && res.data.length > 0) {
							this.listData = res.data
						} else {
							uni.showToast({
								title: "获取员工服务区域失败!",
								icon: 'none'
							})
						}
					}
				})
			},
			submitData() {
				this.http({
					url: 'initializationEmployeeArea',
					method: 'POST',
					data: {
						employeeId: this.employeeId,
						stateTime: this.startTime,
						endTime: this.endTime,
						areaIdList: this.checkbox
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							uni.showToast({
								title: '初始化设置成功!',
								icon: 'none'
							})
							setTimeout(()=>{
								uni.navigateBack();
							},1500)
						} else {
							uni.showToast({
								title: '初始化失败，请稍后重试！',
								icon: 'none',
								duration: 2000
							})
						}
					}
				})
			},
			formatDate(time) {
				this.showStart = false
			},
			formatDateB(time) {
				this.showEnd = false
			},
			change(e) {
				if (e) {
					this.res = '公休'
				} else {
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		height: 100vh;
		background-color: #f6f7fb !important;
	}

	.listStyle {
		justify-content: space-between;
	}

	/deep/.uni-data-checklist .checklist-group .checklist-box.is--button {
		padding: 10px 10px;
	}

	/deep/.uni-data-checklist .checklist-group {
		width: 95%;
		margin: auto;
		justify-content: space-between;
	}

	/deep/.uni-section .uni-section-header__decoration.line {
		height: 18px;
	}

	/deep/.uni-section .uni-section-header__decoration {
		background-color: #f9ae3d;
	}

	/deep/.uni-select,
	/deep/.uni-select__selector-item {
		font-size: 16px;
	}

	/deep/ .u-button {
		border-radius: 0 !important;
	}

	/deep/ .u-button__text {
		font-size: 1rem !important;
	}
</style>