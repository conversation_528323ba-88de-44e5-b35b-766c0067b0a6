弹<template>
	<view class="page">
		<view class="w9 mg-at f16 cf fb text-r lh70">
			<text @click="goScore()">更新记录</text>/
			<text @click="goPage('./introductionPage')">规则详情</text>
		</view>
		<view class="w85 mg-at bacf radius15" style="padding: 40rpx 30rpx;margin-top: 65rpx;">
			<view class="scoreBg text-c" :style="{'backgroundImage':'url(' + bgUrls[bgIndex] + ')'}">
				<view class="f36 fb c325a8c" style="margin-top: 80rpx;">{{agentStore.score || 0}}</view>
				<view class="lh45 c6">当前服务分</view>
			</view>
			<view class="w5">弹
				<view class="f18 fb lh30">{{agentStore.storeName}}</view>
			</view>
			<view class="w10">
				<view class="flac-row-b lh35">
					<view class="f16 fb">服务分明细</view>
				</view>
				<view class="flac-col lh35" v-if="scoreDetail">
					<view v-for="(item,index) in scoreDetail.serviceScoreRuleList" :key="index" class="flac-col t-c"
						v-if="item.groupId<8||(item.groupId>=8&&item.groupIntegral)">
						<text class="fb f16" style="color: #ff4d4b;">{{formatGroupScore(item)}}</text>
						<view class="flac-row t-c fb f15" style="align-items: center;">
							<view class="w7">子项目</view>
							<view class="w3">得分</view>
						</view>
						<view class="flac-row t-c" style="align-items: center;"
							v-for="(item1,index1) in item.ruleDetailList" :key="index1">
							<view class="w7">{{item1.ruleTitle}}
							</view>
							<view class="w3">{{Number(item1.ruleIntegralAwarded).toFixed(2)}}</view>
						</view>
					</view>
					<view vclass="flac-col t-c" v-if="ruleApprovalList.length">
						<text class="fb f16" style="color: #ff4d4b;">加分审批项</text>
						<view class="flac-row t-c fb f15" style="align-items: center;">
							<view class="w7">子项目</view>
							<view class="w3">得分</view>
						</view>
						<view class="flac-row t-c" style="align-items: center;" v-for="(item,index) in ruleApprovalList"
							:key="index">
							<view class="w7">{{item.ruleTitle}}
							</view>
							<view class="w3">{{Number(item.ruleIntegralPresets).toFixed(2)}}</view>
						</view>
					</view>
				</view>
				<u-empty v-else mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png" text="暂无明细" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				employeeId: 0,
				agentStore: {},
				bgUrls: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-arc100.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-arc80.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-arc60.png'
				],
				bgIndex: 2,
				studylist: [],
				ruleApprovalList: [],
				scoreDetail: null,
			};
		},
		onLoad(options) {
			this.employeeId = options.employeeId || uni.getStorageSync('employeeId') || 0
			this.getScoreInfo()
		},
		methods: {
			formatGroupScore(item) {
				let str = item.groupName + '（' + Number(item.groupIntegral).toFixed(2)
				if (item.groupId < 5) {
					str += '/' + Number(item.groupIntegralRate * 100).toFixed(2)
				}
				return str + '）'
			},
			goScore() {
				if (!this.agentStore.id) {
					return
				}
				let url = '../serviceScore/listPage?agentStoreId=' + this.agentStore.id
				uni.navigateTo({
					url: url
				})
			},
			// 获取加分审批列表
			listServiceScoreRuleApproval() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/task/listServiceScoreRuleApproval',
					data: {
						approvalState: 2,
						storeId: this.agentStore.storeId,
						thisWeek: 1
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.ruleApprovalList = res.data
						} else {
							this.ruleApprovalList = []
						}
					}
				})
			},
			getStoreServiceScore() {
				this.http({
					url: 'getStoreServiceScore',
					// outsideUrl: 'http://100.101.143.70:9999/task/getStoreServiceScore',
					data: {
						storeId: this.agentStore.storeId || 0,
						agentStoreId: this.agentStore.id
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.scoreDetail = res.data
							// this.agentStore.score = this.scoreDetail.ruleIntegralResult
						}
					}
				})
			},
			getScoreInfo() {
				this.http({
					url: 'getAgentStoreInfo',
					data: {
						employeeId: this.employeeId
					},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.agentStore = res.data
							if (this.agentStore.score > 80) {
								this.bgIndex = 0
							} else if (this.agentStore.score > 60) {
								this.bgIndex = 1
							} else {
								this.bgIndex = 2
							}
							this.getStoreServiceScore()
							this.listServiceScoreRuleApproval()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}

					}
				})
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		min-height: 100vh;
		background: linear-gradient(120deg, rgba(58, 100, 100, 1), rgba(38, 56, 128, 1));
		// background: #d2f6fd;
	}

	.c325a8c {
		color: #325a8c;
	}

	.scoreBg {
		width: 300rpx;
		height: 200rpx;
		position: absolute;
		z-index: 99;
		top: 110rpx;
		right: 70rpx;
		// background-image: url('{{bgUrls[bgIndex]}}');
		background-repeat: no-repeat;
		background-position: center;
		background-size: 100% 100%;
	}
</style>