<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<button class="w9 mg-at btnStyle" @click="add">创建补签合同</button>
		<view class="swiper-tab" v-for="(contract, index1) in supportList" :key="index1">
			<view class="swiper-head">
				<text class="swiper-title">合同号：{{contract.oldNo}}</text>
				<text class="swiper-tips" v-if="contract.status == 1">生效</text>
				<text class="swiper-tips" v-if="contract.status == 0">暂存</text>
				<text class="swiper-tips" v-if="contract.status == 2">完成</text>
			</view>
			<view class="swiper-content" style="display: flex;flex-direction: column;">
				<view class="content-right">
					<view class="content-text">
						<text>客户名称：{{contract.memberName}}</text>
					</view>
					<view class="content-text">
						<text>创建日期：{{contract.createDate}}</text>
					</view>
					<view class="content-text">
						<text>离职日期：{{contract.quitDate}}</text>
					</view>
					<view class="content-text">
						<text>原阿姨：{{contract.oldEmployeeName}}</text>
					</view>
					<view class="content-text">
						<text>现阿姨：{{contract.employeeName}}</text>
					</view>
				</view>
				<view class="flac-row-c">
					<u-button customStyle="width:28%;" text="签署合同" color="#1e1848" @click="siginUrl(contract)">
					</u-button>
					<u-button customStyle="width:28%;" plain text="编辑合同" color="#1e1848"
						@click="editContract(contract.id)">
					</u-button>
					<u-button customStyle="width:28%;" plain text="下载合同" color="#1e1848"
						@click="downloadContract(contract)">
					</u-button>
					
					<!-- <u-button customStyle="width:28%;" text="暂存" color="#1e1848"
						v-if="contract.status == 1 && !contract.memberSignDate" @click="save(contract,0)">
					</u-button> -->
					<!-- <u-button customStyle="width:28%;" text="生效" color="#1e1848"
						v-if="contract.status == 0 && !contract.memberSignDate" @click="save(contract,1)">
					</u-button> -->
				</view>
				<u-gap height="10"></u-gap>
				<view class="flac-row">
					<u-button customStyle="width:28%;"  text="合同上险" color="#1e1848" v-if="getDayTime(contract.clientDate) && contract.memberSignDate && contract.employeeSignDate"
						 @click="doInsurance(contract)">
					</u-button>
				</view>
				<u-gap height="20"></u-gap>
			</view>
		</view>
		<u-empty mode="list" v-if="supportList.length <= 0"></u-empty>


		<u-modal :show="showModal" title="签名地址" @confirm="showModal = false">
			<view class="slot-content">
				<view class="flac-row">
					<text>客户是否已签名：</text>
					<text>{{memberSign.siginUrl?'已签名':'未签名'}}</text>
					<uni-icons :type="memberSign.siginUrl?'checkmarkempty':'closeempty'"
						:color="memberSign.siginUrl?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制客户签名地址" color="#1e1848" @click="copy(0)"></u-button>
				<u-gap height="30"></u-gap>
				<view class="flac-row">
					<text>员工是否已签名：</text>
					<text>{{employeeSign.siginUrl?'已签名':'未签名'}}</text>
					<uni-icons :type="employeeSign.siginUrl?'checkmarkempty':'closeempty'"
						:color="employeeSign.siginUrl?'#19be6b':'#ff4d4b'" size="20"></uni-icons>
				</view>
				<u-gap height="10"></u-gap>
				<u-button text="复制员工签名地址" color="#1e1848" @click="copy(1)"></u-button>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化
	export default {
		data() {
			return {
				showModal: false,
				id: '',
				no: '',
				supportList: [],
				employeeSign: {},
				memberSign: {},
				tempId: '',
			};
		},
		onShow() {
			this.getData()
		},
		onLoad(option) {
			this.no = option.no
			this.id = option.id
		},
		methods: {
			doInsurance(dom) {
				uni.showModal({
					title:'请核对阿姨身份证/姓名/手机号三者无误后在操作以免后续理赔产生争议',
					confirmText:'确认无误',
					cancelText:'取消',
					success: (res) => {
						if(res.confirm) {
							this.http({
								url: "doInsurance",
								method: 'POST',
								data: {
									insureType:1,
									contractType:2,
									contractId:dom.id
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										uni.showToast({
											title: '上保成功',
											icon: 'none',
											duration: 1500
										})
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none'
										})
									}
								}
							})
						}
					}
				})
			},
			add() {
				uni.navigateTo({
					url: '/pages-work/operations/addContractSupport?no=' + this.no
				})
			},
			copy(val) {
				let info;
				if (val == 0) {
					info = 'https://agent.xiaoyujia.com/upbaomu/contractInfoSupply/' + this.tempId
				} else {
					info = 'https://agent.xiaoyujia.com/upbaomu/baomuContractInfoSupply/' + this.tempId
				}
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
			},
			getData() {
				this.http({
					url: 'contractSuppolyList',
					method: 'GET',
					hideLoading: true,
					data: {
						id: this.id
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.supportList = res.data;
						}
					}

				})
			},
			save(dto, status) {
				dto.status = status;
				dto.operator = uni.getStorageSync('employeeId');
				this.http({
					url: 'saveContractSupply',
					method: 'POST',
					data: dto,
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '更新成功',
									icon: 'success',
									duration: 1000
								})
							}, 1000)
							this.getData();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}

				})
			},
			editContract(id) {
				uni.navigateTo({
					url: '/pages-work/operations/addContractSupport?no=' + this.no + '&id=' + id
				})
			},
			// 下载合同
			downloadContract(contract) {
				let url = 'https://agent.xiaoyujia.com/upbaomu/contractInfoSupply/' + contract.id
				uni.setClipboardData({
					data: url,
					success: () => {
						this.$refs.uNotify.success('补签合同下载链接已复制！请粘贴到电脑浏览器中打开，并点击鼠标右键选择打印！')
					}
				})
			},
			siginUrl(contract) {
				this.tempId = contract.id
				this.getSignUrl(contract.employeeId, null, contract.id)
				this.getSignUrl(null, contract.memberId, contract.id)
				this.showModal = true;
			},
			getSignUrl(employeeId, memberId, contractId) {
				this.employeeSign = {}
				this.memberSign = {}
				this.http({
					url: 'getContractSigin',
					method: 'POST',
					hideLoading: true,
					data: {
						contractId: contractId,
						employeeId: employeeId,
						memberId: memberId
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							if (employeeId) {
								this.employeeSign = res.data || this.employeeSign
							} else {
								this.memberSign = res.data || this.memberSign
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}

				})
			},
			getDayTime(time) {
				const date = moment(time, "YYYY-MM-DD HH:mm:ss");
				const compare = moment('2025-04-01', "YYYY-MM-DD");
				if(date.isAfter(compare)) {
					return true;
				} else {
					return false;
				}
			}	
		}
	}
</script>


<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>