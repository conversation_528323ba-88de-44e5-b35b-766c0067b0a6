<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 下方弹出选择器 -->
		<u-picker :show="showPicker" @cancel="showPicker = false" :columns="storeList" @confirm="confirmState"
			@change="changeHandler" keyName="storeName"></u-picker>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 排序方式 -->
		<view class="popup-picker" v-if="popupShowOrderBy">
			<view class="picker-triangle"></view>
			<view class="picker-tab flac-col">
				<view v-for="(item,index) in orderByList" :key="index" @click="choiceOrderBy(index)">
					{{item.text}}
				</view>
			</view>
		</view>

		<!-- 导航栏 -->
		<u-sticky bgColor="#fff">
			<view class="w9 mg-at">
				<u-tabs :list="tabsList" :current="current" :scrollable="false" lineWidth="100" lineHeight="2"
					lineColor="#4a456b" :activeStyle="{fontSize:'36rpx',color: '#4a456b',fontWeight: 'bold',}"
					:inactiveStyle="{fontSize:'36rpx',color: '#666',}" itemStyle="width:50%;margin: 20rpx auto"
					@click="choiceMenu" />
			</view>
		</u-sticky>

		<!-- 筛选弹窗 -->
		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="请输入线索编号、客户电话等"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">

							<view class="tab">
								<view class="tab-title">
									<text>需求状态</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in flowStatusList" :key="index">
									<view class="checkbox" :class="{activeBox: index==choiceFlowStatusIndex}">
										<text @click="choiceBox(2,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<u-gap height="120"></u-gap>
						</scroll-view>
					</view>
				</view>

				<view class="filter-button w82">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="startFilter()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>线索日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{item.title||'-'}}</text>
					</view>
					<text v-if="formatLongStr(item.message)!='暂无'">{{formatLongStr(item.message)}}</text>
					<text>时间：{{item.createTime}}</text>
					<text>操作人：{{item.operator||'-'}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<u-popup :show="popupShowEmployee" mode="bottom" @close="popupShowEmployee = false">
			<view class="filter-title">
				<text>推荐阿姨</text>
			</view>
			<view class="w9" style="margin: 20rpx auto;">
				<u-search bgColor="#fff" :clearabled="true" :showAction="true" :animation="true" v-model="searchData"
					placeholder="通过工号/姓名/手机号进行搜索" @search="listEmployee" @custom="listEmployee"
					@clear="searchData='';" />
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1000rpx;">
				<view class="log-list flac-row" style="place-items: start;border-bottom: 1rpx solid #dedede;"
					v-for="(item,index) of employeeList" :key="index" @click="openBaomu(item.id)">
					<view class="w3 flac-col" style="margin-top: 10rpx;">
						<img :src="item.headPortrait||blankImg"
							style="width: 170rpx;height: 240rpx;border-radius: 15rpx;margin: 20rpx 0 0 12%;display: block;margin: 0 atuo;">
						<view style="padding: 20rpx 10rpx;margin-left:15rpx;width: 120rpx" v-if="current1==2">
							<uni-tag :text="item.recommendStateName" :type="item.recommendState==1?'':'primary'"
								style="margin: 10rpx 5rpx;display: inline-block;"></uni-tag>
						</view>
						<view style="padding: 20rpx 10rpx;margin-left:15rpx;width: 120rpx" v-else>
							<u-tag text="推荐简历" @click="addInterviewRecommend(item)" color="#FFFFFF" bgColor="#1e1848"
								plain shape="circle" size="mini" borderColor="#1e1848"></u-tag>
						</view>
					</view>
					<view class="w7 flac-col">
						<view class="flac-row">
							<text class="fb 18">{{item.realName}}</text>
							<view style="display: inline-block;margin-left: 15rpx;">
								<view class="small-tag" v-if="hasLevel(item.levelId)">
									<img :src="authIcon" alt="">
									<text>{{formatLevel(item.levelId)}}</text>
								</view>
							</view>
						</view>
						<view class="flac-row">
							<text>{{item.age?item.age+'岁':''}}</text>
							<text>|&nbsp;{{formatStr(0,2,item.hometown||'本地')}}人</text>
							<text>|&nbsp;{{item.workYear!=null&&item.workYear!=0?item.workYear+"年经验":"暂无经验"}}</text>
							<text v-if="item.education!=null&&item.education!=1">
								|&nbsp;{{formatEducation(item.education)}}</text>
						</view>

						<view class="flac-col">
							<view class="tab-text">
								工号：<text class="c0">{{item.no}}</text>
							</view>
							<view class="tab-text">
								类型：<text class="c0">{{item.workType||'-'}}</text>
							</view>
							<view class="tab-text" v-if="item.expectedAddress">
								接单范围：<text class="c0">{{ item.expectedAddress }}</text>
							</view>
							<view class="tab-text">
								最低薪资：<text class="c0">{{item.salaryExpectation?item.salaryExpectation+'元':'-'}}</text>
							</view>
							<view class="tab-text">
								入驻：<text class="c0">{{item.createDate}}</text>
							</view>
							<view class="tab-text">
								状态：<text class="c0"
									:style="item.billingFlag==1?'color:#ff4d4b':'color:#000'">{{item.billingFlag==1?"已开单":"未开单"}}</text>
							</view>
							<view class="tab-text">
								门店：<text
									class="c0">{{formatStore(item.storeName)}}{{item.storeAddress?'-'+item.storeAddress:''}}</text>
							</view>
						</view>
					</view>
				</view>

				<u-empty v-if="!employeeList.length" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<uni-transition mode-class="slide-right" :show="current == 0">
			<view v-if="current==0">
				<!-- 合单数据 -->
				<view class="w95 flac-row-c mg-at f15" style="height: 160rpx;padding: 20rpx 0;">
					<view v-for="(item,index) in oneBillData" :key="index" class="tips-style"
						:style="index!=0?'border-left: 1px solid #dedede; ':''">
						<view class="f22 fb">
							{{item.num}}元
						</view>
						<view class="flac-row">
							<text style="margin-bottom: 10rpx;">
								{{item.name}}
							</text>
							<uni-tooltip :content="item.tips" placement="bottom">
								<uni-icons type="help" size="24" color="#1e1848"></uni-icons>
							</uni-tooltip>
						</view>
					</view>
				</view>
				<!-- 头部菜单栏 -->
				<view class="head-menu">
					<view class="flac-row-b" style="padding-bottom: 20rpx;border-bottom: 1rpx solid #dedede;">
						<view class="choice-menu1 w7">
							<view class="choice-item" style="width: 140rpx;" v-for="(choiceList, index) in choiceList"
								:key="index" @click="choiceIndex = index;popupShowFilter=!popupShowFilter;"
								v-if="choiceList.show">
								<text :class="{activeChoice: choiceIndex == index}"
									class="choice-title">{{choiceList.choiceTitle}}</text>
							</view>
						</view>

						<view class="flac-row f16 w3">
							<view class="flac-row-b" @click="popupShowOrderBy=!popupShowOrderBy">
								<text>{{orderByList[choiceOrderByIndex].text}}</text>
								<uni-icons :type="popupShowOrderBy?'top':'bottom'" size="20"></uni-icons>
							</view>
							<!-- 					<view class="flac-row" @click="popupShow=true" style="margin-left: 20rpx;">
						<u-icon
							name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
							size="15"></u-icon>
						<text class="f16 t-indent">筛选</text>
					</view> -->
						</view>
					</view>

					<uni-transition mode-class="fade" :show="popupShowFilter">
						<view class="filter-popup" v-show="popupShowFilter">
							<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
								<view class="tab" id="fiter0" v-if="choiceIndex==0">
									<uni-search-bar class="w85 mg-at" placeholder="城市搜索" bgColor="#f6f8fa" :radius="100"
										v-model="searchStreet" cancelButton="none" @search="search" @confirm="search"
										@input="change">
										<uni-icons slot="searchIcon" color="#2261ff" size="18" type="search" />
									</uni-search-bar>
									<view class="flac-row-b"
										style="flex-wrap: wrap;align-items: unset;height: 1000rpx;">
										<view class="w3 f15 lh60 text-c navStyle">
											<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
												<view class="w10 mg-at flac-row" v-for="(item,i) in cityList" :key="i"
													@click="selectCity(i)">
													<view class="w10 mg-at lineStyle" v-if="i == choiceCityIndex">
													</view>
													<view
														:class="i == choiceCityIndex ? 'w10 mg-at btnStyle' : 'w10 mg-at c6'">
														{{item.name}}
													</view>
												</view>
											</scroll-view>
										</view>
										<view class="w7">
											<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
												<u-radio-group iconPlacement="right" placement="column" borderBottom>
													<u-radio :customStyle="{margin: '30rpx 20rpx'}" label="全部"
														@change="selectArea(-1)"></u-radio>
													<u-radio :customStyle="{margin: '30rpx 20rpx'}"
														v-for="(item, i) in areaList" :key="i" :label="item.name"
														:name="item.id" @change="selectArea(i)" />
												</u-radio-group>
											</scroll-view>
										</view>
									</view>
								</view>

								<view class="tab" id="fiter1" v-if="choiceIndex==1">
									<view class="tab-checkbox" v-for="(item,index) in workTypeList" :key="index">
										<view class="checkbox" :class="{activeBox: item.value==choiceWorTypeIndex}">
											<text v-model="item.value"
												@click="choiceBox(1,index)">{{item.showText}}</text>
										</view>
									</view>
								</view>

								<view class="tab" id="fiter2" v-if="choiceIndex==2">
									<view class="tab-checkbox" v-for="(item,index) in salaryList" :key="index">
										<view class="checkbox" :class="{activeBox: item.value==choiceSalaryIndex}">
											<text v-model="item.value" @click="choiceBox(3,index)">{{item.text}}</text>
										</view>
									</view>
									<view class="tab-range">
										<input class="range-input" type="number" v-model="searchCondition.salaryLow"
											placeholder="最低" />
										<text class="range-input-interval"> — </text>
										<input class="range-input1" type="number" v-model="searchCondition.salaryHigh"
											placeholder="最高" />
										<text class="range-input-unit">元</text>
									</view>
								</view>
							</scroll-view>
							<view class="btn-group">
								<button @click="cleanFilter()"
									style="border: #dedede 1px solid;background-color: #f9f9f9;color: #000;">重置</button>
								<button @click="startFilter()">确定</button>
							</view>
						</view>
					</uni-transition>
				</view>

				<!-- 需求信息栏目 -->
				<view class="need-tab" v-for="(order, index) in list" :key="index">
					<view class="tab-head-bar">
						<text class="bar-title">{{order.productName}}</text>
						<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#1e1848"
							@click="openLog(index)" v-if="isAdmin&&current==0">
						</uni-icons>
						<text class="bar-price">{{formatSalary(order.salary,order.afterSalesFee)}}</text>
					</view>
					<view class="tab-content">
						<view class="tab-item" @click="copyText(order.id)">
							<view class="item-title">
								<text>编号：</text>
							</view>
							<view class="item-text">
								<text>{{order.id}}</text>
							</view>
						</view>
						<!-- 
						<view class="tab-item" @click="openWorksDetail(index)" v-if="order.storeName">
							<view class="item-title">
								<text>归属门店：</text>
							</view>
							<view class="item-text">
								<text>{{order.storeName}}</text>
							</view>
						</view> -->

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作地点：</text>
							</view>
							<view class="item-text">
								<text>{{formatAddress(order,0)}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)" v-if="order.workType">
							<view class="item-title">
								<text>工作类型：</text>
							</view>
							<view class="item-text">
								<text>{{order.workType}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>客户情况：</text>
							</view>
							<view class="item-text">
								<text>{{formatRemarkStr(order.remark)}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作内容：</text>
							</view>
							<view class="item-text">
								<text>{{order.workContent||'-'}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作要求：</text>
							</view>
							<view class="item-text">
								<text>{{order.workRequire||'-'}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>需求状态：</text>
							</view>
							<view class="item-text">
								<text>{{formatFlowStatus(order.flowStatus)}}</text>
							</view>
						</view>
						<view class="f18 lh40" style="display: flex;justify-content: space-between;color: #ef975c;"
							@click="openClewPage(index)">
							<view class="w5">
								收到简历：{{order.resumeCount|| 0}}份
							</view>
							<view class="w5 text-r" style="margin-right: 20rpx;">
								佣金：{{order.commission?parseInt(order.commission)+'元':'-'}}
							</view>
						</view>
						<!-- 						<view class="tab-item c9" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作地点：</text>
							</view>
							<view class="item-text">
								<text>{{formatAddress(order,0)}}</text>
							</view>
						</view> -->

						<view class="tab-item" @click="openWorksDetail(index)" v-if="order.workTime">
							<view class="item-title">
								<text>{{(order.productId == 66|| order.productId==67)?'预产日期：':'上户日期：'}}</text>
							</view>
							<view class="item-text">
								<text>{{formatDate(order.workTime)}}</text>
							</view>
						</view>
					</view>

					<view class="tab-bottom f16 lh30 c9 flac-row" style="margin-top: 0rpx;">
						<view class="bottom-img" @click="openStoreManager(index)">
							<img :src="order.agentHeadImg||blankHeadImg">
						</view>
						<view class="bottom-title" @click="openStoreManager(index)">
							{{formatAgentName(order)}}
						</view>
						<text class="c9">{{formatDate(order.startTime)}}发布</text>
					</view>

					<view class="w85" style="margin: 0rpx auto 20rpx auto;display: flex;">
						<button class="btn-left" @click="callPhone(order)" :disabled="!allowPhone">拨打电话</button>
						<button class="btn-right"
							@click="popupShowEmployee=true;orderNeedsId=order.id;listEmployee()">我要推荐</button>
					</view>
				</view>
			</view>

		</uni-transition>

		<uni-transition mode-class="slide-left" :show="current == 1">
			<view class="flac-row" style="padding: 25rpx 0;">
				<view v-for="(item,index) in tabsList1" :key="index" class="flac-col-c menu"
					:style="current1==index?'background-color:rgba(30,24,72,0.1)':''" @click="choiceTab(0,index)">
					<view> {{item.num}}</view>
					<view> {{item.name}}</view>
				</view>
			</view>
			<view class="flac-row" style="padding: 25rpx 0;border-bottom: 1rpx solid #dedede;" v-if="current1==0">
				<view v-for="(item,index) in tabsList2" :key="index" class="flac-col-c menu1"
					:style="current2==index?'background-color:rgba(30,24,72,0.1)':''" @click="choiceTab(1,index)">
					<view> {{item.num}}</view>
					<view> {{item.name}}</view>
				</view>
			</view>

			<!-- 需求信息栏目 -->
			<view class="" v-if="current1==1||current1==2">
				<view class="need-tab" v-for="(order, index) in list" :key="index">
					<view class="tab-head-bar">
						<text class="bar-title">{{order.productName}}</text>
						<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#1e1848"
							@click="openLog(index)" v-if="isAdmin||current==1">
						</uni-icons>
						<text class="bar-price">{{formatSalary(order.salary,order.afterSalesFee)}}</text>
					</view>
					<view class="tab-content">
						<view class="tab-item" @click="copyText(order.id)">
							<view class="item-title">
								<text>编号：</text>
							</view>
							<view class="item-text">
								<text>{{order.id}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作地点：</text>
							</view>
							<view class="item-text">
								<text>{{formatAddress(order,0)}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)" v-if="order.workType">
							<view class="item-title">
								<text>类型：</text>
							</view>
							<view class="item-text">
								<text>{{order.workType}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>客户情况：</text>
							</view>
							<view class="item-text">
								<text>{{formatRemarkStr(order.remark)}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作内容：</text>
							</view>
							<view class="item-text">
								<text>{{order.workContent||'-'}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>工作要求：</text>
							</view>
							<view class="item-text">
								<text>{{order.workRequire||'-'}}</text>
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)">
							<view class="item-title">
								<text>需求状态：</text>
							</view>
							<view class="item-text">
								<text>{{formatFlowStatus(order.flowStatus)}}</text>
							</view>
						</view>
						<view class="f18 lh40" style="display: flex;justify-content: space-between;color: #ef975c;"
							@click="openClewPage(index)">
							<view class="w5">
								收到简历：{{order.resumeCount|| 0}}份
							</view>
							<view class="w5 text-r" style="margin-right: 20rpx;">
								佣金：{{order.commission?parseInt(order.commission)+'元':'-'}}
							</view>
						</view>
						<view class="f18 lh20" style="margin-bottom: 20rpx;color: #ef975c;"
							@click="openMyRecommend(index)" v-if="current1==2">
							<view class="w10">
								我推荐了：{{order.recommendNames|| '-'}}
							</view>
						</view>

						<view class="tab-item" @click="openWorksDetail(index)" v-if="order.workTime">
							<view class="item-title">
								<text>{{(order.productId == 66|| order.productId==67)?'预产日期：':'上户日期：'}}</text>
							</view>
							<view class="item-text">
								<text>{{formatDate(order.workTime)}}</text>
							</view>
						</view>
					</view>

					<view class="tab-bottom f16 lh30 c9 flac-row" style="margin-top: 0rpx;">
						<view class="bottom-img" @click="openStoreManager(index)">
							<img :src="order.agentHeadImg||blankHeadImg">
						</view>
						<view class="bottom-title" @click="openStoreManager(index)">
							{{formatAgentName(order)}}
						</view>
						<text class="c9">{{formatDate(order.startTime)}}发布</text>
					</view>

					<view class="w85" style="margin: 0rpx auto 20rpx auto;display: flex;">
						<button class="btn-left" @click="callPhone(order)" :disabled="!allowPhone"
							v-if="current1!=1">拨打电话</button>
						<button class="btn-left" @click="openPublish(order.id)" v-if="current1==1">编辑合单</button>
						<button class="btn-right" @click="cancelOneBill(index)" v-if="current1==1">取消合单</button>
						<button class="btn-right" @click="popupShowEmployee=true;orderNeedsId=order.id;listEmployee()"
							v-if="current1==2">继续推荐</button>
					</view>
				</view>
			</view>

			<view class="" v-if="current1==0">
				<view class="need-tab" v-for="(item, index) in list" :key="index"
					@click="openManage(item.orderNeedsId)">
					<view class="tab-head-bar">
						<text class="bar-title">{{item.title}}</text>
					</view>
					<view class="tab-content">
						<view class="tab-item">
							<view class="item-title">
								<text>线索编号：</text>
							</view>
							<view class="item-text">
								<text>{{item.orderNeedsId||'-'}}</text>
							</view>
						</view>
						<view class="tab-item">
							<view class="item-title">
								<text>操作内容：</text>
							</view>
							<view class="item-text">
								<text>{{item.message||'-'}}</text>
							</view>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text>操作人：</text>
							</view>
							<view class="item-text">
								<text>{{item.operator||'-'}}</text>
							</view>
						</view>

						<view class="tab-item">
							<view class="item-title">
								<text>时间：</text>
							</view>
							<view class="item-text">
								<text>{{item.createTime||'-'}}</text>
							</view>
						</view>
					</view>
				</view>

			</view>
		</uni-transition>

		<!-- 添加按钮 -->
		<view class="bacf" style="position: fixed;bottom: 19%;right:37rpx;z-index:999;border-radius: 50%;
			padding: 20rpx;border: #f4f4f5 2rpx solid;box-shadow: 0 4rpx 20rpx #dedede;" v-if="current==1"
			@click="openPublish(null)">
			<uni-icons type="plusempty" size="20" color="#1e1848"></uni-icons>
		</view>

		<view class="lh40 text-c f16" v-if="list.length">
			<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
			<view v-else @click="searchCondition.current++;getList()">下滑查看更多...</view>
		</view>
		<u-empty v-if="!list.length" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<u-gap height="80"></u-gap>
	</view>
</template>

<script>
	import freeAudio from '@/pages-mine/common/components/free-audio.vue'
	export default {
		components: {
			freeAudio
		},
		name: 'findWorks',
		data() {
			return {
				weChatCodeImgUrl: uni.getStorageSync('weChatCodeImgUrl'),
				// 可配置选项
				// 控制测试数据是否显示
				showTestData: false,
				weChatCodeFlag: false,
				src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17048801043158533717a20af16e170cd00823ca5a13.png',
				// 查询时间范围（单位：天）
				dateRange: 7,
				// 查询位置范围（单位：公里）
				districtRange: 3,
				/// 是否管理员
				isAdmin: false,
				// 允许打电话
				allowPhone: false,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				showPicker: false,
				show: false,
				popupShow: false,
				popupShowLog: false,
				popupShowEmployee: false,
				popupShowFilter: false,
				popupShowOrderBy: false,

				searchText: "",
				choiceWorTypeIndex: 0,
				choiceFlowStatusIndex: 0,
				choiceSalaryIndex: 0,
				choiceOrderByIndex: 0,
				choiceCityIndex: 0,

				choiceWorType: "",
				salaryLow: null,
				salaryHigh: null,
				memberId: uni.getStorageSync('memberId') || 0,
				employeeId: uni.getStorageSync("employeeId") || 0,
				employee: {
					lat: null,
					lng: null
				},

				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				current: 0,
				current1: 1,
				current2: 0,
				choiceIndex: 0,
				total: 0,
				totalNow: 0,
				storeIndex: 0,
				storeList: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				authIcon: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/auth_icon.png",
				shareImg: '',

				cityList: uni.getStorageSync('citylist'),
				getClist: uni.getStorageSync('citylist'),
				areaList: [],
				cityId: 0,
				areaId: 0,
				cityName: '',
				searchStreet: '',
				tabsList: [{
					name: '合单大厅'
				}, {
					name: '我的'
				}],
				tabsList1: [{
					name: '访客痕迹',
					num: 0
				}, {
					name: '我的发单',
					num: 0
				}, {
					name: '我推荐的',
					num: 0
				}],
				tabsList2: [{
					name: '看过我单',
					num: 0
				}, {
					name: '打我电话',
					num: 0
				}, {
					name: '推我简历',
					num: 0
				}, {
					name: '分享我单',
					num: 0
				}],
				oneBillData: [{
						name: '今日合单薪资',
						tips: '今日合单薪资总量',
						num: 0,
					},
					{
						name: '客户服务费',
						tips: '今日合单得客户服务费总量',
						num: 0,
					},
					{
						name: '阿姨上户费',
						tips: '合单阿姨上户费总量',
						num: 0,
					}
				],
				menuList: [{
						index: 0,
						name: '推荐工作',
					},
					{
						index: 1,
						name: '附近工作',
					}
				],
				choiceList: [{
						choiceTitle: "区域",
						value: 0,
						show: true
					}, {
						choiceTitle: "工种",
						value: 1,
						show: true
					},
					{
						choiceTitle: "工资",
						value: 2,
						show: true
					},
				],
				productList: [{
						name: '保姆',
						productIdList: [76, 77, 78, 79, 172, 226, 290],
						index: 0,
						name: '最新工作',
						orderBy: 'ons.startTime DESC'
					},
					{
						name: '月嫂',
						productIdList: [66, 67, 68],
						index: 1,
						name: '人气工作',
						orderBy: 'ons.shareCount DESC'
					},
					{
						name: '育儿嫂',
						productIdList: [999],
					},
					{
						name: '保洁',
						productIdList: [66, 67, 78, 79, 282, 322, 332, 336, 356],
					},
					{
						name: '护工',
						productIdList: [223, 230],
					},
					{
						name: '陪读师',
						productIdList: [336],
					}
				],
				orderByList: [{
					index: 0,
					text: '时间',
					value: 'ons.startTime DESC'
				}, {
					index: 1,
					text: '薪资',
					value: 'ons.salary DESC'
				}, {
					index: 2,
					text: '人气',
					value: 'ons.shareCount DESC'
				}],
				workTypeList: [{
						text: '',
						showText: "不限",
						value: 0
					}, {
						text: '住家',
						showText: "住家",
						value: 1
					}, {
						text: '不住家',
						showText: "不住家",
						value: 2
					},
					{
						text: '单餐',
						showText: "单餐",
						value: 3
					}, {
						text: '中餐',
						showText: "单餐(中)",
						value: 4
					}, {
						text: '晚餐',
						showText: "单餐(晚)",
						value: 5
					}, {
						text: '月嫂',
						showText: "月嫂",
						value: 6
					}, {
						text: '育儿嫂',
						showText: "育儿嫂",
						value: 7
					}, {
						text: '钟点',
						showText: "钟点",
						value: 8
					}
				],
				remarkList: [{
						text: "带宝宝（带睡)",
						showText: "宝宝带睡",
						isCheck: 0
					},
					{
						text: "带宝宝（不带睡)",
						showText: "宝宝不带睡",
						isCheck: 0
					},
					{
						text: "照顾产妇",
						showText: "照顾产妇",
						isCheck: 0
					},
					{
						text: "看护病人",
						showText: "看护病人",
						isCheck: 0
					},
					{
						text: "看护老人",
						showText: "看护老人",
						isCheck: 0
					},
					{
						text: "做饭",
						showText: "做饭",
						isCheck: 0
					},
					{
						text: "纯做饭",
						showText: "纯做饭",
						isCheck: 0
					},
					{
						text: "做卫生",
						showText: "做卫生",
						isCheck: 0
					},
					{
						text: "纯做卫生",
						showText: "纯做卫生",
						isCheck: 0
					},
				],
				flowStatusList: [{
					text: '不限',
					value: null
				}, {
					text: '电联',
					value: 0
				}, {
					text: '需求',
					value: 1
				}, {
					text: '预算',
					value: 2
				}, {
					text: '匹配',
					value: 3
				}, {
					text: '面试',
					value: 4
				}, {
					text: '签约',
					value: 5
				}, {
					text: '结算',
					value: 6
				}],
				// 薪资范围
				salaryList: [{
					value: 0,
					text: '不限',
					salaryLow: null,
					salaryHigh: null,
				}, {
					value: 1,
					text: '1000-3000',
					salaryLow: 1000,
					salaryHigh: 3000,
				}, {
					value: 2,
					text: '3000-6000',
					salaryLow: 3001,
					salaryHigh: 6000,
				}, {
					value: 3,
					text: '6000-8000',
					salaryLow: 6100,
					salaryHigh: 8000,
				}, {
					value: 4,
					text: '8000-10000',
					salaryLow: 8001,
					salaryHigh: 10000,
				}, {
					value: 5,
					text: '10000-15000',
					salaryLow: 10001,
					salaryHigh: 15000,
				}, {
					value: 6,
					text: '15000以上',
					salaryLow: 15001,
					salaryHigh: null,
				}],
				searchCondition: {
					storeName: "",
					// search: "",
					search: "",
					status: null,
					productName: "",
					agentId: null,
					channel: null,
					productId: null,
					lat: null,
					lng: null,
					district: null,
					dateRange: null,
					remarkId: null,
					salaryLow: null,
					salaryHigh: null,
					isPush: null,
					street: '',
					flowStatus: null,
					isAgencyFee: null,
					isSalary: null,
					isStoreName: null,
					idList: null,
					isMine: null,
					orderBy: 'ons.startTime DESC',
					current: 1,
					size: 20,
					isOneBill: 1,
				},
				isMineOrderNeed: false,
				searchParam: '',
				orderList: [],
				pageCount: 0,
				list: [],
				logTypeList: [5, 6, 7, 4],
				orderNeedsChoiceList: [],
				orderNeedsChoiceIdList: [],
				shareContent: {
					title: '',
					path: '',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: ''
				},
				firstOrderNeeds: {},
				checkType: 0,
				checkTitle: "",
				checkText: "",
				logList: [],
				employeeList: [],
				searchData: '',
				orderNeedsId: null,
				educationList: [{
						value: 0,
						text: '无',
					},
					{
						value: 1,
						text: '默认状态',
					},
					{
						value: 3,
						text: '小学',
					},
					{
						value: 4,
						text: '中学',
					},
					{
						value: 5,
						text: '高中',
					},
					{
						value: 8,
						text: '中专',
					},
					{
						value: 6,
						text: '大专',
					},
					{
						value: 7,
						text: '本科及以上',
					},
					{
						value: 9,
						text: '研究生',
					}
				],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			change(e) {
				if (e == '') {
					this.selectCity(0)
				}
			},
			search(e) {
				let value = e.value
				// value自动接收输入框中的内容
				if (value == '') {
					//如果输入的值为空则加载所有的列表
					uni.showToast({
						icon: 'none',
						title: '无匹配内容'
					})
				} else {
					//先清空展示的数据
					this.cityList = []
					this.areaList = []
					let haveData = false
					//然后开始循环全部数据
					for (var i = 0; i < this.getClist.length; i++) {
						//判断数据里面是否有符合输入的内容  不符合返回-1 只需要大于或等于0就是符合
						//（核心所在，其它都是根据需求来自己写）
						if (this.getClist[i].name.indexOf(value) >= 0) {
							this.cityList.push(this.getClist[i])
							haveData = true
						}
					}
					if (!haveData) {
						this.cityList = uni.getStorageSync('citylist')
						this.selectCity(0)
						uni.showToast({
							icon: 'none',
							title: '无匹配内容'
						})
					}
					this.selectCity(0)
				}
			},
			// 打开选择器
			openPicker(index) {
				if (index == 0) {
					this.showPicker = true
					this.popupShow = false
				}
			},
			// 选择器确认
			confirmState(e) {
				this.showPicker = false
				this.popupShow = true
				this.searchCondition.storeName = this.storeList[0][this.storeIndex].storeName
			},
			// 选择器选项变化
			changeHandler(e) {
				const {
					index
				} = e;
				this.storeIndex = index
			},
			// 选择菜单（一级）
			choiceMenu(e) {
				this.list = []
				this.searchCondition.current = 1
				this.current = e.index
				if (this.current == 0) {

				} else {

				}
				this.refreshList()
			},
			// 选择菜单（二级）
			choiceTab(value, index) {
				this.list = []
				this.searchCondition.current = 1
				if (value == 0) {
					this.current1 = index
				} else if (value == 1) {
					this.current2 = index
				}
				this.getList()
			},
			// 选中筛选单选框
			choiceBox(value, index) {
				if (value == -1) {

				} else if (value == 1) {
					this.choiceWorTypeIndex = this.workTypeList[index].value
					this.choiceWorType = this.workTypeList[index].text
				} else if (value == 2) {
					this.choiceFlowStatusIndex = index
				} else if (value == 3) {
					this.choiceSalaryIndex = index
				}
			},
			// 选中我的需求
			choiceItem(index) {
				let orderNeed = this.list[index]
				let id = orderNeed.id
				if (orderNeed.isCheck) {
					this.list[index].isCheck = false
					for (let i = 0; i < this.list.length; i++) {
						if (this.orderNeedsChoiceIdList[i] == id) {
							this.$delete(this.orderNeedsChoiceList, i)
							this.$delete(this.orderNeedsChoiceIdList, i)
						}
					}
				} else {
					if (this.orderNeedsChoiceIdList.length == 0) {
						this.firstOrderNeeds = this.list[index]
					}
					this.list[index].isCheck = true
					this.orderNeedsChoiceList.push(this.list[index])
					this.orderNeedsChoiceIdList.push(this.list[index].id)
				}
				console.log("输出现在的选择：", this.orderNeedsChoiceIdList)
			},
			// 选中排序方式
			choiceOrderBy(index) {
				this.choiceOrderByIndex = index
				this.popupShowOrderBy = false
				this.refreshList()
			},
			// 刷新列表
			refreshList() {
				let index = this.current
				this.clearTabChoice()
				if (index == 0) {
					this.searchCondition.isMine = 0
				} else if (index == 1) {
					this.searchCondition.isMine = 1
				}

				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {
				this.searchCondition.district = null
				this.searchCondition.lat = null
				this.searchCondition.lng = null
				this.searchCondition.dateRange = null
				this.searchCondition.isPush = null
				this.isMineOrderNeed = false
			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
				this.searchCondition.search = ""
				this.searchCondition.productName = ""
			},
			// 清空筛选条件（搜索框、选择器和单选框）
			cleanFilter() {
				this.searchText = ""
				this.searchStreet = ""
				this.choiceWorTypeIndex = 0
				this.choiceFlowStatusIndex = 0
				this.choiceSalaryIndex = 0
				this.choiceWorType = ""
				this.searchCondition.storeName = ""
				this.searchCondition.street = ""
				this.searchCondition.salaryLow = null
				this.searchCondition.salaryHigh = null

				this.cityList = uni.getStorageSync("citylist")
				this.cityName = ''
			},
			// 开始筛选
			startFilter() {
				this.list = []
				this.totalNow = 0
				this.searchCondition.current = 1
				this.searchCondition.search = this.searchText
				this.searchCondition.productName = this.choiceWorType
				if (this.searchParam) {
					this.searchCondition.productName = this.searchParam
				}
				this.searchCondition.flowStatus = this.flowStatusList[this.choiceFlowStatusIndex].value
				this.searchCondition.salaryLow = this.salaryList[this.choiceSalaryIndex].salaryLow
				this.searchCondition.salaryHigh = this.salaryList[this.choiceSalaryIndex].salaryHigh
				this.searchCondition.orderBy = this.orderByList[this.choiceOrderByIndex].value

				if (this.cityName) {
					this.searchCondition.street = this.cityName
				}

				if (this.current == 0) {
					this.searchCondition.isMine = null
					this.searchCondition.agentId = null
					this.searchCondition.channel = null
				} else if (this.current == 1) {
					this.searchCondition.isMine = 1
					this.searchCondition.agentId = this.employeeId
					this.searchCondition.channel = uni.getStorageSync("merchantCode") || ""
				}

				this.getList()
				this.popupShow = false
				this.popupShowFilter = false
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			formatLongStr(str) {
				if (str == null || str == "") {
					return "-"
				} else {
					let long = 60
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			// 格式化备注字段（隐藏隐私信息）
			formatRemarkStr(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "-"
				} else {
					result = result.replace(/^([^s]*)元,/, " ")
				}
				return result
			},
			formatAddress(item, value) {
				let str = item.street
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "-"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
						if (value == 0) {
							result += item.housingEstate || ""
						}
					}
				}

				return result
			},
			// 格式化期望薪资
			formatSalary(salary, salary1) {
				if (salary1 != null && salary1 != 0) {
					salary = salary1
				}

				// 如果工资字段值存在，则直接返回值
				if (salary != null && salary != 0) {
					return salary + "元/月"
				} else {
					return "工资面议"
				}
			},
			// 对请求参数中的工资范围进行格式化
			formatRange() {
				let salaryLow = this.searchCondition.salaryLow
				let salaryHigh = this.searchCondition.salaryHigh
				if (salaryLow == null || salaryLow == "") {
					this.searchCondition.salaryLow = null
				} else {
					this.searchCondition.salaryLow = parseInt(salaryLow)
				}
				if (salaryHigh == null || salaryHigh == "") {
					this.searchCondition.salaryHigh = null
				} else {
					this.searchCondition.salaryHigh = parseInt(salaryHigh)
				}
			},
			// 格式化线索状态
			formatFlowStatus(value) {
				if (value >= this.flowStatusList.length) {
					return '-'
				}
				let result = this.flowStatusList[value + 1].text || "-"
				return result
			},
			// 时间格式化
			formatDate(value) {
				if (!value) {
					return "/"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return MM + '-' + d
			},
			formatStatus(index) {
				let item = this.employeeList[index]
				let str = item.contractId != null ? "已开单" : "未开单"
				let str1 = ""
				let statusList = ['-正在找工作', '-已有工作', '-暂不找工作']
				let statusIndex = item.status != null ? parseInt(item.status) : 0
				str1 = statusList[statusIndex]
				return str + str1
			},
			formatStore(sname) {
				sname = sname || '小羽佳家政'
				if (sname.includes('(')) {
					sname = sname.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				}
				return sname
			},
			formatAgentName(item) {
				let sname = item.sname || ''
				sname = sname.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				return sname + ' ' + item.realName
			},
			// 判断是否已经提审
			hasLevel(level) {
				if (level != null) {
					if (level != 0) {
						return true
					}
				} else {
					return false
				}
			},
			// 格式化保姆等级
			formatLevel(level) {
				let result = ""
				if (level != null) {
					if (level > 1 && level < 6) {
						result = this.levelList[level - 1].text
					} else {
						result = "暂无"
					}
				}
				return result
			},
			// 格式化学历信息
			formatEducation(education) {
				for (let item of this.educationList) {
					if (item.value == education) {
						return item.text
					}
				}
				return ""
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('复制成功!')
					}
				})
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.shareImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			// 打开员工详情
			openBaomu(id) {
				uni.navigateTo({
					url: '/pages-mine/works/employee-detail?baomuId=' + id
				})
			},
			// 打开合单发布
			openPublish(id) {
				let url = '/pages-work/operations/clew/oneBill-publish'
				if (id) {
					url += '?id=' + id
				}
				uni.navigateTo({
					url: url
				})
			},
			// 打开线索详情
			openWorksDetail(index) {
				if (this.isAllowChoice) {
					this.choiceItem(index)
					return
				}
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + this.list[index].id
				})
			},
			// 打开线索管理
			openManage(id) {
				if (!id) {
					return
				}
				uni.navigateTo({
					url: "/pages-work/operations/clew/clewPage?id=" + id
				})
			},
			openStoreManager(index) {
				uni.navigateTo({
					url: "/pages-other/store/index?id=" + this.list[index].storeId
				})
			},
			// 打开线索编辑
			openClewPage(index) {
				if ((this.current == 1 && this.current1 == 1) || this.isAdmin) {
					uni.navigateTo({
						url: "/pages-work/operations/clew/clewPage?id=" + this.list[index].id
					})
				}
			},
			openMyRecommend(index) {
				let id = this.list[index].id
				this.orderNeedsId = id
				this.popupShowEmployee = true
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/work/listInterviewRecommendEmployee',
					method: 'POST',
					hideLoading: true,
					data: {
						orderNeedsID: id,
						create: uni.getStorageSync('employeeId') || 0
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.employeeList = res.data
						} else {
							this.employeeList = []
						}
					}
				})
			},
			selectCity(i) {
				this.choiceCityIndex = i
				this.cityName = this.cityList[i].name
				this.cityName = this.cityName == '全国' ? '' : this.cityName
				this.areaList = this.cityList[i].area
				let arr = this.cityList[i].area
				let arrnew = []
				if (arr) {
					arr.map(((item, i) => {
						arrnew.push(Object.assign({}, item, {
							disabled: false
						}))
					}))
				}
				this.areaList = arrnew
			},
			selectArea(i) {
				if (i == -1) {
					this.areaId = 0
				} else {
					this.areaId = this.areaList[i].id
					this.cityName = this.areaList[i].name
				}
			},
			// 获取所有门店列表
			getAllStoreList() {
				this.http({
					url: 'getAllStoreList',
					method: 'POST',
					hideLoading: true,
					data: {
						isOpen: false
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList.push(res.data)
						}
					}
				})
			},
			// 拨打电话
			callPhone(order) {
				if (!this.allowPhone) {
					this.$refs.uNotify.warning('只有家务管家可以联系客户哦！')
					return
				}
				uni.makePhoneCall({
					phoneNumber: order.agentPhone,
					success: res => {
						this.addOrderNeedLog(1, order)
					},
					fail: res => {}
				})
				return
			},
			// 取消合单
			cancelOneBill(index) {
				let id = this.list[index].id
				uni.showModal({
					title: '确定取消该合单吗？',
					content: '后续可重新编辑再发布！',
					success: res => {
						if (res.confirm) {
							this.http({
								url: "updateOrderNeeds",
								method: 'post',
								data: {
									id: id,
									isOneBill: 0,
								},
								hideLoading: true,
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('取消成功！')
										this.addLog(id)
										this.getTabNum(0)
										this.$delete(this.list, index)
									} else {
										this.$refs.uNotify.warning(res.msg)
									}
								}
							});
						}
					}
				});
			},
			// 获取员工信息
			getEmployee() {
				this.http({
					url: 'getEmployeeDtoById',
					method: 'GET',
					hideLoading: true,
					path: this.employeeId,
					success: res => {
						if (res.code == 0) {
							this.employee = res.data
							this.lat = this.employee.lat
							this.lng = this.employee.lng
						}
					}
				})
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.list[index].id
				this.http({
					url: "getOrderNeedsById",
					data: {
						orderNeedsId :id
					},
					method: 'get',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.logList = res.data.orderNeedsLogs
						} else {
							this.logList = []
						}
					}
				})
			},
			// 推荐面试
			addInterviewRecommend(employee) {
				this.popupShowEmployee = false
				if (employee.state != 1) {
					return this.$refs.uNotify.warning('未上架！无法进行推荐！')
				}

				uni.showModal({
					title: '确定推荐【' + employee.realName + '】吗？',
					content: '员工条件必须符合该合单线索，推荐成功并完成后续面试成单后可获得分佣！',
					success: res => {
						if (res.confirm) {
							this.http({
								url: 'addInterviewRecommend',
								data: {
									orderNeedsID: this.orderNeedsId,
									employeeId: employee.id,
									employeeIntroducer: employee.introducer,
									introducer: uni.getStorageSync("employeeNo") || '',
									create: uni.getStorageSync("employeeId") || '',
									storeId: employee.storeId,
								},
								method: 'POST',
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('推荐成功！')
									} else {
										this.$refs.uNotify.warning(res.msg)
									}
								}
							});
						} else if (res.cancel) {
							this.popupShowEmployee = true
						}
					}
				});
			},
			// 获取员工列表
			listEmployee() {
				let url = this.searchData ? 'getRecommendNanny' : 'getInterviewRecommendOption'
				let data = this.searchData ? {
					orderNeedsId: this.orderNeedsId,
					searchData: this.searchData,
					employeeId: this.employeeId
				} : {
					id: this.orderNeedsId,
					agentId: this.employeeId,
					memberId: this.memberId,
				}
				this.http({
					url: url,
					data: data,
					method: this.searchData ? 'GET' : 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.employeeList = res.data
							if (!res.data.length) {
								this.$refs.uNotify.warning('未查询到相关数据，请检查阿姨是否上架！')
							}
						} else {
							this.employeeList = []
							this.$refs.uNotify.warning(res.msg)
						}
					}
				});
			},
			// 获取线索列表
			getList() {
				this.formatRange()
				let data = this.searchCondition
				let url = 'getOrderNeedsPage'
				if (this.current == 1) {
					if (this.current1 == 0) {
						url = 'pageOrderNeedsLog'
						data = {
							agentId: this.employeeId,
							type: this.logTypeList[this.current2],
							current: this.searchCondition.current,
							size: this.searchCondition.size
						}
					} else if (this.current1 == 2) {
						url = 'pageRecommendOrderNeeds'
						data = {
							agentId: this.employeeId,
							current: this.searchCondition.current,
							size: this.searchCondition.size
						}
					}
				}
				this.http({
					url: url,
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.pageCount = res.data.pages
							this.list = this.list.concat(res.data.records)
						} else {
							if (this.searchCondition.current == 1) {
								// this.$refs.uNotify.warning('暂无记录！')
							}
						}
					}
				})
			},
			addLog(id) {
				let employeeName = uni.getStorageSync("employeeName") || ''
				let employeeNo = uni.getStorageSync("employeeNo") || ''
				this.http({
					url: 'addOrderNeedsLog',
					data: {
						orderNeedsId: id,
						memberId: uni.getStorageSync("memberId"),
						operatorId: uni.getStorageSync("employeeId"),
						operator: employeeName,
						title: '线索合单',
						message: employeeName + '（' + employeeNo + '）' + '取消了线索合单',
						type: 8,
						typeDetail: 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {}
				})
			},
			getTabNum(value) {
				let url = 'getOrderNeedsPage'
				let data = {
					agentId: this.employeeId,
					channel: uni.getStorageSync("merchantCode") || "",
					isOneBill: 1,
					isMing: 1,
					current: 1,
					size: 1
				}
				if (value == 1) {
					url = 'pageRecommendOrderNeeds'
					data = {
						agentId: this.employeeId,
						current: 1,
						size: 1
					}
				}
				this.http({
					url: url,
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let total = res.data.total
							this.tabsList1[value + 1].num = total
						}
					}
				})
			},
			getLogNum(value, item) {
				this.http({
					url: 'pageOrderNeedsLog',
					method: 'POST',
					hideLoading: true,
					data: {
						agentId: this.employeeId,
						type: item,
						current: 1,
						size: 1,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let total = res.data.total
							this.tabsList2[value].num = total
							this.tabsList1[0].num += total
						}
					}
				})
			},
			// 添加线索分享日志
			addOrderNeedsShareLog() {
				this.http({
					url: 'addOrderNeedsShareLog',
					method: 'POST',
					hideLoading: true,
					data: {
						operatorId: this.employeeId,
						memberId: uni.getStorageSync("memberId") || null,
						typeDetail: this.flagShare,
						orderNeedsIdList: this.orderNeedsChoiceIdList
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						console.log(res.msg)
					}
				})
			},
			// 添加线索日志
			addOrderNeedLog(value, order) {
				let data = {}
				if (value == 0) {
					data = {
						orderNeedsId: order.id,
						title: "查看线索",
						message: "线索详情被查看",
						type: 5,
						typeDetail: this.flagShare,
						sourceMemberId: this.sourceMemberId
					}
				} else if (value == 1) {
					data = {
						orderNeedsId: order.id,
						title: "联系线索经纪人",
						message: "拨打线索经纪人电话",
						type: 6
					}
				}

				let operatorId = uni.getStorageSync("employeeId") || null
				let memberId = this.memberId || null
				this.$set(data, "memberId", memberId)
				if (operatorId) {
					this.$set(data, "operatorId", operatorId)
				}

				this.http({
					url: 'addOrderNeedLog',
					method: 'POST',
					data: data,
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {

					}
				})
			},
			getCityInfo() {
				uni.request({
					url: 'https://ortherapi.xiaoyujia.com/store/storecity',
					method: 'GET',
					success: (res) => {
						let data = [{
							"cityid": -1,
							"name": "全国",
						}]
						this.cityList = data
						this.cityList = this.cityList.concat(res.data)
						uni.setStorageSync('citylist', this.cityList)
						this.selectCity(0)
					}
				});
			},
			getOneBillData() {
				this.http({
					url: 'getOneBillData',
					method: 'POST',
					data: {},
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.oneBillData = res.data
						}
					}
				})
			},
		},
		watch: {
			current: {
				handler(newValue, oldVal) {
					this.allowPhone = false
					if (uni.getStorageSync('roleId') == 75 || (this.current == 1 && this.current1 == 1)) {
						this.allowPhone = true
					}
				},
				deep: true
			},
			current1: {
				handler(newValue, oldVal) {
					this.allowPhone = false
					if (uni.getStorageSync('roleId') == 75 || (this.current == 1 && this.current1 == 1)) {
						this.allowPhone = true
					}
				},
				deep: true
			}
		},
		onReachBottom() {
			this.searchCondition.current++
			this.getList()
		},
		onLoad(options) {
			this.current = options.current || this.current
			this.current1 = options.current1 || this.current1
			this.searchParam = options.search || ""
			this.getEmployee()

			this.refreshList()
			this.getAllStoreList()
			this.getCityInfo()
			this.getOneBillData()
			this.getTabNum(0)
			this.getTabNum(1)
			for (let i = 0; i < this.logTypeList.length; i++) {
				this.getLogNum(i, this.logTypeList[i])
			}


			let roleId = uni.getStorageSync('roleId') || 0
			let roleIdList = [1, 75]
			if (roleIdList.findIndex(item => item == roleId) != -1) {
				this.isAdmin = true
			}
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/work-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
		width: 100%;
	}

	.top-tab {
		position: fixed;
		z-index: 999;
		top: 10rpx;
		left: 550rpx;
		width: 80%;
		height: 80rpx;

		text {
			display: block;
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}

	.top-tab2 {
		position: fixed;
		z-index: 999;
		top: 10rpx;
		left: 50rpx;
		width: 80%;
		height: 80rpx;

		text {
			display: block;
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}

	// 头部按钮
	.btn-top {
		width: 100%;
		height: 80rpx;

		text {
			display: block;
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			background-color: rgba(30, 24, 72, 0.7);
		}
	}

	// 需求栏目
	.need-tab {
		width: 100%;
		height: auto;
		// box-shadow: 0 4rpx 20rpx #dedede;
		border-bottom: 2rpx solid #dedede;
		margin: 20rpx auto;
		padding: 20rpx 0;
	}

	// 栏目头部
	.tab-head-bar {
		display: block;
		width: 100%;
		height: 80rpx;
	}

	.bar-title {
		display: bolck;
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		line-height: 80rpx;
		margin-left: 40rpx;
		color: #1e1848;
		font-weight: bold;
	}

	.bar-price {
		display: block;
		float: right;
		margin: 20rpx 40rpx 0 0;
		text-align: right;
		width: 400rpx;
		height: 40rpx;
		line-height: 40rpx;
		color: #ff4d4b;
		font-weight: bold;
		font-size: 32rpx;
	}

	// 栏目底部
	.tab-bottom {
		width: 100%;
		height: 120rpx;
		font-size: 36rpx;
		display: flex;
	}

	.bottom-img {
		width: 16%;
		height: 100rpx;

		img {
			display: block;
			margin: 20rpx 10rpx 0rpx 40rpx;
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
		}
	}

	.bottom-title {
		width: 57%;
		height: 60rpx;

		text {
			height: 60rpx;
			line-height: 60rpx;
		}
	}

	.bottom-button {
		width: 18%;
		margin: 30rpx 0;

		button {
			width: 170rpx;
			height: 60rpx;
			line-height: 60rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 20rpx;
			font-size: 32rpx;
			padding: 0 0;
		}
	}

	.log-list {
		width: 90%;
		height: auto;
		padding: 20rpx 5%;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	// 筛选下拉框
	.filter-popup {
		position: absolute;
		z-index: 888;
		width: 100%;
		top: 275rpx;
		background-color: #fff;
		border-radius: 0 0 40rpx 40rpx;
		border-bottom: 2rpx solid #dedede;
		// box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 按钮组
	.btn-group {
		width: 100%;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		padding: 40rpx 0;

		button {
			width: 40%;
			height: 80rpx;
			line-height: 80rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.choice-menu1 {
		display: flex;
		height: 40rpx;
		margin: 20rpx 0;
		padding-left: 50rpx;

		text {
			font-size: 32rpx;
		}
	}

	// 弹出-选择栏
	.popup-picker {
		position: absolute;
		top: 20.5vh;
		right: 95rpx;
		z-index: 999;
		width: 140rpx;
		height: auto;
		padding: 0 0 0 0;

		.picker-triangle {
			width: 0;
			height: 0;
			border: 15rpx solid transparent;
			border-bottom-color: #f4f4f5;
			margin-left: 65%;
		}

		.picker-tab {
			width: auto;
			mine-height: 60rpx;
			line-height: 60rpx;
			color: #1e1848;
			background-color: rgba(255, 255, 255, 0.98);
			border-radius: 15rpx;
			box-shadow: 0 4rpx 20rpx #dedede;
			padding: 0 20rpx;
		}
	}

	.btnStyle {
		color: #6989df;
		background-color: #fff;
	}

	.lineStyle {
		width: 10rpx;
		height: 50rpx;
		margin-left: 5rpx;
		border-radius: 10rpx;
		background-color: #6989df;
	}

	.tab-text {
		display: flex;
		margin-left: 5rpx;
		height: auto;
		width: 100%;
		color: #909399;
		white-space: nowrap;
		overflow: hidden;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}

	.small-tag {
		display: flex;
		width: 120rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #fff;
		background-color: #f6cc70;
		border-radius: 10rpx;
		margin: 10rpx 0;

		img {
			display: block;
			width: 30rpx;
			height: 30rpx;
			margin: 7rpx 0 0 10rpx;
		}

		text {
			display: block;
			width: 60%;
			text-align: center;
		}
	}

	.btn-left,
	.btn-right {
		width: 280rpx;
		height: 80rpx;
		line-height: 76rpx;
		border-radius: 10rpx;
		font-size: 36rpx;
		font-weight: bold;
	}

	.btn-left {
		color: #1e1848;
		background-color: #fff;
		border: 2rpx solid #1e1848;
	}

	.btn-right {
		line-height: 80rpx;
		background-color: #1e1848;
		color: #f6cc70;
	}

	.tips-style {
		width: 33%;
		height: 120rpx;
		display: flex;
		flex-direction: column;
		line-height: 60rpx;
		text-align: center;
		align-items: center;
	}

	// 二级菜单
	.menu,
	.menu1 {
		width: 223.3rpx;
		height: 70rpx;
		line-height: 35rpx;
		padding: 20rpx 0;
		margin-left: 20rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #1e1848;
		border-radius: 10rpx;
		background-color: #f4f4f5;
	}

	.menu1 {
		width: 164.5rpx;
		font-size: 28rpx;
		border-radius: 40rpx;
	}
</style>