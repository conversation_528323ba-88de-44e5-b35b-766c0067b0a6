<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<img :src="postImg" mode="widthFix" style="width: 100%;" />

		<view style="width: 90%;margin: 0 40rpx;">
			<view class="w10 bacf flac-row" style="border-radius: 80rpx;margin: 30rpx auto;">
				<view style="width: 180rpx;">
					<uni-data-select v-model="choiceSearchType" :localdata="searchTypeList"
						@change="changeSearchType" />
				</view>
				<u-search bgColor="#fff" :clearabled="true" :showAction="true" :animation="true" margin="0 20rpx"
					v-model="searchText" placeholder="请输入搜索关键词" @search="listUnionCertRecord"
					@clear="searchText='';listUnionCertRecord()" />
			</view>
		</view>

		<view class="btn-big" @click="listUnionCertRecord">
			<button>搜索</button>
		</view>

		<view class="list-item flac-row" v-for="(item,index) of certList" :key="index">
			<view class="w25">
				<img :src="item.headImg|| blankImg" alt="" @click="openImgPreview(item.headImg|| blankImg)"
					style="width: 120rpx;height: 120rpx;border-radius: 50%;margin: 0rpx auto;display: block;" />
			</view>
			<view class="w75 flac-col f16" @click="openDetail(item.id)">
				<view class="">
					{{item.realName||''}} {{formatStoreName(item.storeName)}}
				</view>
				<view class="">
					{{item.certTitle}}
				</view>
				<view class="" @longpress="copyText(item.certCode)">证书编号：
					{{item.certCode}}
				</view>
				<view class="">颁发时间：
					{{item.creTime}}
				</view>
			</view>
		</view>

		<u-empty v-if="!certList.length" text="暂无记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<u-gap height="80"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				choiceSearchType: '',
				searchText: '',
				searchTypeList: [
					// {
					// 	value: 'search',
					// 	text: "全局"
					// },
					{
						value: 3,
						text: "门店"
					},
					{
						value: 4,
						text: "讲师"
					}, {
						value: 5,
						text: "技能"
					}, {
						value: 6,
						text: "月嫂"
					}, {
						value: 7,
						text: "育婴师"
					}
				],
				loadMore: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/cert_search_post3.png',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				certList: [],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			changeSearchType(e) {
				this.choiceSearchType = e
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			openDetail(id) {
				uni.navigateTo({
					url: '/pages-mine/certificate/cert-detail?id=' + id
				})
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('证书编号复制成功!')
					}
				})
			},
			formatStoreName(name) {
				if (!name) {
					return ''
				}
				name = name.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				return name
			},
			// 获取资质列表
			listUnionCertRecord() {
				if (!this.searchText) {
					return this.$refs.uNotify.warning('请输入关键词进行搜索！')
				}
				if (!this.choiceSearchType) {
					return this.$refs.uNotify.warning('请选择搜索方式！')
				}
				let data = {
					search: this.searchText,
					storeName: '',
					realName: '',
					certCode: '',
					orderBy: 't.creTime DESC',
					current: 1,
					certType: null,
					size: 100,
					state: 1
				}
				if (this.choiceSearchType) {
					data.certType = this.choiceSearchType
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/getUnionCertRecord',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {
							this.certList = res.data.records
						} else {
							this.certList = []
							this.$refs.uNotify.warning('查询不到相关记录！')
						}
					}
				})
			}
		},
		onLoad(options) {
			if (options.searchType) {
				this.changeSearchType(parseInt(options.searchType))
			}
		},
	}
</script>
<style lang="scss">
	.btn-big {
		button {
			margin: 40rpx auto;
			width: 40%;
			height: 70rpx;
			line-height: 70rpx;
			color: #fdd472;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.list-item {
		width: 90%;
		margin: 20rpx auto;
		height: 240rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		border-radius: 20rpx;
	}
</style>