<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-picker :show="show" @cancel="show = false" :columns="workStateList" @confirm="confirmState"
			@change="changeHandler" keyName="label" :defaultIndex="defaultIndex"></u-picker>

		<!-- 简历分数 -->
		<view class="score-tips">
			<view class="score-tips-title">
				<view class="score-title-head">
					<text>简历&nbsp;</text>
					<text style="font-size:60rpx;color: red;font-weight: bold;">{{resumeScore}}</text>
					<text>&nbsp;分</text>
					<view class="preview-btn" @click="openPreview()">
						<text>预览</text>
					</view>
				</view>
				<view @click="openAccount()">
					<view class="score-title-bottom">
						<text>补充完善个人简历，就可以让老师审核接单啦</text>
					</view>
					<view class="score-title-bottom">
						<text>小贴士：点此完善个人信息，最多可得35分～</text>
					</view>
					<view class="score-title-bottom" style="color: #ff4d4b;font-weight: bold;"
						v-if="isPortraitUnqualified">
						<text>*当前头像不符合规范，请重新上传！</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 个人简历-头部信息 -->
		<view class="resume-head">
			<view class="head-content">
				<view class="head-left"><img class="head-img" @click="openAccount()"
						@longpress="openImgPreview(headImg)"
						:src="headImg !== ''&&headImg !== null ? headImg : blankImg" /></view>
				<view class="head-center">
					<text @click="openAccount()">{{ employee.realName !== null ? employee.realName : '暂无用户名' }}</text>
					<view class="head-tag flac-row">
						<view class="my-tag"
							:style="isAuthenticated?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'"
							@click="openIdentification()">{{isAuthenticated?'已认证':'未认证'}}
						</view>
						<!-- 						<view class="my-tag"
							:style="resumeScore>=80?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'"
							@click="resumeScore>=80?openAbility(0):openAbility(1)">技能认证</view> -->
					<!-- 	<view class="my-tag"
							:style="ifFaceAuth?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'"
							@click="employeeFaceAuth">
							{{ifFaceAuth?'头像认证':'头像认证'}}
						</view> -->
					</view>
				</view>
				<view class="head-right" @click="openAccount()">
					<text style="margin-right: 0rpx;">修改</text>
					<uni-icons type="forward" size="18"></uni-icons>
				</view>
			</view>

			<view class="id-tips" v-if="!isAuthenticated">
				<view class="tips-title">
					<view class="title-head">
						<text>身份认证 优先接单</text>
					</view>
					<view class="title-bottom">
						<text>可上传身份证快速认证</text>
					</view>
				</view>
				<view class="tips-button" @click="openIdentification()">
					<button>去上传</button>
				</view>
			</view>
		</view>


		<!-- 个人简历-求职状态 -->
		<view class="resume-state">
			<text>接单状态</text>
			<text @click="show = true">{{ workState }}</text>
			<uni-icons type="forward" size="18" style="margin-right: 40rpx;"></uni-icons>
		</view>

		<!-- 个人简历-栏目 -->
		<view class="resume-tab" v-for="(tabList, index) in tabList" :key="index">
			<view class="tab-title">
				<text class="tab-title-text">{{ tabList.tabTitle }}</text>
				<uni-tag class="tag" v-if="index == 0 || index == 1 || index == 5" text="找工作必填"
					custom-style="background-color: #fff6f4; border-color: #fff6f4; color: #F9AE3D;">
				</uni-tag>
				<text class="tab-title-button" @click="openTabUrl(index)"
					v-if="!showTabTips(index)&&index!=3&&index!=7">修改</text>
				<uni-icons class="tab-title-button-arrow" type="forward" size="18"
					@click="openTabUrl(index)"></uni-icons>
			</view>
			<view v-if="showTabTips(index)">
				<view class="tab-tips">
					<text>{{ tabList.tabTips }}</text>
				</view>
				<view class="tab-tips">
					<text style="color: #ff4d4b;">{{ tabList.tabTipsAdd }}</text>
				</view>

				<view class="tab-btn">
					<button @click="openTabUrl(index)">{{ tabList.tabBtn }}</button>
				</view>
			</view>

			<!-- 求职意向栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==0" class="tab-info" @click="openTabUrl(index)">
				<view class="info-item">
					<text>工作类型：{{baomuExpectedWork.workModel}}</text>
				</view>
				<view class="info-item">
					<text>工作地点：{{siteName}}{{ baomuExpectedWork.expectedAddress?'/'+baomuExpectedWork.expectedAddress:'' }}</text>
				</view>
				<view class="info-item">
					<text>最低月薪：{{baomuExpectedWork.salaryExpectation}}元/月</text>
				</view>
			</view>

			<!-- 我的证件栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==1" class="tab-info">
				<view class="info-item flac-row" v-if="employeeInfo.idCard" @click="openImgPreview(idCardImg)">
					<text>身份证：已上传</text>
					<uni-icons type="eye" style="margin-left: 10rpx;" size="22">
					</uni-icons>
				</view>
				<view class="info-item flac-row" v-for="(item,index1) in certificateList" :key="index1"
					v-if="showCert(index1)" @click="openImgPreview(item.certificateImg)">
					<text>{{item.title}}：已上传</text>
					<uni-icons type="eye" style="margin-left: 10rpx;" size="22">
					</uni-icons>
				</view>

				<view class="info-item flac-row" v-if="isHealthUpload" @click="openImgPreview(healthImg)">
					<text>体检表：已上传</text>
					<uni-icons type="eye" style="margin-left: 10rpx;" size="22">
					</uni-icons>
				</view>

			</view>

			<!-- 我的优势栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==2" class="tab-info" @click="openTabUrl(index)">
				<view class="info-item">
					<view class="tag-content">
						<view class="my-tag-small" v-for="(item,index1) in advantagesList" :key="index1"
							v-if="item.isCheck==1">
							{{item.text}}
						</view>
					</view>
				</view>
				<view class="info-item">
					<text>一句话介绍：{{introduce}}</text>
				</view>
			</view>

			<!-- 工作经历栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==3" class="tab-info">
				<!-- 				<view class="info-item" v-for="(work, index1) in baomuWorkExperienceList" :key="index1">
					<text>工作{{index1+1}}：{{formatExWorkType(work.workType)}}&ensp;&ensp;&ensp;{{formatDate(work.startWorkTime)}}-{{formatDate(work.endWorkTime)}}</text>
				</view> -->
				<view class="info-item" v-for="(work, index1) in baomuWorkExperienceList" :key="index1"
					@click="openEx(index1)">
					<view class="flac-row">
						<text class="w8"
							style="font-weight: bold;">工作{{index1+1}}：{{formatExWorkType(work.workType)}}</text>
						<view class="w2 flac-row-c">
							<text style="text-align: right;">修改</text>
							<uni-icons type="forward" size="18"></uni-icons>
						</view>
					</view>
					<text>&ensp;&ensp;&ensp;{{formatDate(work.startWorkTime)}}-{{formatDate(work.endWorkTime)}}</text>
					<view class="work-content">
						<text style="line-height: 50rpx;">{{work.workContent}}</text>
					</view>
					<u-gap height="10"></u-gap>
				</view>
			</view>

			<!-- 我的技能栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==4" class="tab-info" @click="openTabUrl(index)">
				<view class="info-item">
					<view class="tag-content">
						<view class="my-tag-small" v-for="(item,index1) in abilityTabList" :key="index1"
							v-if="item.isCheck==1">
							{{item.text}}
						</view>
					</view>
					<view style="clear:both"></view>
				</view>
				<u-gap height="10"></u-gap>
			</view>

			<!-- 我的家庭栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==5" class="tab-info" @click="openTabUrl(index)">
				<view class="info-item">
					<text>联系人关系：{{baomuInfo.urgentType}}</text>
				</view>
				<view class="info-item">
					<text>联系人姓名：{{baomuInfo.urgent}}</text>
				</view>
				<view class="info-item">
					<text>联系人手机号：{{baomuInfo.urgentPhone}}</text>
				</view>
			</view>

			<view v-if="!showTabTips(index)&&index==6">
				<view style="display: flex;flex-wrap: wrap;padding: 20rpx 5%;" @click="openTabUrl(index)">
					<view class="photo-item" v-for="(item,index1) of photeList1" :key="index1"
						v-if="index1<maxShowPhoto||showAllPhoto1">
						<img :src="item.certificateImg" width="40" height="36" />
					</view>
				</view>
				<view v-if="photeList1.length>maxShowPhoto" style="padding-right: 60rpx;text-align: right;"
					@click="showAllPhoto1=!showAllPhoto1">
					<uni-icons :type="showAllPhoto1?'bottom':'top'" labelColor="#000"></uni-icons>
					<text>{{showAllPhoto1?'展开更多':'收起'}}</text>
				</view>
			</view>

			<!-- 工作照详细信息 -->
			<view v-if="!showTabTips(index)&&index==7">
				<view style="display: flex;flex-wrap: wrap;padding: 20rpx 5%;" @click="openTabUrl(index)">
					<view class="photo-item" v-for="(item,index1) of photeList" :key="index1"
						v-if="index1<maxShowPhoto||showAllPhoto">
						<img :src="item.certificateImg" width="40" height="36" />
					</view>
				</view>
				<view v-if="photeList.length>maxShowPhoto" style="padding-right: 60rpx;text-align: right;"
					@click="showAllPhoto=!showAllPhoto">
					<uni-icons :type="showAllPhoto?'bottom':'top'" labelColor="#000"></uni-icons>
					<text>{{showAllPhoto?'展开更多':'收起'}}</text>
				</view>
			</view>

			<!-- 亲友推荐栏目详细信息 -->
			<view v-if="!showTabTips(index)&&index==8" class="tab-info">
				<view class="info-item" v-for="(item1, index1) in recommendList" :key="index1" @click="openRe(index1)">
					<view class="flac-row">
						<text class="w8" style="font-weight: bold;">{{familyList[item1.recommender]}}推荐</text>
						<view class="w2 flac-row-c">
							<text style="text-align: right;">修改</text>
							<uni-icons type="forward" size="18"></uni-icons>
						</view>
					</view>
					<view class="work-content">
						<text style="line-height: 50rpx;">{{item1.recommendContent}}</text>
					</view>
					<u-gap height="10"></u-gap>
				</view>
			</view>
		</view>


		<view class="lh35 text-c" style="position: fixed;display: block;right: 00rpx;top: 70vh;
					width: 160rpx;height: 70rpx;box-shadow: 2rpx 2rpx 10rpx #909399;color: #fff;
					border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;background-color: rgba(30,24,72,0.7);"
			@click="openCall" v-if="isRole">
			电联
		</view>

		<!-- 快速完善 -->
		<view class="lh35 text-c" style="position: fixed;display: block;right: 00rpx;top: 76vh;
					width: 160rpx;height: 70rpx;box-shadow: 2rpx 2rpx 10rpx #909399;color: #fff;
					border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;background-color: rgba(30,24,72,0.7);"
			@click="openQuickly" v-if="isRole">
			快速完善
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 参数设置部分
				// 简历分预览限制
				limitScore: 60,
				// 展示所有照片
				showAllPhoto: false,
				showAllPhoto1: false,
				// 最多展示照片数
				maxShowPhoto: 6,
				// 是否带填写
				isRole: false,

				memberId: null,
				baomuId: null,
				memberName: '',
				resumeScore: 0,
				resumeScoreList: [],
				resumeScoreDetail: [],
				headImg: '',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png',
				iconQuickly: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_resume_quickly.png',
				workState: '正在找工作',
				newIntroducer: '',
				show: false,
				defaultIndex: [0],
				workStateIndex: 0,
				siteName: "",
				certificateList: [],
				isHealthUpload: false,
				isAuthenticated: false,
				ifFaceAuth: false,
				isPortraitUnqualified: false,
				healthImg: '',
				idCardImg: '',
				introduce: "",
				certTitle: '',
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				workStateList: [
					[{
							label: '正在找工作',
							id: 0
						},
						{
							label: '已经有工作',
							id: 1
						},
						{
							label: '暂不找工作',
							id: 2
						}
					]
				],
				tabList: [{
						tabTitle: '求职意向',
						tabTips: '您想要接什么单，我们帮您找',
						tabTipsAdd: '求职意向填写完整能得15分',
						tabBtn: '点击添加',
						tabUrl: '/pages-mine/resume/intention',
						isGetScore: false,
					},
					{
						tabTitle: '我的证件',
						tabTips: '证件越多，实力越强',
						tabTipsAdd: '证件上传完整最多能得10分',
						tabBtn: '上传证件',
						tabUrl: '/pages-mine/resume/certificate/certificate',
						isGetScore: false,
					},
					{
						tabTitle: '自我介绍',
						tabTips: '让客户更了解您',
						tabTipsAdd: '完成自我介绍可增加简历分10分',
						tabBtn: '点击添加',
						tabUrl: '/pages-mine/resume/advantages',
						isGetScore: false,
					},
					{
						tabTitle: '工作经历',
						tabTips: '让客户了解您做过什么',
						tabTipsAdd: '工作内容填写10字以上可以获得5分',
						tabBtn: '添加工作经历',
						tabUrl: '/pages-mine/resume/experience',
						isGetScore: false,
					},
					{
						tabTitle: '我的技能',
						tabTips: '让客户了解您会做什么',
						tabTipsAdd: '我的技能填写完整能得5分',
						tabBtn: '添加工作技能',
						tabUrl: '/pages-mine/resume/ability',
						isGetScore: false,
					},
					{
						tabTitle: '我的家庭',
						tabTips: '背景安全客户更放心',
						tabTipsAdd: '家庭情况填写完整能得10分',
						tabBtn: '添加家人',
						tabUrl: '/pages-mine/resume/family',
						isGetScore: false,
					},
					{
						tabTitle: '生活照',
						tabTips: '让客户更加了解您',
						tabBtn: '上传照片',
						tabUrl: '/pages-mine/resume/photo',
						isGetScore: false,
					},
					{
						tabTitle: '工作照',
						tabTips: '照片越多越容易接单！',
						tabTipsAdd: '上传真实照片可以获得10分',
						tabBtn: '上传照片',
						tabUrl: '/pages-mine/resume/photo',
						isGetScore: false,
					},
					{
						tabTitle: '亲友推荐',
						tabTips: '亲友推荐越多，简历曝光度越高哦！',
						tabTipsAdd: '',
						tabBtn: '填写推荐',
						tabUrl: '/pages-mine/resume/recommend',
						isGetScore: false,
					},
				],
				advantagesList: [],
				searchParam: [
					"保姆",
					"月嫂能力",
					"育儿嫂能力",
					"护工能力",
					"保洁能力",
					"搬家能力",
					"维修清洗能力",
					"疏通能力",
					"陪读师能力",
					"陪诊师能力",
					"整理师能力",
					"装修师能力",
					"豪宅管家能力",
				],
				familyList: ['配偶', '子女', '父母', '朋友', '亲戚', ],
				workTypeList: [{
						urgentType: '保姆',
						workContentTips: '如：从事保姆工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 10
					}, {
						urgentType: '单餐阿姨',
						workContentTips: '如：从事单餐阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 12
					},
					{
						urgentType: '白班阿姨',
						workContentTips: '如：从事白班阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 15
					},
					{
						urgentType: '住家阿姨',
						workContentTips: '如：从事住家阿姨工作XX年， 性格xxx， 掌握xxx技能等。',
						value: 18
					},
					{
						urgentType: '月嫂',
						workContentTips: '如：从事月嫂工作XX年， 服务过XX个宝宝，服务过几对双胞胎，服务过几个早产儿，月嫂经验丰富，有爱心，主要照顾产妇和新生儿的生活起居。',
						value: 20
					},
					{
						urgentType: '白班育儿嫂',
						workContentTips: '如：从事家政工作XX年，最长一家做了XX年，哪年开始在哪个小区带多大带的宝宝（带睡/不带睡），哪年还在哪个小区带多大的宝宝（带睡/不带睡），上一家在哪里做带多大的宝宝。阿姨性格随和，喜欢宝宝，责任感等。',
						value: 25
					},
					{
						urgentType: '住家育儿嫂',
						workContentTips: '如：从事家政工作XX年，最长一家做了XX年，哪年开始在哪个小区带多大带的宝宝（带睡/不带睡），哪年还在哪个小区带多大的宝宝（带睡/不带睡），上一家在哪里做带多大的宝宝。阿姨性格随和，喜欢宝宝，责任感等。',
						value: 28
					},
					{
						urgentType: '育婴师',
						workContentTips: '如：从事育婴师工作XX年， 服务过XX个宝宝，服务过几对双胞胎，服务过几个早产儿，育婴师经验丰富，有爱心，主要照顾产妇和新生儿的生活起居。',
						value: 30
					},
					{
						urgentType: '护工',
						workContentTips: '如：从事护工工作XX年，最长一家/上一家做了XX年，在哪个（小区或医院）照顾什么样的老人（自理/半自理/不自理）。',
						value: 40
					},
					{
						urgentType: '陪读师',
						workContentTips: '如：从事陪读师工作XX年，最长一家/上一家做了XX年，帮助x岁小孩进行学习。',
						value: 45
					},
					{
						urgentType: '保洁师',
						workContentTips: '如：从事保洁师工作XX年，最长一家/上一家做了XX年，在哪个（小区或医院）进行xx打扫工作。',
						value: 50
					},
					{
						urgentType: '收纳师',
						workContentTips: '如：从事收纳师工作XX年，有xx工作经验，会衣物/橱柜收纳等。',
						value: 55
					},
					{
						urgentType: '搬家师',
						workContentTips: '如：从事搬家师工作XX年，有xx工作经验，从事过哪些大型搬家服务等。',
						value: 60
					},
					{
						urgentType: '清洗师',
						workContentTips: '如：从事清洗师工作XX年，有xx工作经验，从事过哪些清洗服务等。',
						value: 70
					},
					{
						urgentType: '维修师',
						workContentTips: '如：从事维修师工作XX年，有xx工作经验，从事过哪些维修服务等。',
						value: 80
					},
					{
						urgentType: '疏通师',
						workContentTips: '如：从事疏通师工作XX年，有xx工作经验，从事过哪些疏通服务等。',
						value: 90
					},
					{
						urgentType: '钟点工',
						workContentTips: '如：从事钟点工工作XX年，有xx工作经验，从事过哪些钟点服务等。',
						value: 95
					},
					{
						urgentType: '其他',
						workContentTips: '请描述这段工作经历您具体在什么地点、做了多长时间、具体工作内容、表现突出的地方',
						value: 100
					}
				],
				abilityTabList: [],
				// 保姆详细信息
				baomuDetail: [],
				// 员工信息
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				// 员工详细信息
				employeeInfo: {
					employeeId: this.baomuId,
					zodiac: 0,
					workingState: 0,
					religion: 0,
					languagenum: null,
					educationnum: null,
					birthTime: null,
					idCard: null,
					hometown: null,
					nation: null,
					education: null,
					married: null,
					tomarried: null,
					language: null,
					workYear: null,
					family: null
				},
				// 保姆信息
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null
				},
				baomuExpectedWork: {
					baomuId: this.baomuId,
					workModel: null,
					elderly: null,
					watchBaby: null,
					cookingSkills: null,
					otherSkills: null,
					salaryExpectation: null,
					siteId: null
				},
				baomuWorkExperienceList: [],
				photeList: [],
				photeList1: [],
				recommendList: [],
			};
		},
		methods: {
			// 打开图片预览
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开简历预览
			openPreview() {
				let limitScore = this.limitScore
				if (this.resumeScore >= limitScore) {
					// 旧版简历阅览
					// let url = "/pages-mine/resume/preview?id=" + this.baomuId
					// 员工详情预览
					let url = "/pages-mine/works/employee-detail?baomuId=" + this.baomuId
					return uni.navigateTo({
						url: url
					})
				} else {
					this.$refs.uNotify.error("简历分超过" + limitScore + "分才可以预览哦！")
				}

			},
			// 电话联系
			openCall() {
				uni.makePhoneCall({
					phoneNumber: this.employee.phone,
					success: function(e) {

					},
					fail: function(e) {

					}
				});
			},
			// 打开快捷填写
			openQuickly() {
				uni.navigateTo({
					url: '/pages-mine/resume/resume-quickly?baomuId=' + this.baomuId
				});
			},
			// 打开个人信息
			openAccount() {
				return uni.navigateTo({
					url: '/pages-mine/resume/account?baomuId=' + this.baomuId
				});
			},
			// 打开身份认证页面
			openIdentification() {
				uni.navigateTo({
					url: '/pages-mine/resume/certificate/identification?baomuId=' + this.baomuId
				});
			},
			// 打开技能认证
			openAbility(value) {
				if (value == 0) {
					uni.navigateTo({
						url: "/pages-mine/auth/auth?baomuId=" + this.baomuId
					})
				} else {
					this.$refs.uNotify.error("简历分达到80分以上才可以去认证哦～")
				}
			},
			// 头像认证
			employeeFaceAuth() {
				if (this.ifFaceAuth) {
					return this.$refs.uNotify.warning("已经通过头像认证了哦～")
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/employeeFaceAuth',
					method: 'POST',
					data: {
						employeeId: this.baomuId,
						operatorId: uni.getStorageSync("employeeId") || 0
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('头像认证成功！')
							this.ifFaceAuth = true
						} else {
							this.$refs.uNotify.warning('认证失败！请重新上传头像后进行认证！')
						}
					},
				});

			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.workStateIndex = index;
			},
			// 确认工作状态选择
			confirmState(e) {
				this.workState = this.workStateList[0][this.workStateIndex].label;
				this.show = false;
				if (this.workStateIndex == 0) {
					this.baomuInfo.status = '0'
				} else if (this.workStateIndex == 1) {
					this.baomuInfo.status = '1'
				} else if (this.workStateIndex == 2) {
					this.baomuInfo.status = '2'
				}
				this.checkBaomuAndInit()
				if (this.baomuId == null) {
					let timer = setTimeout(() => {
						if (this.baomuId !== null) {
							this.updateBaomuInfo()
						}
					}, 1500);
				} else {
					this.updateBaomuInfo()
				}
			},
			cancelState() {
				this.show = false;
			},
			showTabTips(index) {
				let result = true
				if (this.resumeScoreDetail.length == 0) {
					return true
				}
				switch (index) {
					case 0:
						if (this.resumeScoreDetail[6].score > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 1:
						if (this.resumeScoreDetail[9].score > 0 || this.resumeScoreDetail[10].score > 0 ||
							this.resumeScoreDetail[11].score > 0 || this.isAuthenticated) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 2:
						if (this.resumeScoreDetail[12].score > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 3:
						if (this.resumeScoreDetail[13].score > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 4:
						// if (this.resumeScoreDetail[11].score > 0) {
						// 	result = false
						// }
						if (this.resumeScoreDetail[14].score > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 5:
						if (this.resumeScoreDetail[15].score > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 6:
						if (this.photeList1.length > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 7:
						if (this.photeList.length > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
					case 8:
						if (this.recommendList.length > 0) {
							this.tabList[index].isGetScore = true
							result = false
						}
						break;
				}
				return result
			},
			showCert(index) {
				let type = this.certificateList[index].certificateType
				let title = this.certificateList[index].title
				let result = true

				// 不出现特定类型照片
				if (type <= 2 || type == 8 || type == 25 || type == 98 || type == 99) {
					result = false
				}

				if (title == null) {
					result = false
				}
				return result
			},
			// 打开栏目对应页面
			openTabUrl(index) {
				let url = this.tabList[index].tabUrl
				let isGetScore = this.tabList[index].isGetScore
				// 传入不同的简历分数项参数
				url = url + "?baomuId=" + this.baomuId + "&isGetScore=" + isGetScore

				if (index == 6) {
					url += "&type=1"
				} else if (index == 7) {
					url += "&type=0"
				}
				uni.navigateTo({
					url: url
				});
			},
			// 打开工作经历
			openEx(index) {
				let url = '/pages-mine/resume/experience'
				let isGetScore = this.tabList[3].isGetScore
				url += '?baomuId=' + this.baomuId + '&isGetScore=' + isGetScore + '&index=' + index
				uni.navigateTo({
					url: url
				})
			},
			// 打开亲友推荐
			openRe(index) {
				let url = '/pages-mine/resume/recommend'
				url += '?baomuId=' + this.baomuId + '&index=' + index
				uni.navigateTo({
					url: url
				})
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? '0' + MM : MM
				let d = date.getDate()
				d = d < 10 ? '0' + d : d;
				return y + '-' + MM + '-' + d
			},
			formatExWorkType(value) {
				let name = ''
				this.workTypeList.forEach(item => {
					if (item.value == value) {
						name = item.urgentType
					}
				})
				return name
			},
			// 格式化保姆优势介绍
			formatAdvantages(introduce) {
				let choiceCount = 0
				let advantagesIndex = 0
				if (introduce !== null) {
					for (let item of this.advantagesList) {
						if (introduce.includes(item.text)) {
							item.isCheck = 1
							advantagesIndex = advantagesIndex + item.text.length + 1
							choiceCount = choiceCount + 1
						}
					}
					this.introduce = introduce.substr(advantagesIndex)
				}
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				// 请求：通过会员ID获取保姆关联信息
				if (this.baomuId == null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId;
								this.baomuInfo.baomuId = this.baomuId;
								uni.setStorageSync('baomuId', this.baomuId);
								console.log('通过会员ID获取保姆关联信息-成功！');
								console.log('初始化的保姆ID为' + this.baomuId);
							} else {
								console.log('error', '通过会员ID获取保姆关联信息-返回错误！' + res.msg)
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code);
						}
					});
				}
			},
			// 更新保姆信息
			updateBaomuInfo() {
				this.$set(this.baomuInfo, 'baomuId', this.baomuId)
				// 请求：更新保姆信息
				this.http({
					url: 'updateBaomuInfo',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: this.baomuInfo,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('接单状态更新成功！')
							this.initInfo()
						} else {
							this.$refs.uNotify.error('接单状态更新失败-返回错误！' + res.msg)
						}
					},
					fail: err => {
						console.log('更新保姆信息失败-请求错误！' + res.code)
					}
				});
			},
			// 鉴定师完善简历，回写推荐人
			updatePriorIntroducer() {
				console.log("输出新的开发人", this.newIntroducer)
				console.log("是否已经更改简历", uni.getStorageSync("isUpdateResume"))
				let roleId = uni.getStorageSync("roleId") || 0
				if (this.newIntroducer != '' && uni.getStorageSync("isUpdateResume") && (roleId == 1 || roleId == 106)) {
					console.log("----------------鉴定师完善简历，回写推荐人----------------")
					this.http({
						url: "updatePriorIntroducer",
						header: {
							'content-type': "application/json;charset=UTF-8"
						},
						method: 'POST',
						data: {
							id: this.baomuId,
							introducer: this.newIntroducer
						},
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								this.newIntroducer == ''
								uni.setStorageSync("isUpdateResume", false)
							}
						}
					})
				}
			},
			// 获取保姆的技能字典内容
			getBaomuAbility(index) {
				let url = "getDictionaryByText"
				let path = ""
				if (index == 0) {
					url = "getBaomuDictionaryById"
					path = this.baomuId || 0
				}

				let data = this.searchParam
				if (index == 1) {
					data = [
						"性格优势"
					]
				}
				this.http({
					url: url,
					path: path,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: data,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (index == 0) {
								this.abilityTabList = res.data
							} else if (index == 1) {
								this.advantagesList = res.data
								this.formatAdvantages(this.baomuInfo.introduce)
							}
						}
					}
				})
			},
			// 获取个人照片列表
			getMyPhotoList(value) {
				let type = 25
				if (value == 1) {
					type = 98
				}
				this.http({
					url: 'getCertificateByCertType',
					method: 'POST',
					data: {
						employeeId: this.baomuId,
						certificateType: type
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.photeList = res.data
							} else if (value == 1) {
								this.photeList1 = res.data
							}
						} else {
							if (value == 0) {
								this.photeList = []
							} else if (value == 1) {
								this.photeList1 = []
							}
						}
					}
				})
			},
			// 获取亲友推荐列表
			listEmployeeRecommend() {
				if (!this.baomuId) {
					return
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/acn/listEmployeeRecommend',
					method: 'GET',
					path: this.baomuId,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.recommendList = res.data
						}
					},
				});
			},
			// 获取员工已经上传的证件
			getCertificateByEmployeeId() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getCertificateByEmployeeId',
						method: 'GET',
						path: this.baomuId,
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								this.certificateList = res.data
								for (let item of this.certificateList) {
									if (item.certificateType == 8) {
										this.isHealthUpload = true
										this.healthImg = item.certificateImg
									} else if (item.certificateType == 1) {
										this.idCardImg = item.certificateImg
									}
								}
							}
						},
					});
				}
			},
			// 获取简历分
			getResumeScore() {
				let baomuId = this.baomuId !== null ? this.baomuId : -1
				this.http({
					url: 'getResumeScore',
					path: baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.resumeScore = res.data.scoreSum
							this.resumeScoreList = res.data
							this.resumeScoreDetail = res.data.scoreDetail
						}
					}
				});
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						path: this.baomuId,
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								this.baomuDetail = baomuDetail
								this.siteName = baomuDetail.siteName || '本地'
								// 获取员工信息
								this.employee = baomuDetail.employee
								this.headImg = this.employee.headPortrait
								this.employeeName = this.employee.realName
								let remark = this.employee.remark || ""
								if (remark.includes("头像不合格")) {
									this.isPortraitUnqualified = true
								} else {
									this.isPortraitUnqualified = false
								}

								// 获取员工详细信息
								this.employeeInfo = baomuDetail.employeeInfo
								console.log("输出身份证：" + this.employeeInfo.idCard)
								if (this.employeeInfo.idCard && this.employeeInfo.birthTime) {
									this.isAuthenticated = true
									uni.setStorageSync('isAuthenticated', true)
								} else {
									uni.setStorageSync('isAuthenticated', false)
								}

								if (this.employeeInfo.ifFaceAuth == 1) {
									this.ifFaceAuth = true
								}

								// 获取保姆详细
								this.baomuInfo = baomuDetail.baomuInfo
								if (this.baomuInfo.status == '0') {
									this.workStateIndex = 0
								} else if (this.baomuInfo.status == '1') {
									this.workStateIndex = 1
								} else if (this.baomuInfo.status == '2') {
									this.workStateIndex = 2
								}
								this.defaultIndex = []
								this.defaultIndex.push(this.workStateIndex)
								this.workState = this.workStateList[0][this.workStateIndex].label
								this.formatAdvantages(this.baomuInfo.introduce)

								// 获取求职意向和工作经历
								this.baomuExpectedWork = baomuDetail.baomuExpectedWork
								this.baomuWorkExperienceList = baomuDetail.baomuWorkExperience
								console.log('获取保姆详细信息成功-请求成功！')
							} else {
								console.log('获取保姆详细信息成功-请求失败！')
							}
						}
					});
				}
			},
			// 检测是否为保姆
			checkBaomu() {
				this.http({
					url: 'checkBaomuCollectByMemberId',
					method: 'POST',
					data: {
						memberId: this.memberId
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.baomuId = res.data.baomuId
							uni.setStorageSync("baomuId", this.baomuId)
						}
					},
				})
			},
			// 获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let baomuId = uni.getStorageSync('baomuId')
				baomuId = baomuId == '' ? null : baomuId
				this.memberId = memberId

				// 如果在上一个页面没有获取参数，则从缓存中取值（自己填的情况）
				if (this.baomuId == undefined || this.baomuId == null) {
					this.baomuId = baomuId
				} else {
					if (this.baomuId !== baomuId) {
						uni.setNavigationBarTitle({
							title: "个人简历（代填写）"
						})
						this.isRole = true
					}
				}
				console.log('用户ID：' + memberId + '保姆ID：' + baomuId)
				if (this.baomuId == null) {
					this.checkBaomu()
					let timer = setTimeout(() => {
						this.initBaomuInfo()
					}, 600);
				} else {
					this.initBaomuInfo()
				}
			},
			initBaomuInfo() {
				this.getBaomuAbility(0)
				this.getBaomuAbility(1)
				this.getBaomuDetail()
				this.getCertificateByEmployeeId()
				this.getMyPhotoList(0)
				this.getMyPhotoList(1)
				this.listEmployeeRecommend()
				this.getResumeScore()
			},
			initInfo() {
				this.getMemberInfor()
			},
			// 登录状态检查
			checkLogin() {
				console.log('开始检查登录状态！')
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', '/pages-mine/resume/resume')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				} else {
					this.initInfo()
				}
			}
		},
		onShow() {
			this.checkLogin()
			this.updatePriorIntroducer()
		},
		onLoad(options) {
			// 尝试获取邀请员工信息
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				console.log("输出从邀请二维码获取的邀请人信息（员工Id和会员Id）：", obj)
				let introducerId = obj.id
				let introducerId1 = obj.id1
				uni.setStorageSync('introducerId', introducerId)
				uni.setStorageSync('introducerId1', introducerId1)
			} else {
				console.log("未获取邀请人信息！")
			}
			this.baomuId = JSON.parse(options.baomuId)
			console.log("输出现在：", this.baomuId)

			this.newIntroducer = options.newIntroducer || ""
			// 测试时加上
			// this.baomuId = 37779
		},
		mounted() {
			this.checkLogin()
		}
	};
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #ffffff;
	}

	// 头部个人信息部分
	.resume-head {
		width: 100%;
		height: auto;
		// background-color: #ffe102;
		padding: 0 0 40rpx 0;
	}

	.head-content {
		width: 100%;
		height: 280rpx;
	}

	// 头像部分
	.head-left {
		float: left;
		width: 30%;
		height: 100%;
	}

	.head-img {
		display: block;
		margin: 60rpx 40rpx;
		width: 180rpx;
		height: 180rpx;
		border-radius: 50%;
	}

	// 个人信息部分
	.head-center {
		float: left;
		width: 50%;
		height: 260rpx;
		padding-top: 40rpx;
	}

	.head-center text {
		display: block;
		float: left;
		width: 60%;
		height: 40rpx;
		margin: 30rpx 20rpx;
		font-size: 36rpx;
		font-weight: bold;
	}

	.head-tag {
		display: flex;
		flex-direction: row;
		padding: 0;
		float: left;
		width: 400rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		margin: 35rpx 20rpx;
	}

	.my-tag {
		padding: 0 20rpx;
		width: 200rpx;
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #ffffff;
		margin-right: 20rpx;
		text-align: center;
	}

	.my-tag:nth-child(2) {
		width: 165rpx;
	}

	.tag-content {
		float: left;
		width: 100%;
		height: auto;
		padding: 10rpx 0;
	}

	.my-tag-small {
		float: left;
		padding: 0;
		width: auto;
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 20rpx 0rpx;
		padding: 0 10rpx;
	}

	// 修改按钮
	.head-right {
		float: left;
		width: 20%;
		height: 100%;

		text {
			height: 180rpx;
			line-height: 180rpx;
			width: 100%;
			margin-right: 40rpx;
			font-size: 36rpx;
		}
	}

	// 简历分数栏目
	.score-tips {
		width: 100%;
		height: auto;
		margin: 0 auto;
		display: flex;
		flex-direction: row;
		color: #ffffff;
		padding: 20rpx 20rpx;
		background: linear-gradient(rgba(38, 56, 128, 0.8), rgba(58, 100, 100, 0.8));
	}


	.id-tips {
		width: 90%;
		height: 140rpx;
		margin: 0 auto;
		display: flex;
		flex-direction: row;
		color: #ffffff;
		background-color: #1e1848;
		border-radius: 20rpx;
		bottom: 20rpx;
		text-align: center;
	}

	.tips-title,
	.score-tips-title {
		width: 62%;
		height: auto;
		display: flex;
		flex-direction: column;
		font-size: 32rpx;
		padding: 20rpx 20rpx;
		line-height: 50rpx;
	}

	.score-tips-title {
		width: 100%;
	}

	.title-head,
	.score-title-head {
		font-size: 40rpx;
	}

	.title-bottom,
	.score-title-bottom {
		font-size: 32rpx;
		height: 50rpx;
	}

	// 简历预览
	.preview-btn {
		float: right;
		margin-right: 40rpx;
		height: 50rpx;
		line-height: 50rpx;
		width: 140rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		text-align: center;
		font-size: 36rpx;

		text {
			color: #1e1848;
		}
	}

	.tips-button {
		width: 30%;
		height: 100%;

		button {
			width: 160rpx;
			line-height: 70rpx;
			height: 70rpx;
			color: #1e1848;
			background-color: #ffffff;
			padding: 0;
			font-size: 32rpx;
			border-radius: 80rpx;
			margin: 35rpx auto;
		}
	}


	// 求职状态
	.resume-state {
		width: 100%;
		height: 160rpx;
		line-height: 160rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin-bottom: 20rpx;
		display: flex;


		text:first-child {
			width: 74%;
			padding-left: 5.5%;
			font-size: 40rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			width: 30%;
			font-size: 36rpx;
		}
	}

	// 栏目选项
	.resume-tab {
		height: auto;
		width: 100%;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 40rpx 0;
	}

	.tab-title {
		display: block;
		width: 100%;
		height: 100rpx;
		font-size: 40rpx;
		font-weight: bolder;
	}

	.tab-title-text {
		display: block;
		float: left;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 20rpx;
		margin-left: 40rpx;
	}

	.tag {
		display: block;
		padding: 0;
		float: left;
		width: 160rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		margin: 35rpx 0 20rpx 0;
	}

	.tab-tips {
		margin: 20rpx 0;
		width: 100%;
		height: auto;

		text {
			display: block;
			font-size: 32rpx;
			text-align: center;
			color: #aca9a9;
		}
	}

	// 修改按钮
	.tab-title-button {
		float: right;
		height: 60rpx;
		line-height: 60rpx;
		margin: 34rpx 80rpx 0 0;
		font-size: 36rpx;
		font-weight: 400;
	}

	.tab-title-button-arrow {
		position: absolute;
		right: 40rpx;
		float: right;
		height: 60rpx;
		line-height: 60rpx;
		margin: 34rpx 0rpx 0 0;
		// #ifdef  MP-WEIXIN
		margin: 30rpx 0rpx 0 0;
		// #endif
	}

	// 栏目信息预览
	.tab-info {
		width: 100%;
		height: auto;
		padding: 20rpx 50rpx;
	}

	// 栏目信息内容
	.info-item {
		width: 90%;
		height: auto;
		line-height: 80rpx;

		text {
			display: block;
			font-size: 36rpx;
		}
	}

	// 栏目按钮
	.tab-btn {
		width: 100%;
		height: 80rpx;
		padding: 20rpx 0 60rpx 0;
		font-size: 36rpx;

		button {
			padding: 0;
			width: 240rpx;
			line-height: 80rpx;
			height: 80rpx;
			color: #f6cc70;
			background-color: #1e1848;
		}

	}

	// 照片栏目
	.photo-item {
		width: 33.3%;
		height: auto;

		img {
			display: block;
			width: 180rpx;
			height: 180rpx;
			margin: 20rpx auto;
		}
	}
</style>