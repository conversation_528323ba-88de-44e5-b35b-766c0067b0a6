<template>
  <view>
    <view class="flac-row-b">
      <u-button customStyle="border-radius:0!important" text="添加设备" size="large" color="#263880" @click="showLogin=true"></u-button>
      <u-button customStyle="border-radius:0!important" text="设备管理" size="large" color="#3a6464" @click="showManage = true" />
    </view>
    <!-- 数据列表 -->
    <uni-table ref="tableData" stripe emptyText="暂无更多数据">
      <!-- 表头行 -->
      <uni-tr>
        <uni-th align="center" width="90">设备名称</uni-th>
        <uni-th align="center" width="85">到店人数</uni-th>
        <uni-th align="center" width="50">操作</uni-th>
      </uni-tr>
      <!-- 设备列表 -->
      <uni-tr v-for="(item, i) in tableData" :key="i">
        <uni-td align="center">
          <view class="flac-row-c">
            {{item.osdname || ('设备' + (i+1))}}
            <u-icon name="edit-pen-fill" size="20" @click="edit(i)" />
          </view>
        </uni-td>
        <uni-td align="center">{{item.total || (Math.floor(Math.random() * 50 + 0))}}</uni-td>
        <uni-td align="center">
          <!-- 操作 -->
          <view class="flac-row-a">
            <!-- <u-tag text="直播" @click="gotoZB(i)" />
            <u-tag text="删除" type="error" @click="delet(i)" /> -->
            <u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-sxt.png" size="20"
              @click="gotoYS(i)" />
            <u-icon name="more-dot-fill" @click="clickSet(i)"></u-icon>
          </view>
        </uni-td>
      </uni-tr>
    </uni-table>
    <!-- 添加设备弹窗 -->
    <u-popup :show="showLogin" :round="10" mode="center" @close="showLogin=false" :closeable="true">
      <view class="f18 fb text-c lh50">添加设备</view>
      <u--form labelPosition="left" :model="model" :rules="rules" ref="form" labelWidth="90px">
        <u-form-item :required="true" label="设备序列号:" prop="deviceSerial" borderBottom ref="item">
          <u--input v-model="model.deviceSerial" border="none" placeholder="请输入9位字母或数字的序列号"></u--input>
        </u-form-item>
        <u-form-item :required="true" label="设备验证码:" prop="validateCode" borderBottom ref="item">
          <u--input v-model="model.validateCode" border="none" placeholder="请输入设备验证码"></u--input>
        </u-form-item>
      </u--form>
      <u-button text="确定" @click="submit" color="#1e1848" shape="circle"
        customStyle="width:70%;margin: 50rpx auto"></u-button>
    </u-popup>
	<!-- 设备管理弹窗 -->
	<u-popup :show="showManage" :round="10" mode="center" @close="showManage=false" :closeable="true">
	  <view class="f18 fb text-c lh50">设备管理</view>
	  <u--form labelPosition="left" :model="modelManage" :rules="rulesManage" ref="form" labelWidth="90px">
	    <u-form-item :required="true" label="WiFi名称:" prop="deviceSerial" borderBottom ref="item">
	      <u--input v-model="modelManage.wifiName" border="none" placeholder="请输入"></u--input>
	    </u-form-item>
	    <u-form-item :required="true" label="WiFi密码:" prop="validateCode" borderBottom ref="item">
	      <u--input v-model="modelManage.wifiCode" border="none" placeholder="请输入"></u--input>
	    </u-form-item>
		<u-form-item :required="true" label="抖音链接:" prop="validateCode" borderBottom ref="item">
		  <u--input v-model="modelManage.wifiUrl" border="none" placeholder="请输入"></u--input>
		</u-form-item>
	  </u--form>
	  <u-button text="确定" @click="submitManage" color="#1e1848" shape="circle"
	    customStyle="width:70%;margin: 50rpx auto"></u-button>
	</u-popup>
    <!-- 修改设备名称 -->
    <u-modal :show="showEdit" title="编辑设备名称" @confirm="editConfirm" @cancel="showEdit=false" :showCancelButton="true">
      <u--input v-model="editVal" inputAlign="center" placeholder="请输入设备名称" @blur="blur" />
    </u-modal>
    <!-- 监控-实时/回放 -->
    <u-modal :show="showModal" :showConfirmButton="false" :closeOnClickOverlay="true" @close="showModal=false">
      <u-cell-group>
        <u-cell size="large" v-for="(item,i) in list" :key="i" :title="item.title" value="播放入口" isLink @click="selectJK(i)" />
      </u-cell-group>
    </u-modal>
    <!-- 多个操作 -->
    <u-modal :show="showSet" :showConfirmButton="false" :closeOnClickOverlay="true" @close="showSet=false">
      <u-grid col="2" @click="clickIcon">
        <u-grid-item v-for="(item,i,) in setList" :key="i">
          <u-icon :customStyle="{paddingTop: 20+'rpx',margin:'auto 80rpx'}" :name="item.name" :size="30"></u-icon>
          <text class="grid-text">{{item.title}}</text>
        </u-grid-item>
      </u-grid>
    </u-modal>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        showLogin: false,
        showEdit: false,
        showSet: false,
        showModal: false,
		showManage:false,
        editVal: '',
        tableData: [],
        model: {
          deviceSerial: '',
          validateCode: '',
        },
		modelManage: {
		  wifiName: '',
		  wifiCode: '',
		  wifiUrl:''
		},
        rules: {
          'deviceSerial': {
            type: 'string',
            required: true,
            min: 9,
            max: 9,
            pattern: /^[0-9a-zA-Z]*$/g,
            // 正则检验前先将值转为字符串
            transform(value) {
              return String(value);
            },
            message: '序列号不能为空，且为9位字母或数字',
            trigger: ['blur', 'change']
          },
          'validateCode': {
            type: 'string',
            required: true,
            message: '验证码不能为空',
            trigger: ['blur', 'change']
          }
        },
		rulesManage: {
		  'wifiCode': {
		    type: 'string',
		    required: true,
		    min: 9,
		    max: 9,
		    pattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,8}$/,
		    // 正则检验前先将值转为字符串
		    transform(value) {
		      return String(value);
		    },
		    message: 'WiFi密码只能由数字，字母组成，不能有特殊符号,并且长度限制在6-8位',
		    trigger: ['blur', 'change']
		  },
		  'wifiName': {
		    type: 'string',
		    required: true,
		    message: 'WiFi名称不能为空',
		    trigger: ['blur', 'change']
		  },
		  'wifiUrl': {
		    type: 'string',
		    required: true,
		    message: '抖音链接不能为空',
		    trigger: ['blur', 'change']
		  }
		},
        list: [{
          title: '实时监控',
          url: '11111'
        }, {
          title: '回放',
          url: '22222'
        }],
        setList: [{
            name: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zhibo.png',
            title: '直播'
          },
          {
            name: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-delete.png',
            title: '删除'
          }
        ],
      }
    },
    onLoad() {
      this.getAccessToken()
      this.getJKlist()
    },
    methods: {
      getAccessToken() { // 获取accessToken
        let sendData = {
          etoken: uni.getStorageSync('token'),
          storeid: uni.getStorageSync('storeId'),
          url: 'https://ysapi.xiaoyujia.com/getacctoken'
        }
        // console.log(sendData, 'sendData')
        return this.post(sendData).then(res => {
          // console.log(res, 'getAccessToken')
          let accessToken = res.data
          uni.setStorageSync('accessToken', accessToken)
        })
      },
      getJKlist() { //获取列表数据
        this.tableData = []
        let sendData = {
          etoken: uni.getStorageSync('token'),
          storeid: uni.getStorageSync('storeId'),
          url: 'https://ysapi.xiaoyujia.com/getjklist'
        }
        return this.post(sendData).then(res => {
          if (res.data.state == 200) {
            this.tableData = res.data.list || []
            // console.log(this.tableData, 'tableData')
          } else {
            uni.showToast({
              icon: 'none',
              title: res.data.msg,
            });
          }
        })
      },
      addDevice() { //添加设备
        let sendData = {
          etoken: uni.getStorageSync('token'),
          storeid: uni.getStorageSync('storeId'),
          deviceSerial: this.model.deviceSerial,
          validateCode: this.model.validateCode,
          url: 'https://ysapi.xiaoyujia.com/ysadd'
        }
        return this.post(sendData).then(res => {
          if (res.data.state == 200) {
            this.getJKlist()
            this.showLogin = false
          } else {
            uni.showToast({
              icon: 'none',
              title: res.data.msg,
            });
          }
        })
      },
      submit() { //提交设备信息
        this.$refs.form.validate().then(res => {
          this.addDevice()
        }).catch(errors => {
          uni.$u.toast('添加失败，请检查后重试')
        })
      },
	  
	  submitManage() { //提交设备信息
        this.$refs.form.validate().then(res => {
         let sendData2 = {
           etoken: uni.getStorageSync('token'),
           storeid: uni.getStorageSync('storeId'),
           wifiName: this.modelManage.wifiName,
           wifiCode: this.modelManage.wifiCode,
           wifiUrl: this.modelManage.wifiUrl,
         }
         return this.post(sendData2).then(res => {
           if (res.data.state == 200) {
             // this.getJKlist()
             this.showManage = false
           } else {
             uni.showToast({
               icon: 'none',
               title: res.data.msg,
             });
           }
         })
        }).catch(errors => {
          uni.$u.toast('保存失败，请检查后重试')
        })
      },
      edit(i) { //编辑设备名称,初始化输入框
        this.index = i
        this.editVal = this.tableData[this.index].osdname
        this.showEdit = true
      },
      blur() { //输入框失焦
        this.tableData[this.index].name = this.editVal
      },
      editConfirm() { //确定修改设备名称
        let sendData = {
          etoken: uni.getStorageSync('token'),
          storeid: uni.getStorageSync('storeId'),
          deviceSerial: this.tableData[this.index].deviceSerial,
          newname: this.tableData[this.index].name,
          url: 'https://ysapi.xiaoyujia.com/ysupdate'
        }
        return this.post(sendData).then(res => {
          this.showEdit = false
          uni.showToast({
            icon: 'none',
            title: '设备名称修改成功'
          })
          this.getJKlist()
        })
      },
      selectJK(i) { //选择查看实时监控/回放
        // console.log(this.index, 'selectJK')
        let JKpath = ''
        if (i == 0) {
          JKpath = 'https://open.ys7.com/ezopen/h5/live'
        } else if (i == 1) {
          JKpath = 'https://open.ys7.com/ezopen/h5/rec'
        }
        uni.navigateTo({
          url: './videoPage?JKpath=' + JKpath
        })
      },
      gotoYS(i) { //跳转石莹小程序
        this.index = i
        // console.log(this.index, 'gotoYS')
        uni.setStorageSync('deviceSerial', this.tableData[this.index].deviceSerial)
        // #ifdef H5 || APP-PLUS
        this.showModal = true
        // #endif
        // #ifdef MP-WEIXIN
        let channelNo = 1
        uni.navigateToMiniProgram({
          appId: 'wxf2b3a0262975d8c2',
          path: 'pages/live/live?accessToken=' + uni.getStorageSync('accessToken') + '&deviceSerial=' +
            uni.getStorageSync('deviceSerial') + '&channelNo=' + channelNo,
          success(res) {
            // console.log(res, gotoYS)
            // 打开成功
          }
        })
        // #endif
      },
      clickSet(i) { //点击显示更多操作内容
        this.index = i
        console.log(this.index)
        this.showSet = true
      },
      delet() { //删除设备
        this.showSet = false
        uni.showModal({
          title: '提示',
          content: '您确定要删除此设备吗？',
          success: (res) => {
            if (res.confirm) {
              let sendData = {
                etoken: uni.getStorageSync('token'),
                storeid: uni.getStorageSync('storeId'),
                deviceSerial: this.tableData[this.index].deviceSerial,
                url: 'https://ysapi.xiaoyujia.com/ysdelete'
              }
              return this.post(sendData).then(res => {
                uni.showToast({
                  icon: 'none',
                  title: res.data.msg
                })
                this.getJKlist()
              })
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
            this.showSet = true
          }
        });
      },
      gotoZB() { //查看直播
        let sendData = {
          etoken: uni.getStorageSync('token'),
          storeid: uni.getStorageSync('storeId'),
          deviceSerial: this.tableData[this.index].deviceSerial,
          url: 'https://ysapi.xiaoyujia.com/getlive'
        }
        return this.post(sendData).then(res => {
          let ZBpath = res.data.url
          // #ifdef MP-WEIXIN
          let ZBurl = 'https://open.ys7.com/ezopen/h5/iframe?url=' + ZBpath + '&autoplay=' + 1 + '&audio=' +
            1 + '&hd=' + 1 + '&accessToken=' + uni.getStorageSync('accessToken')
          uni.setClipboardData({
            data: ZBurl,
            showToast: true,
            success: function(res) {
              // console.log('success');
              uni.showToast({
                icon: 'success',
                title: '链接已复制'
              })
            }
          });
          // #endif
          // #ifdef H5 || APP-PLUS
          uni.navigateTo({
            url: './videoPage?ZBpath=' + ZBpath
          })
          // #endif
        })
      },
      copyLink() { //复制直播地址
        uni.setClipboardData({
          data: this.urlContent,
          showToast: true,
          success: function(res) {
            // console.log('success');
            uni.showToast({
              icon: 'success',
              title: '链接已复制'
            })
          }
        });
      },
      clickIcon(name) { //点击对应操作
        if (name == 0) {
          this.gotoZB()
        } else if (name == 1) {
          this.delet()
        }
      },
      post(data = {}) {
        return new Promise(ok => {
          uni.request({
            url: 'https://api2.xiaoyujia.com/activity/jdpost',
            method: 'POST',
            data: {
              ...data,
            },
            success: res => ok(res),
            fail: err => {
              console.log('异常链接-->' + url)
              console.log('错误详情-->' + res)
              ok({})
            }
          })
        })
      },
      goWeekPage() {
        uni.navigateTo({
          url:'./weekPage?sendData=' + this.tableData
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .btnStyle {
    border-radius: 0;
  }

  /deep/ .uni-table-th {
    color: #666 !important;
    font-size: 1rem !important;
  }

  /deep/.uni-table-td {
    font-size: 1rem !important;
  }

  /deep/.u-form {
    padding: 0 70rpx;
  }

  /deep/.uni-table-th,
  .uni-table-td {
    padding: 16rpx 10rpx !important;
  }

  .grid-text {
    font-size: 32rpx;
    color: #1a297e;
    padding: 10rpx 0 20rpx 0rpx;
    /* #ifndef APP-PLUS */
    box-sizing: border-box;
    /* #endif */
  }
</style>