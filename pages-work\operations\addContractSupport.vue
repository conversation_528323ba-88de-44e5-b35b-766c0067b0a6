<template>
	<view>

		<view class="cell-body" v-show="active==0">
			<uni-section type="line" title="关联原保姆" sub-title="输入保姆姓名即可关联保姆"></uni-section>
			<u-search placeholder="请输入保姆姓名" v-model="keywordbaomu" @custom="searchbm"></u-search>
			<uni-section type="line" title="乙方姓名">
				<u-input placeholder="(原保姆)姓名" v-model="dom.oldEmployeeName" border="bottom" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="(原保姆)身份证">
				<u-input placeholder="(原保姆)身份证" border="bottom" v-model="dom.oldEmployeeCarId" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="(原保姆)电话">
				<u-input placeholder="(原保姆)电话" border="bottom" v-model="dom.oldEmployeePhone" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="(原保姆)身份证地址">
				<u-input placeholder="(原保姆)身份证地址" border="bottom" v-model="dom.oldEmployeeAdress"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="甲方(雇主)">
				<u-input placeholder="雇主姓名" border="bottom" v-model="dom.memberName" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="雇主电话">
				<u-input placeholder="雇主电话" border="bottom" v-model="dom.memberPhone" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="雇主地址">
				<u-input placeholder="雇主地址" border="bottom" v-model="dom.memberAdress" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="雇主身份证号">
				<u-input placeholder="雇主身份证号" border="bottom" v-model="dom.memberCardId" suffixIcon="arrow-right">
				</u-input>
			</uni-section>

		</view>
		<view class="cell-body" v-show="active==1">
			<uni-section type="line" title="关联保姆" sub-title="输入保姆姓名即可关联保姆"></uni-section>
			<u-search placeholder="请输入保姆姓名" v-model="keywordbaomu" @custom="searchbm"></u-search>
			<uni-section type="line" title="新保姆姓名">
				<u-input placeholder="乙方(新保姆)姓名" v-model="dom.employeeName" border="bottom" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="新保姆身份证">
				<u-input placeholder="乙方(新保姆)身份证" border="bottom" v-model="dom.employeeCarId" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="新保姆电话">
				<u-input placeholder="乙方(新保姆)电话" border="bottom" v-model="dom.employeePhone" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
			<uni-section type="line" title="新保姆地址">
				<u-input placeholder="乙方(新保姆)地址" border="bottom" v-model="dom.employeeAdress" suffixIcon="arrow-right">
				</u-input>
			</uni-section>
		</view>
		<view class="cell-body" v-show="active==2">
			<uni-section type="line" title="新劳务报酬">
				<u-input placeholder="新劳务报酬" border="bottom" v-model="dom.servicePay" type="number"
					suffixIcon="arrow-right">
				</u-input>
			</uni-section>

			<uni-section type="line" title="服务方式">
				<u-input placeholder="选择修改服务方式" border="bottom" v-model="dom.serviceType"
					suffixIcon="arrow-right"></u-input>
			</uni-section>

			<uni-section type="line" title="服务内容(其他)">
				<u-input placeholder="选择修改服务内容(其他)" border="bottom" v-model="dom.serviceContentRemark"
					suffixIcon="arrow-right"></u-input>
			</uni-section>

			<uni-section type="line" title="合同补充条款">
				<u-input placeholder="选填补签时的补充条款" border="bottom" v-model="dom.addendum"
					suffixIcon="arrow-right"></u-input>
			</uni-section>

			<uni-section type="line" title="离职日期" sub-title="点击标题修改时间" @click="showQuitDate = true">
				<u-input v-model="dom.quitDate" placeholder="离职日期" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showQuitDate" mode="date" @cancel="showQuitDate = false"
					:minDate="1577808000000" :closeOnClickOverlay="true" @close="showQuitDate = false"
					@confirm="confirmQuitDate">
				</u-datetime-picker>
			</uni-section>

			<uni-section type="line" title="上户日期" sub-title="点击标题修改时间" @click="showStartDate = true">
				<u-input v-model="dom.startDate" placeholder="上户日期" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showStartDate" mode="date" @cancel="showStartDate = false"
					:minDate="1577808000000" :closeOnClickOverlay="true" @close="showStartDate = false"
					@confirm="confirmStartDate">
				</u-datetime-picker>
			</uni-section>

			<uni-section type="line" title="合同日期" sub-title="点击标题修改时间" @click="showClientDate = true">
				<u-input v-model="dom.clientDate" placeholder="合同日期" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showClientDate" mode="date" @cancel="showClientDate = false"
					:minDate="1577808000000" :closeOnClickOverlay="true" @close="showClientDate = false"
					@confirm="confirmClientDate">
				</u-datetime-picker>
			</uni-section>

			<uni-section type="line" title="截止日期" sub-title="点击标题修改时间" @click="showEndDate = true">
				<u-input v-model="dom.endDate" placeholder="截止日期" border="bottom" suffixIcon="arrow-right"
					:disabled="true"></u-input>
				<u-datetime-picker :show="showEndDate" mode="date" @cancel="showEndDate = false"
					:minDate="1577808000000" :closeOnClickOverlay="true" @close="showEndDate = false"
					@confirm="confirmEndDate">
				</u-datetime-picker>
			</uni-section>

			<u-gap height="40"></u-gap>
		</view>
		<view class="cell-body" v-show="active==3">
			<view v-for="(h,index) in holidayList" style="color: #969799" :key="index"
				:class="dom.holidayId==h.id?'text-box-act':'text-box'" @click="dom.holidayId=h.id">
				<view>周休息天数：<text class="text-show">{{h.weekday}}天</text></view>
				<view>月工资天数：<text class="text-show">{{h.mouthday}}天</text></view>
				<view>节假日休息天数：<text class="text-show">{{h.holiday}}天</text></view>
				<view>国庆休息天数：<text class="text-show">{{h.nationalday==-1?'依法定':h.nationalday+'天'}}</text></view>
				<view>春节休息天数：<text class="text-show">{{h.springday==-1?'依法定':h.springday+'天'}}</text></view>
			
			</view>
		</view>
		
		<u-button :text="nextText" @click="nextStep" color="#1e1848"></u-button>
		<u-popup :show="showPopupbm" @close="showPopupbm = false">
			<u-cell-group v-for="(item,index) in searchList" :key="index">
				<u-cell :title="item.realName+' | '+item.no" isLink @click="changeBaoMu(item)"></u-cell>
			</u-cell-group>

		</u-popup>
	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化
	moment.locale('zh-cn');
	export default {
		data() {
			return {
				showQuitDate: false,
				showStartDate: false,
				showClientDate: false,
				showEndDate: false,
				showPopupbm: false,
				no: '',
				oldContract: {},
				dom: {
					holidayId:'',
					memberCardId: '',
				},
				keywordbaomu: '',
				searchList: [],
				active: 0,
				nextText: '下一步',
				supportId: null,
				serviceType: null,
				serviceContentRemark: null,
				addendum: null,
				holidayList: []
			};
		},
		onLoad(option) {
			this.no = option.no
			this.supportId = option.id || null
			this.getData()
			this.getListContract();
		},
		methods: {
			getListContract() {
				this.http({
					url: 'listContractHoliday',
					method: 'GET',
					success: (res) => {
						this.holidayList = res.data;
					}
			
				})
			},
			confirmQuitDate(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.quitDate = moment(e.value).format('YYYY-MM-DD');
				this.showQuitDate = false;
			},
			confirmStartDate(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.startDate = moment(e.value).format('YYYY-MM-DD');
				this.showStartDate = false;
			},
			confirmClientDate(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.clientDate = moment(e.value).format('YYYY-MM-DD');
				this.showClientDate = false;
			},
			confirmEndDate(e) {
				console.log(moment(e.value).format('YYYY-MM-DD'))
				this.dom.endDate = moment(e.value).format('YYYY-MM-DD');
				this.showEndDate = false;
			},
			nextStep() {
				let activity = this.active;
				if (activity == 0) {
					if (!this.dom.oldEmployeeId) {
						return uni.showToast({
							title: '请将数据补充完整'
						})
					}

				}
				if (activity == 1) {
					if (!this.dom.employeeId) {
						return uni.showToast({
							title: '请将数据补充完整'
						})
					}
				}

				if (this.active <= 2) {
					this.active = this.active + 1
					console.log(this.active)
					if (this.active == 3) {
						this.nextText = this.supportId ? '保存补签合同' : '创建补签合同'
					}
					return
				}
				if (this.active == 3) {
					this.saveSupport()
				}
			},
			saveSupport() {
				if (!this.dom.clientDate || !this.dom.servicePay || !this.dom.quitDate) {
					return uni.showToast({
						title: '请将数据补充完整',
						icon: 'none'
					})
				}
				this.$set(this.dom, 'createDate', null)
				this.$set(this.dom, 'updateDate', null)
				this.$set(this.dom, 'memberSignDate', null)
				this.$set(this.dom, 'employeeSignDate', null)
				this.dom.operator = uni.getStorageSync('employeeId')
				this.http({
					url: 'saveContractSupply',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.dom,
					success: (res) => {
						if (res.code == 0) {
							uni.showToast({
								title: this.supportId ? '保存成功' : '创建成功',
								icon: 'none'

							})
							setTimeout(() => {
								uni.navigateBack()
							}, 1200)
						} else {
							uni.showToast({
								title: res.msg
							})
						}
					}
				})
			},
			searchbm(e) {
				this.http({
					url: 'searchBaomuList',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						realName: e,
						current: 1,
						size: 999
					},
					success: (res) => {
						if (res.data.records.length > 0) {
							this.searchList = res.data.records;
							this.showPopupbm = true;
						} else {
							return uni.showToast({
								title: '找不到对应保姆信息',
								icon: 'none'
							})
						}

					}
				})
				console.log(e)

			},
			changeBaoMu(baomu) {
				if (this.active == 0) {
					this.dom.oldEmployeeId = baomu.id;
					this.dom.oldEmployeeCarId = baomu.idcard;
					this.dom.oldEmployeeName = baomu.realName;
					this.dom.oldEmployeeAdress = baomu.hometown;
					this.dom.oldEmployeePhone = baomu.phone;
				} else {
					this.dom.employeeId = baomu.id;
					this.dom.employeeCarId = baomu.idcard;
					this.dom.employeeName = baomu.realName;
					this.dom.employeeAdress = baomu.hometown;
					this.dom.employeePhone = baomu.phone;
				}

				this.showPopupbm = false;
			},
			getContractSupply() {
				if (!this.supportId) {
					return
				}

				uni.setNavigationBarTitle({
					title: '编辑补签合同'
				})
				this.http({
					outsideUrl: 'https://agentapi.xiaoyujia.com/contractSupply/get',
					method: 'GET',
					hideLoading: true,
					path: this.supportId,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.dom = res.data
							// 兼容旧补签合同，若信息不完整则使用原合同中的数据
							this.dom.serviceType = this.dom.serviceType || this.serviceType
							this.dom.serviceContentRemark = this.dom.serviceContentRemark || this
								.serviceContentRemark
							this.dom.addendum = this.dom.addendum || this.addendum
						}
					}
				})
			},
			getData() {
				this.http({
					url: 'getContractByNo',
					method: 'GET',
					hideLoading: true,
					path: this.no,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: (res) => {
						if (res.code == 0) {
							this.oldContract = res.data
							
							this.dom.oldEmployeeId = res.data.employeeId;
							this.dom.oldEmployeeCarId = res.data.employeeCarId;
							this.dom.oldEmployeeName = res.data.employeeName;
							this.dom.oldEmployeeAdress = res.data.employeeAdress;
							this.dom.oldEmployeePhone = res.data.employeePhone;
							this.dom.clientDate = moment(res.data.createDate).format('YYYY-MM-DD');
							this.dom.endDate = moment(res.data.serviceEndDate).format('YYYY-MM-DD');
							this.dom.memberName = res.data.memberName
							this.dom.memberCardId = res.data.memberCardId
							this.dom.memberPhone = res.data.memberPhone
							this.dom.memberAdress = res.data.memberAdress
							this.dom.memberId = res.data.memberId
							this.dom.agentId = res.data.agentId
							this.dom.agentName = res.data.agentName
							this.dom.orderId = res.data.orderId
							this.dom.contractId = res.data.id
							this.dom.addendum = res.data.addendum
							this.dom.serviceType = res.data.serviceType
							this.dom.serviceContentRemark = res.data.serviceContentRemark
							this.addendum = res.data.addendum
							this.serviceType = res.data.serviceType
							this.serviceContentRemark = res.data.serviceContentRemark
							this.getContractSupply()
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.cell-body {
		padding: 10rpx;
	}
	.text-box-act {
		border: 2rpx solid #ffdd0b;
		background: #ffdd0b;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.text-box {
		border: 2rpx solid #ddd;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.text-show {
		float: right;
		text-align: right;
		color: #323233;
		font-weight: 600;
	}
</style>