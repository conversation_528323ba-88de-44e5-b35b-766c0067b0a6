<template>
	<view class="wallet-container">
		<!-- 钱包余额部分 -->
		<view class="balance-section">
			<!-- 背景装饰 -->
			<view class="balance-bg">
				<view class="bg-circle circle-1"></view>
				<view class="bg-circle circle-2"></view>
				<view class="bg-circle circle-3"></view>
			</view>

			<!-- 余额内容 -->
			<view class="balance-content">
				<view class="balance-header">
					<view class="balance-label">总余额</view>
					<view class="wallet-subtitle">Wallet Balance</view>
				</view>

				<view class="balance-main">
					<view class="balance-amount">
						<text class="currency-symbol">¥</text>
						<text class="amount-value">{{ totalBalance }}</text>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<view class="action-btn recharge-btn" @click="handleRecharge">
						<view class="btn-icon">💰</view>
						<view class="btn-text">充值</view>
					</view>
					<view class="action-btn withdraw-btn" @click="handleWithdraw">
						<view class="btn-icon">💳</view>
						<view class="btn-text">提现</view>
					</view>
					<view class="action-btn transfer-btn" @click="handleTransfer">
						<view class="btn-icon">💸</view>
						<view class="btn-text">从订单转入</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 钱包动态记录部分 -->
		<view class="records-section">
			<view class="records-header">
				<view class="records-title">最新动态</view>
				<view class="records-filter" @click="showFilterModal">
					<text class="filter-text">{{ currentFilter }}</text>
					<text class="filter-icon">▼</text>
				</view>
			</view>

			<!-- 记录列表 -->
			<view class="records-list">
				<view
					class="record-item"
					v-for="(record, index) in filteredRecords"
					:key="index"
					@click="viewRecordDetail(record)"
				>
					<view class="record-icon">
						<view class="icon-wrapper" :class="record.type">
							<text class="icon-text">{{ getRecordIcon(record.type) }}</text>
						</view>
					</view>

					<view class="record-content">
						<view class="record-title">{{ record.title }}</view>
						<view class="record-desc">{{ record.description }}</view>
						<view class="record-time">{{ formatTime(record.createTime) }}</view>
					</view>

					<view class="record-amount">
						<view class="amount-text" :class="record.type">
							{{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount }}
						</view>
						<view class="record-status" :class="record.status">
							{{ getStatusText(record.status) }}
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredRecords.length === 0">
					<view class="empty-icon">📝</view>
					<view class="empty-text">暂无交易记录</view>
					<view class="empty-desc">您的钱包交易记录将在这里显示</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore && filteredRecords.length > 0" @click="loadMoreRecords">
				<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="filter-modal">
				<view class="filter-header">
					<view class="filter-title">筛选条件</view>
					<view class="filter-close" @click="closeFilterModal">✕</view>
				</view>
				<view class="filter-options">
					<view
						class="filter-option"
						:class="{ active: currentFilter === option }"
						v-for="option in filterOptions"
						:key="option"
						@click="selectFilter(option)"
					>
						{{ option }}
					</view>
				</view>
			</view>
		</uni-popup>


	</view>
</template>

<script>
export default {
	data() {
		return {
			// 余额数据
			totalBalance: '12,580.50',
			availableBalance: '10,580.50',
			frozenBalance: '2,000.00',

			// 筛选相关
			currentFilter: '全部',
			filterOptions: ['全部', '订单转入', '支出', '充值', '提现'],

			// 记录数据
			records: [
				{
					id: 1,
					type: 'income',
					title: '订单收入',
					description: '订单编号：XYJ202401150001',
					amount: '580.00',
					createTime: '2024-01-15 14:30:25',
					status: 'completed'
				},
				{
					id: 2,
					type: 'expense',
					title: '提现申请',
					description: '提现到银行卡尾号1234',
					amount: '1000.00',
					createTime: '2024-01-15 10:15:30',
					status: 'processing'
				},
				{
					id: 3,
					type: 'income',
					title: '充值',
					description: '微信支付充值',
					amount: '500.00',
					createTime: '2024-01-14 16:45:12',
					status: 'completed'
				},
				{
					id: 4,
					type: 'expense',
					title: '转账',
					description: '转账给用户张三',
					amount: '200.00',
					createTime: '2024-01-14 09:20:18',
					status: 'completed'
				},
				{
					id: 5,
					type: 'income',
					title: '订单收入',
					description: '订单编号：XYJ202401140002',
					amount: '320.00',
					createTime: '2024-01-13 18:30:45',
					status: 'completed'
				},
				{
					id: 6,
					type: 'expense',
					title: '提现申请',
					description: '提现到支付宝账户',
					amount: '800.00',
					createTime: '2024-01-13 11:25:33',
					status: 'failed'
				}
			],

			// 分页相关
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			loading: false
		}
	},
	computed: {
		// 根据筛选条件过滤记录
		filteredRecords() {
			if (this.currentFilter === '全部') {
				return this.records
			}

			const filterMap = {
				'收入': 'income',
				'支出': 'expense',
				'充值': 'recharge',
				'提现': 'withdraw',
				'转账': 'transfer'
			}

			const filterType = filterMap[this.currentFilter]
			if (filterType) {
				return this.records.filter(record => {
					if (filterType === 'income' || filterType === 'expense') {
						return record.type === filterType
					}
					return record.title.includes(this.currentFilter)
				})
			}

			return this.records
		}
	},
	onLoad() {
		this.loadWalletData()
		this.loadRecords()
	},
	methods: {
		// 加载钱包数据
		async loadWalletData() {
			try {
				// 这里应该调用实际的API
				// const res = await this.http({
				//     url: 'getWalletBalance',
				//     method: 'GET'
				// })
				// if (res.code === 0) {
				//     this.totalBalance = res.data.totalBalance
				//     this.availableBalance = res.data.availableBalance
				//     this.frozenBalance = res.data.frozenBalance
				// }

				// 模拟数据加载
				console.log('钱包数据加载完成')
			} catch (error) {
				uni.showToast({
					title: '加载钱包数据失败',
					icon: 'none'
				})
			}
		},

		// 加载交易记录
		async loadRecords() {
			try {
				this.loading = true

				// 这里应该调用实际的API
				// const res = await this.http({
				//     url: 'getWalletRecords',
				//     method: 'GET',
				//     data: {
				//         page: this.currentPage,
				//         pageSize: this.pageSize
				//     }
				// })
				// if (res.code === 0) {
				//     if (this.currentPage === 1) {
				//         this.records = res.data.records
				//     } else {
				//         this.records.push(...res.data.records)
				//     }
				//     this.hasMore = res.data.hasMore
				// }

				// 模拟数据加载
				setTimeout(() => {
					this.loading = false
					console.log('交易记录加载完成')
				}, 1000)
			} catch (error) {
				this.loading = false
				uni.showToast({
					title: '加载交易记录失败',
					icon: 'none'
				})
			}
		},

		// 加载更多记录
		loadMoreRecords() {
			if (this.loading || !this.hasMore) return

			this.currentPage++
			this.loadRecords()
		},

		// 充值操作
		handleRecharge() {
			uni.navigateTo({
				url: '/pages-other/wallet/recharge'
			})
		},

		// 提现操作
		handleWithdraw() {
			uni.navigateTo({
				url: '/pages-other/income/rollout'
			})
		},

		// 转账操作
		handleTransfer() {
			uni.navigateTo({
				url: '/pages-other/wallet/transfer'
			})
		},

		// 显示筛选弹窗
		showFilterModal() {
			this.$refs.filterPopup.open()
		},

		// 关闭筛选弹窗
		closeFilterModal() {
			this.$refs.filterPopup.close()
		},

		// 选择筛选条件
		selectFilter(filter) {
			this.currentFilter = filter
			this.closeFilterModal()
		},

		// 查看记录详情
		viewRecordDetail(record) {
			uni.navigateTo({
				url: `/pages-other/wallet/recordDetail?id=${record.id}`
			})
		},

		// 获取记录图标
		getRecordIcon(type) {
			const iconMap = {
				'income': '💰',
				'expense': '💸',
				'recharge': '💳',
				'withdraw': '🏦',
				'transfer': '💱'
			}
			return iconMap[type] || '📝'
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'processing': '处理中',
				'failed': '失败',
				'pending': '待处理'
			}
			return statusMap[status] || '未知'
		},

		// 格式化时间
		formatTime(timeStr) {
			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date

			// 一分钟内
			if (diff < 60000) {
				return '刚刚'
			}
			// 一小时内
			if (diff < 3600000) {
				return `${Math.floor(diff / 60000)}分钟前`
			}
			// 一天内
			if (diff < 86400000) {
				return `${Math.floor(diff / 3600000)}小时前`
			}
			// 超过一天
			return timeStr.split(' ')[0]
		}
	}
}
</script>

<style scoped>
.wallet-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
	padding-bottom: 40rpx;
}

/* 钱包余额部分 */
.balance-section {
	position: relative;
	padding: 60rpx 32rpx 40rpx;
	overflow: hidden;
}

.balance-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: -100rpx;
	right: -50rpx;
}

.circle-2 {
	width: 150rpx;
	height: 150rpx;
	top: 200rpx;
	left: -75rpx;
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	bottom: 50rpx;
	right: 100rpx;
}

.balance-content {
	position: relative;
	z-index: 2;
}

.balance-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.wallet-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.wallet-subtitle {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	letter-spacing: 1rpx;
}

.balance-main {
	text-align: center;
	margin-bottom: 40rpx;
}

.balance-amount {
	display: flex;
	align-items: baseline;
	justify-content: center;
	margin-bottom: 12rpx;
}

.currency-symbol {
	font-size: 36rpx;
	color: #ffffff;
	margin-right: 8rpx;
}

.amount-value {
	font-size: 72rpx;
	font-weight: 700;
	color: #ffffff;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

.balance-label {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
}

.balance-details {
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 40rpx;
	backdrop-filter: blur(10rpx);
}

.detail-item {
	flex: 1;
	text-align: center;
}

.detail-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.detail-label {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.detail-divider {
	width: 1rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.3);
	margin: 0 40rpx;
}

.action-buttons {
	display: flex;
	justify-content: space-around;
	gap: 24rpx;
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24rpx 16rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 记录部分 */
.records-section {
	background: #ffffff;
	border-radius: 32rpx 32rpx 0 0;
	margin-top: -20rpx;
	padding: 32rpx;
	min-height: 60vh;
}

.records-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.records-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2c3e50;
}

.records-filter {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 1rpx solid #e9ecef;
}

.filter-text {
	font-size: 26rpx;
	color: #6c757d;
	margin-right: 8rpx;
}

.filter-icon {
	font-size: 20rpx;
	color: #6c757d;
	transition: transform 0.3s ease;
}

.records-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.record-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #ffffff;
	border-radius: 16rpx;
	border: 1rpx solid #f1f3f4;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.record-item:active {
	transform: translateY(2rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

.record-icon {
	margin-right: 24rpx;
}

.icon-wrapper {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.icon-wrapper.income {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}

.icon-wrapper.expense {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.record-content {
	flex: 1;
}

.record-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 8rpx;
}

.record-desc {
	font-size: 24rpx;
	color: #6c757d;
	margin-bottom: 8rpx;
}

.record-time {
	font-size: 22rpx;
	color: #95a5a6;
}

.record-amount {
	text-align: right;
}

.amount-text {
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.amount-text.income {
	color: #52c41a;
}

.amount-text.expense {
	color: #ff4d4f;
}

.record-status {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	display: inline-block;
}

.record-status.completed {
	background: #f6ffed;
	color: #52c41a;
}

.record-status.processing {
	background: #fff7e6;
	color: #fa8c16;
}

.record-status.failed {
	background: #fff2f0;
	color: #ff4d4f;
}

.record-status.pending {
	background: #f0f5ff;
	color: #1890ff;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #6c757d;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #95a5a6;
	line-height: 1.5;
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx;
}

.load-text {
	font-size: 28rpx;
	color: #6c757d;
}

/* 筛选弹窗 */
.filter-modal {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid #f1f3f4;
}

.filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
}

.filter-close {
	font-size: 32rpx;
	color: #6c757d;
	padding: 8rpx;
}

.filter-options {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.filter-option {
	padding: 20rpx;
	text-align: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #6c757d;
	transition: all 0.3s ease;
}

.filter-option.active {
	background: #667eea;
	color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.balance-section {
		padding: 40rpx 24rpx 32rpx;
	}

	.wallet-title {
		font-size: 36rpx;
	}

	.amount-value {
		font-size: 64rpx;
	}

	.records-section {
		padding: 24rpx;
	}

	.records-title {
		font-size: 32rpx;
	}

	.record-item {
		padding: 20rpx;
	}

	.icon-wrapper {
		width: 72rpx;
		height: 72rpx;
		font-size: 28rpx;
	}

	.record-title {
		font-size: 28rpx;
	}

	.amount-text {
		font-size: 28rpx;
	}
}
</style>