<template>
	<view class="wallet-container">
		<!-- 钱包余额部分 -->
		<view class="balance-section">
			<!-- 背景装饰 -->
			<view class="balance-bg">
				<view class="bg-circle circle-1"></view>
				<view class="bg-circle circle-2"></view>
				<view class="bg-circle circle-3"></view>
			</view>

			<!-- 余额内容 -->
			<view class="balance-content">
				<view class="balance-header">
					<view class="balance-label">总余额</view>
					<view class="wallet-subtitle">Wallet Balance</view>
				</view>

				<view class="balance-main">
					<view class="balance-amount">
						<text class="currency-symbol">¥</text>
						<text class="amount-value">{{ totalBalance }}</text>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<view class="action-btn recharge-btn" @click="handleRecharge">
						<view class="btn-icon">💰</view>
						<view class="btn-text">充值</view>
					</view>
					<view class="action-btn withdraw-btn" @click="handleWithdraw">
						<view class="btn-icon">💳</view>
						<view class="btn-text">提现</view>
					</view>
					<view class="action-btn transfer-btn" @click="handleTransfer">
						<view class="btn-icon">💸</view>
						<view class="btn-text">从订单转入</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 钱包动态记录部分 -->
		<view class="records-section">
			<view class="records-header">
				<view class="records-title">最新动态</view>
				<view class="records-filter" @click="showFilterModal">
					<text class="filter-text">{{ currentFilter }}</text>
					<text class="filter-icon">▼</text>
				</view>
			</view>
			<view style="display: flex;">
			<view style="color: darkgrey;font-size: 18rpx;margin-top: 10rpx;margin-left: auto;">支出：￥0.00&nbsp;&nbsp;&nbsp;充值￥0.00</view>
			</view>
			<view class="tab-menu" v-if="this.currentFilter=='支出'">
				<u-sticky>
					<u-tabs :list="menuList" @click="choiceMenu" :current="current1" lineWidth="22" lineHeight="8"
						:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
				        color: '#1e1848',
				        fontWeight: 'bold',
				        transform: 'scale(1.2)'
				    }" :inactiveStyle="{
				        color: '#333',
				        transform: 'scale(1.05)'
				    }" itemStyle="padding-left: 15px; height: 45px;">
					</u-tabs>
				</u-sticky>
			</view>

			<!-- 记录列表 -->
			<view class="records-list">
				<view
					class="record-item"
					v-for="(record, index) in filteredRecords"
					:key="index"
					@click="viewRecordDetail(record)"
				>
					<view class="record-icon">
						<view class="icon-wrapper" :class="record.type" v-if="!record.iconUrl">
							<text class="icon-text">{{ getRecordIcon(record.type) }}</text>
						</view>
						<view style="margin: auto;" v-if="record.iconUrl">
							<image style="width: 80rpx; height: 80rpx;border-radius: 50%;"
								:src="record.iconUrl"></image>
						</view>
					</view>

					<view class="record-content">
						<view class="record-title">{{ record.title }}</view>
						<view class="record-desc">{{ record.description }}</view>
						<view class="record-time">{{ formatTime(record.createTime) }}</view>
					</view>

					<view class="record-amount">
						<view class="amount-text" :class="record.type">
							{{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount }}
						</view>
						<!-- <view class="record-status" :class="record.status">
							{{ getStatusText(record.status) }}
						</view> -->
						<view class="record-status" :class="record.title=='充值'&&record.payStatus==0?'failed':record.status">
							{{ record.title=='充值'&&record.payStatus==0?'未支付':getStatusText(record.status) }}
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredRecords.length === 0">
					<view class="empty-icon">📝</view>
					<view class="empty-text">暂无交易记录</view>
					<view class="empty-desc">您的钱包交易记录将在这里显示</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore && filteredRecords.length > 0" @click="loadMoreRecords">
				<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="filter-modal">
				<view class="filter-header">
					<view class="filter-title">筛选条件</view>
					<view class="filter-close" @click="closeFilterModal">✕</view>
				</view>
				<view class="filter-options">
					<view
						class="filter-option"
						:class="{ active: currentFilter === option }"
						v-for="option in filterOptions"
						:key="option"
						@click="selectFilter(option)"
					>
						{{ option }}
					</view>
				</view>
			</view>
		</uni-popup>


	</view>
</template>

<script>
export default {
	data() {
		return {
			// 余额数据
			totalBalance: '0',
			menuList: [{
					index: 0,
					name: '全部',
					value: null
			},{
					index: 0,
					name: '线索费',
					value: null
			},{
					index: 0,
					name: '合同保险',
					value: null
			},{
					index: 0,
					name: '品牌费',
					value: null
			}],
			lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
			// 筛选相关
			currentFilter: '全部',
			filterOptions: ['全部', '订单转入', '支出', '充值', '提现'],
			changeDescribe: '',
			// 记录数据
			records: [],
			current1: 0,
			// 分页相关
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			loading: false
		}
	},
	computed: {
		// 根据筛选条件过滤记录
		filteredRecords() {
			// 由于已经在API层面进行筛选，这里直接返回records
			return this.records
		}
	},
	onLoad() {
		this.loadWalletData()
		this.loadRecords()
	},
	onShow() {
		// 页面显示时刷新数据
		this.refreshData()
	},
	onPullDownRefresh() {
		// 下拉刷新
		this.refreshData()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1000)
	},
	onReachBottom() {
		// 上拉加载更多
		this.loadMoreRecords()
	},
	methods: {
		// 刷新数据
		refreshData() {
			this.currentPage = 1
			this.hasMore = true
			this.loadWalletData()
			this.loadRecords()
		},

		// 加载钱包数据
		async loadWalletData() {
			try {
				// 获取门店资金统计
				this.http({
					url: 'getWalletBalance',
					method: 'GET',
					data: {
						storeId: uni.getStorageSync("storeId")
					},
					success: res => {
						if (res.code === 0) {
							// 格式化金额显示
							this.totalBalance = this.formatAmount(res.data.totalBalance || 0)
						} else {
							uni.showToast({
								title: res.msg || '获取余额失败',
								icon: 'none'
							})
						}
					},
					fail: error => {
						console.error('获取钱包数据失败:', error)
						uni.showToast({
							title: '网络错误，请重试',
							icon: 'none'
						})
					}
				})
			} catch (error) {
				console.error('loadWalletData error:', error)
				uni.showToast({
					title: '加载钱包数据失败',
					icon: 'none'
				})
			}
		},

		// 加载交易记录
		async loadRecords() {
			try {
				this.loading = true

				// 获取钱包交易记录
				const requestData = {
					storeId: uni.getStorageSync("storeId"),
					page: this.currentPage,
					pageSize: this.pageSize,
					changeDescribe: this.changeDescribe
				}

				// 如果有筛选条件，添加changeType参数
				const changeTypeMap = {
					'充值': 1,
					'订单转入': 2,
					'支出': 3,
					'提现': 4
				}

				if (this.currentFilter !== '全部' && changeTypeMap[this.currentFilter]) {
					requestData.changeType = changeTypeMap[this.currentFilter]
				}

				this.http({
					outsideUrl: 'http://localhost:15012/getWalletRecords',
					// url: 'getWalletRecords',
					method: 'GET',
					data: requestData,
					success: res => {
						this.loading = false
						if (res.code === 0) {
							const newRecords = this.formatRecords(res.data.records || [])

							if (this.currentPage === 1) {
								this.records = newRecords
							} else {
								this.records.push(...newRecords)
							}

							// 根据接口返回的hasMore字段判断是否还有更多数据
							this.hasMore = res.data.hasMore || false
						} else {
							uni.showToast({
								title: res.msg || '获取交易记录失败',
								icon: 'none'
							})
						}
					},
					fail: error => {
						this.loading = false
						console.error('获取交易记录失败:', error)
						uni.showToast({
							title: '网络错误，请重试',
							icon: 'none'
						})
					}
				})
			} catch (error) {
				this.loading = false
				console.error('loadRecords error:', error)
				uni.showToast({
					title: '加载交易记录失败',
					icon: 'none'
				})
			}
		},

		// 加载更多记录
		loadMoreRecords() {
			if (this.loading || !this.hasMore) return

			this.currentPage++
			this.loadRecords()
		},

		// 充值操作
		handleRecharge() {
			uni.navigateTo({
				url: '/pages-other/wallet/recharge'
			})
		},

		// 提现操作
		handleWithdraw() {
			uni.showToast({
				title: '暂未开放，敬请期待！',
				icon: 'none'
			})
			// uni.navigateTo({
			// 	url: '/pages-other/income/rollout'
			// })
		},

		// 从订单转入操作
		handleTransfer() {
			uni.showToast({
				title: '暂未开放，敬请期待！',
				icon: 'none'
			})
			// 跳转到可提现订单页面
			// uni.navigateTo({
			// 	url: '/pages-other/income/rollout'
			// })
		},

		// 获取可提现订单（用于从订单转入）
		getCanWithdrawalOrder() {
			this.http({
				url: 'getCanWithdrawalOrder',
				method: 'GET',
				data: {
					storeId: uni.getStorageSync("selectStoreId")
				},
				success: res => {
					if (res.code === 0) {
						// 处理可提现订单数据
						console.log('可提现订单:', res.data)
					} else {
						uni.showToast({
							title: res.msg || '获取可提现订单失败',
							icon: 'none'
						})
					}
				},
				fail: error => {
					console.error('获取可提现订单失败:', error)
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					})
				}
			})
		},

		// 显示筛选弹窗
		showFilterModal() {
			this.$refs.filterPopup.open()
		},

		// 关闭筛选弹窗
		closeFilterModal() {
			this.$refs.filterPopup.close()
		},

		// 选择筛选条件
		selectFilter(filter) {
			this.changeDescribe = ''
			this.currentFilter = filter
			this.closeFilterModal()

			// 重新加载数据
			this.currentPage = 1
			this.hasMore = true
			this.records = []
			this.loadRecords()
		},

		// 查看记录详情
		viewRecordDetail(record) {
			if(record.title=='合同保险支出'||record.title=='合同保险返还'){
				uni.navigateTo({
					url: `/pages-work/operations/contract?search=`+record.expensesVoucher
				})
			}
			if(record.title=='线索费支出'){
				uni.navigateTo({
					url: `/pages-work/operations/clew/clewIndex?id=`+record.expensesVoucher
				})
			}
		},

		// 获取记录图标
		getRecordIcon(type) {
			const iconMap = {
				'income': '💰',
				'expense': '💸'
			}
			return iconMap[type] || '📝'
		},
		choiceMenu(e) {
			if(e.name=='全部'){
				this.changeDescribe = ''
			}else{
				this.changeDescribe = e.name
			}
			this.loadRecords()
		},
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'processing': '处理中',
				'failed': '失败',
				'pending': '待处理'
			}
			return statusMap[status] || '未知'
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return ''

			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date

			// 一分钟内
			if (diff < 60000) {
				return '刚刚'
			}
			// 一小时内
			if (diff < 3600000) {
				return `${Math.floor(diff / 60000)}分钟前`
			}
			// 一天内
			if (diff < 86400000) {
				return `${Math.floor(diff / 3600000)}小时前`
			}
			// 超过一天
			return timeStr.split(' ')[0]
		},

		// 格式化金额
		formatAmount(amount) {
			if (!amount && amount !== 0) return '0.00'
			return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
		},

		// 格式化交易记录
		formatRecords(records) {
			return records.map(record => {
				return {
					id: record.id,
					type: this.getRecordType(record.changeType),
					title: this.getRecordTitle(record),
					description: this.getRecordDescription(record),
					expensesVoucher: record.expensesVoucher,
					amount: this.formatAmount(Math.abs(record.changeAmount || 0)),
					createTime: record.createTime,
					status: 'completed', // 钱包记录都是已完成状态
					serialNumber: record.serialNumber,
					iconUrl: record.iconUrl,
					operator: record.operate,
					changeAmount: record.changeAmount,
					afterChangeAmount: record.afterChangeAmount,
					payStatus: record.payStatus
				}
			})
		},

		// 获取记录类型
		getRecordType(changeType) {
			// 根据接口文档的changeType映射前端类型
			// 1-充值，2-订单转入，3-支出，4-提现
			const typeMap = {
				1: 'income',   // 充值
				2: 'income',   // 订单转入
				3: 'expense',  // 支出
				4: 'expense'   // 提现
			}
			return typeMap[changeType] || 'income'
		},

		// 获取记录标题
		getRecordTitle(record) {
			// 根据changeType生成标题
			const typeMap = {
				1: '充值',
				2: '订单转入',
				3: '支出',
				4: '提现',
				5: '返还'
			}

			let title = typeMap[record.changeType] || '交易记录'

			// 如果是支出，添加支出类型
			if (record.changeType === 3 && record.changeDescribe) {
				title = `${record.changeDescribe}支出`
			}
			// 如果是返还，添加返还类型
			if (record.changeType === 5 && record.changeDescribe) {
				title = `${record.changeDescribe}返还`
			}

			return title
		},

		// 获取记录描述
		getRecordDescription(record) {
			// 根据不同的changeType生成描述
			switch (record.changeType) {
				case 1: // 充值
					return record.rechargeBillNo ? `充值订单：${record.rechargeBillNo}` : '账户充值'
				case 2: // 订单转入
					return record.toTransferIntoBillNo ? `订单编号：${record.toTransferIntoBillNo}` : '订单收入转入'
				case 3: // 支出
					if (record.expensesVoucher) {
						return `凭证：${record.expensesVoucher}`
					}
					return record.changeDescribe ? `${record.changeDescribe}费用支出` : '账户支出'
				case 4: // 提现
					return record.withdrawalId ? `提现申请ID：${record.withdrawalId}` : '账户提现'
				case 5: // 返还
					if (record.expensesVoucher) {
						return `凭证：${record.expensesVoucher}`
					}
					return record.changeDescribe ? `${record.changeDescribe}费用返还` : '费用返还'
				default:
					return `流水号：${record.serialNumber || '未知'}`
			}
		},

		// 映射记录状态
		mapRecordStatus(status) {
			const statusMap = {
				'0': 'pending',    // 待审核
				'1': 'completed',  // 已完成
				'2': 'failed',     // 已拒绝/失败
				'3': 'processing', // 处理中
				'pending': 'pending',
				'completed': 'completed',
				'failed': 'failed',
				'processing': 'processing'
			}
			return statusMap[status] || 'pending'
		},

		// 重试加载数据
		retryLoadData() {
			uni.showLoading({
				title: '重新加载中...'
			})

			this.refreshData()

			setTimeout(() => {
				uni.hideLoading()
			}, 2000)
		},

		// 检查网络状态
		checkNetworkStatus() {
			uni.getNetworkType({
				success: (res) => {
					if (res.networkType === 'none') {
						uni.showToast({
							title: '网络连接失败，请检查网络设置',
							icon: 'none',
							duration: 3000
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.wallet-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #667eea 0%, #f1f3f4 100%);
	padding-bottom: 40rpx;
}

/* 钱包余额部分 */
.balance-section {
	position: relative;
	padding: 60rpx 32rpx 40rpx;
	overflow: hidden;
}

.balance-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: -100rpx;
	right: -50rpx;
}

.circle-2 {
	width: 150rpx;
	height: 150rpx;
	top: 200rpx;
	left: -75rpx;
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	bottom: 50rpx;
	right: 100rpx;
}

.balance-content {
	position: relative;
	z-index: 2;
}

.balance-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.wallet-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.wallet-subtitle {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	letter-spacing: 1rpx;
}

.balance-main {
	text-align: center;
	margin-bottom: 40rpx;
}

.balance-amount {
	display: flex;
	align-items: baseline;
	justify-content: center;
	margin-bottom: 12rpx;
}

.currency-symbol {
	font-size: 36rpx;
	color: #ffffff;
	margin-right: 8rpx;
}

.amount-value {
	font-size: 72rpx;
	font-weight: 700;
	color: #ffffff;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

.balance-label {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
}

.balance-details {
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 40rpx;
	backdrop-filter: blur(10rpx);
}

.detail-item {
	flex: 1;
	text-align: center;
}

.detail-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.detail-label {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.detail-divider {
	width: 1rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.3);
	margin: 0 40rpx;
}

.action-buttons {
	display: flex;
	justify-content: space-around;
	gap: 24rpx;
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24rpx 16rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 记录部分 */
.records-section {
	background: #ffffff;
	border-radius: 32rpx 32rpx 0 0;
	margin-top: -20rpx;
	padding: 32rpx;
	min-height: 60vh;
}

.records-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.records-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2c3e50;
}

.records-filter {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 1rpx solid #e9ecef;
}

.filter-text {
	font-size: 26rpx;
	color: #6c757d;
	margin-right: 8rpx;
}

.filter-icon {
	font-size: 20rpx;
	color: #6c757d;
	transition: transform 0.3s ease;
}

.records-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.record-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #ffffff;
	border-radius: 16rpx;
	border: 1rpx solid #f1f3f4;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.record-item:active {
	transform: translateY(2rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

.record-icon {
	margin-right: 24rpx;
}

.icon-wrapper {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.icon-wrapper.income {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}

.icon-wrapper.expense {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.record-content {
	flex: 1;
}

.record-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 8rpx;
}

.record-desc {
	font-size: 24rpx;
	color: #6c757d;
	margin-bottom: 8rpx;
}

.record-time {
	font-size: 22rpx;
	color: #95a5a6;
}

.record-amount {
	text-align: right;
}

.amount-text {
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.amount-text.income {
	color: #52c41a;
}

.amount-text.expense {
	color: #ff4d4f;
}

.record-status {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	display: inline-block;
}

.record-status.completed {
	background: #f6ffed;
	color: #52c41a;
}

.record-status.processing {
	background: #fff7e6;
	color: #fa8c16;
}

.record-status.failed {
	background: #fff2f0;
	color: #ff4d4f;
}

.record-status.pending {
	background: #f0f5ff;
	color: #1890ff;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #6c757d;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #95a5a6;
	line-height: 1.5;
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx;
}

.load-text {
	font-size: 28rpx;
	color: #6c757d;
}

/* 筛选弹窗 */
.filter-modal {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid #f1f3f4;
}

.filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
}

.filter-close {
	font-size: 32rpx;
	color: #6c757d;
	padding: 8rpx;
}

.filter-options {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.filter-option {
	padding: 20rpx;
	text-align: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #6c757d;
	transition: all 0.3s ease;
}

.filter-option.active {
	background: #667eea;
	color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.balance-section {
		padding: 40rpx 24rpx 32rpx;
	}

	.wallet-title {
		font-size: 36rpx;
	}

	.amount-value {
		font-size: 64rpx;
	}

	.records-section {
		padding: 24rpx;
	}

	.records-title {
		font-size: 32rpx;
	}

	.record-item {
		padding: 20rpx;
	}

	.icon-wrapper {
		width: 72rpx;
		height: 72rpx;
		font-size: 28rpx;
	}

	.record-title {
		font-size: 28rpx;
	}

	.amount-text {
		font-size: 28rpx;
	}
}
</style>