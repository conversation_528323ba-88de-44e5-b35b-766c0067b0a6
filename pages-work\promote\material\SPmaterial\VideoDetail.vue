<template>
	<view>
		<u-notify ref="uNotify" message="Hi uView"></u-notify>
		<view class="" v-for="(item,index) in dataList" :key="index">
			<uni-card :title="item.title" :sub-title="'创建时间:'+item.createTime">
				<u-text class="" :text="'视频分类:'+item.cateName"></u-text>
				<u-text class="lh35" v-if="item.description" :text="'视频描述:'+item.description"></u-text>
				<view class="w10 mg-at">
					<video :src="item.videoUrl"></video>
				</view>
				<view class="flac-row-b">
					<u-button customStyle="margin: 20rpx 20rpx 20rpx 0;color:#f6cc70" text="打开小程序" color="#1e1848" @click="openxcx(item)"></u-button>
					<u-button customStyle="margin: 20rpx 0 20rpx 20rpx" text="复制视频链接" color="#1e1848" plain @click="fuzhi(item.videoUrl)"></u-button>
					<u-button customStyle="margin: 20rpx 0 20rpx 20rpx" text="下载视频" color="#1e1848" plain @click="download(item.videoUrl)"></u-button>
				</view>
			</uni-card>
		</view>
		<u-modal :show="showModal" title="长按识别小程序，进去后转发" @confirm="confirm">
			<view>
				<u-image :src="imgCode" height="900rpx"></u-image>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cateId: '',
				dataList: [],
				videoUrl: '',
				showModal: false,
				no: uni.getStorageSync('account'),
				imgCode: '',
				eid: uni.getStorageSync('employeeId')
			};
		},
		onLoad(option) {
			this.cateId = option.cateId
			this.getList()
		},
		methods: {
			async  download(url){
				// #ifdef H5
				return uni.showToast({
					title:'请进入小程序下载视频',
					icon:'none'
				})
				// #endif
				// #ifdef APP-PLUS || H5 || MP-WEIXIN
				    await uni.showLoading({
						title: '下载中,请勿退出界面',
						mask:true
					})
				var parts = url.split("/");
				var lastPart = parts[parts.length - 1];
					let filePath = wx.env.USER_DATA_PATH + '/' + lastPart;
				const downloadTask =  uni.downloadFile({
						url: url,//临时路径
						filePath: filePath,//指定路径，去文件管理的微信中查看
						success: (res) => {
						   // 2 成功下载后而且状态码为200时将视频保存到本地系统
						   if (res.statusCode === 200) {
							  uni.saveVideoToPhotosAlbum({
							  // filePath: res.tempFilePath,
							  filePath: filePath,
							  success(res) {
								 console.log(res, 'success')
							  },
							  fail(error) {
								 console.log(error, 'error')
							  }
						   })
						   uni.hideLoading();
						   // 提示用户下载成功
						   uni.showToast({
							  title: "下载成功",
							  icon: "success"
						   });
					     }
						 // 如果该资源不可下载或文件格式出错则提示用户
						 else {
							uni.showToast({
								title: "资源格式错误，请联系管理员"
							});
						 }
						},
						fail: (err) => {
							console.log(err)
							// 下载失败提醒
							uni.hideLoading();
							if(err.errMsg === "downloadFile:fail exceed max file size"){
								uni.showToast({
									title: "文件视频过大,请复制链接去浏览器下载",
									icon:'none'
								})
							} else {
								uni.showToast({
									title: "下载失败"
								})
							}
							
						}
					})
					downloadTask.onProgressUpdate((res) => {
						 this.$refs.uNotify.warning('视频下载进度:' + res.progress+'%')
						// 满足测试条件，取消下载任务。
						// if (res.progress > 50) {
						// 	downloadTask.abort();
						// }
					});
					
					
				// #endif
				
			},
			fuzhi(item) {
				
				uni.showModal({
					content: item, //模板中提示的内容
					confirmText: '复制内容',
					success: (res) => { //点击复制内容的后调函数
						if (res.confirm) {
							uni.setClipboardData({
							  data: item, //要被复制的内容
							  success: () => { //复制成功的回调函数
							    uni.showToast({ //提示
							      title: `复制成功`,
							      icon: 'success'
							    })
							  }
							}, true);
							// // #ifdef H5 
							// let textarea = document.createElement("textarea")
							// textarea.value = item
							// textarea.readOnly = "readOnly"
							// document.body.appendChild(textarea)
							// textarea.select() // 选中文本内容
							// textarea.setSelectionRange(0, info.length)
							// uni.showToast({ //提示
							// 	title: '复制成功'
							// })
							// result = document.execCommand("copy")
							// textarea.remove()
							// // #endif
						} else {
							console.log('取消')
						}
					}
				});
				// let param = {
				// 	'source': 'appminiprogram',
				// 	'path': 'pages/content/newsvideo',
				// 	'query': 'id=' + item.id + '&tg=' + this.no,
				// 	'creater': this.no
				// }
				// uni.request({
				// 	url: 'https://api.xiaoyujia.com/openapi/insertQiyeDataLink',
				// 	method: 'POST',
				// 	data: param,
				// 	success: (res) => {
				// 		if (res.data.code == 0) {
				// 			// window.location.href = res.data.data
				// 			let info = res.data.data
				// 			uni.showModal({
				// 				content: info, //模板中提示的内容
				// 				confirmText: '复制内容',
				// 				success: (res) => { //点击复制内容的后调函数
				// 					if (res.confirm) {
				// 						let result
				// 						// #ifndef H5
				// 						//uni.setClipboardData方法就是讲内容复制到粘贴板
				// 						uni.setClipboardData({
				// 							data: info, //要被复制的内容
				// 							success: () => { //复制成功的回调函数
				// 								uni.showToast({ //提示
				// 									title: '复制成功'
				// 								})
				// 							}
				// 						});
				// 						// #endif
				// 						// #ifdef H5 
				// 						let textarea = document.createElement("textarea")
				// 						textarea.value = info
				// 						textarea.readOnly = "readOnly"
				// 						document.body.appendChild(textarea)
				// 						textarea.select() // 选中文本内容
				// 						textarea.setSelectionRange(0, info.length)
				// 						uni.showToast({ //提示
				// 							title: '复制成功'
				// 						})
				// 						result = document.execCommand("copy")
				// 						textarea.remove()
				// 						// #endif
				// 					} else {
				// 						console.log('取消')
				// 					}
				// 				}
				// 			});
				// 		} else {
				// 			uni.showToast({
				// 				title: '链接生成失败',
				// 				icon: 'error'
				// 			})
				// 		}
				// 	}
				// })
			},
			openxcx(item) {
				// #ifdef MP-WEIXIN
				uni.navigateToMiniProgram({
					appId: 'wx9272efff9f958cc0',
					path: 'pages/content/newsvideo?id='+item.id+'&no='+this.no,
					envVersion: "release",
					success: res => {},
					fail: err => {
						console.log(err);
					}
				});
				// #endif
				
				// #ifdef H5 
				let param = {
					"source": "appminiprogram",
					"path": "pages/content/newsvideo",
					"type": "1",
					"scene": "id/" + item.id + "*no/" + this.no,
					"title": "营销视频文章",
				}
				uni.request({
					url: 'https://api.xiaoyujia.com/product/getEmployeePoster',
					method: 'POST',
					data: param,
					success: (res) => {
						if (res.data.code == 0) {
							this.imgCode = res.data.data;
						} else {
							this.imgCode = '';
							uni.showToast({
								title: '二维码生成错误',
								icon: 'error'
							})
						}
						this.showModal = true;
					}
				})
				// #endif
			},
			confirm() {
				this.showModal = false;
			},
			getList() {
				uni.request({
					url: 'https://api.xiaoyujia.com/employee/getUploadVideoList',
					data: {
						cateId: this.cateId
					},
					method: 'POST',
					success: (res) => {
						if (res.data.data.length <= 0) {
							return uni.showToast({
								title: '该栏目下没有内容'
							})
						}
						let p = res.data.data;
						p.forEach(v => {
							uni.request({
								url: 'https://api.xiaoyujia.com/employee/getVideoInfo',
								data: {
									videoId: v.videoId
								},
								success: (res) => {
									if (res.data.code === 0) {
										v.videoUrl = res.data.data.playInfoList[0].playURL
										v.format = res.data.data.playInfoList[0].format
										// console.log(v)
										this.dataList.push(v)
									}
								}
							})
						})
					}
				})
			},
			clickDownLoad(videoUrl, title, format) {
				// ***判断是否在微信内打开，如果是，提示用户在外部浏览器打开，否则运行下载代码***
				var ua = navigator.userAgent.toLowerCase();
				if (ua.match(/MicroMessenger/i) == "micromessenger") {
					alert("请在右上角...点击【在浏览器打开】")
				} else {
					this.saveVideo(videoUrl, title, format) //调用下载函数
					this.showVideo = false
					uni.showLoading({
						title: "正在下载"
					});
				}
			},
			saveVideo(videoUrl, title, format) {
				let that = this;
				let url = 'http://vi.xiaoyujia.com/sv/4fd0010b-182d4d0e763/4fd0010b-182d4d0e763.mp4';
				//**调用download.js下载，下载速度相对快一些**
				let downjs = require("../../../static/download"); //引用当前目录下的自定义函数
				let x = new XMLHttpRequest();
				x.open("GET", url, true);
				x.responseType = 'blob';
				x.onload = function(e) {
					downjs(x.response, title + '.' + format, "video")
					uni.hideLoading()
					that.showVideo = true
				};
				x.send();
			},
			getVideoInfoList() {
				this.dataList.forEach(v => {
					uni.request({
						url: 'https://api.xiaoyujia.com/employee/getVideoInfo',
						data: {
							videoId: v.videoId
						},
						success: (res) => {
							if (res.data.code === 0) {
								v.videoUrl = res.data.data.playInfoList[0].playURL
							}
						}
					})
				})
			}
		}
	}
</script>
<style scoped>
	page {
		background-color: #f4f2f3 !important;
	}


	/deep/.uni-card {
		/* background-color: #fffff3 !important; */
		line-height: 40rpx;
	}

	/deep/.uni-card--border {
		border-radius: 16rpx !important;
	}
</style>
