<template>
	<view v-if="dom">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 备注弹窗 -->
		<u-popup :show="popupShowEdit" :round="10" mode="bottom" @close="popupShowEdit=false" :closeable="true">
			<view class="f20 fb text-c lh60">编辑学员资料</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1100rpx;">
				<view class="tab-title">
					<text>学员姓名</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="text" v-model="dom.realName"
							placeholder="请填写学员姓名" /></view>
				</view>

				<view class="tab-title">
					<text>学员手机号</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="text" v-model="dom.phone"
							placeholder="请填写学员手机号" /></view>
				</view>

				<view class="tab-title">
					<text>学员简介</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox-high" style="margin: 10rpx 0rpx 30rpx 30rpx;">
					<u--textarea class="multiline-input" confirmType="done" maxlength="100"
						v-model="dom.studentIntroduction" placeholder="请填写学员简介" height="100" count></u--textarea>
				</view>

				<view class="tab-title">
					<text>学员头像</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="flac-col" style="margin: 20rpx 40rpx;">
					<img :src="dom.headImg||imgUpload" alt="" @click="uploadImg(0)" mode="widthFix"
						style="width: 200rpx;height: auto;">
				</view>

				<view class="tab-title">
					<view style="font-size: 32rpx;font-weight: 100;color: #909399;margin:40rpx 0;">
						* 排课时可分配不同学员</view>
				</view>

				<u-gap height="200"></u-gap>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="popupShowEdit = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="update()">
					<view class="filter-button-right">
						<text>更新</text>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowEditWish" :round="10" mode="bottom" @close="popupShowEditWish=false" :closeable="true">
			<view class="f20 fb text-c lh60">编辑课程心愿单</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1100rpx;" v-if="dom&&wish">
				<view class="tab-title">
					<text>学员姓名</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="text" v-model="dom.realName"
							placeholder="请填写学员姓名" disabled="true" /></view>
				</view>

				<view class="tab-title">
					<text>学员电话</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="text" v-model="dom.phone"
							placeholder="请填写学员电话" disabled="true" /></view>
				</view>

				<view class="tab-title">
					<text>心愿课程</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="text" v-model="wish.courseTitle"
							placeholder="请填写心愿课程" disabled="true" /></view>
				</view>

				<view class="tab-title">
					<view style="font-size: 32rpx;margin:20rpx 0;">
						时间：{{wish.createTime||'-'}}</view>
				</view>
				<view class="tab-title" v-if="wish.wishContent">
					<view style="font-size: 32rpx;margin:20rpx 0;">
						留言：{{wish.wishContent||'-'}}</view>
				</view>
				<view class="tab-title" v-if="wish.introducerName">
					<view style="font-size: 32rpx;margin:20rpx 0;">
						推荐人：{{wish.introducerName||'-'}}</view>
				</view>

				<view class="tab-title">
					<text>导师备注</text>
					<text style="color: #ff4d4b;">*</text>
				</view>
				<view class="tab-inputbox-high" style="margin: 10rpx 0rpx 30rpx 30rpx;">
					<u--textarea class="multiline-input" confirmType="done" maxlength="100" v-model="wish.wishRemark"
						placeholder="请填写导师备注" height="100" count></u--textarea>
				</view>


				<view class="tab-title">
					<view style="font-size: 32rpx;font-weight: 100;color: #909399;margin:40rpx 0;">
						* 用于导师备注学员对该课程的意向情况</view>
				</view>

				<u-gap height="200"></u-gap>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="popupShowEditWish = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="updateWish()">
					<view class="filter-button-right">
						<text>更新</text>
					</view>
				</view>
			</view>
		</u-popup>

		<view class="flac-row" style="margin-top: 40rpx;">
			<view class="w2" style="position: relative;">
				<img :src="dom.headImg||blankImg" @click="openImgPreview(dom.headImg||blankImg)"
					style="display: block;width: 120rpx;height: 120rpx;border-radius: 50%;margin: 0 auto;" />
				<img v-if="dom.sex==1||dom.sex==2"
					:src="dom.sex==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/icon_woman.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/course-icon/icon_man.png'"
					alt="" style="position: absolute;width: 50rpx;height: 50rpx;top: 75rpx;right: 5rpx" />
			</view>
			<view class="f16 lh32 flac-col w5" style="margin-left: 20rpx;">
				<view class="flac-row" style="justify-content: space-between;width: 205%;">
					<view>
						{{dom.realName||'匿名用户'}}
					</view>
				</view>
				<view class="lh36 flac-row">
					{{dom.phone}}
					<uni-icons type="phone-filled" style="margin-left: 10rpx;" size="24" color="#1e1848"
						@click="call(dom.phone)">
					</uni-icons>
				</view>
			</view>
			<view class="flex-col-c w3">
				<view class="f20 fb lh50">{{dom.level==1?'白金会员':
						dom.level==2?'黄金会员':
						dom.level==3?'铂金会员':
						dom.level==4?'钻石会员':
						dom.level>=5?'黑钻会员':'-'}}</view>
				<image style="width: 70rpx;" :src="dom.level==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_1.png':
													dom.level==2?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_2.png':
													dom.level==3?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_3.png':
													dom.level==4?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_4.png':
													dom.level==5?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/hyzx/level_5.png':''"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="w10 mg-at lh30 t-indent2 f16" v-if="dom.idCard">
			<text>
				身份证号：{{dom.idCard||'-'}}
			</text>
		</view>
		<view class="w10 mg-at lh30 t-indent2 f16">
			<text class="fb">
				编辑资料
			</text>
			<uni-icons type="compose" style="display: inline-block;" size="18" color="#909399" @click="openEdit()">
			</uni-icons>
		</view>

		<view class="fb t-indent2 f18 lh30">
			最近操作
		</view>
		<view class="f16 lh30 mg-at">
			<view v-if="dom.lastTime" class="t-indent2">
				近15天有{{dom.operateNum}}条操作记录，最近一次{{formatDate(dom.lastTime)}}</view>
			<view v-else class="t-indent2">
				近15天无课程操作记录{{dom.finalTime?'，上一次'+formatDate(dom.finalTime):''}}</view>
		</view>
		<u-gap height="20"></u-gap>

		<view class="fb t-indent2 f18 lh30">
			操作统计-{{dom.operateNumTotal||0}}次
		</view>
		<view class="flac-row f16 lh30">
			<view class="w5 t-indent2">
				课程查看：{{dom.viewNum||0}}次
			</view>
			<view class="w5 t-indent2">
				课程报名：{{dom.addNum||0}}次
			</view>
		</view>
		<view class="flac-row f16 lh30">
			<view class="w5 t-indent2">
				课程分享：{{dom.shareNum||0}}次
			</view>
			<view class="w5 t-indent2">
				课程评价：{{dom.commentNum||0}}次
			</view>
		</view>
		<u-gap height="20"></u-gap>

		<view class="fb t-indent2 f18 lh30">
			课程心愿单
		</view>
		<view class="w10 mg-at">
			<uni-table ref="tableData" stripe emptyText="暂无更多数据">
				<!-- 表头行 -->
				<uni-tr>
					<uni-th class="thStyle" align="center">操作</uni-th>
					<uni-th class="thStyle" align="center">课程</uni-th>
					<uni-th class="thStyle" align="center">留言</uni-th>
					<uni-th class="thStyle" align="center">推荐人</uni-th>
					<uni-th class="thStyle" align="center">备注</uni-th>
					<uni-th class="thStyle" align="center" width="100">时间</uni-th>
				</uni-tr>
				<!-- 表格内容 -->
				<uni-tr v-for="(item, index) in wishList" :key="index">
					<uni-td align="center">
						<uni-icons type="compose" size="20" @click="openEditWish(index)"></uni-icons>
					</uni-td>
					<uni-td align="center">
						<text @click="openCourse(index)">{{item.courseTitle}}</text>
					</uni-td>
					<uni-td align="center">{{item.wishContent||'-'}}</uni-td>
					<uni-td align="center">{{item.introducerName||'-'}}</uni-td>
					<uni-td align="center">{{item.wishRemark||'-'}}</uni-td>
					<uni-td align="center">{{item.createTime}}</uni-td>
				</uni-tr>
			</uni-table>
		</view>


		<view class="fb t-indent2 f18 lh30">
			操作明细
		</view>
		<view class="w10 mg-at">
			<uni-table ref="tableData" stripe emptyText="暂无更多数据">
				<!-- 表头行 -->
				<uni-tr>
					<uni-th class="thStyle" align="center" width="100">课程</uni-th>
					<uni-th class="thStyle" align="center" width="60">操作</uni-th>
					<uni-th class="thStyle" align="center" width="100">时间</uni-th>
				</uni-tr>
				<!-- 表格内容 -->
				<uni-tr v-for="(item, index) in list" :key="index">
					<uni-td align="center">{{item.courseTitle}}</uni-td>
					<uni-td align="center">{{item.logTitle}}</uni-td>
					<uni-td align="center">{{item.createTime}}</uni-td>
				</uni-tr>
			</uni-table>
		</view>

		<view class="lh40 text-c f16" v-if="list.length">
			<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
			<view v-else @click="searchCondition.current++;pageCourseLog()">下滑查看更多...</view>
		</view>

		<u-gap height="40"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				popupShowEdit: false,
				popupShowEditWish: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				id: null,
				dom: null,
				pageCount: 0,
				searchCondition: {
					memberId: null,
					current: 1,
					size: 20,
					search: '',
					orderBy: 'c.createTime DESC'
				},
				wish: null,
				list: [],
				wishList: []
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			formatDate(value) {
				if (!value) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			call(phone) {
				uni.makePhoneCall({
					phoneNumber: phone,
					success: res => {},
					fail: res => {}
				})
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 上传图片
			uploadImg(value) {
				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.$set(this.dom, 'headImg', result.data)
								}
							}
						});
					}
				});
			},
			openEdit() {
				this.$set(this.dom, 'headImg', this.dom.headImg ? this.dom.headImg : '')
				this.$set(this.dom, 'studentIntroduction', this.dom.studentIntroduction ? this.dom.studentIntroduction :
					'')
				this.popupShowEdit = true
			},
			openEditWish(index) {
				this.wish = this.wishList[index]
				this.$set(this.wish, 'wishRemark', this.wish.wishRemark ? this.wish.wishRemark : '')
				this.popupShowEditWish = true
			},
			openCourse(index) {
				let id = this.wishList[index].courseId
				uni.navigateTo({
					url: '/pages-mine/studyCenter/course-detail?id=' + id
				})
			},
			update() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/updateCourseStudent',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.dom,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('操作成功！')
							this.popupShowEdit = false
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			updateWish() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/updateCourseWish',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.wish,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('操作成功！')
							this.popupShowEditWish = false
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			listCourseWish() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/pageCourseWish',
					data: {
						memberId: this.memberId,
						current: 1,
						size: 100,
						orderBy: 't.createTime DESC'
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.wishList = res.data.records
						}
					}
				})
			},
			pageCourseLog() {
				this.searchCondition.memberId = this.id
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/pageCourseLog',
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					hideLoading: true,
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.list = this.list.concat(res.data.records)
							this.pageCount = res.data.pages
						} else if (this.searchCondition.current > 1) {
							this.$refs.uNotify.warning('暂无更多内容了哦')
						}
					}
				})
			},
			//获取潜客详情
			getCourseStudentByMemberId() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/course/getCourseStudentByMemberId',
					path: this.id,
					hideLoading: true,
					method: 'GET',
					success: res => {
						if (res.code == 0) {
							this.dom = res.data
						}
					}
				})
			}
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageCourseLog()
		},
		onLoad(options) {
			this.id = options.id || 0
			this.getCourseStudentByMemberId()
			this.pageCourseLog()
			this.listCourseWish()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";

	// 筛选组件按钮
	.filter-button {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 0;
		display: flex;
		flex-direction: row;
	}

	.filter-button-left,
	.filter-button-right {
		width: 86%;
		height: 80rpx;
		// background-color: red;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		line-height: 80rpx;

		text {
			height: 80rpx;
			font-size: 36rpx;
		}

		button {
			height: 80rpx;
			font-size: 36rpx;
		}
	}

	.filter-button-left {
		border: #dedede 1px solid;
		// color: #909399;
		background-color: #f9f9f9;
	}

	.filter-button-right {
		color: #fff;
		background-color: #1e1848;
	}

	.tab-inputbox-high {
		display: block;
		width: 90%;
		margin: 30rpx 40rpx;
	}

	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}
</style>