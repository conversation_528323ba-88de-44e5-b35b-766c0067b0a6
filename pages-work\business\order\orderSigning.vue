<template>
	 <view>
	        <jushi-signature :settings="settings" @change="signatureChange"></jushi-signature>
	        <view class="" style="margin-top: 20rpx;">
	            <text class="text">保存后的签名图片</text>
	            <view class="preview">
	                <image :src="imgUrl" mode="" style="width: 100%;"></image>
	            </view>
	        </view>
			<u-button customStyle="color:#f6cc70" color="#1e1848" @click="submitMsg" text="上传签名"></u-button>
	    </view>
</template>
<script>
	import {
		imgBase64
	} from '@/pages-work/business/js/imgUrlTool.js'
    export default {
        data() {
            return {
				billNo: '',
				id: null,
                settings:{ //签名设置
                    width: '750',//签名区域的宽
                    height: '500',//签名区域的高
                    lineWidth:3,//签名时线宽
                    textColor:'#007AFF' //签名文字颜色
                },
                imgUrl: ''
            }
        },
		onLoad(option) {
			this.billNo = option.billNo
			this.id = option.id
		},
        methods: {
            signatureChange(e) {
				// #ifdef MP-WEIXIN
				let type = "jpg"
				if (!!uni.getFileSystemManager()) {
				    uni.getFileSystemManager().readFile({
				        filePath: e, //选择图片返回的相对路径
				        encoding: "base64", //编码格式
				        success: (res) => {
							this.imgUrl = "data:image/" +
				                    type.toLocaleLowerCase() +
				                    ";base64," +
				                    res.data
				        },
				    });
				} else {
				    uni.request({
				        url: e,
				        method: "GET",
				        responseType: "arraybuffer",
				        success: (ress) => {
				            let base64 = wx.arrayBufferToBase64(ress.data); //把arraybuffer转成base64
				            this.imgUrl = "data:image/jpeg;base64," + base64; //不加上这串字符，在页面无法显示的
				        },
				    });
				}
				// #endif
				
				// #ifdef H5
				this.imgUrl = e
				// #endif
            },
			submitMsg() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/system/imageUploadByBase64',
					method: 'POST',
					data:{
						base64: this.imgUrl
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res=>{
						if(res.code==0){
							this.linkJump(res.data)
						}
					}
				})
			},
			linkJump(imgUrl){
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/serviceAcceptanceForm/linkJump?orderNo='+this.billNo,
					method: 'GET',
					success: res=>{
						if(res.status==200){
							this.http({
									url: 'saveOrderQm',
									method: 'POST',
									data:{
										productPicture: res.data.productPicture,
										productName: res.data.productName,
										productType: res.data.productType,
										productPrice: res.data.productPrice,
										orderNo: this.billNo,
										serviceProject: res.data.serviceProject,
										serviceNumberOfPeople: res.data.serviceNumberOfPeople,
										isSOP:	res.data.isSOP,
										autographPicture: imgUrl
							
									},
									header: {
										'content-type': "application/json;charset=UTF-8"
									},
								success: res=>{
									if(res.code==0){
									uni.showToast({
										icon:'none',
										title: '上传签名成功!'
									})
									setTimeout(()=>{
									return	uni.redirectTo({
											url: '../order/orderDetail?id=' + this.id
										})
									},2000)
									}
								}
							})
						}
					}
				})
			},
        }
    }
</script>

<style>
    .preview{
        margin: 10rpx;
        border: 1rpx solid #aaaaaa;
        border-radius: 10rpx;
    }
    .text {
        margin: 20rpx;
        color: #aaaaaa;
    }
</style>