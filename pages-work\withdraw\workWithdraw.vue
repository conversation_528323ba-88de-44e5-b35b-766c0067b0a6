<template>
	<view>
		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- <view class="w95 mg-at f14 c9 " style="color: red;">提示：如每月三号过后未提现上月数据，系统将自动提现！</view> -->
		<view v-if="storeType!=1&&storeId!=1&&storeId!=2" class="w95 mg-at f14 c9 " style="color: red;">可转出金额说明：单次提现最低200元，最高不限额</view>
		<view v-if="storeType==1||storeId==1&&storeId!=2" class="w95 mg-at f14 c9 " style="color: red;">可转出金额说明：上月产生的金额，不含其他月份</view>
		<view class="w95 mg-at f14 c9 lh40">提现审批通过后钱款将汇入以下银行卡，如信息有误可点</view>
		<view class="w95 mg-at flac-row-b">
			<view class="f14 c9">点击以下按钮修改或查看</view>
		</view>
		<view class="w95 mg-at flac-row-b">
			<u-tag text="修改/查看提现信息" color="#1e1848" plain plainFill @click="toRlb" borderColor="#1e1848" />
			<u-tag text="查看提现记录" color="#1e1848" plain plainFill @click="getWithdrawalLog" borderColor="#1e1848" />
		</view>
		<view style="height: 25rpx;padding-bottom: 20rpx;padding-top: 20rpx;" v-if="slideNotice.noticeMsg">
		 <u-notice-bar :text="slideNotice.noticeMsg" :speed="slideNotice.speed"
		  mode="closable"></u-notice-bar>
		</view>
		<view style="height: 40rpx;"></view>
		<u-radio-group v-model="radiovalue1" placement="row" v-if="storeType!=1&&storeId!=1&&storeId!=2"
			style="margin-left: 20rpx;margin-top: 60rpx;">
			<u-radio :customStyle="{marginBottom: '8px',marginLeft: '30px'}" v-for="(item, index) in radiolist1" :key="index"
				:label="item.name" :name="item.name" @change="radioChange">
			</u-radio>
		</u-radio-group>
		<view v-if="withdrawalType==1&&storeType!=1&&storeId!=2&&storeId!=1" class="bacf f16 lh30" style="padding: 10rpx 40rpx;margin-top: 20rpx;">
			<view class="flac-row" style="flex-wrap: wrap;">
				<view class="tagStyle"  @click="fileFpFlag = true">查看上传文件发票流程</view>
				<view class="tagStyle" @click="showModal2=true">查看发票购买方信息</view>
				<view class="tagStyle" @click="lookEx(fpslUrl,1,fpsl)">查看发票示例</view>
			</view>
			<view class="fb">上传图片发票（最多五张）</view>
			<view @click="uploadFp(1)">
			<u-upload maxCount="5" :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1"
				multiple></u-upload>
				</view>
				<view class="fb">上传文件发票（微信聊天记录文件）</view>
				<view @click="uploadFp(2)">
				<u-upload  :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="2" multiple accept="all"></u-upload>
				</view>
		</view>
		<view class="bacf f16 lh40" style="padding: 20rpx 40rpx;margin-top: 20rpx;">
			<view style="display: flex;" @click="openDetailsPopup(1)">
				<view class="fb" >业务奖金提现</view>
				<uni-tag text="查看奖励详情" color="#1e1848" style="margin-left: 35%" plain plainFill borderColor="#1e1848" />
			</view>
			<view class="flac-row">
				<text class="f30 fb4">¥</text>
				<u--input placeholder="0" disabled border="bottom" fontSize="16px"
					v-model="inputAmount" />
			</view>
			<view class="f14 fb">总余额：{{unionMerchatData.allBusinessCanTxMoney||0}}元{{storeType==1||storeType==5||storeId==1?"，可转出金额："+inputAmount+"元":''}}</view>
		</view>
		<view class="bacf f16 lh40"  style="padding: 20rpx 40rpx;margin-top: 20rpx;">
			<view style="display: flex;" @click="openDetailsPopup(2)">
				<view class="fb" >其他奖金提现</view>
				<uni-tag text="查看奖励详情" color="#1e1848" style="margin-left: 35%" plain plainFill borderColor="#1e1848" />
			</view>
			<view class="flac-row">
				<text class="f30 fb4">¥</text>
				<u--input placeholder="0" disabled border="bottom" fontSize="16px"
					v-model="unionMerchatData.otherCanTxMoney" />
			</view>
			<view class="f14 fb">总余额：{{unionMerchatData.allOtherCanTxMoney||0}}元，可转出金额：{{unionMerchatData.otherCanTxMoney||0}}元</view>
		</view>
		<u-button text="确认转出，请等待财务审核打款" shape="circle" color="#1e1848"
			customStyle="width:90%;margin:50rpx auto;letter-spacing: 2rpx;color:#f6cc70" @click="rollout"></u-button>
		<u-popup :show="popupFlag" @close="popupFlag = false">
			<view>
				<uni-section title="提现记录" type="line" padding style="height: calc(90vh - 90px);">
					</u-search>
					<u-empty text="暂无提现记录!" width="120" textSize="18" v-if="!withdrawalList.length"
						customStyle="padding-top:200rpx">
					</u-empty>
					<scroll-view scroll-y="true" class="scroll-Y">
						<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
							v-for="(item,i) in withdrawalList" :key="i">
							<view class="f15 flac-row">
								<view style="margin: auto 20rpx;">
									<view class="lh36">交易编号：{{item.traNumber}}</view>
									<view class="">提现时间：{{item.createTime}}</view>
									<view class=""
										v-if="item.withdrawalType==0||item.withdrawalType==1||item.withdrawalType==2">
										提现类型：{{item.withdrawalType==0?'代扣':item.withdrawalType==1?'发票':item.withdrawalType==2?'灵工':'未知'}}
									</view>
									<view class="lh36">
										奖金类型：{{item.amountType==1?'业务奖金':'其他奖金'}}
									</view>
									<view class="lh36">原账户余额：{{item.merchantAccount}}</view>
									<view class="lh36">提现金额：{{item.changeAmount}}</view>
									<view class="lh36">现账户余额：{{item.amount}}</view>
									<view class="lh36">手续费：{{item.deductionAmount||0.00}}</view>
									<view class="lh36">真实打款金额：{{item.realPaymentAmount||0.00}}</view>
									<view class="lh36" v-if="item.withdrawalType==1"
										@click="lookEx(item.invoiceImgUrl,2,ckfp)">发票：点击查看发票</view>
									<view class="lh36">状态：{{item.state==0?'未处理':
											item.state==1?'已通过':
											item.state==2?'申请未通过':
											item.state==3?'到账成功':
											item.state==4?'到账失败':'未知'}}</view>
									<view class="" v-if="item.receivedTime">到账时间：{{item.receivedTime}}</view>
									<view class="lh36" v-if="item.state==2||item.state==4">备注：{{item.withdrawalRemark}}
									</view>
									<view class="lh36" v-if="item.financeRemark">财务备注：{{item.financeRemark}}
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</uni-section>
			</view>
		</u-popup>
		
		
		<u-popup :show="detailsPopupFlag" @close="detailsPopupFlag = false">
			<view>
				<uni-section :title="getType==1?'业务奖励明细':'其他奖励明细'" type="line" padding style="height: calc(90vh - 80px);">
					<scroll-view scroll-y="true" class="scroll-Y">
						<uni-segmented-control :current="businessCurrent" :values="businessItems" @clickItem="onClickBusinessItem" styleType="button"
							activeColor="#1e1848" v-if="getType==1"></uni-segmented-control>
							<uni-segmented-control :current="otherCurrent" :values="otherItems" @clickItem="onClickOtherItem" styleType="button"
								activeColor="#1e1848" v-if="getType==2"></uni-segmented-control>
						<u-empty text="暂无奖励明细!" width="120" textSize="18" v-if="!withdrawalDetailsList.length"
							customStyle="padding-top:200rpx">
						</u-empty>
						<view style="margin-left: 30rpx;font-size: 30rpx;margin-top: 20rpx;margin-bottom: 20rpx;" v-if="accumulate">累计：{{accumulate}}￥</view>
						<!-- <checkbox-group @change="changeCheckbox"> -->
							<view class="w85 mg-at bacf" style="padding: 20rpx;border: 2rpx solid #ddd;"
								v-for="(item,i) in withdrawalDetailsList" :key="i">
							<!-- 	<view class="checkBox">
									<checkbox :disabled="item.orderDisabled" :value="item.orderId"
										:checked="checkedArr.includes(item.orderId)"
										:class="{'checked':checkedArr.includes(item.orderId)}"></checkbox>
								</view> -->
								<view class="f15 flac-row">
									<view style="margin: auto 20rpx;">
										<view class="lh36">日期：{{item.paySettlementTime}}</view>
										<view class="lh36" v-if="otherCurrent!=2">订单编号：{{item.billNo}}</view>
										<view class="flac-row" style="justify-content: space-between;">
											<view class="" v-if="businessCurrent==1||businessCurrent==2||getType==2">服务项目：{{item.productName}}</view>
										</view>
										<view class="lh36" v-if="businessCurrent==1||businessCurrent==2||businessCurrent==3">订单金额：{{item.amount}}</view>
										<view class="lh36">可提金额：{{item.earnings}}</view>
										
										<view class="lh36" v-if="businessCurrent==0&&item.name">姓名：{{item.name}}</view>
										<view class="lh36" v-if="businessCurrent==0&&item.validDay">在户天数：{{item.validDay}}</view>
										<view class="lh36" v-if="businessCurrent==0&&item.remark">说明：{{item.remark}}</view>
										
										<view class="lh36" v-if="businessCurrent==3">合同编号：{{item.contractNo}}</view>
										<view class="lh36" v-if="businessCurrent==3">上户期数：{{item.validDay}}</view>
									</view>
								</view>
							</view>
						<!-- </checkbox-group> -->
					</scroll-view>
				</uni-section>
			</view>
		</u-popup>
		
		<!-- 提现信息--弹窗 -->
		<u-popup :show="withdrawPopup" :round="10" mode="bottom" @close="withdrawPopup=false" :closeable="true">
			<view class="f18 fb text-c lh50">提现认证信息</view>
			<u--form labelPosition="left" labelWidth="100px" ref="form" class="w85 mg-at">
				<u-form-item label="姓名:" prop="userInfo.name" borderBottom ref="item">
					<u--input v-model="withdrawName" border="none" placeholder="请填写姓名"></u--input>
				</u-form-item>
				<u-form-item label="手机号:" prop="userInfo.tags" borderBottom ref="item">
					<u--input v-model="mobile" border="none" placeholder="请填写手机号"></u--input>
				</u-form-item>
				<u-form-item label="身份证号码:" prop="userInfo.tags" borderBottom ref="item">
					<u--input v-model="identityCard" border="none" placeholder="请填写身份证号码"></u--input>
				</u-form-item>
			</u--form>
			<u-button text="提交" @click="authentication()" color="#1e1848" shape="circle"
				customStyle="width:80%;margin: 30rpx auto"></u-button>
		</u-popup>
		<u-modal :show="showModal" :title="fpText" @confirm="showModal = false">
			<view>
				<view style="color: red;">注意：发票内的项目名称请开佣金类，否则系统将审核不通过！</view>
				<u-image :src="srcImgUrl" mode="aspectFit"></u-image>
			</view>
		</u-modal>
		<u-modal :show="showModal2" title="购买方信息" @confirm="showModal2 = false">
			<uni-table border>
			<uni-tr>
				<uni-td>
					<view class="fb">名称：厦门市小羽佳电子商务有限公司</view>
				</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td>
					<view class="fb">电话：05922619629</view>
				</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td>
					<view class="fb">税号：91350200302914027Q</view>
				</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td>
					<view class="fb">卡号：35101589001052511750</view>
				</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td>
					<view class="fb">地址：厦门火炬高新创业园轩业楼3031室</view>
				</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td>
					<view class="fb">开户行：中国建设银行股份有限公司厦门祥东支行</view>
				</uni-td>
			</uni-tr>
			</uni-table>
		</u-modal>
		
		<u-popup :show="fileFpFlag" @close="fileFpFlag = false">
			<uni-section title="上传文件发票流程" type="line" padding></uni-section>
				<scroll-view scroll-y="true" class="scroll-Y" style="height:80vh;">
							<image  src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17049403084996f664d65e8a80333625822030950adf.png" class="w10" mode="widthFix"></image>
							<image  src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17049404379905197a70f5346b3d359e87cea1fd3e67.png" class="w10" mode="widthFix"></image>
							<image  src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1704940552454dce6cba6c218f7856d7ab5e505802fb.png" class="w10" mode="widthFix"></image>
							<image  src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1704940660655260eb199b54356884f5a25ec0e60472.png" class="w10" mode="widthFix"></image>
				</scroll-view>
		</u-popup>
		
		<movable-area class="movableArea" @click="trigger">
			<movable-view class="movableView" :position="position" x="300" y="400" :direction="direction"
				:damping="damping">
					<image src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1739258409627客服.png" mode="widthFix" class="iconImage"></image>
					<view style="font-size: 24rpx;margin-top: -7rpx;margin-left: 11rpx;">财务</view>
			</movable-view>
		</movable-area>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fpText: '',
				detailsPopupFlag: false,
				slideNotice: {
					noticeMsg: ''
				},
				getType: null,
				businessCurrent: 0,
				otherCurrent: 0,
				businessItems: ['招工', '线索','开发','上户'],
				otherItems: ['课程/陪跑', '软件推广','其他'],
				fpsl: '发票示例',
				inputAmount: 0.00,
				fileFpFlag: false,
				fileList2: [],
				ckfp: '查看发票',
				fpslUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1706002923586c8a16f7c356c618d82b1810df185b50.png",
				srcImgUrl: '',
				radiolist1: [{
						name: '代扣(扣除20%)',
						disabled: false
					},
					{
						name: '发票(无扣减)',
						disabled: false
					}
				],
				// radiolist1: [{
				// 		name: '代扣(扣除20%)',
				// 		disabled: false
				// 	},
				// 	{
				// 		name: '发票(无扣减)',
				// 		disabled: false
				// 	},
				// 	{
				// 		name: '灵工(扣减10%)',
				// 		disabled: false
				// 	}
				// ],
				radiovalue1: '发票(无扣减)',
				checkType: 0,
				withdrawPopup: false,
				identityCard: '',
				accumulate: 0.00,
				mobile: '',
				withdrawName: '',
				fileList1: [],
				withdrawalDetailsList: [],
				checkedArr: [], //复选框选中的值
				showModal: false,
				showModal2: false,
				event: {},
				state: null,
				uploadType: null,
				unionMerchatData: {},
				withdrawalList: [],
				popupFlag: false,
				bankData: {},
				checkTitle: "",
				checkText: "",
				withdrawalType: 1,
				storeType: uni.getStorageSync('storeType'),
				storeId: uni.getStorageSync('storeId'),
				amountMoney: 0.00,
			};
		},
		props: {
			damping: {
				type: Number,
				default: 10
			},
			direction: {
				type: String,
				default: "all"
			},
			position: {
				type: Number,
				default: 4
			}
		},
		onLoad() {
				this.getSlideNotice()
			this.ifEmployeeOrUnion()
			setTimeout(()=>{
				this.getUnionMerchantData()
			},200)
		},
		methods: {
			getSlideNotice(){
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				const parts = route.split('/');
				const lastPart = parts[parts.length - 1];
				this.http({
					url: "getSlideNotice",
					method: 'GET',
					data: {
						pageRoute: lastPart
					},
					success: res => {
						if (res.code == 0) {
							if(res.data){
								this.slideNotice = res.data
							}
						}
					}
				})
			},
			trigger() {
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfcda2587f0eecab0c8' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  MP-TOUTIAO || H5
				uni.showToast({
					icon: 'none',
					title: '请在小程序内操作!'
				})
				// #endif
			},
			openDetailsPopup(getType){
				 this.detailsPopupFlag= true
				 this.getType =getType
				 this.getWithdrawalDetails()
			},
			getWithdrawalDetails(){
				this.accumulate = 0.00
					this.http({
						url: "getWithdrawalDetails",
						method: 'GET',
						data: {
							memberId: uni.getStorageSync("memberId"),
							memberType: this.bankData.memberType,
							getType: this.getType,
							detailsType: this.getType==1?this.businessCurrent:this.otherCurrent
						},
						success: res => {
							if (res.code == 0) {
								this.withdrawalDetailsList= res.data
								let accumulate = 0; 
								let factor = 100;
								for (var i = 0; i < res.data.length; i++) {
									accumulate+=Number(res.data[i].earnings * factor)
								}
								this.accumulate = accumulate / factor
							}
						}
					})
			},
			onClickBusinessItem(e) {
					this.businessCurrent = e.currentIndex
					this.getWithdrawalDetails()
			},
			onClickOtherItem(e){
				this.businessCurrent = e.currentIndex
				this.getWithdrawalDetails()
			},
			uploadFp(val){
				this.uploadType=val
			},
			radioChange(n) {
				if (n == '发票(无扣减)') {
					this.withdrawalType = 1
				}
				if (n == '灵工(扣减10%)') {
					this.withdrawalType = 2
					this.getRlwLogData()
				}
				if (n == '代扣(扣除20%)') {
					this.withdrawalType = 0
				}
				if (n == '发票(无扣减)'||n == '代扣(扣除20%)') {
					this.ifEmployeeOrUnion()
				}
			},
			// 删除图片
			deletePic(event) {
				this.event = event
				this.openCheck(1, "确认删除该发票信息吗？")
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let obj = JSON.parse(result);
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: obj.data
					}))
					if(this.uploadType==1){
						this.fileList2 =[] 
					}else{
						this.fileList1 =[] 
					}
					// this.voteImgUrl = obj.data
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: url,
						name: 'file',
						formData: {},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			lookEx(imgUrl, type, text) {
				this.fpText = text
				this.srcImgUrl = imgUrl
				if (type == 1) {
					this.noVotePopupFlag = false
				}
				if (type == 2) {
					this.popupFlag = false
				}
				this.showModal = true
			},
			authentication() {
				if (!this.withdrawName) {
					return uni.showToast({
						title: "请填写姓名!",
						icon: 'none'
					})
				}
				if (!this.mobile) {
					return uni.showToast({
						title: "请填写手机号!",
						icon: 'none'
					})
				} else {
					let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
					if (!reg.test(this.mobile)) {
						return uni.showToast({
							title: "请填写正确的手机号!",
							icon: 'none'
						})
					}
				}
				if (!this.identityCard) {
					return uni.showToast({
						title: "请填写身份证号码!",
						icon: 'none'
					})
				}
				//提现认证
				this.http({
					url: "getFranchiseIfAuth",
					method: 'GET',
					data: {
						merchantId: uni.getStorageSync("merchantId"),
						mobile: this.mobile,
						name: this.withdrawName,
						identityCard: this.identityCard,
					},
					success: res => {
						if (res.code == 0) {
							this.bankData.flag = res.data.flag
							if (res.data.flag == 0) {
								this.withdrawPopup = false
								return uni.showToast({
									title: '查询到已完成签约灵工平台！快去提现吧！',
									icon: 'none'
								})
							}
							if (res.data.checkRemark) {
								uni.showToast({
									title: res.data.checkRemark + ",即将跳转认证签约平台...",
									icon: 'none'
								})
							}
							this.withdrawPopup = false
							setTimeout(() => {
								if (res.data.flag == 1) {
									let param = {
										url: 'https://lingong-h5-sign-prod.renliwo.com/#/login?vendorId=1745&invitationCode=BUIAOE2E4T&contractConfigId=89&channelChildrenCode=xiaoyujia'
									}
									let data = JSON.stringify(param);
									uni.navigateTo({
										url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
									})
								}

							}, 2000)
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			ifEmployeeOrUnion() {
				this.http({
					url: 'ifEmployeeOrUnion',
					method: 'GET',
					data: {
						merchantId: uni.getStorageSync("merchantId")
					},
					success: res => {
						if (res.code == 0) {
							this.bankData = res.data
							this.withdrawName = res.data.withdrawName
							this.identityCard = res.data.identityCard
							this.mobile = res.data.mobile
							if ((res.data.memberType == 1 || res.data.memberType == 3) && res.data.flag == 1) {
								uni.showModal({
									title: '提示',
									content: '检测到银行卡等信息不完整，请填写完整后操作!',
									success: function(res) {
										if (res.confirm) {
											uni.navigateTo({
												url: '/pages-work/withdraw/bindingBank'
											})
										} else if (res.cancel) {
											uni.navigateBack();
										}
										
									}
								});
							} else if (res.data.memberType == 2 && res.data.flag == 1) {
								uni.showModal({
									title: '提示',
									content: '检测到您还未进行身份信息认证，请认证通过后操作提现!',
									success: res => {
										if (res.confirm) {
											this.withdrawPopup = true
										} else if (res.cancel) {
											uni.navigateTo({
												url: '/pages-work/index'
											})
										}
									}
								});
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			getWithdrawalLog() {
				let param = {
					merchantId: this.unionMerchatData.id,
				}
				if (this.state) {
					param = {
						merchantId: this.unionMerchatData.id,
						state: this.state
					}
				}
				this.popupFlag = true
				this.http({
					url: 'getWithdrawalLog',
					method: 'GET',
					data: param,
					success: res => {
						if (res.code == 0) {
							this.withdrawalList = res.data
						}
					}
				})
			},
			getUnionMerchantData() {
				this.http({
					url: "getUnionMerchantData",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						memberType: this.bankData.memberType
					},
					success: res => {
						if (res.code == 0) {
							this.unionMerchatData = res.data
							if(this.storeType!=1&&this.storeType!=5&&this.storeId!=1&&this.storeId!=2){
								this.inputAmount = this.unionMerchatData.allBusinessCanTxMoney
							}else {
								this.inputAmount = this.unionMerchatData.businessCanTxMoney
							}
						}
					}
				})
			},
			toRlb() {
				if (this.bankData.flag == 1) {
					return this.ifEmployeeOrUnion()
				}
				if (this.withdrawalType == 2) {
					let param = {
						url: 'https://lingong-h5-sign-uat.renliwo.com/#/login?vendorId=34&invitationCode=TQM5LVKJY7&contractConfigId=2&channelChildrenCode=xiaoyujia'
					}
					let data = JSON.stringify(param);
					uni.navigateTo({
						url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
					})
				} else {
					uni.navigateTo({
						url: '/pages-work/withdraw/bindingBank'
					})
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					let fileList = []
					if(this.uploadType==1){
						fileList = this.fileList1
					}else{
						fileList = this.fileList2
					}
					this.http({
						url: "balanceWithdrawal",
						method: 'POST',
						data: {
							businessCanTxMoney: this.inputAmount,
							otherCanTxMoney: this.unionMerchatData.otherCanTxMoney,
							memberType: this.bankData.memberType,
							merchantCode: uni.getStorageSync('merchantCode'),
							mapList: fileList,
							withdrawalType: this.withdrawalType
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							setTimeout(() => {
								uni.showToast({
									title: res.msg,
									icon: 'none',
									duration: 2000
								})
							}, 30)
							if (res.code == 0) {
								this.unionMerchatData.canTxAmount = 0
								this.getUnionMerchantData()
							}
						}
					})
				}
				if (this.checkType == 1) {
					this[`fileList${this.event.name}`].splice(this.event.index, 1)
				}
			},
			getRlwLogData() {
				this.http({
					url: 'getRlwLogData',
					method: 'GET',
					data: {
						merchantId: uni.getStorageSync('merchantId')
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.bankData.flag = 0
							} else {
								this.bankData.flag = 1
							}
						}
					}
				})
			},
			rollout() {
				if (this.withdrawalType == 1&&this.storeType!=1&&this.storeId!=1&&this.storeId!=2) {
					if (this.withdrawalType == 1 && this.fileList1.length <= 0&&this.fileList2.length <= 0) {
						return uni.showToast({
							title: '请上传发票!',
							icon: 'none',
							duration: 2000
						})
					}
					this.ifEmployeeOrUnion()
				}
				if (this.withdrawalType == 2 && this.bankData.flag == 1) {
					this.getRlwLogData()
					setTimeout(() => {
						if (this.bankData.flag == 1) {
							uni.showModal({
								title: '提示',
								content: '检测到您还未进行身份信息认证，请认证通过后操作!',
								success: res => {
									if (res.confirm) {
										this.withdrawPopup = true
									} else if (res.cancel) {
										uni.navigateTo({
											url: '/pages-work/index'
										})
									}
								}
							});
						}
					}, 500)
				}
				setTimeout(() => {
					if (this.bankData.flag == 0) {
						if(this.storeType!=1&&this.storeId!=1&&this.storeId!=2&&this.inputAmount< 200&&this.unionMerchatData.otherCanTxMoney < 200){
							return uni.showToast({
								title: '单次提现最低200元',
								icon: 'none',
								duration: 2000
							})
						}
						if (this.inputAmount<= 0&& this.unionMerchatData.otherCanTxMoney <= 0) {
							uni.showToast({
								title: '转出余额不能为零',
								icon: 'none',
								duration: 2000
							})
						} else {
							this.openCheck(0, "是否确认提交转出申请?")
						}
					}
				}, 500)
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5;
	}

	.scroll-Y {
		height: 700rpx;
	}
	
	.tagStyle {
		padding: 0 30rpx;
		border-radius: 10rpx;
		margin: 20rpx auto;
		border: 2rpx solid #f6cc70;
		color: #333;
	}
	
	.movableArea {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
		z-index: 999;
	
		.movableView {
			pointer-events: auto; //可以点击
			width: 70rpx;
			height: 70rpx;
			padding: 10rpx;
			border-radius: 100%;
			border: 2px solid #1e1848;
	
			.iconImage {
				display: block;
				width: 50rpx;
				height: 50rpx;
				margin-left: 10rpx;
				// animation: iconImage 5s linear infinite;
			}
	
			@keyframes iconImage {
				0% {
					-webkit-transform: rotate(0deg);
				}
	
				25% {
					-webkit-transform: rotate(90deg);
				}
	
				50% {
					-webkit-transform: rotate(180deg);
				}
	
				75% {
					-webkit-transform: rotate(270deg);
				}
	
				100% {
					-webkit-transform: rotate(360deg);
				}
			}
	
		}
	}
</style>