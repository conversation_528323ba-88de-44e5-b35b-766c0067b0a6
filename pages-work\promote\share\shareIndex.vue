<template>
	<view class="page">
		<u-sticky v-if="userInfo.roleId !== 110">
			<view class="u-head cf">
				<view class="head-part1">
					<image class="head-img" :src="userInfo.headImg!=null?userInfo.headImg:src1"></image>
					<view class="title">
						<view class="f20 lh40">{{ userInfo.name }}</view>
						<view class="phoneNum f16">
							<u-text mode="phone" format="encrypt" :text="userInfo.phone"></u-text>
						</view>
					</view>
				</view>
				<view class="f16" @click="jump">推广订单 ></view>
			</view>
			<u-subsection :list="list" :current="curNow" @change="sectionChange" mode="button" bgColor="#eee"
				activeColor="#f0b438" inactiveColor="#888" fontSize="18" v-if="userInfo.roleId !== 110"></u-subsection>
		</u-sticky>

		<view v-for="(item,index) in PromoteProductList" :key="index" class="content w95 mg-at bacf"
			v-if="userInfo.roleId !== 110">
			<view class="main">
				<u-image :src="item.iconimg" width="200rpx" height="150rpx"></u-image>
				<view class="flex-col" style="">
					<text class="f18 fb">{{item.title}}</text>
					<view v-if="item.saleprice">
						<text class="f16 red">¥<text class="f18">{{item.saleprice}}</text></text>
					</view>
					<!-- <view v-if="item.price">
						<u-text decoration="line-through" :text="'原价:¥:'+item.price"></u-text>
					</view> -->
					<text class="f14 fb" style="color: #999;">佣金比例{{item.ratio *100}}%({{item.scale}})</text>

				</view>
				<!-- <u-button :text="'赚¥'+item.price*item.ratio" type="error" @click="gomoni(item)"></u-button> -->
				<u-button text="立即赚钱" type="error" @click="openxcx(item)"></u-button>
			</view>
			<!-- <text class="f14 fb" style="color: #999;" align="center">三个保姆后院乱，保姆代管家安心</text> -->
		</view>

		<view v-if="userInfo.roleId === 110">
			<u--image :src="posterImg" mode="aspectFit" height="766rpx" width="100%"></u--image>
			
			
			<!-- <u-gap height="20"></u-gap>
				<button
					style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
					@click="showBackImg(1)" v-if="backgroupImg">查看上传的海报图</button>
			<u-gap height="20"></u-gap>
				<button
					style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
					@click="showBackImg(2)" v-if="posterImg">查看生成的海报图</button>
			

			<u-text text="1:上传需要制作的海报图(图片必须大于200*200)"></u-text>
			<button
				style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
				@click="upload">上传背景图</button>
				
			<u-text text="2:点击生成分享海报即可保存分享给客户"></u-text>
			<button
				style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
				@click="createPoster">生成分享海报图</button>
				
			<view v-if="posterImg">
				<u-text text="3:点击保存海报二维码图"></u-text>
				<button
					style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
					@click="savePoster">保存生成的图片</button> -->
					
					<!-- <button
						style="background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1)); color: aliceblue;"
						@click="savePoster">保存分享海报图片</button> -->
						<u-text text="点击图片长按保存海报"></u-text>
					
			</view>	
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					name: uni.getStorageSync('memberName'),
					phone: uni.getStorageSync('account'),
					headImg: uni.getStorageSync("memberHeadImg") || null,
					roleId: uni.getStorageSync("roleId")
				},
				// list: ['招工', '线索', '业务', '生态', '联盟'],
				list: ['招人', '拓客', '分销'],
				curNow: 0,
				RecruitList: [],
				ExploitList: [],
				PromoteProductList: [],
				src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669184098274wx.png',
				backgroupImg: '',
				posterImg: ''
			}
		},
		onLoad(options) {
			if (this.userInfo.roleId === 110) {
				this.createPoster()
			} else {
				this.curNow = options.curNow || 0
				this.getpromoteProductList(parseInt(this.curNow) + 1);
			}

		},
		methods: {
			savePoster() {
				uni.downloadFile({ //下载文件资源到本地,返回文件的本地临时路径
					url: this.posterImg, //网络图片路径
					success: (res) => {
						var imageUrl = res.tempFilePath; //临时文件路径
						uni.saveImageToPhotosAlbum({ //保存图片到系统相册
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			createPoster() {
				let url = 'http://www.jiahome.com/?storeId='+uni.getStorageSync('storeId');
				this.http({
					url: 'createPoster',
					data: {
						imgUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17005494191912(1).jpg',
						linkUrl: url 
					},
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if(res.code == 0){
							this.posterImg = res.data
							console.log(this.posterImg)
						} else {
							uni.showToast({
								title:res.msg
							})
						}
					},
					fail: err => {
						console.log(res)
					}

				})
			},
			showBackImg(type) {
				let url;
				if(type == 1){
					url = this.backgroupImg;
				} else {
					url = this.posterImg
				}
				uni.previewImage({
					urls: [url],
					current: 0,
					indicator: 'none',
					loop: true,
					indicator: 'default',
					longPressActions:{}
				})
			},
			upload() {
				const url = 'https://api2.xiaoyujia.com/system/imageUpload'
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								this.backgroupImg = result.data

							}
						});
					}
				});
			},
			gomoni(item) {
				if (!item.path) {
					return uni.showToast({
						title: '跳转路径为空'
					})
				}
				uni.navigateToMiniProgram({
					appId: item.appid,
					path: item.path,
					envVersion: "release",
					success: res => {
						// 打开成功
						console.log("打开成功", res);
					},
					fail: err => {
						console.log(err);
					}
				});
			},
			jump() {
				uni.navigateTo({
					url: '/pages-work/promote/extension/extensionList'
				})
			},
			getpromoteProductList(type) {
				this.http({
					url: 'getPromotionProductList',
					data: {
						type: type,
						memberId: uni.getStorageSync('memberId')
					},
					hideLoading: true,
					method: 'GET',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.PromoteProductList = res.data;
					},
					fail: err => {
						console.log(res)
					}

				})
			},
			getPromoteInfoList(type) {
				this.http({
					url: 'getPromotionInfoList',
					method: 'GET',
					hideLoading: true,
					data: {
						type: type
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.RecruitList = res.data;
					},
					fail: err => {
						console.log(res)
					}

				})
			},

			getDevelopData() {
				this.http({
					url: 'getAllProductRetail',
					method: 'GET',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: null,
					success: res => {
						this.ExploitList = res.data;
					},
					fail: err => {
						console.log(res)
					}
				});


			},
			sectionChange(index) {
				this.getpromoteProductList(index + 1);
				// this.getPromoteInfoList(index + 1);
				// if (index !== 2 && index !== 3) {
				// 	this.getPromoteInfoList(index + 1);
				// } else {
				// 	this.getpromoteProductList();
				// }
				this.curNow = index;
			},
			openxcx(item) {
				if (uni.getStorageSync('roleId') == 110 && (this.curNow == 1 || this.curNow == 2)) {
					uni.navigateTo({
						url: "/pages-work/promote/share/shareCodeB?id=" + item.id + "&shareType=" + this.curNow
					})
				} else {
					uni.navigateTo({
						url: "/pages-work/promote/share/shareCode?id=" + item.id
					})
				}
			},
			share(id) {
				uni.navigateTo({
					url: "/pages-work/promote/share/shareCode?id=" + id + '&type=1'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background-color: #f4f2f3 !important;
	}

	/deep/.u-button {
		width: 150rpx !important;
		border-radius: 15rpx !important;
	}

	/deep/.u-image {
		margin: auto 20rpx !important;
	}

	.u-head,
	.head-part1 {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.u-head {
		padding: 30rpx;
		background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
	}

	.head-img {
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		// background-color: #fdd472;
		border: 4rpx solid #fdd472;
		border-radius: 130rpx;
	}

	.phoneNum {
		background: rgba(252, 255, 255, 0.35);
		padding: 8rpx 25rpx;
		border-radius: 40rpx;
	}

	.main {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 25rpx 10rpx;
		padding: 50rpx 0;
	}

	.content {
		box-shadow: 3px 5px 15px 3px rgba(0, 0, 0, 0.1);
	}
</style>