<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 栏目菜单 -->
		<view class="swiper-menu">
			<u-sticky>
        <u-tabs :list="menuList" @click="choiceMenu" :current="menuIndex" lineWidth="22" lineHeight="8" :lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
                color: '#1e1848',
                fontWeight: 'bold',
                transform: 'scale(1.05)'
            }" :inactiveStyle="{
                color: '#333',
                transform: 'scale(1.05)'
            }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
        </u-tabs>
			</u-sticky>
		</view>

		<!-- 栏目信息 -->
		<swiper class="swiper" :style="contentHeight" :autoplay="false" @change="swiperMenu" :current="menuIndex">
			<swiper-item ref="swiperItem" class="swiper-item" v-for="(menu, index) in menuList" :key="index">
				<view class="swiper-tab" v-for="(item, index1) in itemList" :key="index1">
					<view class="swiper-head">
						<text class="swiper-title">编号：xxx</text>
						<text class="swiper-tips">xxxx</text>
					</view>
					<view class="swiper-content">
						<view class="content-left">
							<img :src="itemImg" alt="">
						</view>
						<view class="content-right">
							<view class="content-title">
								<text>xxxx</text>
							</view>
							<view class="content-text">
								<text>xxxx</text>
							</view>
							<u-gap height="60"></u-gap>
						</view>
					</view>


				</view>

				<u-empty v-if="itemCount[index].count==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</swiper-item>
		</swiper>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				memberId: 0,
				memberName: '',
				headImg: '',
				tabNum: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				menuIndex: 0,
				swiperItemCount: 0,
				contentHeight: 0,
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				itemImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				itemList: [],
        lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [{
						name: '全部',
					},
					{
						name: '已获得',
					},
					{
						name: '未获得',
					}
				],
				itemCount: [{
					count: 0
				}, {
					count: 0
				}, {
					count: 0
				}],
			}
		},
		methods: {
			// 格式化时间
			formatDate(value) {
				var dt = new Date(value)
				let year = dt.getFullYear()
				let month = (dt.getMonth() + 1).toString().padStart(2, '0')
				let date = dt.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${date}`
			},
			// 切换栏目
			changeTab(index) {
				this.tabNum = index
			},
			// 跳转到栏目详情页面
			openDetail() {
				uni.navigateTo({
					url: '/pages-mine/certificate/certificate-detail'
				})
			},
			// 控制不同类型的栏目显示
			checkItem(index, index1) {
				let status = parseInt(this.itemList[index1].status)
				return false
			},
			// 计算各类栏目信息数量
			calculationItemCount() {
				for (let item of this.itemList) {
					let status = parseInt(item.status)
					if (status >= 4) {
						this.itemCount[1].count = this.itemCount[1].count + 1
					} else if (status < 4) {
						this.itemCount[2].count = this.itemCount[2].count + 1
					}
				}
				this.itemCount[0].count = this.itemList.length
				console.log("输出数量：", this.itemList.length)
				// 初始化第一个栏目高度
				this.calculateContentHeight()
			},
			calculateContentHeight() {
				let height = (this.itemCount[this.menuIndex].count + 1) * 190 + "px"
				this.contentHeight = "height:" + height
			},
			// 滑动切换菜单
			swiperMenu(e) {
				this.menuIndex = e.detail.current
				this.calculateContentHeight()
			},
			// 点击切换菜单
			choiceMenu(e) {
				console.log("切换菜单栏：", e.index)
				this.menuIndex = e.index
				this.calculateContentHeight()
			},
			// 获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let employeeId = uni.getStorageSync('employeeId')
				employeeId = employeeId == "" ? null : employeeId
				this.memberId = memberId
				this.employeeId = employeeId
				this.memberName = uni.getStorageSync("memberName")
				console.log('获取会员Id：', memberId, '获取员工Id：', employeeId)
				// 测试时加上
				// this.employeeId = 34737
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx
				if (this.checkType == 0) {

				}
			},
			// 用户信息初始化
			orginInfo() {
				this.getMemberInfor()
			}
		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>
