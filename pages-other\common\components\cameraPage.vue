<template>
	<view v-if="showCameraPage">

		<!-- 图片裁剪 -->
		<l-clipper v-if="showClipper" @success="clipperSuccess" @cancel="showClipper = false" :width="500" :height="750"
			:min-width="200" :min-height="300" :scale-ratio="6" style="z-index: 1001;" />

		<!-- 相机状态 -->
		<view class="content" v-if="!imgSrc&&!showClipper" :style="showTopBar?'margin-top: 180rpx':''">
			<!-- 返回上一页 -->
			<view class="back" @click="backPage">
				<img :src="backBtnIcon"></img>
			</view>

			<view class="camera-box">
				<camera class="camera" :device-position="devicePosition" flash="off" @error="error">
					<cover-image style="width: 750rpx;height: 1125rpx;"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/facial_identify.png" />
					<cover-view v-show="tipsText" class="camera-tip cf">{{ tipsText }}</cover-view>
				</camera>
			</view>

			<view class="bottom">
				<view class="wrap flac-row-b">
					<!-- 上传照片 -->
					<view class="switch" @click="showClipper=true;">
						<image class="icon-w69-h56" :src="albumIcon"></image>
					</view>
					<!-- 拍照 -->
					<view class="take" @click="initData">
						<image class="icon-w100-h100" :src="takeBtnIcon"></image>
					</view>
					<!-- 切换摄像头 -->
					<view class="switch" @click="switchCamera">
						<image class="icon-w69-h56" :src="switchCameraIcon"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- #ifdef  H5 || APP-PLUS -->
		<view class="tab">
			<view class="img-upload"><img :src="imgSrc != ''&&imgSrc != null ? imgSrc : blankImg"
					@click="uploadImg()" />
			</view>
		</view>
		<!-- #endif -->

		<!-- 照片预览 -->
		<view class="preview" v-if="imgSrc" :style="showTopBar?'margin-top: 180rpx':''">
			<image mode="widthFix" class="imgStyle" :src="imgSrc"></image>
			<view class="btnStyle flac-row-b cf f20 fb">
				<view class="" @click="afresh">重拍</view>
				<view class="" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>
<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		props: {
			showCameraPage: {
				type: Boolean,
				default: false
			},
			showTopBar: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				// 可配置选项
				// 是否开启图片裁剪
				isOpenClipper: true,
				// 是否开启拍照倒计时
				openCountdown: false,

				showClipper: false,
				devicePosition: 'front', //前置摄像头
				tipsText: '', //提示词
				backBtnIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/back_before_icon.png',
				takeBtnIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/take_camera_btn_icon.png',
				switchCameraIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/switch_camera_icon.png',
				albumIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/album_icon.png',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png',
				imgSrc: '',
			};
		},

		methods: {
			// 上传照片
			uploadImg() {
				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								this.imgSrc = result.data
								this.$emit("submitCameraPhoto", tempFilePaths)
							}
						});
					}
				});
			},
			// 提交照片
			submit() {
				let tempFilePaths = []
				tempFilePaths.push(this.imgSrc)
				this.$emit("submitCameraPhoto", tempFilePaths)
			},
			// 切换摄像头
			switchCamera() {
				if (this.devicePosition === 'back') {
					this.devicePosition = 'front' //前置
				} else {
					this.devicePosition = 'back' //后置
				}
			},
			// 图片裁剪成功
			async clipperSuccess(e) {
				this.showCameraPage = false
				let path = []
				// #ifdef H5
				await base64ToPath(e.url).then(res => {
					path.push(res)
				})
				// #endif

				// #ifdef MP-WEIXIN || APP-PLUS
				path.push(e.url)
				// #endif

				this.$emit("submitCameraPhoto", path)
			},
			// 初始化相机引擎
			initData() {
				let that = this
				// #ifdef MP-WEIXIN
				// 1、初始化人脸识别
				wx.initFaceDetect()
				// 2、创建 camera 上下文 CameraContext 对象
				that.cameraEngine = wx.createCameraContext()
				// 3、获取 Camera 实时帧数据
				const listener = that.cameraEngine.onCameraFrame(frame => {
					// 4、人脸识别，使用前需要通过 wx.initFaceDetect 进行一次初始化，推荐使用相机接口返回的帧数据
					wx.faceDetect({
						frameBuffer: frame.data,
						width: frame.width,
						height: frame.height,
						enablePoint: true,
						enableConf: true,
						enableAngle: true,
						enableMultiFace: true,
						success: faceData => {
							let face = faceData.faceInfo[0]
							if (faceData.x == -1 || faceData.y == -1) {
								that.tipsText = '检测不到人'
							}

							if (faceData.faceInfo.length > 1) {
								that.tipsText = '请保证只有一个人'
							} else {
								const {
									pitch,
									roll,
									yaw
								} = face.angleArray;
								const standard = 0.3;
								if (
									Math.abs(pitch) >= standard ||
									Math.abs(roll) >= standard ||
									Math.abs(yaw) >= standard
								) {
									that.tipsText = '请平视摄像头';
								} else if (
									face.confArray.global <= 0.8 ||
									face.confArray.leftEye <= 0.8 ||
									face.confArray.mouth <= 0.8 ||
									face.confArray.nose <= 0.8 ||
									face.confArray.rightEye <= 0.8
								) {
									that.tipsText = '请勿遮挡五官';
									console.log("请勿遮挡五官！")
									console.log("参数1", face.confArray.global)
									console.log("参数2", face.confArray.leftEye)
									console.log("参数3", face.confArray.mouth)
									console.log("参数4", face.confArray.nose)
									console.log("参数5", face.confArray.rightEye)
								} else {
									console.log("开始拍照！")
									listener.stop()
									if (this.openCountdown) {
										let time = 3
										that.tipsText = time + '秒后拍照，请保持！'
										let timer = setInterval(function() {
											time--
											if (time <= 0) {
												clearInterval(timer)
												// 拍照
												return that.takePhoto()
											} else {
												that.tipsText = time + '秒后拍照，请保持！'
											}
										}, 1000);
									} else {
										return that.takePhoto()
									}
									listener.stop()
								}
							}
						},
						fail: err => {
							if (err.x == -1 || err.y == -1) {
								that.tipsText = '检测不到人'
							} else {
								that.tipsText = err.errMsg || '网络错误，请退出页面重试';
							}
						}
					});
				});
				// 5、开始监听帧数据
				listener.start()
				// #endif
			},
			takePhoto() { //开始拍照
				console.log('点击拍照')
				// 检测是否授权相机
				uni.getSetting({
					success: res => {
						if (!res.authSetting['scope.camera']) {
							this.isAuthCamera = false;
							uni.openSetting({
								success: res => {
									if (res.authSetting['scope.camera']) {
										this.isAuthCamera = true;
									}
								}
							});
						}
					}
				});
				const ctx = uni.createCameraContext();
				console.log(ctx)
				ctx.takePhoto({ //获取图片地址，展示
					quality: 'high',
					success: (res) => {
						console.log(res)
						this.imgSrc = res.tempImagePath
					}
				});
			},
			error(e) {
				console.log(e.detail);
			},
			backPage() { //返回
				this.$emit("closeCameraPage", false)
				// uni.navigateBack({
				// 	delta: 1
				// });
			},
			afresh() { //重拍
				this.tipsText = ''
				this.imgSrc = ''
			},
		}
	};
</script>
<style lang="less" scoped>
	page {
		width: 100%;
		z-index: 1000;
	}

	.content {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;

		.camera-box {
			position: relative;
			width: 100%;
			height: 100%;
			z-index: 1;

			.camera {
				width: 750rpx;

				height: 1625rpx;
				border-top: 100rpx solid black;

				border-bottom: 400rpx solid black;
				box-sizing: border-box;

				.camera-tip {
					position: absolute;
					bottom: 50rpx;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}

		.bottom {
			position: fixed;
			width: 100%;
			bottom: 0;
			padding: 80rpx 0;
			z-index: 999;

			.wrap {
				height: 80px;
				padding: 0 73rpx;

				.icon-w55-h49 {
					width: 55rpx;
					height: 49rpx;
				}

				.icon-w100-h100 {
					width: 100rpx;
					height: 100rpx;
				}

				.icon-w69-h56 {
					width: 69rpx;
					height: 56rpx;
				}
			}
		}
	}

	.preview {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: black;

		.imgStyle {
			margin-top: 15%;
			display: block;
			width: 100%;
			height: auto;
			// height: 90vh !important;
		}

		.btnStyle {
			line-height: 100rpx;
			width: 80%;
			margin: auto;
			background-color: black;
		}
	}

	.back {
		position: absolute;
		z-index: 1001;
		top: 25rpx;
		left: 40rpx;

		img {
			display: block;
			width: 55rpx;
			height: 49rpx;
		}
	}

	.img-upload {
		width: 100%;
		height: 600rpx;

		img {
			display: block;
			width: 400rpx;
			height: 600rpx;
			margin: 80rpx auto;
			border-radius: 10rpx;
		}
	}
</style>