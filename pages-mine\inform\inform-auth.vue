<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="resume-tab" v-if="!isLogin">
			<view class="tab">
				<view class="tips" @click="saveText(0)">
					<text>授权码：{{code||'暂未获取'}}</text>
				</view>
				<view class="tab-head">
					<text>手机号</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input"><input class="single-input" type="number" v-model="phone"
							placeholder="暂未填写手机号" /></view>
				</view>
			</view>

			<view style="width: 86%;padding: 20rpx 7% 40rpx 7%;">
				<text style="display: block;font-size: 32rpx;font-weight: 100;color: #909399;">
					* 授权后可即时接收来自家姐联盟小程序的订阅消息！</text>
			</view>
		</view>

		<view class="btn-big" v-if="!isLogin">
			<button @click="tryAuth()">确认授权</button>
		</view>

		<view style="width: 86%;padding: 20rpx 7% 40rpx 7%;" v-if="isLogin">
			<text style="display: block;font-size: 60rpx;line-height: 60rpx; color: #19be6b;">
				恭喜您！已授权成功！可以在公众号中接收家姐联盟的最新消息啦！</text>
		</view>

	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可配置选项

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				loadMore: 0,

				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				isLogin: false,

				code: 'xx',
				state: '',
				res: '',
				phone: '',

			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			tryAuth() {
				this.openCheck(0, "确定进行授权吗？", "将提高消息接受即时性！")
			},
			// 复制文本
			saveText(value) {
				let text = ''
				let name = ''
				let nameData = ['授权码', '授权状态', '请求响应']
				switch (value) {
					case 0:
						text = this.code
						break
					case 1:
						text = this.state
						break
					case 2:
						text = this.res
						break
				}
				name = nameData[value]
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success(name + '复制成功!')
					}
				})
			},
			checkMemberBind() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/member/checkMemberBind',
					method: 'POST',
					data: {
						source: "xyjAcnOa",
						phone: this.phone
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("该账号已授权！无需重复授权！")
							this.isLogin = true
						} else {
							this.isLogin = false
						}
					},
				})
			},
			async officialAccountOpenLogin() {
				const phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.phone)) {
					this.$refs.uNotify.error("请输入正确手机号！")
					return
				}
				await this.checkMemberBind()
				if (this.isLogin) {
					return
				} else {
					this.http({
						outsideUrl: 'https://api2.xiaoyujia.com/member/officialAccountOpenLogin',
						method: 'POST',
						data: {
							source: "xyjAcnOa",
							sourceType: 63,
							code: this.code,
							phone: this.phone
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							this.res = res
							if (res.code == 0) {
								this.$refs.uNotify.success("公众号消息通知授权成功！")
								this.isLogin = true
							} else {
								this.$refs.uNotify.error(res.msg)
								this.isLogin = false
							}
						},
						fail: err => {
							this.$refs.uNotify.error(JSON.stringify(err))
						}
					})
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.officialAccountOpenLogin()
				}
			},
		},
		watch: {
			scrollTop: {
				handler(newValue, oldVal) {

				},
				deep: true
			}
		},
		onLoad(options) {
			this.code = options.code
			this.state = options.state
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.tips {
		font-size: 36rpx;
		margin: 20rpx 40rpx;
	}
</style>