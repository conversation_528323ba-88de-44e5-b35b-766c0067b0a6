<template>
	<view style="padding: 150rpx 0 0;" class="bg-style">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="bgBox flex-col-c c6 f16" style="padding-top: 80rpx;">
			<image class="imgBox" :src="storeManager.headPortrait||storeHeadImg" mode=""></image>
			<view class="fb c1e1848">{{storeManager.realName || ''}}</view>
			<view class="f14 lh30">{{store.storeName || ''}}</view>
			<view class="w10 mg-at flac-row-a text-c" style="margin-top: 20rpx;">
				<view class="">
					<view class="fb c1e1848">{{storeScore || '-'}}</view>
					<view class="f14">门店评分</view>
				</view>
				<view class="">
					<view class="fb c1e1848">{{agentStoreInfo.orderNeedsCount || '-'}}</view>
					<view class="f14">累计成交</view>
				</view>
				<view class="">
					<view class="fb c1e1848">{{agentStoreInfo.score || '-'}}</view>
					<view class="f14">服务分</view>
				</view>
			</view>
		</view>

		<!-- 直播预告 -->
		<!-- 		<view class="bgBox flac-row-b f14">
			<view class="flac-row">
				<image class="iconbox"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/hot.png" />
				<view class="t-indent">直播/直播预告</view>
			</view>
			<view class="flac-row">
				<view class="t-indent">{{liveTitle || '9.8家姐空降直播间！'}}</view>
				<uni-icons type="forward" size="16"></uni-icons>
			</view>
		</view> -->

		<swiper class="swiper" :autoplay="autoplay&&!isEdit" :interval="interval" :duration="duration">
			<swiper-item>
				<!-- 门店介绍 -->
				<view class="bgBox" style="padding-top: 40rpx;margin: 20rpx auto 0 auto;">
					<view class="c1e1848 fb">门店照片
						<uni-icons :type="isEdit?'checkmarkempty':'compose'" size="18" @click="editStore()"
							v-if="openEdit"></uni-icons>
					</view>
					<view class="f15 c6 flac-col" style="margin: 20rpx auto;" v-if="!isEdit">
						<text>{{store.storeName || ''}}</text>
						<text>{{store.storeIntroduce || ''}}</text>
					</view>
					<view class="f15 c6" style="margin: 20rpx auto;" v-if="isEdit">
						<input type="text" v-model="store.storeIntroduce" placeholder="请填写门店简介">
					</view>

					<uni-swiper-dot class="uni-swiper-dot-box" @clickItem="clickItem1" :info="storeImgList"
						:current="current1" :mode="mode" :dots-styles="dotsStyles" field="content1">
						<swiper class="" @change="change1" :current="swiperDotIndex1">
							<swiper-item class="w9 mg-at" v-for="(item, index) in storeImgList" :key="index"
								@click="isEdit?uploadImg(index):openImgPreview(item.url)">
								<image class="imgStyle" :src="item.url" mode="aspectFill">
								</image>
							</swiper-item>
						</swiper>
					</uni-swiper-dot>
				</view>
			</swiper-item>
			<swiper-item>
				<!-- 门店位置 -->
				<view class="bgBox" style="padding-top: 40rpx;margin: 20rpx auto 0 auto;">
					<view class="c1e1848 fb">门店位置</view>
					<view class="f15 c6" style="margin: 20rpx auto;">
						{{storeAddress || ''}}
					</view>
					<map style="width: 100%; height: 275rpx;margin: 0rpx auto;" :show-location="false" ref="map"
						id="map" :scale="scale" :latitude="lat" :longitude="lng" @tap="openMap" />
				</view>
			</swiper-item>
		</swiper>

		<!-- 课程推荐 -->
		<view class="bgBox" style="padding-top: 20rpx;">
			<view class="flac-row-b lh50">
				<view class="c1e1848 fb">课程推荐</view>
				<view class="f14" @click="openMoreCourse()">更多课程 ></view>
			</view>
			<scroll-view scroll-x="true" :scroll-left="scrollLeft" id="course">
				<view style="display: flex;">
					<view class="swiper-box" v-for="(item, index) in courseList" :key="index"
						@click="openCourseDetail(item.id)">
						<image class="swiper-imgBox" :src="item.courseImg||blankCourseImg" mode=""></image>
						<view class="textBox">
							<text>{{item.courseTitle}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 门店订单推荐 -->
		<view class="bgBox" style="padding-top: 20rpx;">
			<view class="c1e1848 fb lh50">限时好单推荐</view>
			<view class="bgBox2 radius10" v-for="(item,index) in orderNeedsList" :key="index">
				<view class="flac-row-b lh30 c1e1848">
					<view class="">{{item.productName || '-'}}</view>
					<view class="">{{formatSalary(item.salary)}}</view>
				</view>
				<view class="flac-row c6" style="margin: 10rpx auto;">
					工作地点：
					<view class="w6">{{formatAddress(item.street)}}</view>
				</view>
				<view class="flac-row c6" style="margin: 10rpx auto;word-break: break-all;">
					工作内容：
					<view class="w6">{{item.workContent || '-'}}</view>
				</view>
				<view class="flac-row c6" style="margin: 10rpx auto;">
					工作要求：
					<view class="w6">{{item.workRequire || '-'}}</view>
				</view>
				<view class="btnStyle" @click="openWorksDetail(index)">我要接单</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 轮播设置
				autoplay: true,
				interval: 5000,
				duration: 600,
				// 是否开启资料编辑
				openEdit: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				scrollLeft: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				swiperPlayTime: 0,

				isEdit: false,
				num: '',
				storeHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				blankCourseImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_course_img.png',
				mode: 'default',
				dotsStyles: {
					backgroundColor: 'rgba(0, 0, 0, .3)',
					border: '1px rgba(0, 0, 0, .3) solid',
					color: '#fff',
					selectedBackgroundColor: 'rgba(0, 0, 0, .9)',
					selectedBorder: '1px rgba(0, 0, 0, .9) solid'
				},
				current1: 0,
				current2: 0,
				swiperDotIndex1: 0,
				swiperDotIndex2: 0,
				orderList: [{}],
				searchCondition: {
					productName: '',
					productId: null,
					isPush: 1,
					flowStatus: -1,
					isMine: 1,
					isSalary: 1,
					agentId: 0,
					orderBy: 'ons.startTime DESC',
					current: 1,
					size: 4
				},
				lat: uni.getStorageSync('lat'),
				lng: uni.getStorageSync('lng'),
				scale: 16,

				storeManagerId: 0,
				storeManager: {},
				store: {},
				storeImgList: [],
				storeAddress: '',
				agentStoreInfo: {},
				storeScore: 0,
				courseList: [],
				orderNeedsList: [],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			change1(e) {
				this.current1 = e.detail.current
			},
			clickItem1(e) {
				this.swiperDotIndex1 = e
			},
			change2(e) {
				this.current2 = e.detail.current
			},
			clickItem2(e) {
				this.swiperDotIndex2 = e
			},
			// 编辑门店
			editStore() {
				if (this.isEdit) {
					this.http({
						url: 'updateStoreIntroduce',
						method: 'POST',
						hideLoading: true,
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: this.store,
						success: res => {
							if (res.code == 0) {
								this.isEdit = false
								this.$refs.uNotify.success("门店信息保存成功！")
							}
						}
					})
				} else {
					this.isEdit = true
				}
			},
			// 上传图片
			uploadImg(index) {
				if (!this.isEdit) {
					return
				}

				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: 'https://api2.xiaoyujia.com/system/imageUpload',
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								this.storeImgList[index].url = result.data
								this.$set(this.store, this.storeImgList[index].imgKey, result.data)
							}
						});
					}
				});
			},
			openMoreCourse() {
				// #ifdef MP-WEIXIN
				uni.navigateToMiniProgram({
					appId: 'wxd64d55d779f303d0',
					path: 'pages/course/course',
					envVersion: "release",
					success: res => {},
					fail: err => {
						this.$refs.uNotify.error(err)
					}
				});
				// #endif

				// #ifndef MP-WEIXIN
				uni.navigateTo({
					url: '/pages-mine/studyCenter/index'
				})
				// #endif
			},
			openCourseDetail(id) {
				// let id = this.courseList[index].id
				// #ifdef MP-WEIXIN
				uni.navigateToMiniProgram({
					appId: 'wxd64d55d779f303d0',
					path: 'pages/course/course-detail/course-detail?courseId=' + id,
					envVersion: "release",
					success: res => {},
					fail: err => {
						this.$refs.uNotify.error(err)
					}
				});
				// #endif

				// #ifndef MP-WEIXIN
				uni.navigateTo({
					url: '/pages-mine/studyCenter/course-detail?id=' + id
				})
				// #endif
			},
			// 打开线索详情
			openWorksDetail(index) {
				uni.navigateTo({
					url: "/pages-mine/works/works-detail?id=" + this.orderNeedsList[index].id
				})
			},
			// 打开头像预览
			openImgPreview(url) {
				let data = []
				data.push(url)

				uni.previewImage({
					urls: data,
					current: url
				})
			},
			// 打开地图
			openMap() {
				let name = this.storeAddress
				uni.openLocation({
					latitude: this.lat,
					longitude: this.lng,
					name: name,
					success: function() {
						console.log('success')
					}
				})
			},
			// 格式化期望薪资
			formatSalary(salary) {
				// 如果工资字段值存在，则直接返回值
				if (salary != null && salary != 0) {
					return salary + "元/月"
				} else {
					return "工资面议"
				}
			},
			formatAddress(str) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
					}
				}

				return result
			},
			listCourse() {
				this.http({
					url: 'listCourse',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						storePush: 1
					},
					success: res => {
						if (res.code == 0) {
							this.courseList = res.data
						}
					}
				})
			},
			getOrderNeedsPage() {
				this.searchCondition.agentId = this.storeManagerId
				this.searchCondition.channel = this.storeManager.no
				this.http({
					url: 'getOrderNeedsPage',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let data = res.data.records
							this.orderNeedsList = this.orderNeedsList.concat(data)
							if (this.searchCondition.current != 1 && data.length == 0) {
								this.$refs.uNotify.error("没有更多推荐了哦！")
							}
						}
					}
				})
			},
			getStore() {
				this.http({
					url: 'getStoreById',
					method: 'GET',
					hideLoading: true,
					path: this.storeId,
					success: res => {
						if (res.code == 0) {
							this.store = res.data
							this.storeAddress = this.store.cityName + this.store.areaName + this.store.addr
							this.lat = this.store.lat
							this.lng = this.store.lng
							this.storeImgList = []
							let data = {
								imgKey: 'storeHeadImg',
								url: this.store.storeHeadImg || this.blankCourseImg
							}
							this.storeImgList.push(data)
							data = {
								imgKey: 'storeInImg',
								url: this.store.storeInImg || this.blankCourseImg
							}
							this.storeImgList.push(data)
						}
					}
				})
			},
			getAgentStoreInfo() {
				this.http({
					url: 'getAgentStoreInfo',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						employeeId: this.storeManagerId
					},
					success: res => {
						if (res.code == 0) {
							this.agentStoreInfo = res.data
						}
					}
				})
			},
			getServiceAcceptanceScore() {
				this.http({
					url: 'getServiceAcceptanceScore',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						storeId: this.storeId
					},
					success: res => {
						if (res.code == 0) {
							this.storeScore = parseFloat(res.data[0].score).toFixed(1)
						}
					}
				})
			},
			getStoreManager() {
				this.http({
					url: 'getStoreManager',
					method: 'GET',
					hideLoading: true,
					path: this.storeId,
					success: res => {
						if (res.code == 0) {
							this.storeManager = res.data
							this.storeManagerId = res.data.id
							this.getStore()
							this.getAgentStoreInfo()
							this.getServiceAcceptanceScore()
							this.getOrderNeedsPage()
						}
					},
				})
			},
			// 分享
			onShareAppMessage(res) {
				return {
					title: '快来了解我们的门店吧！',
					path: '/pages-other/store/index',
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
		watch: {
			swiperPlayTime: {
				handler(newValue, oldVal) {
					// 课程自动轮播
					let interval = this.interval / 1000
					let screenWidth = 200
					let needUpdate = this.swiperPlayTime % interval == 0 || this.swiperPlayTime == 0
					if (needUpdate) {
						if (this.scrollLeft < (this.courseList.length - 2) * screenWidth) {
							this.scrollLeft += screenWidth
						} else {
							this.scrollLeft = 0
						}
					}
				},
				deep: true
			}
		},
		onReachBottom() {
			this.searchCondition.current++
			this.getOrderNeedsPage()
		},
		onLoad(options) {
			this.storeId = options.id || 0
			this.getStoreManager()
			this.listCourse()
			// 开启计时器
			let time = setInterval(() => {
				this.swiperPlayTime += 1
			}, 1000);
		},
	}
</script>
<style lang="scss" scoped>
	.bg-style {
		width: 100%;
		min-height: 100vh;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/storeBg.png') no-repeat center;
		background-size: 100% 100%;
	}

	.bgBox {
		position: relative;
		width: 80%;
		margin: 20rpx auto;
		padding: 40rpx;
		background-color: #fff;
		border-radius: 20rpx;
	}

	.imgBox {
		width: 150rpx;
		height: 150rpx;
		background-color: #a8a8a8;
		border: 4rpx solid #fff;
		border-radius: 50%;
		position: absolute;
		bottom: 75%;
	}

	.iconbox {
		width: 30rpx;
		height: 35rpx;
	}

	.swiper-box {
		margin-bottom: 30rpx;
		margin: 10rpx 20rpx 0rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.swiper-imgBox {
		position: relative;
		width: 260rpx;
		height: 156rpx;
		margin: auto;
		border-radius: 20rpx;
		// background: #f4f3f2;
		z-index: 999;
	}

	.textBox {
		width: 260rpx;
		height: 45rpx;
		line-height: 60rpx;
		padding: 14rpx 0;
		margin: -20rpx 0 0 0;
		text-align: center;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.5);
		border-bottom-left-radius: 20rpx;
		border-bottom-right-radius: 20rpx;
	}

	.bgBox2 {
		background-color: #f5f5f5;
		padding: 20rpx 40rpx;
		margin: 20rpx 0;
	}

	.btnStyle {
		background-color: #1e1848;
		color: #fff;
		text-align: center;
		width: 30%;
		font-size: 14px;
		padding: 10rpx;
		margin-top: 20rpx;
		border-radius: 16rpx;
		margin-left: 68%;
	}

	.imgStyle {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
	}

	.c1e1848 {
		color: #1e1848;
	}

	.swiper {
		margin: 0 0;
		width: 100%;
		height: 550rpx;
	}
</style>