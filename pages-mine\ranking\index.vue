<template>
	<view class="bgStyle">
		<view class="headBox">
			<view class="w8 mg-at flac-row-a">
				<view v-for="(item,i) in tablist" :key="i" :class="item.id == tabIndex ? 'Tactive cf f18':'noTActive'"
					@click="changeType(item.id)">{{item.name}}</view>
			</view>
			<view class="tagBox flac-row-c">
				<view v-for="(item,i) in datelist" :key="i"
					:class="item.id == dateIndex ? 'w45 mg-at text-c lh20 Dactive':'w45 mg-at text-c lh20 noDActive'"
					@click="changeDate(i)">{{item.name}}</view>
			</view>
			<view class="rankTop cf f15">
				<view class="top-itemA">
					<view class="top-item-boxA">
						<image class="top-item-bgA"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/rank2.png"
							mode="widthFix">
						</image>
						<image class="top-item-avatarA" :src="rankList[1].headPortrait || headImg" mode="widthFix">
						</image>
						<view class="top-item-textA flex-col-c">
							<view class="top-item-name">{{rankList[1].storeName}}</view>
							<view class="top-item-score">{{rankList[1].orderNum || 0 }}</view>
						</view>
					</view>
				</view>
				<view class="top-itemB">
					<view class="top-item-boxB">
						<image class="top-item-bgB"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/rank1.png"
							mode="widthFix">
						</image>
						<image class="top-item-avatarB" :src="rankList[0].headPortrait || headImg" mode="widthFix">
						</image>
						<view class="top-item-textB flex-col-c">
							<view class="top-item-name">{{rankList[0].storeName}}</view>
							<view class="top-item-score">{{rankList[0].orderNum || 0 }}</view>
						</view>
					</view>
				</view>
				<view class="top-itemC">
					<view class="top-item-boxC">
						<image class="top-item-bgC"
							src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/rank3.png"
							mode="widthFix">
						</image>
						<image class="top-item-avatarC" :src="rankList[2].headPortrait || headImg" mode="widthFix">
						</image>
						<view class="top-item-textC flex-col-c">
							<view class="top-item-name">{{rankList[2].storeName}}</view>
							<view class="top-item-score">{{rankList[2].orderNum || 0 }}</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <image class="iconStyle"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon02.png"
				mode="widthFix"></image> -->
		</view>
		<view class="ranking">
			<view class="w9 mg-at f14 c9 flac-row-b">
				<view class="flac-row">
					<view>{{text}}</view>
					<image style="width: 30rpx;margin-left: 10rpx;"
						src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon01.png"
						mode="widthFix"></image>
				</view>
				<view class="">总计</view>
			</view>
			<view class="ranking-list-item" v-for="(item, i) in rankList" :key="i" v-if="i > 2">
				<text class="ranking-list-number">{{i+1}}</text>
				<view class="ranking-list-nickname">
					<image :src="item.headPortrait||headImg">
					</image>
					<text>{{item.storeName || '未命名'}}</text>
				</view>
				<text class="ranking-list-score">{{item.orderNum}}</text>
			</view>
			<view class="myranking flac-row-b" v-if="roleId!=1&&memberId&&myChart">
				<view class="fb">{{myChart.ranking>10?'10+':myChart.ranking+1}}</view>
				<view class="w6 flac-row">
					<image style="width: 50rpx;" :src="myChart.headPortrait||headImg" mode="widthFix"></image>
					<view class="t-indent2 f15">{{myChart.storeName}}</view>
				</view>
				<view class="f14 ce">
					{{myChart.ranking>10?'未上榜':myChart.ranking<=3?'继续保持哦':myChart.ranking>3?'继续加油哦':''}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'rank',
		props: {
			refresh: {
				type: Boolean
			}
		},
		data() {
			return {
				text: '',
				roleId: uni.getStorageSync('roleId'),
				memberId: uni.getStorageSync('memberId'),
				tabIndex: 1, //根据角色的不同，默认展示的数据也不同
				tablist: [],
				nowIndex: null,
				myChart: {},
				dateIndex: 0,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				datelist: [{
					id: 0,
					name: '周榜'
				}, {
					id: 1,
					name: '月榜'
				}],
				rankList: [],
			};
		},
		mounted() {
			let arr = []
			if (uni.getStorageSync('employeeType') == 20 || uni.getStorageSync('roleId') == 1 || !uni.getStorageSync(
					'memberId')) {
				let obj = {
					id: 1,
					name: '单量榜'
				}
				this.nowIndex = 1
				arr.push(obj)
				this.getStoreOrderCharts()
				this.text = '全国门店接单奖励'
			}
			if (uni.getStorageSync('employeeType') == 10 || uni.getStorageSync('roleId') == 1 || !uni.getStorageSync(
					'memberId')) {
				let obj = {
					id: 0,
					name: '好评榜'
				}
				arr.push(obj)
				let obj2 = {
					id: 2,
					name: '复购榜'
				}
				arr.push(obj2)
				if (uni.getStorageSync('employeeType') == 10 || uni.getStorageSync('roleId') == 1) {
					this.tabIndex = 0
					this.nowIndex = 0
					this.getEmployeeRatingCharts()
					this.text = '全国员工好评奖励'
				}
			}
			this.tablist = arr
		},
		methods: {
			getEmployeeRepurchaseCharts() {
				this.http({
					url: "getEmployeeRepurchaseCharts",
					method: 'GET',
					hideLoading: true,
					data: {
						getType: this.dateIndex,
						employeeNo: uni.getStorageSync('employeeNo')
					},
					success: res => {
						if (res.code == 0) {
							this.rankList = res.data.chartsList
							this.myChart = res.data.myChart
						}
					},
				})
			},
			getEmployeeRatingCharts() {
				this.http({
					url: "getEmployeeRatingCharts",
					method: 'GET',
					hideLoading: true,
					data: {
						getType: this.dateIndex,
						employeeNo: uni.getStorageSync('employeeNo')
					},
					success: res => {
						if (res.code == 0) {
							this.rankList = res.data.chartsList
							this.myChart = res.data.myChart
						}
					},
				})
			},
			getStoreOrderCharts() {
				this.http({
					url: "getStoreOrderCharts",
					method: 'GET',
					hideLoading: true,
					data: {
						getType: this.dateIndex,
						storeId: uni.getStorageSync('storeId')
					},
					success: res => {
						if (res.code == 0) {
							this.rankList = res.data.chartsList
							this.myChart = res.data.myChart
						}
					},
				})
			},
			changeType(i) {
				this.tabIndex = i
				this.nowIndex = i
				switch (this.nowIndex) {
					case 0:
						this.getEmployeeRatingCharts()
						this.text = '全国员工好评奖励'
						break;
					case 1:
						this.getStoreOrderCharts()
						this.text = '全国门店接单奖励'
						break;
					case 2:
						this.getEmployeeRepurchaseCharts()
						this.text = '全国员工复购奖励'
						break;
				}
			},
			changeDate(i) {
				this.dateIndex = i
				switch (this.nowIndex) {
					case 0:
						this.getEmployeeRatingCharts()
						break;
					case 1:
						this.getStoreOrderCharts()
						break;
					case 2:
						this.getEmployeeRepurchaseCharts()
						break;
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.bgStyle {
		width: 100%;
		min-height: 100vh;
		background-color: #25256c;
	}

	.headBox {
		width: 100%;
		height: 660rpx;
		padding-top: 60rpx;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/bg03.png') no-repeat center;
		background-size: 100% 100%;
	}

	.noTActive {
		color: #B3B2FF;
	}

	.Tactive {
		// padding-bottom: 10rpx;
		// border-bottom: 2px solid #fff;
		text-decoration: underline;
		text-underline-offset: 20rpx;
	}

	.tagBox {
		width: 30%;
		margin: 50rpx auto 20rpx;
		background-color: rgba(255, 255, 255, 0.4);
		color: #fff;
		padding: 6rpx 0;
		border-radius: 30rpx;
		font-size: 28rpx;
	}

	.Dactive {
		background-color: #fff;
		color: #25256c;
		border-radius: 20rpx;
	}

	.noDActive {
		color: #fff;
		border-radius: 20rpx;
	}

	.rankTop {
		width: 100%;
		margin: auto;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
	}

	.top-item-boxA,
	.top-item-boxB,
	.top-item-boxC {
		width: 250rpx;
		height: auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.top-item-bgA,
	.top-item-bgB,
	.top-item-bgC {
		//头像背景
		position: absolute;
		border-radius: 50%;
		z-index: 99;
	}

	.top-item-bgA {
		width: 230rpx;
		top: 80rpx;
		left: 50rpx;
	}

	.top-item-bgB {
		width: 200rpx;
		top: 10rpx;
		left: 40rpx;
	}

	.top-item-bgC {
		width: 220rpx;
		top: 140rpx;
		right: 60rpx;
	}

	.top-item-avatarA,
	.top-item-avatarB,
	.top-item-avatarC {
		//头像
		position: absolute;
		border-radius: 50%;
		z-index: 9;
		background-color: #fff;
	}

	.top-item-avatarA {
		width: 120rpx;
		top: 140rpx;
		left: 105rpx;
	}

	.top-item-avatarB {
		width: 150rpx;
		top: 40rpx;
		left: 60rpx;
	}

	.top-item-avatarC {
		width: 110rpx;
		top: 200rpx;
		right: 115rpx;
	}

	.top-item-textA,
	.top-item-textB,
	.top-item-textC {
		position: absolute;
		border-radius: 50%;
		z-index: 9;
	}

	.top-item-textA {
		width: 120rpx;
		top: 300rpx;
		left: 105rpx;
	}

	.top-item-textB {
		width: 150rpx;
		top: 230rpx;
		left: 60rpx;
	}

	.top-item-textC {
		width: 110rpx;
		top: 330rpx;
		right: 115rpx;
	}

	.iconStyle {
		width: 100rpx;
		position: fixed;
		top: 660rpx;
		right: 0;
	}

	.ranking {
		width: 100%;
		border-top-right-radius: 30rpx;
		border-top-left-radius: 30rpx;
		margin: auto;
		background: #fff;
		box-sizing: border-box;
		padding: 20rpx;
		padding-bottom: 120rpx;

		.ranking-list-item {
			width: 90%;
			margin: 40rpx auto;
			display: flex;
			align-items: center;
			font-size: 14px;

			.ranking-list-number {
				display: block;
				width: 70rpx;
				color: #999;
				font-weight: 500;
			}

			.ranking-list-score {
				display: block;
				// width: 70rpx;
				// color: #E28935;
				font-size: 16px;
			}

			.ranking-list-nickname {
				display: flex;
				align-items: center;
				width: calc(100% - 120rpx);

				image {
					display: block;
					width: 70rpx;
					height: 70rpx;
					border-radius: 50%;
					margin-right: 20rpx;
				}

				text {
					width: auto;
				}
			}

		}

		.myranking {
			width: 90%;
			color: #fff;
			background-color: #4646a4;
			position: fixed;
			bottom: 130rpx;
			left: 32;
			border-radius: 20rpx;
			padding: 20rpx;
		}

	}
</style>