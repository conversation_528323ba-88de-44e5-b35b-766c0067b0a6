<template>
	<view class="f15 page">
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 报告摘要 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">报告摘要</view>
				<view class="w3 c6fb888 flac-row f14"><uni-icons type="checkmarkempty" color="#6fb888"
						size="18"></uni-icons>
					查询成功</view>
			</view>
			<view class="flex-col-c" style="margin: 30rpx auto;">
				<image style="width: 120rpx;"
					:src=" authenticationFlag == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
					mode="widthFix"></image>
				<view class="fb lh30">{{ authenticationFlag == 0 ? '确认过背景，TA是对的人' : '确认过背景，TA不是对的人'}}</view>
				<view class="w10 mg-at flac-row-c" style="margin: 50rpx auto 40rpx;">
					<view class="line"></view>
					<view class="c9" style="margin: auto 30rpx;">查询项目</view>
					<view class="line"></view>
				</view>
				<view class="w10 mg-at flac-row-c f12 text-c" style="flex-wrap: wrap;">
					<view class="tagStyle">失信被执行人查询</view>
					<view class="tagStyle">人员涉诉信息精准查询</view>
					<view class="tagStyle">人员涉诉信息查询【详情版】</view>
					<!-- <view class="tagStyle">不良人员名单查询</view> -->
				</view>
			</view>
		</view>
		<!-- 身份信息验证 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">身份信息验证</view>
				<image class="w2" style="position: absolute;right: 15%;top: 15%;"
					:src="photoWitness.photoWitnessFlag == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/success.png' :'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/fail.png'"
					mode="widthFix">
				</image>
			</view>
			<view class="t-indent2 f12 lh30 c9">流水号：{{photoWitness.outTradeNo || ''}}</view>
			<view class="flex-col-c lh35">
				<view class="w9 flac-row-b">姓名：<view class="f14 c6">{{name || 'XXX'}}</view>
				</view>
				<!-- <view class="w9 flac-row-b">性别：<view class="f14 c6">{{cardidInfo.sex || '女'}}</view></view> -->
				<view class="w9 flac-row-b">身份证号：<view class="f14 c6">
						{{(cardID || '12345678901').replace(/(\w{6})\w*(\w{4})/, '$1******$2')}}
					</view>
				</view>
				<view class="w9 flac-row-b">照片人证对比：<view class="f14 c6">{{photoWitness.resultMsg}}</view>
				</view>
				<view class="w9 flac-row-b" style="margin-left: 95%" v-if="photoWitness.photoWitnessFlag==1">
					<u-tag text="更新认证图片并重新认证" @click="uploadImg" plain size="mini" type="error"></u-tag>
				</view>
			</view>
		</view>
		<!-- 失信被执行人查询 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">失信被执行人查询</view>
				<view class="w3 c6fb888 flac-row f14"><uni-icons type="checkmarkempty" color="#6fb888"
						size="18"></uni-icons>
					查询成功</view>
			</view>
			<view class="t-indent f12 lh30 c9">流水号：{{specialList.outTradeNo}}</view>
			<view class="text-c" style="color: #ddd;margin-bottom: 20rpx;">—<text class="c0"
					style="margin: auto 20rpx;">查询内容</text> —
			</view>
			<view class="w9 mg-at flex-col-c lh35 c6fb888">
				<view class="w95 flac-row-b lh40 cellStyle">
					<view class="w8">失信人命中次数</view>
					<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
						<image class="w15"
							:src="slIdCourtBadAllnum == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
							mode="widthFix"></image>
						<view class="">{{slIdCourtBadAllnum || 0}}次</view>
					</view>
				</view>
				<view class="w95 flac-row-b lh40">
					<view class="w8">被执行人命中次数</view>
					<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
						<image class="w15"
							:src="slIdCourtExecutedAllnum == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
							mode="widthFix"></image>
						<view class="">{{slIdCourtExecutedAllnum}}次</view>
					</view>
				</view>
				<view class="w95 flac-row-b lh40 cellStyle">
					<view class="w8">资信不佳命中次数</view>
					<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
						<image class="w15"
							:src="slIdBankFraudAllnum == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
							mode="widthFix"></image>
						<view class="">{{slIdBankFraudAllnum}}次</view>
					</view>
				</view>
			</view>
			<view class="w9 mg-at f12 c9" style="margin-top: 20rpx;">
				<text class="fb">失信被执行人</text>是指在执行过程中违反法院的判决或裁定，或者其他法律法规规定的行为，被法院列入失信被执行人名单。
			</view>
		</view>
		<!-- 人员涉诉信息精准查询 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">人员涉诉信息精准查询</view>
				<view class="w3 c6fb888 flac-row f14"><uni-icons type="checkmarkempty" color="#6fb888"
						size="18"></uni-icons>
					查询成功</view>
			</view>
			<view class="" v-if="realname==null">
				<view class="w10 mg-at c9 flac-row-c" style="margin: 50rpx auto 40rpx;">
					—— 查无记录 ——
				</view>
			</view>
			<view class="" v-if="realname!=null">
				<view class="t-indent f12 lh30 c9">流水号：{{realname.outTradeNo || ''}}</view>
				<view class="text-c" style="color: #ddd;margin-bottom: 20rpx;">—<text class="c0"
						style="margin: auto 20rpx;">查询内容</text> —
				</view>
				<view class="w9 mg-at flex-col-c lh35 c6fb888">
					<view class="w95 flac-row-b lh30 cellStyle">
						<view class="w8">是否为失信被执行人</view>
						<view class="w9 f14 flac-row-c lh30" style="text-indent: 8rpx;justify-content: flex-end;">
							<image class="w15"
								:src="realname.isSxbzxr == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{realname.isSxbzxr == 0 ? '否' : '是'}}</view>
						</view>
					</view>
					<view class="w95 flac-row-b lh30 cellStyle">
						<view class="w8">是否为限高被执行人</view>
						<view class="w9 f14 flac-row-c lh30" style="text-indent: 8rpx;justify-content: flex-end;">
							<image class="w15"
								:src="realname.isXgbzxr == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{realname.isXgbzxr == 0 ? '否' : '是'}}</view>
						</view>
					</view>
					<view class="w95 flac-row-b lh30 cellStyle">
						<view class="w8">是否涉案</view>
						<view class="w9 f14 flac-row-c lh30" style="text-indent: 8rpx;justify-content: flex-end;"
							@click="showDetail = !showDetail">
							<image class="w15"
								:src="realname.isCases == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{realname.isCases == 0 ? '否' : '是'}}</view>
							<uni-icons :type="showDetail == true ? 'top' : 'bottom'" size="18" color="#888"
								v-if="realname.isCases == 1"></uni-icons>
						</view>
					</view>
					<view class="w95 lh30 c3 f14" v-if="showDetail == true">
						<view class="lh25">
							<view class="">民事案件总数：{{realname.casesCount.civilCount || 0}}次</view>
							<view class="">刑事案件总数：{{realname.casesCount.criminalCount || 0}}次</view>
							<view class="">行政案件总数：{{realname.casesCount.administrativeCount || 0}}次</view>
							<view class="">执行案件总数：{{realname.casesCount.implementCount || 0}}次</view>
							<view class="">破产案件总数：{{realname.casesCount.bankruptCount || 0}}次</view>
							<view class="">作为原告涉案总数：{{realname.casesCount.plaintiffCount || 0}}次<text
									v-if="(realname.casesCount.plaintiffCount || 0) > 0" class="red"
									@click="openShow(0)">（查看详情）</text></view>
							<view class="">作为被告涉案总数：{{realname.casesCount.defendantCount || 0}}次<text
									v-if="(realname.casesCount.defendantCount || 0) > 0" class="red"
									@click="openShow(1)">（查看详情）</text></view>
							<view class="">作为第三方涉案总数：{{realname.casesCount.otherCount || 0}}次<text
									v-if="(realname.casesCount.otherCount|| 0) > 0" class="red"
									@click="openShow(2)">（查看详情）</text></view>
						</view>
					</view>
				</view>
			</view>
			<!-- 弹窗展示详情 -->
			<u-modal :show="showModal" title="详情内容" @confirm="showModal = false">
				<view class="w10 mg-at" v-if="status == 0 && realname">
					<view class="fb"><text class="lh40 fb4">作为原告涉案总金额等级：</text>{{realname.plaintiffList.plaintiffMoney}}
					</view>
					<view class="">
						<view class="lh40">作为原告未结案件案由分布：</view>
						<view class="f14 fb">{{realname.plaintiffList.endPlaintiffAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为原告已结案件案由分布：</view>
						<view class="f14 fb">{{realname.plaintiffList.openPlaintiffAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为原告涉案时间分布：</view>
						<view class="f14 fb">{{realname.plaintiffList.plaintiffLarqStat}}</view>
					</view>
				</view>
				<view class="w10 mg-at" v-if="status == 1 && realname">
					<view class="fb"><text class="lh40 fb4">作为被告涉案总金额等级：</text>{{realname.defendantList.defendantMoney}}
					</view>
					<view class="">
						<view class="lh40">作为被告未结案件案由分布：</view>
						<view class="f14 fb">{{realname.defendantList.endDefendantAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为被告已结案件案由分布：</view>
						<view class="f14 fb">{{realname.defendantList.openDefendantAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为被告涉案时间分布：</view>
						<view class="f14 fb">{{realname.defendantList.defendantLarqStat}}</view>
					</view>
				</view>
				<view class="w10 mg-at" v-if="status == 2 && realname">
					<view class="fb"><text class="lh40 fb4">作为第三方涉案总金额等级：</text>{{realname.otherList.otherMoney}}
					</view>
					<view class="">
						<view class="lh40">作为第三方未结案件案由分布：</view>
						<view class="f14 fb">{{realname.otherList.endOtherAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为第三方已结案件案由分布：</view>
						<view class="f14 fb">{{realname.otherList.openOtherAyStat}}</view>
					</view>
					<view class="">
						<view class="lh40">作为第三方涉案时间分布：</view>
						<view class="f14 fb">{{realname.otherList.otherLarqStat}}</view>
					</view>
				</view>
			</u-modal>
		</view>
		<!-- 人员涉诉信息精准查询【详情版】 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">人员信息精准查询【详情版】</view>
				<view class="w3 c6fb888 flac-row f14"><uni-icons type="checkmarkempty" color="#6fb888"
						size="18"></uni-icons>
					查询成功</view>
			</view>
			<view class="" v-if="litigationDetails == null">
				<view class="w10 mg-at c9 flac-row-c" style="margin: 50rpx auto 40rpx;">
					—— 查无记录 ——
				</view>
			</view>
			<view class="" v-if="litigationDetails != null">
				<view class="t-indent f12 lh30 c9">流水号：{{litigationDetails.outTradeNo}}</view>
				<view class="text-c" style="color: #ddd;margin-bottom: 20rpx;">—<text class="c0"
						style="margin: auto 20rpx;">案件总数：{{countList.totalCount || 0}}</text> —
				</view>
				<view class="w9 mg-at flex-col-c lh40 c6fb888">
					<view class="w95 flac-row-b cellStyle">
						<view class="w8">原告总数</view>
						<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
							<image class="w15"
								:src="countList.plaintiffCount == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{countList.plaintiffCount || 0}}次</view>
						</view>
					</view>
					<view class="w95 flac-row-b">
						<view class="w8">被告总数</view>
						<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
							<image class="w15"
								:src="countList.defendantCount == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{countList.defendantCount || 0}}次</view>
						</view>
					</view>
					<view class="w95 flac-row-b cellStyle">
						<view class="w8">第三方总数</view>
						<view class="w9 f14 flac-row-c" style="text-indent: 8rpx;justify-content: flex-end;">
							<image class="w15"
								:src="countList.otherCount == 0 ? 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/save01.png' : 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png'"
								mode="widthFix"></image>
							<view class="">{{countList.otherCount || 0}}次</view>
						</view>
					</view>
				</view>
				<view class="w9 mg-at lh30 f14" v-if="countList.totalCount > 0">
					<view class=""><text class="c6">涉案时间分布：</text>{{countList.larqStat}}</view>
					<view class=""><text class="c6">涉案地点分布：</text>{{countList.areaStat}}</view>
					<view class=""><text class="c6">涉案案由分布：</text>{{countList.ayStat}}</view>
					<view class=""><text class="c6">结案方式：</text>{{countList.endStat}}</view>
				</view>
				<view class="text-c red lh30 f14" @click="showModal2 = true" v-if="caselist.length">【 查看详情 】</view>
			</view>
			<u-modal :show="showModal2" title="详情内容" @confirm="showModal2 = false">
				<scroll-view scroll-y="true" style="height: 60vh;">
					<view v-for="(item,i) in caselist" :key="i">
						<view class="titleStyle t-indent f16">
							{{item.caseType == 0 ? '民事案件' : (item.caseType == 1 ? '执行案件': (item.caseType == 2 ? '刑事案件' : '强制清算与破产'))}}
						</view>
						<view v-for="(item2,i2) in item.caseDetailVos" :key="i2">
							<view class="lh30 f14 listStyle">
								<view class=""><text class="c6">案号：</text>{{item2.caseNo}}</view>
								<view class=""><text class="c6">立案时间：</text>{{item2.sortDate}}</view>
								<view class=""><text class="c6">立案案由：</text>{{item2.caseCause}}</view>
								<view class=""><text class="c6">立案案由详细：</text>{{item2.caseCauseTree}}</view>
								<view class=""><text class="c6">相关案件号：</text>{{item2.relaCaseNo}}</view>
								<view class=""><text class="c6">当事人：</text>{{item2.partyName}}</view>
								<view class=""><text class="c6">当事人说明：</text>{{item2.partyDesc}}</view>
								<view class=""><text class="c6">案件类型：</text>{{item2.caseType}}</view>
								<view class=""><text class="c6">案件进展阶段：</text>{{item2.caseProStage}}</view>
								<view class=""><text class="c6">后续案号：</text>{{item2.hxCase}}</view>
								<view class=""><text class="c6">结案案由：</text>{{item2.endCaseCause}}</view>
								<view class=""><text class="c6">结案案由详细：</text>{{item2.endCaseCauseTree}}</view>
								<view class=""><text class="c6">结案时间：</text>{{item2.endCaseDate}}</view>
								<view class=""><text class="c6">结案方式：</text>{{item2.endCaseWay}}</view>
								<view class=""><text class="c6">判决结果：</text>{{item2.judgmentResult}}</view>
								<view class=""><text class="c6">定罪罪名：</text>{{item2.dzzm}}</view>
								<view class=""><text class="c6">定罪罪名详情：</text>{{item2.dzzmTree}}</view>
								<view class=""><text class="c6">诉讼地位：</text>{{item2.partyPosition}}</view>
								<view class=""><text class="c6">一审诉讼地位：</text>{{item2.firstPartyPosition}}</view>
								<view class=""><text class="c6">经办法院：</text>{{item2.court}}</view>
								<view class=""><text class="c6">法院所属层级：</text>{{item2.courtRank}}</view>
								<view class=""><text class="c6">所属地域：</text>{{item2.region}}</view>
								<view class=""><text class="c6">审理程序：</text>{{item2.hearProcedure}}</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</u-modal>
		</view>
		<!-- 不良人名单信息查询 -->
		<!-- <view class="bgBox">
			<view class="w10 mg-at f16 title borderB flac-row-b">
				<view class="">不良人名单信息查询</view>
				<view class="w3 c6fb888 flac-row f14"><uni-icons type="checkmarkempty" color="#6fb888"
						size="18"></uni-icons>
					查询成功</view>
			</view>
			<view class="" v-if="badPersonnel == null ">
				<view class="w10 mg-at c9 flac-row-c" style="margin: 50rpx auto 40rpx;">
					—— 查无记录 ——
				</view>
			</view>
			<view class="" v-if="badPersonnel != null">
				<view class="t-indent f12 lh30 c9">流水号：{{badPersonnel.outTradeNo}}</view>
				<view class="text-c" style="color: #ddd;margin-bottom: 20rpx;">—<text class="c0"
						style="margin: auto 20rpx;">查询内容</text> —
				</view>
				<view class="w9 mg-at flex-col-c lh35 c6fb888" v-for="(item,i) in badPersonnel.badPersonnelList"
					:key="i">
					<view class="w95 flac-row-b lh40 cellStyle">
						<view class="w8">案件类型{{i+1}}</view>
						<view class="w9 f14 flac-row"
							style="text-indent: 8rpx;justify-content: flex-end;margin-right: 20rpx;"
							@click="clickOpenC(i)">
							<image class="w15"
								src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/dangerous01.png"
								mode="widthFix"></image>
							<view class="">{{item.from}}</view>
						</view>
						<uni-icons :type="item.showDetail == true ? 'top' : 'bottom'" size="18"
							color="#888"></uni-icons>
					</view>
					<view class="w95 lh40 c3 f12" v-if="item.showDetail == true">
						<view class="w10 flac-row">
							<text class="fb">案件明细：</text>
							<view class="" style="margin: auto 10rpx;" v-for="(item2,i2) in item.respTagList" :key="i2">
								{{item2}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view> -->
		<!-- 报告基本信息 -->
		<view class="bgBox">
			<view class="w10 mg-at f16 title borderB">报告基本信息</view>
			<view class="f14 lh30" style="margin: 20rpx auto;">
				<view class="flac-row">
					<view class="w25 text-r">查询日期：</view><text class="f14 c6">{{basicData.queryTime}}</text>
				</view>
				<view class="flac-row">
					<view class="w25 text-r">操作人：</view><text class="f14 c6">{{basicData.crePerson}}</text>
				</view>
			</view>
		</view>
		<!-- 使用说明 -->
		<view class="bgBox">
			<view class="text-c lh50">使用说明</view>
			<view class="w9 flac-row f12 c9" style="align-items: unset;margin: 20rpx auto;">
				<view class="c4a7aea fb f20" style="line-height: 50rpx;">·</view>
				<view class="w9 mg-at">本结果仅供委托人/门店招聘参考之用，未经被人事先书面许可，委托人/门店不得复制、转载、
					播或以其他任何方式公开被查询人个人信息，否则，委托人将承担法律责任。</view>
			</view>
			<view class="w9 flac-row f12 c9" style="align-items: unset;margin: 20rpx auto;">
				<view class="c4a7aea fb f20" style="line-height: 50rpx;">·</view>
				<view class="w9 mg-at">本结果仅供委托人/门店招聘参考之用，未经被人事先书面许可，委托人/门店不得复制、转载、
					播或以其他任何方式公开被查询人个人信息，否则，委托人将承担法律责任。</view>
			</view>
			<view class="w9 flac-row f12 c9" style="align-items: unset;margin: 20rpx auto;">
				<view class="c4a7aea fb f20" style="line-height: 50rpx;">·</view>
				<view class="w9 mg-at">
					本结果仅供参考，如查询结果与实际情况不符，请联系相应技术人员进行反馈，本平台与第三方机构负责寻找原因并更正查询结果。您及被查询人须自行判断雇用、交易、合作风险，本平台不为您的决定、行为和后果承担任何法律或经济责任。您不得以查询结果错误为由向平台主张经济损失补偿或赔偿。
				</view>
			</view>
			<view class="w9 flac-row f12 c9" style="align-items: unset;margin: 20rpx auto;">
				<view class="c4a7aea fb f20" style="line-height: 50rpx;">·</view>
				<view class="w9 mg-at">
					为保障被查询人平等就业的权利，除反馈与投诉有相关犯罪记录人员不得担任相应职务外，委托人/门店不应以查询结果作为录用标准，查询结果仅供参考。如因委托人/门店之决定导致被查询人无法录用、不予交易的，被查询人应与委托人/门店自行协商解决，与平台无关。
				</view>
			</view>
		</view>
		<!-- <view class="" style="position: fixed;bottom: 7%;right: 3%;" v-if="authenticationFlag == 1" @click="goPage">
			<image style="width: 150rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/lookdetail.png" mode="widthFix">
			</image>
		</view> -->
		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="warn" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
					@confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>

		<view class="lh35 text-c" style="position: fixed;display: block;right: 00rpx;top: 76vh;
					width: 160rpx;height: 70rpx;box-shadow: 2rpx 2rpx 10rpx #909399;color: #fff;
					border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;background-color: rgba(30,24,72,0.7);"
			@click="reAuth" v-if="isAdmin">
			重新认证
		</view>
	</view>
</template>

<script>
	import test from '../../../uni_modules/uview-ui/libs/function/test'
	export default {
		data() {
			return {
				// 可设置
				// 是否为管理员，管理员则拥有重新认证权限
				isAdmin: false,

				checkType: 0,
				checkTitle: "",
				checkText: "",
				id: '',
				name: '',
				cardID: '',
				authenticationFlag: 1, //是否有征信问题 0否 1是
				// photoWitnessFlag: 0, //人证统一判断
				status: 0, //内容判断
				showDetail: false, // 收起展开判断
				showModal: false, //弹窗展示详情
				showModal2: false, //弹窗展示详情
				photoWitness: {}, //身份信息验证
				realname: null,
				specialList: null, //特殊名单验证
				slIdCourtBadAllnum: 0,
				slIdCourtExecutedAllnum: 0,
				slIdBankFraudAllnum: 0,
				badPersonnel: null, //不良人名单
				badPersonnelList: null,
				basicData: null, //报告信息
				litigationDetails: null,
				countList: null,
				caselist: null,
			}
		},
		onLoad(option) {
			this.id = option.id
			this.isAdmin = option.isAdmin ? true : false
			this.getBaomuDetail()
			this.getJzyAuthenticationData()
		},
		methods: {
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					uni.chooseImage({
						success: (chooseImageRes) => {
							const tempFilePaths = chooseImageRes.tempFilePaths;
							uni.uploadFile({
								url: 'https://api.xiaoyujia.com/system/imageUpload',
								filePath: tempFilePaths[0],
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: (uploadFileRes) => {
									let result = JSON.parse(uploadFileRes.data)
									this.http({
										outsideUrl: 'https://biapi.xiaoyujia.com/jizhengyun/updatePictureAuthData',
										data: {
											id: this.photoWitness.id,
											imgUrl: result.data,
											crePerson: uni.getStorageSync('realName'),
											employeeId: this.id,
										},
										header: {
											"content-type": "application/json;charset=UTF-8"
										},
										method: 'POST',
										success: res => {
											if (res.status == 200) {
												uni.showToast({
													title: '认证成功!',
													icon: 'none'
												})
												setTimeout(() => {
													this.getBaomuDetail()
													this.getJzyAuthenticationData()
												}, 500)
											} else {
												uni.showToast({
													title: res.msg,
													icon: 'none'
												})
											}
										}
									})
								}
							});
						}
					});

				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			uploadImg() {
				this.openCheck(1, "是否重新上传认证图片并从新认证")
			},
			// clickOpenJ(i) {
			// 	this.judiciallist[i].showDetail = !this.judiciallist[i].showDetail
			// 	console.log(this.judiciallist[i], '---------------------------');
			// },
			clickOpenC(i) {
				this.badPersonnelList[i].showDetail = !this.badPersonnelList[i].showDetail
				console.log(this.badPersonnelList[i], '---------------------------');
			},
			goPage() {
				uni.navigateTo({
					url: './securityAuthDetail?id=' + this.id
				})
			},
			openShow(i) {
				this.status = i
				this.showModal = true
			},
			getBaomuDetail() {
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					path: this.id,
					success: res => {
						if (res.code == 0) {
							let baomuDetail = res.data
							this.name = baomuDetail.employee.realName
							this.cardID = baomuDetail.employeeInfo.idCard
						} else {
							console.log('获取保姆详细信息成功-请求失败！')
						}
					}
				});
			},
			getJzyAuthenticationData() {
				this.http({
					url: 'getJzyAuthenticationData',
					method: 'GET',
					hideLoading: true,
					data: {
						employeeId: this.id
					},
					success: res => {
						if (res.code == 0) {
							console.log("res---------", res);
							if (res.data.authenticationFlag != null) {
								this.authenticationFlag = res.data.authenticationFlag // 是否有征信问题
							}
							if (res.data.basicData != null) {
								this.basicData = res.data.basicData //报告基本信息
							}
							if (res.data.photoWitness != null) { //照片认证对比
								this.photoWitness = res.data.photoWitness
							}
							if (res.data.specialList != null) {
								this.specialList = res.data.specialList //特殊名单验证
								this.slIdCourtBadAllnum = this.specialList.slIdCourtBadAllnum || 0
								this.slIdCourtExecutedAllnum = this.specialList.slIdCourtExecutedAllnum || 0
								this.slIdBankFraudAllnum = this.specialList.slIdBankFraudAllnum || 0
								console.log(this.specialList, 'specialList------------');
							}
							if (res.data.involvingDetails != null) {
								this.realname = res.data.involvingDetails //人员信息精准查询
							}
							if (res.data.litigationDetails != null) {
								this.litigationDetails = res.data.litigationDetails //人员信息精准查询
								this.countList = res.data.litigationDetails.countList
								this.caselist = res.data.litigationDetails.litigationDetailsList
							}
							if (res.data.badPersonnel != null) {
								this.badPersonnel = res.data.badPersonnel
								let arr2 = res.data.badPersonnel.badPersonnelList //不良人名单查询
								arr2.forEach(item => {
									this.$set(item, "showDetail", false)
								})
								this.badPersonnelList = arr2
								console.log(this.badPersonnelList, 'badPersonnelList-----------------');
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							})
							if(res.msg=='请先认证后再查询认证结果!'){
								setTimeout(()=>{
									uni.navigateBack();
								},2000)
							}
						}
					}
				})
			},
			// 重新认证
			reAuth() {
				uni.showModal({
					title: '是否重新认证？',
					content: "将排除所有失信/涉诉信息并重新认证！请确保人工审核下该员工为正常可用状态！",
					success: res => {
						if (res.confirm) {
							this.http({
								outsideUrl: 'https://biapi.xiaoyujia.com/jizhengyun/reAuthentication',
								method: 'POST',
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								data: {
									employeeId: this.id,
									crePerson: localStorage.getItem("employeeName") || '超级管理员'
								},
								success: res => {
									if (res.status == 200) {
										this.$refs.uNotify.success('认证成功！')
										this.getJzyAuthenticationData()
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								}
							})
						}
					}
				});
			}
		}
	}
</script>
<style lang="scss" scoped>
	.page {
		width: 100%;
		min-height: 100vh;
		padding: 20rpx 0;
		background-color: rgba(122, 131, 142, 0.8);
	}

	.bgBox {
		position: relative;
		width: 90%;
		margin: 30rpx auto;
		border-radius: 10rpx;
		padding: 10rpx 10rpx 40rpx 10rpx;
		background-color: #fff;
	}

	.cellStyle {
		background-color: #f4f8fb;
		padding: 10rpx 8%;
		margin: 10rpx 0;
	}

	.title {
		text-indent: 16rpx;
		padding: 30rpx 0;

		&::before {
			content: '';
			position: absolute;
			left: 0;
			width: 10rpx;
			height: 40rpx;
			background-color: #4b7eef;
			color: #4b7eef;
		}
	}

	.borderB {
		border-bottom: 2rpx solid #eee;
	}

	.c6fb888 {
		color: #6fb888;
	}

	.c4a7aea {
		color: #4a7aea;
	}

	.line {
		width: 30%;
		height: 2rpx;
		background-color: #ddd;
	}

	.tagStyle {
		padding: 10rpx 30rpx;
		margin: 20rpx;
		border-radius: 30rpx;
		color: #6fb888;
		border: 2rpx solid #6fb888;
		background-color: #ecf6f4;
	}

	.title {
		text-indent: 16rpx;
		padding: 30rpx 0;

		&::before {
			content: '';
			position: absolute;
			left: 0;
			width: 10rpx;
			height: 40rpx;
			background-color: #4b7eef;
			color: #4b7eef;
		}
	}

	.borderB {
		border-bottom: 2rpx solid #eee;
	}

	.c6fb888 {
		color: #6fb888;
	}

	.c4a7aea {
		color: #4a7aea;
	}

	.line {
		width: 30%;
		height: 2rpx;
		background-color: #ddd;
	}

	.tagStyle {
		padding: 10rpx 30rpx;
		margin: 20rpx;
		border-radius: 30rpx;
		color: #6fb888;
		border: 2rpx solid #6fb888;
		background-color: #ecf6f4;
	}

	.titleStyle {
		position: relative;
		margin: 20rpx auto;
		text-indent: 20rpx;

		&:before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			width: 8rpx;
			height: 40rpx;
			border-radius: 40rpx;
			background-color: #4a7aea;
		}
	}

	.listStyle {
		padding: 20rpx;
		margin: 30rpx auto;
		background: #f7f7f7;
		border-radius: 10rpx;
	}
</style>