<template>
  <view>
    <u-cell-group v-for="(item,index) in cateList" :key="index">
      <u-cell :title="item.cateName" isLink @click="openPopup(item)">
        <u-avatar slot="icon" shape="square" size="30" :src="src1" customStyle="margin: -3px 5px -3px 0"></u-avatar>
      </u-cell>
    </u-cell-group>
    <TabBar :value="value"></TabBar>
  </view>
</template>

<script>
  import TabBar from '@/pages-work/components/TabBar.vue'
  export default {
    components:{
      TabBar
    },
    data() {
      return {
        cateList:[],
        value: '1',
        src1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669000509423tw.png',
      }
    },
    onLoad() {
     this.getCateId()
    },
    methods: {
      getCateId(){
      	uni.request({
      		url:'https://api.xiaoyujia.com/employee/getVodCateIdList',
      		method:"GET",
      		success: (res) => {
      			res.data.data.forEach(v =>{
      				v.subCategories.forEach(i =>{
      					this.cateList.push(i)
      				})	
      			})
            console.log(this.cateList)
      		}
      	})
      },
      openPopup(item) {	
      	uni.navigateTo({
      		url:'/pages-work/promote/material/SPmaterial/VideoDetail?cateId='+item.cateId
      	})
      },
      // goto(url) {
      // 	uni.navigateTo({
      // 		url:url
      // 	})
      // },
    }
  }
</script>

<style scoped>
/*  page {
    background-color: #fffff3 !important;
  } */
  /deep/.u-line {
    border-bottom: none!important;
  }
  /deep/.u-cell-group {
    border-bottom: 1px solid #eee;
  }
  /deep/.u-tabbar {
    background-color: #FFFAFA !important;
  }

  .tab-icon {
    width: 50rpx;
    height: 50rpx;
  }

  /deep/.u-subsection--button {
    height: 80rpx;
    background-color: #FFFAFA !important;
  }

  /deep/.u-subsection__item__text {
    font-size: 28rpx !important;
  }

  /deep/.u-subsection--button__bar {
    border-radius: 0px !important;
    border-bottom: 1px solid #dd524d !important;
    background-color: transparent !important;
  }

  /deep/.u-search {
    width: 90%;
    height: 100rpx !important;
    margin: auto !important;
  }

  /deep/.u-search__content,
  /deep/.u-search__content__input {
    background-color: #eee !important;
  }
</style>
