<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="cert-img">
			<img :src="cert.certPreview" mode="widthFix" />
			<text style="top: 500rpx;left: 140rpx;font-size: 36rpx;">{{memberName}}</text>
			<text style="top: 910rpx;left: 200rpx;font-size: 16rpx">{{cert.certCode}}</text>
			<text style="top: 910rpx;left: 500rpx;font-size: 16rpx">{{formatDate(cert.creTime)}}</text>
		</view>
		<!-- 		<view class="btn-big">
			<button>打印证书</button>
		</view> -->

		<view class="btn-big">
			<button @click="saveToPhone()">保存到手机</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				cert: {
					certImg: '',
					certCode: 'xxx',
					creTime: null
				},
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				memberId: uni.getStorageSync("memeberId"),
				memberName: uni.getStorageSync("memberName"),
				headImg: uni.getStorageSync("memberHeadImg"),
			}
		},
		methods: {
			// 保存图片到手机
			saveToPhone() {
				let img = this.cert.certPreview
				console.log("想要保存的图片地址：", img)
				uni.downloadFile({
					url: img,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				// return this.$moment().format('YYYY-MM-DD')
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '.' + MM + '.' + d
			},
		},
		onLoad(options) {
			if (options.cert != undefined) {
				this.cert = JSON.parse(options.cert)
			}

		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.cert-img {
		img {
			width: 100%;
			height: 1055rpx;
		}

		text {
			display: block;
			color: #000000;
			position: absolute;
			z-index: 999;
		}
	}

	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
</style>
