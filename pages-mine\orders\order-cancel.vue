<template>
  <view>
    <view class="" style="height: 100vh;">
      <uni-card title="取消原因">
        <u-radio-group v-model="reasonId" placement="column" iconPlacement="right">
          <u-radio activeColor="#1e1848" :customStyle="{marginBottom: '13px'}" v-for="(item, index) in reasonList"
            :key="item.id" :label="item.name" :name="item.id" @change="radioChange(item)"></u-radio>
        </u-radio-group>
      </uni-card>
      <uni-card title="备注说明">
        <u--input placeholder="可输入退款详细原因" border="surround" v-model="cancelRemark"></u--input>
      </uni-card>
    </view>
    <view class="view_botom flac-row-b">
      <u-button color="#1e1848" plain @click="cancel()" text="确认取消"></u-button>
      <u-button color="#1e1848" customStyle="color:#f6cc70" text="暂不取消" @click="back()"></u-button>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        billNo: '',
        reasonId: null,
        cancelRemark: null,
        pid: null,
        reasonList: []
      }

    },
    onLoad(option) {
      this.billNo = option.billNo
      this.getcancelreason()
    },
    methods: {
      back() {
        uni.navigateBack();
      },
      radioChange(e) {
        this.reasonId = e.id;
        this.pid = e.pid;
      },
      getcancelreason() {
        this.http({
          url: 'getCancelReason',
          method: 'GET',
          success: res => {
            if (res.code === 0) {
              this.reasonList = res.data
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'error'
              });
              return;
            }
            console.log(res)
          },
          fail: err => {
            console.log(res)
          }
        })
      },
      cancel() {
        if (this.reasonId == null) {
          return uni.showToast({
            title: '请选择取消原因',
            icon: 'error'
          });
        }
        let param = {
          billNo: this.billNo,
          reasonId: this.reasonId,
          pid: this.pid,
          memberId: uni.getStorageSync("memberId"),
          cancelRemark: this.cancelRemark,
          memberPhone: uni.getStorageSync("account")
        }
        console.log(JSON.stringify(param))
        this.http({
          url: 'memberRefund',
          method: 'POST',
          header: {
            "content-type": "application/json;charset=UTF-8"
          },
          data: param,
          success: res => {
            if (res.code === 0) {
              uni.showToast({
                title: '取消成功',
                duration: 2000,
                icon: 'none'
              });
              uni.navigateTo({
                url: 'orderdetail?billNo=' + this.billNo
              });
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'error'
              });
              return;
            }
            console.log(res)
          },
          fail: err => {
            console.log(res)
          }
        })

      }
    }
  }
</script>

<style>
  .view_botom {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: #fff;
    /* 
    padding: 40rpx 0; */
  }
</style>