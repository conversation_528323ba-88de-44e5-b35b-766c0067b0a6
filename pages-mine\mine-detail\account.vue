<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog :type="type1" cancelText="关闭" confirmText="确认" :title="checkTitle"
					:content="checkText" @confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>

		<!-- 头部 用户信息 -->
		<view class="user-info">
			<view class="info-item">
				<text class="title-big">用户信息</text>
			</view>

			<view class="info-item">
				<text class="title">头像</text>
				<view class="head-img">
					<img :src="headImg!==''?headImg:blankImg">
				</view>
			</view>

			<view class="info-item">
				<text class="title">姓名</text>
				<text class="content">{{ memberName!==''?memberName:"暂无用户名"}}</text>
			</view>

		</view>

		<view class="user-info">
			<view class="info-item">
				<text class="title-big">其他功能</text>
			</view>

			<view class="list-column" v-for="(mineList, index) in mineList" :key="index"
				@click="openMineDetail(mineList.listUrl)">
				<image :src="mineList.listImage"></image>
				<text class="column-text">{{mineList.listTitle}}</text>
				<text class="column-text-right">&ensp;❯</text>
			</view>


		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: 'center',
				type1: 'success',
				msgType: "success",
				msgText: "",
				checkType: 0,
				checkTitle: "",
				checkText: "",
				memberId: null,
				baomuId: null,
				memberName: '',
				headImg: "",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				settingImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664265540790settings.png",
				mineList: [{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ks.png',
						listTitle: '考试中心',
						listUrl: '/pages-mine/exam/exam-center'
					},
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-zs.png',
						listTitle: '我的证书',
						listUrl: '/pages-mine/mine-detail/my-certificate'
					},
					{
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-yhq.png',
						listTitle: '我的优惠券',
						listUrl: '/pages-mine/mine-detail/my-discount'
					}
				]
			}
		},
		methods: {
			// 打开对应的详细页面
			openMineDetail(url) {
				console.log(url)
				uni.navigateTo({
					url: url
				});
			},
			// 尝试从登录后的缓存中获取用户信息
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let baomuId = uni.getStorageSync('baomuId')
				let memberName = uni.getStorageSync('memberName')
				let headImg = uni.getStorageSync("memberHeadImg")
				baomuId = baomuId == "" ? null : baomuId
				this.memberId = memberId
				this.baomuId = baomuId
				this.memberName = memberName
				this.headImg = headImg
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx
				if (this.checkType == 0) {

				}
			},
			// 用户信息初始化
			orginInfo() {
				this.getMemberInfor()
			}
		},
		// 页面载入之后
		mounted() {
			this.orginInfo()
		},
		// 页面首次创建后
		created() {

		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.user-info {
		width: 100%;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin-bottom: 20rpx;
	}

	// 用户信息每个栏目
	.info-item {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
	}

	.info-item text {
		margin-left: 40rpx;
	}

	.title-big {
		font-size: 40rpx;
		font-weight: bold;
	}

	.title {
		font-size: 36rpx;
	}

	.content {
		font-size: 36rpx;
		float: right;
		margin-right: 40rpx;
	}

	.head-img {
		float: right;
		width: 100rpx;
		height: 100rpx;
		margin-right: 40rpx;
	}

	.head-img img {
		display: block;
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.list-column {
		width: 100%;
		height: 120rpx;
	}

	.list-column image {
		float: left;
		margin: 35rpx 0 0 40rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.column-text {
		line-height: 120rpx;
		margin: 0 0 0 20rpx;
		font-size: 32rpx;
	}

	.column-text-right {
		float: right;
		margin-right: 40rpx;
		line-height: 120rpx;
		color: #909399;
	}
</style>
