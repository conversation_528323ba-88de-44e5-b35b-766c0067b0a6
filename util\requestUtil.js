// 这边不再配置 请求地址，由 vue.config.js 接管（跨域所需）

// production environment
const DEV_URL = 'https://api.xiaoyujia.com';
const PRO_URL = 'https://api.xiaoyujia.com';

// location environment
// const DEV_URL = 'http://localhost:9999';
// const PRO_URL = 'http://localhost:9999';

// test environment
// const DEV_URL = 'http://***************:9999';
// const DEV_URL = 'http://**************:15004';

const DEV_IMG_URL = '';
const PRO_IMG_URL = '';

let LOGIN_USER = {
	id: null,
	sex: null,
	name: null,
	token: null,
	level: null,
	source: null,
	openId: null,
	account: null,
	headImg: null,
	bindTel: null,
	loginTime: null
}

/**
 * 根据当前环境获得不同的基础路径
 */
const getBaseUrl = function() {
	if (process.env.NODE_ENV === 'development') {
		return DEV_URL
	} else {
		return PRO_URL
	}
}

/**
 * 根据当前环境获得图片or文件host路径
 */
export const getImgUrl = function() {
	if (process.env.NODE_ENV === 'development') {
		return DEV_IMG_URL
	} else {
		return PRO_IMG_URL
	}
}

/**
 * 远程请求工具
 * @param {Object} postParam 传入参数
 */
export const requestUtil = function(postParam) {
	// 设置请求头
	let header = {}
	if (LOGIN_USER.token === '') {
		// 假如暂存失效，从缓存中取用户信息
		try {
			console.log('尝试从缓存中获取信息')
			const userInfo = uni.getStorageSync('user_info')
			LOGIN_USER.id = userInfo.id;
			LOGIN_USER.sex = userInfo.sex;
			LOGIN_USER.name = userInfo.name;
			LOGIN_USER.level = userInfo.level;
			LOGIN_USER.token = userInfo.token;
			LOGIN_USER.source = userInfo.source;
			LOGIN_USER.openId = userInfo.openId;
			LOGIN_USER.account = userInfo.account;
			LOGIN_USER.headImg = userInfo.headImg;
			LOGIN_USER.bindTel = userInfo.bindTel;
			LOGIN_USER.loginTime = userInfo.loginTime;
		} catch (e) {
			console.log(e)
		}
	}
	// console.log(LOGIN_USER.token)
	header.Authorization = 'Bearer ' + LOGIN_USER.token;
	// 合并自定义请求头
	if (postParam.hasOwnProperty('header')) {
		header = Object.assign(header, postParam.header)
	}

	let _dataParams = {}
	// 遍历数据，过滤不合法的内容（如null）
	if (postParam.hasOwnProperty('data')) {
		for (let item in postParam.data) {
			if (postParam.data[item] !== null) {
				_dataParams[item] = postParam.data[item]
			}
		}
	}

	// uni.showLoading({
	// 	title: 'loading...',
	// 	mask: true
	// })

	uni.request({
		url: getBaseUrl() + '' + postParam.url,
		data: _dataParams,
		header: header,
		method: postParam.method,
		success: (res) => {
			// const code = res.data.code;
			// if (code != null && code == 0) {
			postParam.success(res.data);
			// } else {
			// console.log('信息过期');
			// const msg = res.data.msg;
			// if (msg != null && msg.indexOf('token') > -1) {
			// 	uni.redirectTo({
			// 		url: '/pages/login?msg=' + res.data.msg
			// 	})
			// }
			// }
			// uni.hideLoading();
		},
		fail: res => {
			// console.log('请求失败' + postParam.url);
			if (postParam.onFail) {
				postParam.onFail(res);
			}
			// uni.hideLoading();
		},
		complete: res => {
			if (postParam.complete) {
				postParam.complete(res);
			}
			// uni.hideLoading();
		}
	})
}

/**
 * 保存登录用户信息
 * @param {Object} userInfo
 */
export const setLoginUserInfo = function(userInfo) {
	LOGIN_USER.id = userInfo.id;
	LOGIN_USER.sex = userInfo.sex;
	LOGIN_USER.name = userInfo.name;
	LOGIN_USER.level = userInfo.level;
	LOGIN_USER.token = userInfo.token;
	LOGIN_USER.source = userInfo.source;
	LOGIN_USER.openId = userInfo.openId;
	LOGIN_USER.account = userInfo.account;
	LOGIN_USER.headImg = userInfo.headImg;
	LOGIN_USER.bindTel = userInfo.bindTel;
	LOGIN_USER.loginTime = userInfo.loginTime;
	// 将用户信息插入到缓存中
	uni.setStorage({
		key: 'user_info',
		data: userInfo,
		success: (res) => {
			console.log('缓存成功');
			// console.log(res.data);
		}
	})
}

/**
 * 获取当前登录的用户信息
 */
export const getLoginUserInfo = function() {
	if (LOGIN_USER == null || LOGIN_USER.token == null) {
		try {
			// console.log('尝试从缓存中获取信息')
			const userInfo = uni.getStorageSync('user_info');
			LOGIN_USER.id = userInfo.id;
			LOGIN_USER.sex = userInfo.sex;
			LOGIN_USER.name = userInfo.name;
			LOGIN_USER.level = userInfo.level;
			LOGIN_USER.token = userInfo.token;
			LOGIN_USER.source = userInfo.source;
			LOGIN_USER.openId = userInfo.openId;
			LOGIN_USER.account = userInfo.account;
			LOGIN_USER.headImg = userInfo.headImg;
			LOGIN_USER.bindTel = userInfo.bindTel;
			LOGIN_USER.loginTime = userInfo.loginTime;
		} catch (e) {
			console.log(e);
		}
	}
	return LOGIN_USER;
}

/**
 * 清除当前登录用户信息
 */
export const clearUserInfo = function() {
	// 清除暂存信息
	LOGIN_USER.id = null;
	LOGIN_USER.sex = null;
	LOGIN_USER.name = null;
	LOGIN_USER.level = null;
	LOGIN_USER.token = null;
	LOGIN_USER.source = null;
	LOGIN_USER.openId = null;
	LOGIN_USER.account = null;
	LOGIN_USER.headImg = null;
	LOGIN_USER.bindTel = null;
	LOGIN_USER.loginTime = null;
	// LOGIN_USER.loginUser = '';
	// 清除缓存信息
	try {
		uni.removeStorageSync('user_info');
		// uni.clearStorage();
	} catch (e) {
		console.log(e);
	}
}

/**
 * 获取远程文件上传地址
 */
export const getUploadUrl = function() {
	return getBaseUrl() + '';
}

/**
 * 获取上传必带请求头
 */
export const getPostHeader = function() {
	let header = {
		Authorization: 'Bearer ' + LOGIN_USER.token
	}
	return header;
}

/**
 * get请求
 */
export const reqGet = function(url, param) {
	requestUtil({
		method: 'GET',
		url: url,
		data: param.data,
		success: param.onSuccess,
		complete: param.onComplate
	})
}

/**
 * post请求
 */
export const reqPost = function(url, param) {
	requestUtil({
		method: 'POST',
		url: url,
		data: param.data,
		success: param.onSuccess,
		complete: param.onComplate
	})
}
