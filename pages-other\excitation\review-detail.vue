<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="review-img f26 lh40" style="color: #fff;" v-if="dailyReview">
			<img :src="reviewDetailImg" alt="" mode="widthFix"
				style="width: 1000rpx;height: 800rpx;;position: absolute;top: 520rpx;left:1400rpx;" />
			<view style="width: 2000rpx;position: absolute;top: 220rpx;left: 100rpx;">
				<text class="fb f32">流程一句话：</text>
				{{dailyReview.flowContent||''}}
			</view>

			<view class="flac-row" style="width: 1400rpx;position: absolute;top: 460rpx;left: 2360rpx;">
				<view><text class="fb">日目标：</text>{{dailyReview.dailyGoal}}</view>
				<view v-if="dailyReview.dailyGoalRate" style="margin-left: 40rpx;">
					<text class="fb">达成率：</text>{{dailyReview.dailyGoalRate}}%
				</view>
			</view>
			<view class="flac-row" style="width: 1400rpx;position: absolute;top: 560rpx;left: 2360rpx;"
				v-if="dailyReview.weeklyGoal">
				<view><text class="fb">周目标：</text>{{dailyReview.weeklyGoal}}</view>
				<view class="" v-if="dailyReview.weeklyGoalRate" style="margin-left: 40rpx;">
					<text class="fb">达成率：</text>{{dailyReview.weeklyGoalRate}}%
				</view>
			</view>

			<view style="width: 1400rpx;position: absolute;top:740rpx;left:1080rpx;transform: rotate(-42.5deg);"
				v-if="dailyReview.collaborator">
				可以帮助你的人：{{dailyReview.collaborator}}
			</view>

			<view style="width: 1300rpx;position: absolute;top: 1360rpx;left: 940rpx;">
				<text class="fb">左手：</text>
				{{dailyReview.leftHand||'-'}}
			</view>
			<view style="width: 1400rpx;position: absolute;top: 1360rpx;left: 2360rpx;">
				<text class="fb">团队目标：</text>
				{{dailyReview.teamGoal||'-'}}
			</view>

			<view style="width: 3200rpx;position: absolute;top: 1600rpx;left: 100rpx;">
				<!-- 		<text class="fb">基本动作（已做对的可重复动作）：</text> -->
				<text class="fb">左手工作项：</text>
				<view class="" v-for="(item,index) in textList" :key="index" v-if="index<pointNum"
					style="margin-left: 400rpx;">
					{{index+1}}、{{item}}
				</view>
			</view>
			<view :style="textStyle">
				<!-- <text class="fb">复盘动作（新增做对动作）：</text> -->
				<text class="fb">做对的工作项：</text>
				<view class="" v-for="(item,index) in textList1" :key="index" v-if="index<pointNum1"
					style="margin-left: 400rpx;">
					{{index+1}}、{{item}}
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 分点数量
				pointNum: 3,
				// 分点数量
				pointNum1: 3,
				// 最大分点数量
				maxPointNum: 9,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_head_portrait.png",
				backImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_back.png',
				reviewDetailImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/review_detail_img_3.png',
				headImg: '',
				userName: '',
				departName: '',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				employeeNo: uni.getStorageSync("employeeNo") || null,
				reviewId: null,
				dailyReview: null,
				textStyle: '',
				textList: ['', '', '', '', '', '', '', '', '', ''],
				textList1: ['', '', '', '', '', '', '', '', '', ''],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 文本转化为列表
			textToList(text, list) {
				let count = 0
				if (!text) {
					return 3
				}
				if (!text.includes('|')) {
					list[0] = text
					count++
				} else {
					let array = text.split('|')
					for (let i = 0; i < array.length && i < this.maxPointNum; i++) {
						list[i] = array[i]
						count++
					}
				}
				return count
			},
			// 跳转页面
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 获取每日复盘信息
			getDailyReviewById() {
				if (!this.reviewId) {
					return
				}
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/content/getDailyReviewById',
					method: 'GET',
					hideLoading: true,
					path: this.reviewId || 0,
					success: res => {
						if (res.code == 0) {
							this.dailyReview = res.data
							this.userName = res.data.employeeName
							this.headImg = res.data.headImg
							this.departName = res.data.departName
							this.pointNum = this.textToList(this.dailyReview.dailyThing, this.textList)
							this.pointNum1 = this.textToList(this.dailyReview.dailyImprovement, this.textList1)
							let top = 2000
							top += 170 * (this.pointNum - 1)
							this.textStyle = 'width: 3200rpx;position: absolute;top: ' + top +
								'rpx;left: 100rpx;'
						}
					},
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.reviewId = obj.t || this.reviewId
			}
			// #ifdef MP-WEIXIN
			this.$refs.uNotify.warning('推荐在电脑浏览器上查看哦！')
			// #endif
			this.reviewId = options.id || this.reviewId
			this.getDailyReviewById()
		},
	}
</script>
<style lang="scss">
	page {
		height: auto;
		background-color: #444677;
	}

	.review-img {
		img {
			display: block;
			position: relative;
			width: 750rpx;
			height: 100%;
		}
	}

	.back-img {
		img {
			display: block;
			width: 100%;
			height: 550rpx;
		}
	}

	.head-tab {
		position: absolute;
		width: 100%;
		top: 20rpx;
		left: 0%;
		color: #fff;
	}

	.head-tab1 {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 40%;
		top: 100rpx;
		left: 30%;

		img {
			display: block;
			width: 180rpx;
			height: 180rpx;
			border-radius: 50%;
		}

		text {
			margin-top: 20rpx;
			text-align: center;
			color: #ffffff;
		}
	}
</style>