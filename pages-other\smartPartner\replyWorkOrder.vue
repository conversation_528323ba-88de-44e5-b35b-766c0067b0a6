<template>
	<view style="padding-bottom: 160rpx;background-color: #f1f0f0;">

		<scroll-view :style="{height: `${windowHeight-inputHeight}rpx`}" id="scrollview" scroll-y="true"
			:scroll-top="scrollTop" class="scroll-view">
	
			<view @click="moreOperationShow = false" id="msglistview">
				<checkbox-group @change="changeCheckbox">
					<view v-for="(item,i) in contentData" :key="i">

						<!--门店方-->
						<view style="display: flex;" v-if="item.initiator">
							<view style="margin-left: 20rpx;margin-top: 40rpx;" class="checkBox" v-if="isTurnWordOrder">
								<checkbox :value="item.id" :checked="checkedArr.includes(item.id)"
									:class="{'checked':checkedArr.includes(item.id)}"></checkbox>
							</view>
							<view :style="!isTurnWordOrder?'color: gray;margin-left: 20rpx;margin-top: 40rpx;font-size: 25rpx;padding-bottom: 10rpx;'
					  :'color: gray;margin-left: 100rpx;margin-top: 40rpx;font-size: 25rpx;padding-bottom: 10rpx;'">
								{{smartPartnerName}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.createTime||item.dateTime}}
							</view>
						</view>

						<view class="content left"
							v-if="item.initiator&&item.msgType==1">
							<view >{{item.messageData}}</view>
						</view>

						<!--管理人员方-->
						<view style="display: flex;" v-if="!item.initiator">
							<view style="margin-left: 20rpx;margin-top: 40rpx;" class="checkBox" v-if="isTurnWordOrder">
								<checkbox :value="item.id" :checked="checkedArr.includes(item.id)"
									:class="{'checked':checkedArr.includes(item.id)}"></checkbox>
							</view>
							<view :style="isTurnWordOrder?'color: gray;margin-left: 30%;;margin-top: 40rpx;font-size: 25rpx;padding-bottom: 10rpx;'
										 :item.oldContactsName?'color: gray;margin-left: 50%;;margin-top: 40rpx;font-size: 25rpx;padding-bottom: 10rpx;'
										 :'color: gray;margin-left: 60%;;margin-top: 40rpx;font-size: 25rpx;padding-bottom: 10rpx;'"
								v-if="!item.initiator&&(item.msgType==1||item.msgType==2)">
								{{item.createTime||item.dateTime}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.oldContactsName}}
							</view>

						</view>
						<u-image v-if="item.initiator&&item.msgType==2" customStyle="margin-left: -10rpx"
							:src="item.messageData" mode="aspectFit"
							@click="openImgPreview(item.messageData)"></u-image>
						<!-- #ifdef H5 -->
						<uni-link v-if="item.initiator&&item.msgType==2" :href="item.messageData"
							style="margin-left: 50rpx;color: #007BFF;font-size: 20rpx;">点击查看原图</uni-link>
						<!-- #endif -->


						<u-image v-if="!item.initiator&&item.msgType==2" customStyle="margin-left: 25%;"
							:src="item.messageData" mode="aspectFit"
							@click="openImgPreview(item.messageData)"></u-image>

						<!-- #ifdef H5 -->
						<uni-link v-if="!item.initiator&&item.msgType==2" :href="item.messageData"
							style="margin-left: 65%;color: #007BFF;font-size: 20rpx;">点击查看原图</uni-link>
						<!-- #endif -->
						<view class="contentB right"
							v-if="!item.initiator&&item.msgType==1">
							<view >{{item.messageData}}</view>
						</view>

					</view>
				</checkbox-group>

			</view>
	
		</scroll-view>


		<view class="chatFrame">
			<view class="w9 mg-at flac-row-b bacf">
				<u-input v-if="!isTurnWordOrder" class="w9" v-model="msgContent" placeholder="请输入回复内容" border="surround"
					clearable @confirm="sendMsg"></u-input>
				<u-icon v-if="!isTurnWordOrder" class="w1" @click="switchMoreOperation" size="30"
					name="plus-circle"></u-icon>
				<u-button v-if="isTurnWordOrder" text="取消" shape="circle" color="gray"
					customStyle="width:40%;letter-spacing: 2rpx;"
					@click="isTurnWordOrder=false,checkedArr=[]"></u-button>
				<u-button v-if="isTurnWordOrder" text="选择人员" shape="circle" color="#1e1848"
					customStyle="width:40%;letter-spacing: 2rpx;color:#f6cc70" @click="selectWordOrder"></u-button>
			</view>
			<uni-grid v-if="moreOperationShow" :column="3" :highlight="true" :showBorder="false" :square="false">
				<uni-grid-item v-for="(item, i) in iconList" :index="i" :key="i">
					<view class="flex-col-c" style="margin-top: 30rpx;" @click="clickMore(item)">
						<image class="iconBox" :src="item.image" mode=""></image>
						<text class="f10">{{item.text}}</text>
					</view>

				</uni-grid-item>
			</uni-grid>
		</view>

		<u-popup :show="popupFlag" @close="popupFlag = false">
			<view>
				<uni-section title="选择人员" type="line" padding >
					<scroll-view scroll-y="true" class="scroll-Y">
						<radio-group class="radioGroupClass" style="width: 40%;">
							<view v-for="(item,index) in agentList" :key="index" class="flac-row-b"
								style="display: flex;">
								<radio style="padding-top: 25rpx;" activeColor="#1e1848" @click="radioCheckAgent(item)"
									:checked="item.name==radioVal.name">{{item.name}}
								</radio>
							</view>
						</radio-group>
					</scroll-view>
				</uni-section>
			</view>
			<button style="width: 30%;margin-left: 60%;margin-top: -80rpx;" class="btnStyle"
				@click="transmitWorkOrder">转发</button>
		</u-popup>

	</view>
</template>

<script>
	import moment from 'moment'; //时间格式化 
	export default {
		name: 'index',
		data() {
			return {
				popupFlag: false,
				radioVal: {},
				agentList: [{
					name: '区域商务经理',
					role: 'commerceManager'
				}, {
					name: '标准交付',
					role: 'standardDeliver'
				}, {
					name: '三嫂交付',
					role: 'sanSaoDeliver'
				}, {
					name: '联盟运营',
					role: 'allianceOperations'
				}, {
					name: '区域督导',
					role: 'regionalSupervision'
				}, {
					name: '培训老师',
					role: 'trainingTeacher'
				}, {
					name: '私域',
					role: 'privateDomain'
				}, {
					name: '流量',
					role: 'flow'
				}, {
					name: '技术',
					role: 'technology'
				}, {
					name: '设计',
					role: 'design'
				}, {
					name: '售后',
					role: 'afterSales'
				}, {
					name: '财务',
					role: 'finance'
				}, {
					name: '行政',
					role: 'administration'
				}],
				isTurnWordOrder: false,
				smartPartnerName: '',
				msgKey: '',
				//滚动距离
				scrollTop: 0,
				//键盘高度
				keyboardHeight: 0,
				//底部消息发送高度
				bottomHeight: 0,
				msgContent: '',
				contentData: [],
				iconList: [{
						image: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1718352309768转工单 (1).png',
						text: '转工单',
						id: 1
					},
					{
						image: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1715071150227219相册.png',
						text: '相册',
						id: 2
					}
				],
				moreOperationShow: false,
				checkedArr: [], //复选框选中的值
				employeeNo: '',
				inputClearValue: "",
				tabsActiveStyle: {
					color: '#1e1848',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				},
				tabsInactiveStyle: {
					color: '#606266',
					transform: 'scale(1)'
				},
				unionMerchant: {},
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				storeType: uni.getStorageSync('storeType'),
				roleId: uni.getStorageSync("roleId") || 0,
				isLogin: false,
				checkText: "",
				selectAgent: "",
			}
		},
		computed: {
			windowHeight() {
				return this.rpxTopx(uni.getSystemInfoSync().windowHeight)
			},
			// 键盘弹起来的高度+发送框高度
			inputHeight() {
				return this.bottomHeight + this.keyboardHeight
			}
		},
		updated() {
			//页面更新时调用聊天消息定位到最底部
			this.scrollToBottom();
		},
		onLoad(option) {
			this.employeeNo = option.employeeNo
			this.getChatDetails()
		},
		methods: {
			transmitWorkOrder(){
				if(!this.selectAgent){
					return uni.showToast({
						icon: 'none',
						title: '请选择人员！'
					})
				}
				this.http({
					url: "transmitWorkOrder",
					method: 'POST',
					data: {
						employeeNo: this.employeeNo,
						chatLogIds: this.checkedArr,
						transmitRole: this.selectAgent.role,
						transmitRoleName: this.selectAgent.name,
						contacts: uni.getStorageSync('employeeNo')
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let obj = {
								messageData: '抱歉，暂时无法为您解决此类问题，已为您转接'+this.selectAgent.name,
								contacts: uni.getStorageSync('employeeNo'),
								msgType: 1,
								chatType: 2,
								channel: this.msgKey,
								employeeNo: this.employeeNo,
								dateTime: moment().format('yyyy-MM-DD HH:mm:ss'),
								id: res.data.id,
								ifTurnFlag: 1,
								turnContacts: res.data.turnContacts,
								turnContactsRole: this.selectAgent.name
							};
							this.goEasy.pubsub.publish({
								channel: this.msgKey,
								message: JSON.stringify(obj),
								onFailed: function(error) {
									console.log("消息发送失败，错误编码：" + error.code + " 错误信息：" + error
										.content);
								}
							});
							this.contentData.push(obj)
							this.popupFlag = false
							this.isTurnWordOrder = false
							this.checkedArr = []
							this.selectAgent = {}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
				
			},
			radioCheckAgent(item) {
				this.selectAgent = item;
			},
			selectWordOrder() {
				if (this.checkedArr.length <= 0) {
					return uni.showToast({
						icon: 'none',
						title: '请选择要转的记录！'
					})
				} else {
					this.popupFlag = true
				}
			},
			changeCheckbox(e) {
				this.checkedArr = e.detail.value;
			},
			// 滚动至聊天底部
			scrollToBottom(e) {
				setTimeout(() => {
					let query = uni.createSelectorQuery().in(this);
					query.select('#scrollview').boundingClientRect();
					query.select('#msglistview').boundingClientRect();
					query.exec((res) => {
						if (res[1].height > res[0].height) {
							this.scrollTop = this.rpxTopx(res[1].height - res[0].height)
						}
					})
				}, 15)
			},
			// px转换成rpx
			rpxTopx(px) {
				let deviceWidth = wx.getSystemInfoSync().windowWidth
				let rpx = (750 / deviceWidth) * Number(px)
				return Math.floor(rpx)
			},
			openImgPreview(val) {
				let data = []
				data.push(val)
				uni.previewImage({
					urls: data,
					current: val
				})
			},
			getChatDetails() {
				this.http({
					url: "getChatDetails",
					data: {
						employeeNo: this.employeeNo,
						contacts: uni.getStorageSync('employeeNo')
					},
					method: "GET",
					success: res => {
						if (res.code == 0) {
							this.contentData = res.data.chatLog
							this.smartPartnerName = res.data.realName
							uni.setNavigationBarTitle({
								title: res.data.realName
							});
							this.msgKey = res.data.channel
							this.recevice()
						} else {
							uni.showToast({
								icon: 'none',
								title: '获取记录!请退出重试！'
							})
						}
					}
				})
			},
			clickMore(item) {
				console.log(item);
				if (item.id == 2) {
					uni.chooseImage({
						success: (chooseImageRes) => {
							const tempFilePaths = chooseImageRes.tempFilePaths;
							uni.uploadFile({
								url: 'https://api2.xiaoyujia.com/system/imageUpload',
								filePath: tempFilePaths[0],
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: (uploadFileRes) => {
									let result = JSON.parse(uploadFileRes.data)
									let obj = {
										messageData: result.data,
										chatType: 2,
										contacts: uni.getStorageSync('employeeNo'),
										msgType: 2,
										channel: this.msgKey,
										employeeNo: this.employeeNo,
										dateTime: moment().format('yyyy-MM-DD HH:mm:ss'),
									};
									//保存聊天信息
									this.http({
										url: "saveSmartPartnerChatLog",
										method: 'POST',
										data: obj,
										header: {
											"content-type": "application/json;charset=UTF-8"
										},
										success: res => {
											if (res.code == 0) {
												obj.id = res.data
												this.goEasy.pubsub.publish({
													channel: this.msgKey,
													message: JSON.stringify(
														obj),
													onFailed: function(error) {
														console.log(
															"消息发送失败，错误编码：" +
															error
															.code +
															" 错误信息：" +
															error
															.content);
													}
												});
												this.contentData.push(obj)
												this.moreOperationShow = false
												this.msgContent = ''
											} else {
												uni.showToast({
													title: res.msg,
													icon: 'none'
												})
											}
										}
									})




								}
							});


						}
					});

				} else {
					console.log(this.contentData);
					for (var i = 0; i < this.contentData.length; i++) {
						this.contentData[i].id = this.contentData[i].id.toString()
					}
					this.isTurnWordOrder = true
					this.moreOperationShow = false
				}
			},
			recevice() {
				//订阅消息
				let _this = this;
				this.goEasy.pubsub.subscribe({
					channel: this.msgKey, //替换为您自己的channel
					onMessage: function(message) { //收到消息
						console.log("Channel:" + message.channel + " content:" + message.content);
						let content = JSON.parse(message.content);
						console.log("content", content);
						//对方回复的回存  
						if (content.hasOwnProperty('initiator')) {
							let obj = {
								messageData: content.messageData,
								chatType: content.chatType,
								contacts: content.contacts,
								msgType: content.msgType,
								initiator: content.initiator,
								channel: content.channel,
								employeeNo: content.employeeNo,
								dateTime: content.dateTime,
								id: content.id,
							};
							_this.contentData.push(obj)
						} else {
							console.log('自己发送的不回存');
						}
					},
					onFailed: function(error) {
						console.log("Channel订阅失败, 错误编码：" + error.code + " 错误信息：" + error.content)
					}
				});
			},
			sendMsg() {
				if (!this.msgContent) {
					return uni.showToast({
						icon: 'none',
						title: '不能发送空白信息!'
					})
				}
				let obj = {
					messageData: this.msgContent,
					contacts: uni.getStorageSync('employeeNo'),
					msgType: 1,
					chatType: 2,
					channel: this.msgKey,
					employeeNo: this.employeeNo,
					dateTime: moment().format('yyyy-MM-DD HH:mm:ss'),
				};

				//保存聊天信息
				this.http({
					url: "saveSmartPartnerChatLog",
					method: 'POST',
					data: obj,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							obj.id = res.data
							this.goEasy.pubsub.publish({
								channel: this.msgKey,
								message: JSON.stringify(obj),
								onFailed: function(error) {
									console.log("消息发送失败，错误编码：" + error.code + " 错误信息：" + error
										.content);
								}
							});
							this.contentData.push(obj)
							this.msgContent = ''
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})

			},
			choiceWorkOrder(item) {
				this.nowWorkOrder = item
				uni.setNavigationBarTitle({
					title: item.name + "-" + item.no
				});
			},
			switchMoreOperation() {
				if (this.moreOperationShow) {
					this.moreOperationShow = false
				} else {
					this.moreOperationShow = true
				}
			},
			clearInput: function(event) {
				this.inputClearValue = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			getMemberData() {
				this.http({
					url: "getMemberById",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.isAuth = res.data.isAuth
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			// 打开登录
			openLogin() {
				uni.reLaunch({
					url: '/pages-mine/login/login'
				});
			},
			// 打开个人信息
			openAccount() {
				// 未登录的情况
				if (!this.isLogin) {
					return uni.navigateTo({
						url: "/pages-mine/login/notlogin"
					})
				}
				uni.navigateTo({
					url: '/pages-mine/mine-detail/setting'
				});
			},
			// 退出登录
			tryLogout() {
				this.openCheck(0, "确定退出登录吗？", "下次需要重新登录哦～")
			},
			// 退出登录
			logout() {
				// 清除用户数据并返回到登录页
				this.$toast.toast('退出成功！')
				if (uni.getStorageSync('employeeId')) {
					//更新员工token
					this.http({
						url: 'updateEmployeeToken',
						method: 'POST',
						data: {
							employeeId: uni.getStorageSync('employeeId')
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						hideLoading: true,
						success: res => {

						}
					})
				}
				uni.clearStorage()
				let timer = setTimeout(() => {
					uni.redirectTo({
						url: "/pages-mine/login/login"
					})
				}, 500);
			},
			// 检查员工状态
			checkState() {
				if (uni.getStorageSync("isInvited") !== null) {
					if (uni.getStorageSync("isInvited") == true) {
						return true
					}
				}
				let employeeState = uni.getStorageSync("employeeState")
				if (employeeState != -1) {
					return true
				} else {
					return false
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-tabs__wrapper__nav__item__text {
		font-size: 25rpx !important;
	}


	$chatContentbgc: #C2DCFF;
	$sendBtnbgc: #4F7DF5;
	
	.right {
		background-color: $chatContentbgc;
	}
	
	.left {
		background-color: #FFFFFF;
	}
	
	// 聊天消息的三角形
	.right::after {
		position: absolute;
		display: inline-block;
		content: '';
		width: 0;
		height: 0;
		left: 100%;
		top: 10px;
		border: 12rpx solid transparent;
		border-left: 12rpx solid $chatContentbgc;
	}
	
	
	.left::after {
		position: absolute;
		display: inline-block;
		content: '';
		width: 0;
		height: 0;
		top: 10px;
		right: 100%;
		border: 12rpx solid transparent;
		border-right: 12rpx solid #FFFFFF;
	}
	
	.content {
		position: relative;
		max-width: 486rpx;
		border-radius: 8rpx;
		word-wrap: break-word;
		padding: 24rpx 24rpx;
		margin: 0 24rpx;
		border-radius: 5px;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 42rpx;
	}
	
	.contentB {
		position: relative;
		max-width: 486rpx;
		border-radius: 8rpx;
		word-wrap: break-word;
		padding: 24rpx 24rpx;
		margin-left: 25%;
		border-radius: 5px;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 42rpx;
	}
	

	/* 聊天消息 */
	.chat {
		.scroll-view {
			::-webkit-scrollbar {
				display: none;
				width: 0 !important;
				height: 0 !important;
				-webkit-appearance: none;
				background: transparent;
				color: transparent;
			}

			// background-color: orange;
			background-color: #F6F6F6;

		}
	}

	.iconBox {
		width: 60rpx;
		height: 60rpx;
		background-color: #eeeff4;
		padding: 25rpx;
		margin: 20rpx;
	}

	.typeBox {
		white-space: nowrap;
		text-align: center;
		font-size: 28rpx;
		color: #1e1848;
		margin: 10rpx 20rpx 10rpx 0;
		background-color: #eee;
		padding: 15rpx;
		border-radius: 30rpx;
	}

	.iconImage {
		display: block;
		margin-top: 10rpx;
		width: 80rpx;
		height: 80rpx;
		margin-left: 30rpx;
		border-radius: 50%;
		// animation: iconImage 5s linear infinite;
	}

	.menu-list {
		display: block;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		background-color: #fff;
		padding: 0 20rpx;
	}

	.interval {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.replyStyle {
		background: #eee;
		/* 箭头靠左边 */
		clip-path: polygon(5% 0, 100% 0, 100% 100%, 5% 100%, 5% 65%, 0 50%, 5% 35%);

	}

	.sendStyle {
		background: #eee;
		/* 箭头靠左边 */
		clip-path: polygon(0 0, 88% 0, 88% 35%, 95% 50%, 88% 65%, 88% 100%, 0 100%);

	}

	.div {
		width: 90%;
		margin: auto 40rpx;
	}

	.div1 {
		width: 90%;
		margin: auto 10rpx;
	}

	.chatFrame {
		width: 100%;
		margin: auto;
		padding: 30rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		box-shadow: 0 4rpx 20rpx #dedede;
	}
</style>