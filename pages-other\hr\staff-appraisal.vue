<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<view class="staff-tab">
			<view class="tab">
				<view class="img-upload">
					<img :src="checkStr(trialStaff.headPortrait)!='-'?trialStaff.headPortrait:blankImg"
						@click="openImgPreview()" />
				</view>
				<view class="tab-head">
					<text>姓名</text>
				</view>
				<view class="tab-inputbox">
					<view><input class="single-input" type="text" v-model="trialStaff.name" placeholder="暂未填写姓名"
							:disabled="true" />
					</view>
				</view>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>服务类别</text>
					<text v-if="showOtherWorkType">（主要工作）</text>
					<text v-if="!showOtherWorkType" @click="showOtherWorkType=!showOtherWorkType"
						style="margin-right: 0rpx;padding-right: 0rpx;">添加更多</text>
					<uni-icons type="plus-filled" size="18" color="#909399" v-if="!showOtherWorkType"
						@click="showOtherWorkType=!showOtherWorkType" style="margin-left: 0;"></uni-icons>
				</view>
				<view class="tab-head lh30" style="height: 80rpx;">
					<text></text>
					<text>需添加对应项目，派单时才可选择该员工哦</text>
				</view>
				<view class="tab-checkbox" v-for="(item, index) in workTypeList" :key="index">
					<view class="checkbox" :class="{ activeBox: trialStaff.workType == item.id ? true : false }">
						<text @click="choiceTab(0, index, 0)">{{ item.typeName }}</text>
					</view>
				</view>
			</view>

			<view class="tab" v-for="(item, index) in workTypeList" :key="index" v-if="item.id==trialStaff.workType">
				<view class="tab-head">
					<text>服务项目</text>
					<text>（{{item.typeName}}）</text>
				</view>
				<view class="tab-checkbox" v-for="(item1, index1) in item.productList" :key="index1">
					<view class="checkbox" :class="{ activeBox: item1.isCheck == true ? true : false }">
						<text @click="choiceTab(1, index, index1)">{{ item1.name }}</text>
					</view>
				</view>
			</view>

			<view class="tab" v-if="showOtherWorkType">
				<view class="tab-head">
					<text>服务类别</text>
					<text>（次要工作）</text>
				</view>
				<view class="tab-checkbox" v-for="(item, index) in workTypeList" :key="index">
					<view class="checkbox" :class="{ activeBox: item.isCheck == true ? true : false }">
						<text @click="choiceTab(2, index, 0)">{{ item.typeName }}</text>
					</view>
				</view>
			</view>

			<view class="tab" v-for="(item, index) in workTypeList" :key="index" v-if="item.isCheck==true">
				<view class="tab-head">
					<text>服务项目</text>
					<text>（{{item.typeName}}）</text>
				</view>
				<view class="tab-checkbox" v-for="(item1, index1) in item.productList" :key="index1">
					<view class="checkbox" :class="{ activeBox: item1.isCheck == true ? true : false }">
						<text @click="choiceTab(3, index, index1)">{{ item1.name }}</text>
					</view>
				</view>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>员工指标</text>
					<text>（反映员工综合能力）</text>
				</view>
				<view class="flac-col w9 mg-at f18 lh30">
					<text>入户服务：{{employeeTarget.workTime?employeeTarget.workTime+'小时':'-'}}</text>
					<text>技能培训：{{employeeTarget.studyDuration?employeeTarget.studyDuration+'小时':'-'}}</text>
					<text>开发复购：{{employeeTarget.averageAmount?employeeTarget.averageAmount+'元':'-'}}</text>
					<text>实操考试：{{employeeTarget.examNum?employeeTarget.examNum+'次':'-'}}</text>
					<text>推荐上架：{{employeeTarget.putNum?employeeTarget.putNum+'人':'-'}}</text>
				</view>
			</view>

			<view class="tab f18">
				<view class="tab-head">
					<text>鉴定等级</text>
					<text>（参考指标，选择对应等级）</text>
				</view>
				<view class="flex-col">
					<view class="w9 mg-at lh30" v-for="(item,index) in taskList" :key="index"
						v-if="choiceTaskIndex==index">
						<text>当前等级：【{{item.level}}星】{{workTypeName}}</text>
						<view v-for="(item1,index1) in item.taskList" :key="index1">
							<text>{{index1+1}}：{{item1.taskContent}}</text>
							<uni-icons :type="item1.isFinish?'checkmarkempty':'closeempty'" size="20"
								:color="item1.isFinish?'#19be6b':'#ff4d4b'" v-if="item1.isFinish!=null"></uni-icons>
						</view>
						<view class="flac-row-c" style="margin-top: 20rpx;">
							<view @click="changeTaskIndex(0)" class="flac-row w5">
								<uni-icons type="arrow-left" size="20"
									:color="choiceTaskIndex!=0?'#000':'#909399'"></uni-icons>
								<text :style="choiceTaskIndex!=0?'color:#000':'color:#909399'">上一级</text>
							</view>
							<view @click="changeTaskIndex(1)" class="flac-row w5"
								style="align-items: right;justify-content:end;">
								<text :style="choiceTaskIndex!=4?'color:#000':'color:#909399'">下一级</text>
								<uni-icons type="arrow-right" size="20"
									:color="choiceTaskIndex!=4?'#000':'#909399'"></uni-icons>
							</view>
						</view>
					</view>
					<u-empty v-if=" !taskList.length" text="暂无等级数据"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>

			<view class="tab">
				<view class="tab-head">
					<text>预估工资</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" type="number" v-model="trialStaff.authSalary" placeholder="暂无估值" />
					</view>
				</view>
			</view>

			<view class="tab" v-if="departList.length!=0">
				<view class="tab-head">
					<text>所属部门</text>
				</view>
				<view class="tab-picker" @click="openPickerMine(1)">
					<text class="picker-text" v-if="departName == ''">点击选择部门</text>
					<text class="picker-text" v-if="departName !== ''">{{ departName }}</text>
					<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 按钮组 -->
		<view class="btn-group" style="padding: 80rpx 0;">
			<button @click="cleanChoice()"
				:style="trialStaff.workType!=0?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">清空选项</button>
			<button @click="startWorkTypeAuth()"
				:style="trialStaff.workType!=0?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'">{{isAuthBefore?'重新鉴定':'确认鉴定'}}</button>
		</view>
		<view class="btn-big" v-if="isAuthBefore">
			<button @click="quickPutStaff()" style="margin-top: 0rpx;" v-if="trialStaff.state!=1">快速上架</button>
			<button @click="openTime" style="margin-top: -20rpx;">时间管理</button>
		</view>

		<!-- 		<view class="btn-big">
			<button @click="openAuthReport()"
				:style="isAuthBefore?'background-color:#1e1848;':'background-color:#909399;'">查看鉴定报告</button>
		</view> -->

		<u-gap height="100"></u-gap>
	</view>
</template>

<script>
	export default {
		props: {
			startLoadMore: {
				type: Number
			}
		},
		data() {
			return {
				// 可设置
				// 显示次要服务类别
				showOtherWorkType: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},

				// 员工信息
				trialId: null,
				employeeId: null,
				processId: null,
				staffLevel: 0,
				trialStaff: {},
				record: {},
				list: {},
				popupShow: false,
				isGrounding: false,
				isAuthBefore: false,
				choiceWokrType: -1,
				choiceWokrTypeCount: 0,
				choiceProductCount: 0,
				choiceProductCount1: 0,

				storeName: "",
				departName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],
				storeList: [],
				departList: [],

				memberId: uni.getStorageSync('memberId'),
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",

				productList: [],
				workTypeList: [],
				employeeTarget: {
					putNum: 0,
					putNum: 0,
					averageAmount: 0,
					examNum: 0,
					studyDuration: 0,
					id: null
				},
				choiceTaskIndex: 0,
				workTypeName: '',
				taskList: []
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.pickerIndex = index
			},
			// 打开头像预览
			openImgPreview() {
				let data = []
				let img = this.trialStaff.headPortrait
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开选择器
			openPickerMine(value) {
				let id = 1
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
					id = this.trialStaff.storeId || id
				} else if (value == 1) {
					this.pickerMineName = "name"
					this.pickerMineList = this.departList
					id = this.trialStaff.departId || id
				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				// 初始化选择器位置
				for (let i = 0; i < this.pickerMineList.length; i++) {
					if (id == this.pickerMineList[i].id) {
						this.pickerIndex = i
						break
					}
				}
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				let name = this.pickerMineList[index][this.pickerMineName]
				let id = this.pickerMineList[index].id
				if (value == 0) {
					this.storeName = name
					this.trialStaff.storeId = id
				} else if (value == 1) {
					this.departName = name
					this.trialStaff.departId = id
				}
				this.showPickerMine = false
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 初始化选择器
			initPickerMine(value, id, name, list) {
				for (let i = 0; i < list.length; i++) {
					if (id == list[i].id) {
						let param = value == 0 ? 'storeName' : 'departName'
						this[param] = list[i][name]
						this.pickerIndex = i
						break
					}
				}
			},
			// 选择选框
			choiceTab(value, index, index1) {
				// 选择主要服务类别
				if (value == 0) {
					if (this.choiceWokrType == index) {
						return
					}

					this.choiceWokrType = index
					this.trialStaff.workType = this.workTypeList[index].id
					this.workTypeName = this.workTypeList[index].typeName
					// 清空所有选项
					this.workTypeList.forEach(item => {
						if (this.trialStaff.workType == item.id) {
							item.productList.forEach(item1 => {
								item1.isCheck = false
							})
						}
					})
					this.choiceProductCount = 0
					this.listWorkTypeTaskGroup()
				}
				// 选择服务项目
				else if (value == 1) {
					if (this.workTypeList[index].productList[index1].isCheck) {
						this.workTypeList[index].productList[index1].isCheck = false
						this.choiceProductCount--
					} else {
						this.workTypeList[index].productList[index1].isCheck = true
						this.choiceProductCount++
					}
				}
				// 选择次要服务类别
				else if (value == 2) {
					if (this.workTypeList[index].isCheck == true) {
						this.workTypeList[index].isCheck = false
						this.choiceWokrTypeCount--
					} else {
						if (this.trialStaff.workType == this.workTypeList[index].id) {
							this.$refs.uNotify.error("选择的服务类别不能与主要类别相同！")
							return
						}
						this.workTypeList[index].isCheck = true
						this.choiceWokrTypeCount++
					}
				}
				// 选择次要服务项目
				else if (value == 3) {
					if (this.workTypeList[index].productList[index1].isCheck) {
						this.workTypeList[index].productList[index1].isCheck = false
						this.choiceProductCount1--
					} else {
						this.workTypeList[index].productList[index1].isCheck = true
						this.choiceProductCount1++
					}
				}
			},
			// 清空选项
			cleanChoice() {
				this.choiceWorkType = 0
				this.trialStaff.workType = 0
				this.trialStaff.workTypeList = ''
				this.productList = []
				this.choiceProductCount = 0
				this.getWorkType(1)
			},
			// 格式化请求参数
			formatProductList() {
				let employeeId = this.trialStaff.employeeId
				let workType = this.trialStaff.workType
				let workTypeList = ''
				let data = []

				this.workTypeList.forEach(item => {
					if (item.isCheck || workType == item.id) {
						item.productList.forEach(item1 => {
							if (item1.isCheck) {
								let it = {}
								this.$set(it, "employeeId", employeeId)
								this.$set(it, "prdouctId", item1.id)
								data.push(it)
							}
						})

						// 将工种列表加入到员工字段中
						if (item.isCheck) {
							workTypeList += item.id + ','
						}
					}
				})

				// 清除末尾逗号
				if (workTypeList.lastIndexOf(',') == workTypeList.length - 1) {
					workTypeList = workTypeList.substring(0, workTypeList.length - 1)
				}
				this.trialStaff.workTypeList = workTypeList || 0
				return data
			},
			// 获取部门列表
			getDepartList() {
				this.http({
					outsideUrl: 'https://api2.xiaoyujia.com/system/getDeptByStoreId',
					path: this.trialStaff.storeId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.departList = res.data
							let id = this.trialStaff.departId
							this.initPickerMine(1, id, 'name', this.departList)
						}
					}
				});
			},
			// 员工工种鉴定
			startWorkTypeAuth() {
				if (this.choiceProductCount == 0) {
					this.$refs.uNotify.error("请至少选择一项服务项目！")
					return
				}

				if (this.departList.length && this.departName == '') {
					this.$refs.uNotify.error("请选择员工部门！")
					return
				}
				let data = this.formatProductList()
				this.staffLevel = this.choiceTaskIndex + 1
				this.http({
					url: "startWorkTypeAuth",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						trialId: this.trialId,
						workType: this.trialStaff.workType,
						workTypeList: this.trialStaff.workTypeList,
						operatorId: uni.getStorageSync("employeeId") || 0,
						staffLevel: this.staffLevel,
						authSalary: this.trialStaff.authSalary,
						list: data
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("鉴定成功！")
							this.isAuthBefore = true
							this.updateStaff()
						} else {
							// this.$refs.uNotify.error(res.msg)
							this.openCheck(0, "还不符合鉴定条件哦", res.msg)
						}
					}
				})
			},
			updateStaff() {
				this.http({
					url: 'insertUpdateById',
					data: this.trialStaff,
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {

					}
				});
			},
			quickPutStaff() {
				uni.showModal({
					title: '快速上架',
					content: '鉴定通过后可使用，将跳过培训、考试等流程，直接进行上架！',
					success: res => {
						if (res.confirm) {
							this.http({
								url: "quickPutStaff",
								header: {
									'content-type': "application/json;charset=UTF-8"
								},
								method: 'POST',
								data: {
									trialId: this.trialId,
									operatorId: uni.getStorageSync("employeeId") || 0,
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success("快速上架成功！")
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								}
							})
						}
					},
				})
			},
			// 打开时间管理
			openTime() {
				uni.navigateTo({
					url: '/pages-work/business/time/timeIndex?employeeId=' + this.trialStaff.employeeId
				})
			},
			// 获取员工服务项目
			getEmployeeProduct() {
				this.http({
					url: "getEmployeeProduct",
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.trialStaff.employeeId
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.choiceProductCount = 0
							let data = (this.trialStaff.workTypeList || '').split(',')
							res.data.forEach(item => {
								for (let i = 0; i < this.workTypeList.length; i++) {
									// 格式化服务类别
									if (this.workTypeList[i].id == this.trialStaff.workType) {
										this.choiceWokrType = i
										this.choiceProductCount++
										this.workTypeName = this.workTypeList[i].typeName
									}
								}
							})
						}
					}
				})
			},
			// 获取工种字典内容
			getWorkType(value) {
				this.http({
					url: "getWorkTypeByTrialId",
					method: 'GET',
					hideLoading: true,
					path: this.trialId,
					success: res => {
						if (res.code == 0) {
							this.workTypeList = res.data
							if (value == 0) {
								this.getEmployeeProduct()
							}
						}
					}
				})
			},
			// 改变等级
			changeTaskIndex(value) {
				if (value == 0) {
					this.choiceTaskIndex != 0 ? this.choiceTaskIndex-- : this.$refs.uNotify.warning("已是最低等级！")
				} else {
					this.choiceTaskIndex != 4 ? this.choiceTaskIndex++ : this.$refs.uNotify.warning("已是最高等级！")
				}
				this.formatTask(1)
			},
			formatTask(value) {
				if (!this.taskList[this.choiceTaskIndex]) {
					return
				}
				let salary = this.taskList[this.choiceTaskIndex].basicSalary || 0
				this.taskList[this.choiceTaskIndex].taskList.forEach(item => {
					if (item.checkField && item.reachFlag) {
						let result = false
						let num = this.employeeTarget[item.checkField] || 0
						if (num && num >= item.reachFlag) {
							result = true
							salary += (item.increaseSalary || 0)
						}
						this.$set(item, 'isFinish', result)
					}
				})
				if (value == 1) {
					salary = salary == 0 ? null : salary
					this.trialStaff.authSalary = salary
				}
			},
			// 获取员工详细指标
			getEmployeeTarget() {
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/acn/getEmployeeTarget",
					method: 'GET',
					hideLoading: true,
					path: this.employeeId,
					success: res => {
						if (res.code == 0) {
							this.employeeTarget = res.data
							this.formatTask(0)
						}
					}
				})
			},
			// 获取工作类型任务列表（按等级分组）
			listWorkTypeTaskGroup() {
				if (!this.trialStaff.workType) {
					return
				}
				this.http({
					outsideUrl: "https://api2.xiaoyujia.com/staff/listWorkTypeTaskGroup",
					method: 'GET',
					hideLoading: true,
					path: this.trialStaff.workType,
					success: res => {
						if (res.code == 0) {
							this.taskList = res.data
							this.formatTask(0)
						} else {
							this.taskList = []
						}
					}
				})
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "-"
				} else {
					return str
				}
			},
			//员工信息
			getTrialStaffByTrialId() {
				this.http({
					url: 'getTrialStaffByTrialId',
					data: {
						trialId: this.trialId
					},
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.trialStaff = res.data
							this.processId = this.trialStaff.processId
							this.staffLevel = this.trialStaff.staffLevel || 0
							this.employeeId = this.trialStaff.employeeId
							this.isAuthBefore = this.trialStaff.isComplete == 2 ? true : false
							// 校验是否添加了次要工种
							let workTypeList = this.trialStaff.workTypeList
							if (workTypeList != null && workTypeList != "0" && workTypeList != "") {
								this.showOtherWorkType = true
							}
							if (this.staffLevel != 0) {
								this.choiceTaskIndex = this.staffLevel - 1
							}
							this.trialStaff.authSalary = this.trialStaff.authSalary <= 0 ? null : this
								.trialStaff.authSalary
							this.getWorkType(0)
							this.getEmployeeTarget()
							this.listWorkTypeTaskGroup()
							this.getDepartList()
						}
					}
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {
					uni.navigateTo({
						url: '/pages-other/hr/staff-resume?id=' + this.trialId + "&isAdmin=true"
					})
				}
			},
		},
		watch: {

		},
		onReachBottom() {

		},
		onShow() {

		},
		onLoad(options) {
			this.trialId = parseInt(options.id)
			this.getTrialStaffByTrialId()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/hrbp.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.img-upload {
		img {
			display: block;
			width: 200rpx;
			height: 200rpx;
			margin: 100rpx auto;
			border-radius: 50%;
		}
	}

	.head-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
	}

	// 底部固定按钮
	.btn-bottom-float-group {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		display: flex;

		view {
			width: 50%;
			height: 120rpx;

			button {
				width: 80%;
				height: 80rpx;
				margin: 0rpx auto;
				line-height: 80rpx;
				color: #1e1848;
				background-color: #fff;
				border: #1e1848 2rpx solid;
				border-radius: 50rpx;
				font-size: 32rpx;
				box-shadow: 4rpx 4rpx 10rpx #dedede;
			}
		}
	}
</style>