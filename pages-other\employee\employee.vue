<template>
	<view v-if="show">
		<!-- find-employee页面注释掉 -->
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200" style="z-index: 999 !important;"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 弹出式排序气泡 -->
		<view class="popup-picker" v-if="popupShowOrderBy">
			<view class="picker-triangle"></view>
			<view class="picker-tab flac-col">
				<view v-for="(item,index) in orderByList" :key="index" @click="choiceOrderBy(index)">
					{{item.showText}}
				</view>
			</view>
		</view>

		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 头像照片预览弹窗 -->
		<uni-popup ref="popupHeadPortrait" background-color="#fff">
			<view class="head-review">
				<img :src="headPortrait" mode="widthFix" />
			</view>
		</uni-popup>

		<!-- 备注弹框 -->
		<u-popup :show="popupShowRemark" mode="bottom" @close="popupShowRemark = false">
			<view class="filter-title">
				<text>员工备注</text>
			</view>

			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="workRemark"
							placeholder="员工工作备注" height="100" count
							:disabled="choiceIndex!=0&&choiceIndex!=1"></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 备注内容将不会展示给客户，仅开发人可编辑</text>
					</view>
				</view>
			</view>

			<view class="filter-button" v-if="choiceIndex==0||choiceIndex==1">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="workRemark=''">
						<text>清空备注</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="saveRemark()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 添加员工 -->
		<u-popup :show="popupShowNew" mode="bottom" @close="popupShowNew = false">
			<view class="filter-title">
				<text>添加员工</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1300rpx;">
				<view class="filter-tab">
					<view class="tab-title">
						<text>手机号</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="number" v-model="employeeNew.phone"
								placeholder="填写员工手机号" /></view>
					</view>

					<view class="tab-title">
						<text>姓名</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.realName"
								placeholder="填写员工姓名/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>身份证号</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.idCard"
								placeholder="填写员工身份证号码/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>身份证上传</text>
					</view>
					<view class="flac-row">
						<view class="w5">
							<img :src="identityFront"
								style="display:block;width:280rpx;height: 160rpx;margin: 0 auto;border-radius: 10rpx;"
								@click="uploadImg(0)">
						</view>
						<view class="w5">
							<img :src="identityBack"
								style="display:block;width: 280rpx;height: 160rpx;margin: 0 auto;border-radius: 10rpx;"
								@click="uploadImg(1)">
						</view>
					</view>

					<view class="tab-title" style="margin-top: 20rpx;">
						<text>员工类型</text>
					</view>
					<view class="tab-checkbox" style="width: 300rpx;margin-left: 40rpx;"
						v-for="(item,index) in typeList" :key="index">
						<view class="checkbox" :class="{activeBox: index==choiceTypeIndex}">
							<text v-model="item.value" @click="choiceTypeIndex=index">{{item.text}}</text>
						</view>
					</view>

					<view style="width: 86%;padding: 20rpx 7% 200rpx 7%">
						<text style="display: block;font-size: 32rpx;color: #909399;">
							自助添加的员工将入驻到您的门店，并计入开发！（记得后续帮Ta完善资料哦）</text>
						<text style="display: block;font-size: 32rpx;color: #ff4d4b;">
							* 请在 15 日内完成资料完善，否则将清除对应的开发权益</text>
					</view>
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employeeNew.realName='';employeeNew.phone=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="checkEmployeeByIdCard()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 筛选弹窗 -->
		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view class="popup-filter">
				<view class="filter-title">
					<text>筛选</text>
				</view>

				<view class="filter-content">
					<view class="filter-tab">
						<view class="tab">
							<u-search :clearabled="true" :showAction="false" margin="0 20rpx" v-model="searchText"
								placeholder="可输入员工姓名、手机号"></u-search>
						</view>

						<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">
							<!-- 							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>缺少员工？</text>
									<text style="color: #1e1848" @click="openCreate()">自助添加</text>
								</view>
							</view> -->

							<view class="tab" v-if="isRole">
								<view class="tab-title-choice">
									<text>全国接单</text>
									<img @click="isNationwide=!isNationwide"
										:src="isNationwide?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
										mode="widthFix" />
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title-choice">
									<text>已开单</text>
									<img @click="isHaveOrder=!isHaveOrder"
										:src="isHaveOrder?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
										mode="widthFix" />
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>筛选模式</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in searchModeList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceSearchModeIndex}">
										<text v-model="item.value" @click="choiceBox(-1,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>工号</text>
								</view>
								<view class="tab-inputbox">
									<view class="tab-input"><input class="single-input" v-model="searchCondition.no"
											placeholder="请输入搜索工号" /></view>
								</view>
							</view>

							<!-- <view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>排序方式</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in orderByList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceOrderByIndex}">
										<text v-model="item.value" @click="choiceBox(10,index)">{{item.showText}}</text>
									</view>
								</view>
							</view> -->

							<view class="tab">
								<view class="tab-title">
									<text>工作类型</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in workTypeList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceWorTypeIndex}">
										<text v-model="item.value" @click="choiceBox(0,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作内容</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in serverList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceServerIndex}">
										<text v-model="item.value" @click="choiceBox(1,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab" v-if="isRole&&(choiceIndex==0||choiceIndex==1)">
								<view class="tab-title">
									<text>是否合格</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in qualifiedList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceQualifiedIndex}">
										<text v-model="item.value" @click="choiceBox(15,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab" v-if="isRole&&(choiceIndex==0||choiceIndex==1)">
								<view class="tab-title">
									<text>是否上架</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in stateList1" :key="index">
									<view class="checkbox" :class="{activeBox: index==choiceStateIndex}">
										<text v-model="item.value" @click="choiceBox(16,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<!-- 						<view class="tab">
							<view class="tab-title">
								<text>是否开单</text>
							</view>
							<view class="tab-checkbox" v-for="(item,index) in orderIdList" :key="index">
								<view class="checkbox" :class="{activeBox: item.index==choiceOrderIdIndex}">
									<text v-model="item.value" @click="choiceBox(13,index)">{{item.showText}}</text>
								</view>
							</view>
						</view>

						<view class="tab">
							<view class="tab-title">
								<text>年龄</text>
							</view>
							<view class="tab-checkbox" v-for="(item,index) in ageList" :key="index">
								<view class="checkbox" :class="{activeBox: item.value==choiceAgeIndex}">
									<text v-model="item.value" @click="choiceBox(2,index)">{{item.text}}</text>
								</view>
							</view>
						</view> -->

							<!-- 	<view class="tab">
								<view class="tab-title">
									<text>年龄</text>
								</view>
								<view class="tab-range">
									<input class="range-input" type="number" v-model="searchCondition.ageLow"
										placeholder="最低" />
									<text class="range-input-interval"> — </text>
									<input class="range-input1" type="number" v-model="searchCondition.ageHigh"
										placeholder="最高" />
									<text class="range-input-unit">岁</text>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>最低薪资</text>
								</view>
								<view class="tab-range">
									<input class="range-input" type="number" v-model="searchCondition.salaryLow"
										placeholder="最低" />
									<text class="range-input-interval"> — </text>
									<input class="range-input1" type="number" v-model="searchCondition.salaryHigh"
										placeholder="最高" />
									<text class="range-input-unit">元</text>
								</view>
							</view> -->

							<!-- <view class="tab">
								<view class="tab-title">
									<text>户籍地</text>
								</view>
								<view class="tab-inputbox">
									<view class="tab-input"><input class="single-input"
											v-model="searchCondition.hometown" placeholder="请输入搜索户籍地" /></view>
								</view>
							</view> -->

							<view class="tab">
								<view class="tab-title">
									<text>现居地</text>
								</view>
								<view class="tab-inputbox">
									<view class="tab-input"><input class="single-input"
											v-model="searchCondition.address" placeholder="请输入搜索现居地" /></view>
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>所属门店</text>
								</view>
								<view class="tab-picker" @click="openPickerMine(0)">
									<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
									<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
									<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>工作年限</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in workYearList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceWorkYearIndex}">
										<text v-model="item.value" @click="choiceBox(12,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>学历</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in educationList" :key="index">
									<view class="checkbox" :class="{activeBox: item.value==choiceEducationIndex}">
										<text v-model="item.value" @click="choiceBox(4,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>语言</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in languagesList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceLanguageIndex}">
										<text v-model="item.value" @click="choiceBox(14,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>婚姻状态</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in marriedList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceMarriedIndex}">
										<text v-model="item.value" @click="choiceBox(3,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<view class="tab">
								<view class="tab-title">
									<text>鉴定等级</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in levelList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceLevelIndex}">
										<text v-model="item.value" @click="choiceBox(8,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>入驻时间</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in timeList" :key="index">
									<view class="checkbox" :class="{activeBox: item.index==choiceCreTimeIndex}">
										<text v-model="item.value" @click="choiceBox(7,index)">{{item.showText}}</text>
									</view>
								</view>
							</view>

							<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>接单状态</text>
								</view>
								<view class="tab-checkbox" v-for="(item,index) in statusList" :key="index">
									<view class="checkbox"
										:class="{activeBox: item.value==statusList[choiceStatusIndex].value}">
										<text v-model="item.value" @click="choiceBox(6,index)">{{item.text}}</text>
									</view>
								</view>
							</view>

							<!-- <view class="tab">
							<view class="tab-title">
								<text>接单范围</text>
							</view>
							<view class="tab-checkbox" v-for="(item,index) in siteFlagList" :key="index">
								<view class="checkbox" :class="{activeBox: item.index==choiceSiteFlagIndex}">
									<text v-model="item.value"
										@click="choiceBox(11,index)">{{item.showText}}</text>
								</view>
							</view>
						</view> -->

							<!-- 	<view class="tab" v-if="isRole">
								<view class="tab-title">
									<text>简历分范围</text>
								</view>
								<view class="tab-range">
									<input class="range-input" type="number" v-model="searchCondition.scoreLow"
										placeholder="最低" />
									<text class="range-input-interval"> — </text>
									<input class="range-input1" type="number" v-model="searchCondition.scoreHigh"
										placeholder="最高" />
									<text class="range-input-unit">分</text>
								</view>
							</view> -->

							<u-gap height="120"></u-gap>
						</scroll-view>

					</view>
				</view>

				<view class="filter-button w82 flac-row">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="cleanFilter()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="startFilter()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>

			</view>
		</u-popup>

		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>员工日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{checkStr(item.title)}}</text>
					</view>
					<text v-if="item.workContent">{{item.workContent}}</text>
					<text>时间：{{item.creatTime}}</text>
					<text>操作人：{{checkStr(item.crePerson)}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<u-popup :show="popupShowStore" mode="bottom" @close="popupShowStore = false">
			<view class="filter-title">
				<text>门店信息</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-if="store">
					<text>门店名称：{{store.storeName}}</text>
					<text>地址：{{store.cityName}}{{store.areaName}}</text>
				</view>
				<u-empty v-else text="暂无门店信息" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<uni-transition mode-class="fade" :show="popupShowFilter">
			<view class="filter-popup" v-show="popupShowFilter">
				<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
					<view class="tab" id="fiter0" v-if="choiceIndex1==0">
						<uni-search-bar class="w85 mg-at" placeholder="城市搜索" bgColor="#f6f8fa" :radius="100"
							v-model="searchStreet" cancelButton="none" @search="search" @confirm="search">
							<uni-icons slot="searchIcon" color="#2261ff" size="18" type="search" />
						</uni-search-bar>
					</view>
					<view class="flac-row-b" style="flex-wrap: wrap;align-items: unset;height: 1000rpx;"
						v-if="choiceIndex1==0">
						<view class="w3 f15 lh60 text-c navStyle">
							<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
								<view class="w10 mg-at flac-row" v-for="(item,i) in cityList" :key="i"
									@click="selectCity(i)">
									<view class="w10 mg-at lineStyle" v-if="i == choiceCityIndex"></view>
									<view :class="i == choiceCityIndex ? 'w10 mg-at btnStyle' : 'w10 mg-at c6'">
										{{item.name}}
									</view>
								</view>
							</scroll-view>
						</view>
						<view class="w7">
							<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
								<u-radio-group iconPlacement="right" placement="column" borderBottom>
									<u-radio :customStyle="{margin: '30rpx 20rpx'}" label="全部"
										@change="selectArea(-1)"></u-radio>
									<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, i) in areaList"
										:key="i" :label="item.name" :name="item.id" @change="selectArea(i)" />
								</u-radio-group>
							</scroll-view>
						</view>
					</view>

					<view class="tab" id="fiter1" v-if="choiceIndex1==1">
						<view class="tab-checkbox" v-for="(item,index) in ageList" :key="index">
							<view class="checkbox" :class="{activeBox: item.value==choiceAgeIndex}">
								<text v-model="item.value" @click="choiceBox(17,index)">{{item.text}}</text>
							</view>
						</view>
						<view class="tab-range">
							<input class="range-input" type="number" v-model="searchCondition.ageLow"
								placeholder="最低" />
							<text class="range-input-interval"> — </text>
							<input class="range-input1" type="number" v-model="searchCondition.ageHigh"
								placeholder="最高" />
							<text class="range-input-unit">岁</text>
						</view>
					</view>

					<view class="tab" id="fiter2" v-if="choiceIndex1==2">
						<view class="tab-checkbox" v-for="(item,index) in salaryList" :key="index">
							<view class="checkbox" :class="{activeBox: item.value==choiceSalaryIndex}">
								<text v-model="item.value" @click="choiceBox(18,index)">{{item.text}}</text>
							</view>
						</view>
						<view class="tab-range">
							<input class="range-input" type="number" v-model="searchCondition.salaryLow"
								placeholder="最低" />
							<text class="range-input-interval"> — </text>
							<input class="range-input1" type="number" v-model="searchCondition.salaryHigh"
								placeholder="最高" />
							<text class="range-input-unit">元</text>
						</view>
					</view>
				</scroll-view>
				<view class="btn-group">
					<button @click="cleanFilter()"
						style="border: #dedede 1px solid;background-color: #f9f9f9;color: #000;">重置</button>
					<button @click="startFilter()">确定</button>
				</view>
			</view>
		</uni-transition>

		<!-- 菜单栏 -->
		<view class="tab-menu">
			<view class="choice-menu">
				<view class="choice-item" v-for="(choiceList, index) in choiceList" :key="index"
					@click="choiceTab(index)" v-if="isRole">
					<text :class="{activeChoice: choiceIndex == index}"
						class="choice-title">{{choiceList.choiceTitle}}</text>
				</view>
				<view v-else>
					<view class="choice-item" v-for="(item,index) in choiceList1" :key="index"
						@click="choiceTag(index)">
						<text :class="{activeChoice: item.value==choiceTagIndex}"
							class="choice-title">{{item.choiceTitle}}</text>
					</view>
				</view>
				<view class="flac-row choice-filter" @click="popupShow=true">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
						size="16"></u-icon>
					<text class="f18 t-indent">筛选</text>
				</view>
			</view>
		</view>

		<view class="flac-row-b">
			<view class="choice-menu1">
				<view class="choice-item" style="width: 140rpx;" v-for="(item, index) in choiceListMain" :key="index"
					@click="choiceTab1(index)" v-if="item.show">
					<text :class="{activeChoice: choiceIndex1 == index}"
						class="choice-title">{{item.choiceTitle}}</text>
				</view>
			</view>

			<view class="flac-row-b f16" style="margin-right: 40rpx;" @click="popupShowOrderBy=!popupShowOrderBy">
				<text>{{orderByList[choiceOrderByIndex].showText}}</text>
				<uni-icons :type="popupShowOrderBy?'top':'bottom'" size="20"></uni-icons>
			</view>
		</view>

		<view class="tips-box" v-if="choiceIndex<=1&&total!=0&&!canPut" style="margin: 10rpx 13% -10rpx 13%;">
			<text>{{rankTips}}</text>
		</view>

		<u-gap height="20"></u-gap>

		<!-- 员工列表 -->
		<!-- 员工鉴定栏目 -->
		<uni-swipe-action>
			<uni-swipe-action-item v-for="(item, index) in list" :key="index" :disabled="choiceIndex!=0"
				:right-options="option" @click="clickOption" @change="clickOptionChange(index)">
				<view class="baomu-tab" style="border-bottom: #f4f4f5 4rpx solid;">
					<view class="tab-left">
						<view class="tab-left-img">
							<img :src="item.headPortrait!==null?item.headPortrait:blankImg"
								@click="openImgPreview(index)">
							<img src="https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/unqualified-bg.png"
								v-if="checkHeadPortrait(index)" @click="unqualifiedTips()">
						</view>
						<view class="tab-left-btn" v-if="choiceIndex==0||choiceIndex==1">
							<!-- 					<u-tag text="一键认证" v-if="item.securityAuth==0" @click="authentication(index)" color="#FFFFFF"
						bgColor="#1e1848" plain shape="circle" size="mini" borderColor="#1e1848"></u-tag> -->
							<u-tag text="认证结果" @click="getRzReport(index)" color="#FFFFFF" bgColor="#1e1848" plain
								shape="circle" size="mini" borderColor="#1e1848" v-if="item.securityAuth==1"></u-tag>
						</view>
					</view>
					<view class="tab-right">
						<view>
							<text class="tab-name">{{formatRealName(index)}}</text>
							<text v-if="!isRole" style="font-size: 28rpx;">{{formatWorkType(index)}}</text>
							<text v-if="isRole">（简历：{{checkStr(item.resumeScore)!='暂无'?item.resumeScore:0}}分）</text>
							<view style="display: inline-block;">
								<view class="small-tag" v-if="hasLevel(item.levelId)">
									<img :src="authIcon" alt="">
									<text>{{formatLevel(item.levelId)}}</text>
								</view>
							</view>
							<text
								v-if="isAppraisaler&&!hasLevel(item.levelId)&&item.resumeScore>=80&&(choiceIndex==0||choiceIndex==1)"
								style="color:#ff4d4b" @click="openWorkSkill(index)">去鉴定</text>
							<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20" color="#1e1848"
								@click="openLog(index)" v-if="isRole">
							</uni-icons>
							<!-- <text class="tab-name">{{formatStr(0,1,item.realName)}}阿姨</text> -->
						</view>
						<view class="tab-info" @click="openTab(1,index)">
							<text>{{item.age?item.age+'岁':''}}</text>
							<text>|&nbsp;{{formatStr(0,2,item.hometown||'本地')}}人</text>
							<text>|&nbsp;{{item.workYear!=null&&item.workYear!=0?item.workYear+"年经验":"暂无经验"}}</text>
							<text v-if="item.education!=null&&item.education!=1">
								|&nbsp;{{formatEducation(item.education)}}</text>
						</view>

						<view class="skills-content" @click="openTab(1,index)">
							<view>
								<view class="my-tag" v-for="(item1,index1) in formatServerContent(item.serverContent)"
									:key="index1">
									{{item1}}
								</view>
							</view>
							<view>
								<view class="my-tag" v-for="(item2,index2) in formatOtherSkills(item.otherSkills)"
									:key="index2">
									{{item2}}
								</view>
							</view>
						</view>

						<view v-if="isRole">
							<view class="tab-text" @click="openTab(1,index)">
								<text>员工工号：<text class="c0">{{checkStr(item.no)}}</text></text>
							</view>
							<view class="tab-text" @click="openTab(1,index)">
								<text>工作类型：<text class="c0">{{checkStr(item.workType)}}</text></text>
							</view>
							<view class="tab-text" @click="openTab(1,index)">
								<text>接单范围：<text
										class="c0">{{ item.expectedAddress?item.expectedAddress:formatSiteFlag(index) }}</text></text>
							</view>
							<view class="tab-text" @click="openTab(1,index)">
								<text>最低薪资：<text class="c0">{{checkSalary(item.salaryExpectation)}}</text></text>
							</view>
							<view class="tab-text" @click="openTab(1,index)">
								<text>入驻：<text class="c0">{{item.createDate}}</text></text>
							</view>
							<view class="tab-text" v-if="isRole" @click="openContractLog(item.id)">
								<text>上户状态：<text class="c0"
										:style="item.contractId!=null?'color:#ff4d4b':'color:#000'">{{formatStatus(index)}}</text></text>
								<uni-icons type="eye" style="margin-left: 5rpx;display: inline-block;" size="18"
									color="#909399" v-if="choiceIndex==0||choiceIndex==1">
								</uni-icons>
							</view>
							<view class="tab-text" @click="openStore(index)">
								<text>门店：<text
										class="c0">{{formatStore(item.storeName)}}{{item.storeAddress?'-'+item.storeAddress:''}}</text></text>
								<uni-icons type="eye" style="margin-left: 5rpx;display: inline-block;" size="18"
									color="#909399" v-if="choiceIndex==0||choiceIndex==1">
								</uni-icons>
							</view>
							<view class="tab-text1" v-if="isRole">
								<text style="display: inline-block;" @click="openTab(1,index)">备注：<text
										class="c0">{{formatLongStr(item.workRemark)}}</text>
								</text>
								<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
									color="#909399" @click="openRemark(index)" v-if="choiceIndex==0||choiceIndex==1">
								</uni-icons>
								<uni-icons type="eye" style="margin-left: 5rpx;display: inline-block;" size="18"
									color="#909399" @click="openRemark(index)" v-if="choiceIndex!=0&&choiceIndex!=1">
								</uni-icons>
							</view>
							<view class="tab-text" v-if="isRole&&item.state!=1" @click="openTab(1,index)">
								<text style="color:#ff4d4b">状态：{{stateList[item.state].text}}</text>
							</view>
							<view class="tab-text" v-if="checkRemindTime(index)" @click="remindTips()">
								<text style="color: #ff4d4b;">提醒：{{item.remindTime}}</text>
							</view>

							<view style="display: flex; flex-direction: row;width: 100%;height: 100rpx;"
								v-if="choiceIndex==0||choiceIndex==1">
								<view style="width: 50%;height: 60rpx;" @click="openTab(0,index)">
									<view class="button-left">
										<text>简历</text>
									</view>
								</view>
								<!-- 					<view style="width: 50%;height: 60rpx;" @click="openLog(index)">
						<view class="button-right">
							<text>日志</text>
						</view>
					</view> -->
								<view style="width: 50%;height: 60rpx;" @click="putEmployee(index)" v-if="canPut">
									<view class="button-right">
										<text>{{item.state!=1?'上架':'下架'}}</text>
									</view>
								</view>
								<view style="width: 50%;height: 60rpx;" @click="openTab(2,index)" v-else>
									<view class="button-right"
										:style="item.resumeScore>=80?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color: #fff;'">
										<text>{{hasLevel(item.levelId)?'重新提审':'提审'}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</uni-swipe-action-item>
		</uni-swipe-action>

		<u-empty v-if="list.length==0" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="btn-fixed" @click="openCreate">
			添加
			员工
		</view>
	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		name: 'findWorks',
		props: {
			startLoadMore: {
				type: Number
			}
		},
		data() {
			return {
				// 可配置选项
				// 查询时间范围（单位：天）
				dateRange: 7,
				// 查询位置范围（单位：公里）
				districtRange: 4,
				// 页面是否可查看（设为false则登录后才可查看）
				show: true,
				// 是否可以直接上架
				canPut: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				loadMore: 0,
				popupShow: false,
				popupShowLog: false,
				popupShowStore: false,
				popupShowRemark: false,
				popupShowNew: false,
				popupShowOrderBy: false,
				popupShowFilter: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				option: [{
					text: '流转',
					style: {
						backgroundColor: '#19be6b',
					}
				}],

				cityList: uni.getStorageSync('citylist'),
				getClist: uni.getStorageSync('citylist'),
				areaList: [],
				cityId: 0,
				areaId: 0,
				cityName: '',
				searchStreet: '',

				storeName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				workRemark: "",
				searchText: "",
				choiceSearchModeIndex: 0,
				choiceWorTypeIndex: 0,
				choiceServerIndex: 0,
				choiceLevelIndex: 0,
				choiceUpdateTimeIndex: 0,
				choiceCreTimeIndex: 0,
				choiceAgeIndex: 0,
				choiceSalaryIndex: 0,
				choiceMarriedIndex: 0,
				choiceEducationIndex: 0,
				choiceStatusIndex: 0,
				choiceUpdaterIndex: 0,
				choiceSiteFlagIndex: 0,
				choiceOrderByIndex: 0,
				choiceOrderIdIndex: 0,
				choiceWorkYearIndex: 0,
				choiceLanguageIndex: 0,
				choiceQualifiedIndex: 0,
				choiceStateIndex: 0,
				choiceTypeIndex: 0,
				choiceCityIndex: 0,

				choiceWorType: "",
				choiceServer: "",
				choiceMarried: null,
				choiceEducation: null,

				searchParam: "",

				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || 0,
				baomuId: null,
				roleId: uni.getStorageSync("roleId") || 0,
				isRole: false,
				isOutside: false,
				isAppraisaler: false,
				isAllowAuth: false,
				isNationwide: false,
				isHaveOrder: false,
				choiceBaomuIndex: 0,
				choiceIndex: 0,
				choiceIndex1: 0,
				choiceIndexOld: 0,
				education: "",
				total: 0,
				totalNow: 0,
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				headPortrait: "",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				blankHeadPortrait: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankDataImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664504265079blank_data.png",
				authIcon: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/auth_icon.png",
				identityFront: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBack: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				identityFrontOld: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBackOld: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				rankTips: '',
				store: null,
				list: [],
				logList: [],
				choiceList: [{
						choiceTitle: "开发",
						value: 0,
						show: false
					},
					{
						choiceTitle: "门店",
						value: 1,
						show: false
					},
					{
						choiceTitle: "推荐",
						value: 2,
						show: true
					},
					{
						choiceTitle: "附近",
						value: 3,
						show: true
					},
					{
						choiceTitle: "最新",
						value: 4,
						show: true
					},
				],
				choiceListMain: [{
						choiceTitle: "区域",
						value: 0,
						show: true
					}, {
						choiceTitle: "年龄",
						value: 1,
						show: true
					},
					{
						choiceTitle: "工资",
						value: 2,
						show: true
					},
				],
				choiceTagIndex: -1,
				choiceList1: [{
						choiceTitle: "认证优先",
						value: 0,
						show: true
					},
					{
						choiceTitle: "就近优先",
						value: 1,
						show: true
					},
					{
						choiceTitle: "星级优先",
						value: 2,
						show: true
					},
					{
						choiceTitle: "经验优先",
						value: 3,
						show: true
					},
				],
				searchModeList: [{
					text: '按分类',
					value: 0
				}, {
					text: '全局',
					value: 1
				}],
				stateList: [{
						text: '默认',
						value: 0
					},
					{
						text: '已上架',
						value: 1
					},
					{
						text: '未上架',
						value: 2
					},
					{
						text: '已离职',
						value: 3
					},
					{
						text: '已删除',
						value: 4
					}
				],
				stateList1: [{
						text: '不限',
						value: null
					},
					{
						text: '否',
						value: 2
					},
					{
						text: '是',
						value: 1
					},
				],
				statusList: [{
						text: '不限',
						value: null
					}, {
						text: '找工作中',
						value: 0
					},
					{
						text: '已有工作',
						value: 1
					},
					{
						text: '暂不接单',
						value: 2
					}
				],
				timeList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: 0
				}, {
					index: 1,
					text: '',
					showText: "今天",
					value: 1
				}, {
					index: 2,
					text: '',
					showText: "三天内",
					value: 3
				}, {
					index: 3,
					text: '',
					showText: "一周内",
					value: 7
				}, {
					index: 4,
					text: '',
					showText: "一个月内",
					value: 30
				}, {
					index: 5,
					text: '',
					showText: "三个月内",
					value: 90
				}, {
					index: 6,
					text: '',
					showText: "半年内",
					value: 180
				}],
				workTypeList: [{
					text: '',
					showText: "不限",
					value: 0
				}, {
					text: '住家',
					showText: "住家",
					value: 1
				}, {
					text: '不住家',
					showText: "不住家",
					value: 2
				}, {
					text: '单餐',
					showText: "单餐",
					value: 3
				}, {
					text: '育儿嫂',
					showText: "育儿嫂",
					value: 4
				}, {
					text: '月嫂',
					showText: "月嫂",
					value: 5
				}, {
					text: '钟点',
					showText: "钟点",
					value: 6
				}, {
					text: '护工',
					showText: "护工",
					value: 7
				}, {
					text: '陪读师',
					showText: "陪读师",
					value: 8
				}, {
					text: '陪诊师',
					showText: "陪诊师",
					value: 9
				}, {
					text: '管家',
					showText: "管家",
					value: 10
				}, {
					text: '育婴师',
					showText: "育婴师",
					value: 11
				}],
				serverList: [{
						text: "",
						showText: "不限",
						value: 0
					}, {
						text: "带宝宝（带睡)",
						showText: "宝宝带睡",
						value: 1
					},
					{
						text: "带宝宝（不带睡)",
						showText: "宝宝不带睡",
						value: 2
					},
					{
						text: "照顾产妇",
						showText: "照顾产妇",
						value: 3
					},
					{
						text: "看护病人",
						showText: "看护病人",
						value: 4
					},
					{
						text: "看护老人",
						showText: "看护老人",
						value: 5
					},
					{
						text: "做饭",
						showText: "做饭",
						value: 6
					},
					{
						text: "纯做饭",
						showText: "纯做饭",
						value: 7
					},
					{
						text: "做卫生",
						showText: "做卫生",
						value: 8
					},
					{
						text: "纯做卫生",
						showText: "纯做卫生",
						value: 9
					},
					// {
					// 	text: "收纳",
					// 	showText: "收纳整理",
					// 	value: 10
					// },
					// {
					// 	text: "玻璃擦",
					// 	showText: "擦玻璃",
					// 	value: 11
					// },
					// {
					// 	text: "烫",
					// 	showText: "熨烫",
					// 	value: 12
					// },
					// {
					// 	text: "电动车",
					// 	showText: "骑电动车",
					// 	value: 13
					// },
					// {
					// 	text: "开车",
					// 	showText: "开车",
					// 	value: 14
					// }
				],
				// 年龄段
				ageList: [{
					value: 0,
					text: '不限',
					ageLow: null,
					ageHigh: null,
				}, {
					value: 1,
					text: '18-20岁',
					ageLow: 18,
					ageHigh: 20,
				}, {
					value: 2,
					text: '21-30岁',
					ageLow: 21,
					ageHigh: 30,
				}, {
					value: 3,
					text: '31-40岁',
					ageLow: 31,
					ageHigh: 40,
				}, {
					value: 4,
					text: '41-50岁',
					ageLow: 41,
					ageHigh: 50,
				}, {
					value: 5,
					text: '51-60岁',
					ageLow: 51,
					ageHigh: 60,
				}, {
					value: 6,
					text: '60岁以上',
					ageLow: 61,
					ageHigh: 200,
				}],
				// 薪资范围
				salaryList: [{
					value: 0,
					text: '不限',
					salaryLow: null,
					salaryHigh: null,
				}, {
					value: 1,
					text: '1000-3000',
					salaryLow: 1000,
					salaryHigh: 3000,
				}, {
					value: 2,
					text: '3000-6000',
					salaryLow: 3001,
					salaryHigh: 6000,
				}, {
					value: 3,
					text: '6000-8000',
					salaryLow: 6100,
					salaryHigh: 8000,
				}, {
					value: 4,
					text: '8000-10000',
					salaryLow: 8001,
					salaryHigh: 10000,
				}, {
					value: 5,
					text: '10000-15000',
					salaryLow: 10001,
					salaryHigh: 15000,
				}, {
					value: 6,
					text: '15000以上',
					salaryLow: 15001,
					salaryHigh: null,
				}],
				// 婚姻情况
				marriedList: [{
					value: null,
					text: '不限',
					index: 0
				}, {
					value: 0,
					text: '未婚未育',
					index: 1
				}, {
					value: 1,
					text: '已婚未育',
					index: 2
				}, {
					value: 2,
					text: '已婚已育',
					index: 3
				}, {
					value: 3,
					text: '未婚已孕',
					index: 4
				}, {
					value: 4,
					text: '离异',
					index: 5
				}, {
					value: 5,
					text: '其它',
					index: 6
				}],
				languagesList: [{
					value: "",
					text: "不限",
					index: 0
				}, {
					value: "普通话",
					text: "普通话",
					index: 1
				}, {
					value: '闽南话',
					text: '闽南话',
					index: 2
				}, {
					value: '英语',
					text: '英语',
					index: 3
				}, {
					value: '粤语',
					text: '粤语',
					index: 4
				}, {
					value: '客家话',
					text: '客家话',
					index: 5
				}],
				educationList: [{
						value: 0,
						text: '无',
					},
					{
						value: 1,
						text: '默认状态',
					},
					{
						value: 3,
						text: '小学',
					},
					{
						value: 4,
						text: '中学',
					},
					{
						value: 5,
						text: '高中',
					},
					{
						value: 8,
						text: '中专',
					},
					{
						value: 6,
						text: '大专',
					},
					{
						value: 7,
						text: '本科及以上',
					},
					{
						value: 9,
						text: '研究生',
					}
				],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
				siteFlagList: [{
					index: 0,
					value: null,
					showText: "不限",
					show: true
				}, {
					index: 1,
					value: -1,
					showText: "本地",
					show: true
				}, {
					index: 2,
					value: 1,
					showText: "本地",
					show: false
				}, {
					index: 3,
					value: 2,
					showText: "本地",
					show: false
				}, {
					index: 4,
					value: 3,
					showText: "全国",
					show: true
				}, {
					index: 5,
					value: -1,
					showText: "非全国",
					show: false
				}],
				orderIdList: [{
						index: 0,
						value: null,
						showText: "不限",
					}, {
						index: 1,
						value: 0,
						showText: "否",
					},
					{
						index: 2,
						value: 1,
						showText: "是",
					}
				],
				orderByList: [{
						index: 0,
						value: "remindTime DESC,createDate DESC",
						showText: "默认",
					}, {
						index: 1,
						value: "createDate DESC",
						showText: "入驻时间",
					},
					{
						index: 2,
						value: "idtTime DESC",
						showText: "鉴定时间",
					}, {
						index: 3,
						value: "putTime DESC",
						showText: "上架时间",
					}, {
						index: 4,
						value: "siteFlag DESC",
						showText: "接单范围",
					}, {
						index: 5,
						value: "bi.resumeScore DESC,putTime DESC",
						showText: "简历分",
					}
				],
				workYearList: [{
					value: 0,
					text: '不限',
					workYearLow: null,
					workYearHigh: null,
				}, {
					value: 1,
					text: '无经验',
					workYearLow: 0,
					workYearHigh: 0,
				}, {
					value: 2,
					text: '1-3年',
					workYearLow: 1,
					workYearHigh: 2,
				}, {
					value: 3,
					text: '3-5年',
					workYearLow: 3,
					workYearHigh: 5,
				}, {
					value: 4,
					text: '5-10年',
					workYearLow: 6,
					workYearHigh: 10,
				}, {
					value: 5,
					text: '10-20年',
					workYearLow: 11,
					workYearHigh: 20,
				}, {
					value: 6,
					text: '20年以上',
					workYearLow: 21,
					workYearHigh: 200,
				}],
				qualifiedList: [{
						index: 0,
						value: null,
						showText: "不限",
					}, {
						index: 1,
						value: 0,
						showText: "否",
					},
					{
						index: 2,
						value: 1,
						showText: "是",
					}
				],
				// 查询条件
				searchCondition: {
					education: null,
					otherSkills: null,
					agentName: null,
					baomuLevel: null,
					minAge: null,
					maxAge: null,
					storeId: null,
					address: null,
					phone: null,
					showTime: null,
					startTime: null,
					endTime: null,
					source: null,
					time: null,
					hometown: null,
					age: null,
					workYear: null,
					health: null,
					serverContent: null,
					workType: null,
					state: null,
					sites: null,
					id: null,
					no: null,
					realName: "",
					baomuWorkType: null,
					// 开启查阅权限
					introducer: uni.getStorageSync("employeeNo") || "-1",
					merchantCode: uni.getStorageSync('merchantCode') || null,
					privater: null,
					shimingState: null,
					level: null,
					search: "",
					isPush: null,
					lat: null,
					lng: null,
					district: null,
					dateRange: null,
					creTimeRange: null,
					updateTimeRange: null,
					salaryLow: null,
					salaryHigh: null,
					scoreLow: null,
					scoreHigh: null,
					ageLow: null,
					ageHigh: null,
					status: null,
					siteFlag: null,
					orderId: null,
					orderBy: "remindTime DESC,createDate DESC",
					current: 1,
					size: 10
				},
				orderBy: "",
				serverContentList: [{
						text: "带宝宝（带睡)",
						showText: "宝宝带睡",
						isCheck: 0
					},
					{
						text: "带宝宝（不带睡)",
						showText: "宝宝不带睡",
						isCheck: 0
					},
					{
						text: "照顾产妇",
						showText: "照顾产妇",
						isCheck: 0
					},
					{
						text: "看护病人",
						showText: "看护病人",
						isCheck: 0
					},
					{
						text: "看护老人",
						showText: "看护老人",
						isCheck: 0
					},
					{
						text: "做饭",
						showText: "做饭",
						isCheck: 0
					},
					{
						text: "纯做饭",
						showText: "纯做饭",
						isCheck: 0
					},
					{
						text: "做卫生",
						showText: "做卫生",
						isCheck: 0
					},
					{
						text: "纯做卫生",
						showText: "纯做卫生",
						isCheck: 0
					}
				],
				otherSkillsList: [{
						text: "收纳",
						showText: "收纳整理",
						isCheck: 0
					},
					{
						text: "玻璃擦",
						showText: "擦玻璃",
						isCheck: 0
					},
					{
						text: "烫",
						showText: "熨烫",
						isCheck: 0
					},
					{
						text: "电动车",
						showText: "骑电动车",
						isCheck: 0
					},
					{
						text: "开车",
						showText: "开车",
						isCheck: 0
					},
				],
				levelList: [{
					index: 0,
					value: 6,
					text: "不限",
					showText: "不限",
				}, {
					index: 1,
					value: 2,
					text: "三星",
					showText: "三星",
				}, {
					index: 2,
					value: 3,
					text: "四星",
					showText: "四星",
				}, {
					index: 3,
					value: 4,
					text: "五星",
					showText: "五星",
				}, {
					index: 4,
					value: 5,
					text: "六星",
					showText: "六星",
				}],
				employeeNew: {
					realName: '',
					phone: '',
					idCard: '',
					storeId: uni.getStorageSync("storeId") || 2,
					introducer: uni.getStorageSync("employeeNo") || '',
					operator: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || "",
					idCardTime: null,
					birthTime: null,
					hometown: '',
					zodiac: '',
					nation: '',
					sex: '',
				},
				employeeNewOld: {
					realName: '',
					phone: '',
					idCard: '',
					storeId: uni.getStorageSync("storeId") || 2,
					introducer: uni.getStorageSync("employeeNo") || '',
					operator: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || "",
					idCardTime: null,
					birthTime: null,
					hometown: '',
					zodiac: '',
					nation: '',
					sex: '',
				},
				idcard1: [{
					content: "",
					certificate: {
						title: "身份证正面",
						employeeId: null,
						certificateType: 1,
						certificateImg: null,
						validity: null
					}
				}],
				idcard2: [{
					content: "",
					certificate: {
						title: "身份证反面",
						employeeId: null,
						certificateType: 2,
						certificateImg: null,
						validity: null
					}
				}],
				typeList: [{
						value: 0,
						text: '保姆月嫂育儿嫂',
					},
					{
						value: 1,
						text: '保洁搬家维修',
					},
				]
			}
		},
		methods: {
			// find-employee页面注释掉
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 去鉴定
			openWorkSkill(index) {
				let resumeScore = this.list[index].resumeScore
				if (resumeScore < 80) {
					this.$refs.uNotify.error("简历分不符合要求，无法鉴定！")
					return
				}

				uni.navigateTo({
					url: "/pages-other/employee/workSkill?baomuId=" + this.list[index].id
				})
			},
			// 选择菜单（二级）
			choiceTab(index) {
				this.choiceIndex = index
				// 重置筛选模式
				this.choiceSearchModeIndex = 0
				this.refreshList()
			},
			choiceTab1(index) {
				this.popupShowFilter = !this.popupShowFilter
				if (this.popupShowFilter) {
					uni.pageScrollTo({
						selector: '#fiter' + index,
						duration: 300
					})
				}
				this.choiceIndex1 = index
			},
			// 选中排序方式
			choiceOrderBy(index) {
				this.choiceOrderByIndex = index
				this.popupShowOrderBy = false
				this.startFilter()
			},
			change(e) {
				if (e == '') {
					this.selectCity(0)
				}
			},
			search(e) {
				console.log(e)
				let value = e.value
				// value自动接收输入框中的内容
				if (value == '') {
					//如果输入的值为空则加载所有的列表
					uni.showToast({
						icon: 'none',
						title: '无匹配内容'
					})
				} else {
					//先清空展示的数据
					this.cityList = []
					this.areaList = []
					let haveData = false
					//然后开始循环全部数据
					for (var i = 0; i < this.getClist.length; i++) {
						//判断数据里面是否有符合输入的内容  不符合返回-1 只需要大于或等于0就是符合
						//（核心所在，其它都是根据需求来自己写）
						if (this.getClist[i].name.indexOf(value) >= 0) {
							this.cityList.push(this.getClist[i])
							haveData = true
						}
					}
					if (!haveData) {
						this.cityList = uni.getStorageSync('citylist')
						this.selectCity(0)
						uni.showToast({
							icon: 'none',
							title: '无匹配内容'
						})
					}
					this.selectCity(0)
				}
			},
			getCityInfo() {
				uni.request({
					url: 'https://ortherapi.xiaoyujia.com/store/storecity',
					method: 'GET',
					success: (res) => {
						let data = [{
							"cityid": -1,
							"name": "全国",
						}]
						this.cityList = data
						this.cityList = this.cityList.concat(res.data)
						uni.setStorageSync('citylist', this.cityList)
						this.selectCity(0)
					}
				});
			},
			selectCity(i) {
				this.choiceCityIndex = i
				this.cityName = this.cityList[i].name
				this.cityName = this.cityName == '全国' ? '' : this.cityName
				this.areaList = this.cityList[i].area
				let arr = this.cityList[i].area || []
				let arrnew = []
				arr.map(((item, i) => {
					arrnew.push(Object.assign({}, item, {
						disabled: false
					}))
				}))
				this.areaList = arrnew
			},
			selectArea(i) {
				if (i == -1) {
					this.areaId = 0
				} else {
					this.areaId = this.areaList[i].id
					this.cityName = this.areaList[i].name
				}
			},
			// 选择标签
			choiceTag(index) {
				// 重复勾选，则取消
				if (this.choiceTagIndex == index) {
					this.choiceTagIndex = -1
					this.choiceTab(2)
					return
				}

				this.choiceTagIndex = index
				this.searchCondition.isAppraisaler = 1
				this.clearTabChoice()
				if (index == 0) {
					this.searchCondition.isPush = 1
					this.orderBy = "t1.putTime DESC"
				} else if (index == 1) {
					this.searchCondition.district = this.districtRange
					this.searchCondition.lat = uni.getStorageSync("lat")
					this.searchCondition.lng = uni.getStorageSync("lng")
					this.orderBy = "t1.putTime DESC"
				} else if (index == 2) {
					// this.searchCondition.dateRange = this.dateRange
					this.orderBy = "t1.levelId DESC"
				} else if (index == 3) {
					this.searchCondition.isPush = 1
					this.orderBy = "ei.workYear DESC"
				}
				this.cleanSearch()
				this.cleanFilter()
				this.choiceWorType = this.searchParam
				this.startFilter()
			},
			// 选中单选框
			choiceBox(value, index) {
				if (value == -1) {
					this.choiceSearchModeIndex = this.searchModeList[index].value
					// 记录选择全局筛选之前的分类
					if (index == 0) {
						this.choiceIndex = this.choiceIndexOld
						this.choiceTab(this.choiceIndex)
					} else if (index == 1) {
						this.choiceIndexOld = this.choiceIndex
						this.choiceIndex = 5

						// 清空栏目筛选
						this.searchCondition.introducer = null
						this.searchCondition.privater = null
						this.searchCondition.state = null
						this.searchCondition.dateRange = null
						this.searchCondition.isPush = null
						this.searchCondition.district = null
						this.searchCondition.lat = null
						this.searchCondition.lng = null
						this.searchCondition.scoreLow = null
						this.searchCondition.scoreHigh = null
						// 全局搜索-只显示未开单
						// this.isHaveOrder = false
					}
				} else if (value == 0) {
					this.choiceWorTypeIndex = this.workTypeList[index].value
					this.choiceWorType = this.workTypeList[index].text
				} else if (value == 1) {
					this.choiceServerIndex = this.serverList[index].value
					this.choiceServer = this.serverList[index].text
				} else if (value == 2) {
					this.choiceAgeIndex = this.ageList[index].value
				} else if (value == 3) {
					this.choiceMarriedIndex = this.marriedList[index].index
					this.choiceMarried = this.marriedList[index].value
				} else if (value == 4) {
					this.choiceEducationIndex = this.educationList[index].value
					this.choiceEducation = this.educationList[index].value
				} else if (value == 6) {
					this.choiceStatusIndex = index
					this.searchCondition.status = this.statusList[index].value
				} else if (value == 7) {
					this.choiceCreTimeIndex = index
					this.searchCondition.creTimeRange = this.timeList[index].value
				} else if (value == 8) {
					this.choiceLevelIndex = this.levelList[index].index
				} else if (value == 10) {
					this.choiceOrderByIndex = index
					this.searchCondition.orderBy = this.orderByList[index].value
				} else if (value == 11) {
					this.choiceSiteFlagIndex = index
					this.searchCondition.siteFlag = this.siteFlagList[index].value
				} else if (value == 12) {
					this.choiceWorkYearIndex = index
				} else if (value == 13) {
					this.choiceOrderIdIndex = index
					this.searchCondition.orderId = this.orderIdList[index].value
				} else if (value == 14) {
					this.choiceLanguageIndex = index
				} else if (value == 15) {
					this.choiceQualifiedIndex = index
					this.searchCondition.isQualified = this.qualifiedList[index].value
				} else if (value == 16) {
					this.choiceStateIndex = index
					this.searchCondition.state = this.stateList1[index].value
				} else if (value == 17) {
					this.choiceAgeIndex = index
					this.searchCondition.ageLow = this.ageList[index].ageLow
					this.searchCondition.ageHigh = this.ageList[index].ageHigh
				} else if (value == 18) {
					this.choiceSalaryIndex = index
					this.searchCondition.salaryLow = this.salaryList[index].salaryLow
					this.searchCondition.salaryHigh = this.salaryList[index].salaryHigh
				}
			},
			// 刷新列表
			refreshList() {
				let index = this.choiceIndex
				this.clearTabChoice()
				// 默认检索参数
				if (index == 0) {
					// 开发
					this.searchCondition.introducer = uni.getStorageSync("employeeNo") || uni.getStorageSync(
						'merchantCode') || "-1"
					this.searchCondition.state = null
				} else if (index == 1) {
					// 管理
					this.searchCondition.privater = uni.getStorageSync("employeeNo") || uni.getStorageSync(
						'merchantCode') || "-1"
					this.searchCondition.state = null
				} else if (index == 2) {
					// 推荐
					// this.searchCondition.isPush = 1
				} else if (index == 3) {
					// 附近
					this.searchCondition.district = this.districtRange
					this.searchCondition.lat = uni.getStorageSync("lat")
					this.searchCondition.lng = uni.getStorageSync("lng")
				} else if (index == 4) {
					// 最新
					this.searchCondition.dateRange = this.dateRange
				}
				this.cleanSearch()
				this.cleanFilter()
				this.startFilter()
			},
			// 清空筛选条件（二级菜单）
			clearTabChoice() {
				// 清空各个菜单特有的筛选条件
				this.searchCondition.isPush = null
				this.searchCondition.district = null
				this.searchCondition.lat = null
				this.searchCondition.lng = null
				this.searchCondition.dateRange = null
				this.searchCondition.state = 1
			},
			// 清空已经存入的搜索条件
			cleanSearch() {
				this.searchCondition.current = 1
				this.searchCondition.agentName = ""
			},
			// 清空筛选条件（搜索框和单选框）
			cleanFilter() {
				this.searchText = ""
				this.searchStreet = ""
				this.searchPickerMineText = ""
				this.choiceWorTypeIndex = 0
				this.choiceServerIndex = 0
				this.choiceLevelIndex = 0
				this.choiceUpdateTimeIndex = 0
				this.choiceCreTimeIndex = 0
				this.choiceAgeIndex = 0
				this.choiceSalaryIndex = 0
				this.choiceMarriedIndex = 0
				this.choiceEducationIndex = 0
				this.choiceStatusIndex = 0
				this.choiceUpdaterIndex = 0
				this.choiceSiteFlagIndex = 0
				this.choiceOrderIdIndex = 0
				this.choiceOrderByIndex = 0
				this.choiceWorkYearIndex = 0
				this.choiceLanguageIndex = 0
				this.choiceQualifiedIndex = 0
				this.choiceStateIndex = 0

				this.choiceWorType = ""
				this.choiceServer = ""
				this.choiceMarried = null
				this.choiceEducation = null

				this.searchCondition.creTimeRange = null
				this.searchCondition.levelId = null
				this.searchCondition.salaryLow = null
				this.searchCondition.salaryHigh = null
				this.searchCondition.scoreLow = null
				this.searchCondition.scoreHigh = null
				this.searchCondition.ageLow = null
				this.searchCondition.ageHigh = null
				this.searchCondition.address = ""
				this.searchCondition.hometown = ""
				this.searchCondition.no = ""

				this.storeName = ""
				this.searchCondition.storeId = null

				this.isNationwide = false
				this.isHaveOrder = false

				this.cityList = uni.getStorageSync("citylist")
				this.cityName = ''
			},
			// 开始筛选
			startFilter() {
				this.list = []
				this.total = 0
				this.searchCondition.current = 1
				this.searchCondition.search = this.searchText
				this.searchCondition.workType = this.choiceWorType
				this.searchCondition.serverContent = this.choiceServer
				this.searchCondition.creTimeRange = this.timeList[this.choiceCreTimeIndex].value
				this.searchCondition.status = this.statusList[this.choiceStatusIndex].value
				// this.searchCondition.ageLow = this.ageList[this.choiceAgeIndex].ageLow
				// this.searchCondition.ageHigh = this.ageList[this.choiceAgeIndex].ageHigh
				this.searchCondition.levelId = this.levelList[this.choiceLevelIndex].value
				this.searchCondition.siteFlag = this.siteFlagList[this.choiceSiteFlagIndex].value
				// this.searchCondition.orderId = this.orderIdList[this.choiceOrderIdIndex].value
				this.searchCondition.orderBy = this.orderByList[this.choiceOrderByIndex].value
				this.searchCondition.workYearLow = this.workYearList[this.choiceWorkYearIndex].workYearLow
				this.searchCondition.workYearHigh = this.workYearList[this.choiceWorkYearIndex].workYearHigh
				this.searchCondition.workYearHigh = this.workYearList[this.choiceWorkYearIndex].workYearHigh
				this.searchCondition.languages = this.languagesList[this.choiceLanguageIndex].value
				this.searchCondition.isQualified = this.qualifiedList[this.choiceQualifiedIndex].value
				this.searchCondition.state = this.stateList1[this.choiceStateIndex].value

				this.searchCondition.married = this.choiceMarried
				this.searchCondition.education = this.choiceEducation

				if (this.choiceIndex != 0) {
					this.searchCondition.introducer = null
				} else {
					this.searchCondition.shimingState = null
				}

				if (this.choiceIndex != 1) {
					this.searchCondition.privater = null
				} else {
					this.searchCondition.shimingState = null
				}

				if (this.choiceLevelIndex == 0) {
					// 不限保姆等级
					this.searchCondition.levelId = null
				}

				// 排序规则调整（默认排序下才生效）
				if (this.choiceOrderByIndex == 0) {
					if (this.choiceIndex == 0) {
						this.searchCondition.orderBy = "remindTime DESC,createDate DESC"
					} else if (this.choiceIndex == 5) {
						this.searchCondition.orderBy = this.orderBy
					} else {
						this.searchCondition.orderBy = "createDate DESC"
					}
				}

				// 是否全国接单
				if (this.isNationwide) {
					this.searchCondition.siteFlag = 3
				} else {
					this.searchCondition.siteFlag = null
				}

				// 除了开发/管理/全局栏目，其余都只显示未开单
				if (this.choiceIndex != 0 && this.choiceIndex != 1 && this.choiceIndex != 5) {
					// this.isHaveOrder = false
					this.searchCondition.state = 1
				}

				// 是否开单
				if (this.isHaveOrder) {
					this.searchCondition.orderId = 1
				} else {
					this.searchCondition.orderId = 0
					if (this.choiceIndex == 0 || this.choiceIndex == 1 || this.choiceIndex == 5) {
						this.searchCondition.orderId = null
					}
				}

				// 启用工作类型搜索词
				if (this.choiceWorTypeIndex == 0 && this.searchParam != '') {
					this.choiceWorType = this.searchParam
					this.searchCondition.workType = this.searchParam
				}


				if (this.cityName) {
					this.searchCondition.hometown = this.cityName
				}
				this.getList()
				this.popupShow = false
				this.popupShowFilter = false
			},
			// 打开头像预览
			openImgPreview(index) {
				this.headPortrait = this.list[index].headPortrait != null ? this.list[index].headPortrait : this
					.blankHeadPortrait
				// this.$refs.popupHeadPortrait.open()

				let data = []
				data.push(this.headPortrait)
				uni.previewImage({
					urls: data,
					current: this.headPortrait
				})
			},
			// 打开详情
			openTab(index, index1) {
				this.choiceBaomuIndex = index1
				// 打开简历
				if (index == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.list[index1].baomuId
					})
				}
				// 打开员工详情
				else if (index == 1) {
					this.addBaomuWorkLog(0, index1)
					uni.navigateTo({
						url: "/pages-mine/works/employee-detail?baomuId=" + this.list[index1].baomuId
					})
				}
				// 打开技能提审
				else if (index == 2) {
					let resumeScore = this.list[index1].resumeScore
					if (resumeScore >= 80) {
						if (!this.isAllowAuth) {
							this.$refs.uNotify.error("暂时无法提审！努力提升自己的经纪人积分吧！")
							return
						}

						if (this.hasLevel(this.list[index1].levelId)) {
							this.openCheck(1, "该员工已通过提审", "确定要重新进行提审吗？")
						} else {
							uni.navigateTo({
								url: "/pages-other/employee/workSkill?baomuId=" + this.list[index1].id
							})
						}
					} else {
						this.openCheck(0, "请先帮助员工完善简历吧！", "简历达到80分以上才可进行提审哦")
					}
				}
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.list[index].id
				this.http({
					url: 'getBaomuWorkLog',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.logList = res.data
						} else {
							this.logList = []
						}
					}
				})
			},
			// 打开门店信息
			openStore(index) {
				if (!this.isRole) {
					return
				}
				let id = this.list[index].storeId
				uni.navigateTo({
					url: "/pages-other/store/index?id=" + id
				})
				// this.store = null
				// this.popupShowStore = true

				// this.http({
				// 	url: 'getStoreById',
				// 	method: 'GET',
				// 	path: id,
				// 	success: res => {
				// 		if (res.code == 0) {
				// 			this.store = res.data
				// 		} else {
				// 			this.store = null
				// 		}
				// 	}
				// })
			},
			// 打开备注
			openRemark(index) {
				this.choiceBaomuIndex = index
				this.workRemark = this.list[index].workRemark || ""
				this.popupShowRemark = true
			},
			// 打开合同日志
			openContractLog(id) {
				if (!id) {
					return
				}
				uni.navigateTo({
					url: "/pages-mine/contract/contract-history?id=" + id + '&isAdmin=1'
				})
			},
			// 打开自助添加员工
			openCreate() {
				this.popupShow = false
				this.popupShowNew = true
			},
			putEmployee(index) {
				let id = this.list[index].id
				let state = this.list[index].state
				state = state == 1 ? 2 : 1
				let data = {
					id: id,
					state: state,
					operatorName: uni.getStorageSync("employeeName") || ""
				}
				this.http({
					url: 'updateBaomuState',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: data,
					success: res => {
						if (res.code == 0) {
							this.list[index].state = state
							this.$refs.uNotify.success("操作成功！")
						}
					}
				});
			},
			uploadImg(value) {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						// 将图片上传至后台
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'idCard'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.idcard1[0].certificate.certificateImg = result.data
								} else if (value == 1) {
									this.idcard2[0].certificate.certificateImg = result.data
								}
							}
						})
						// #ifdef  H5
						pathToBase64(tempFilePaths)
							.then(base64 => {
								if (value == 0) {
									this.identityFront = tempFilePaths[0]
									this.idcard1[0].content = base64
									this.vidcardNext()
								} else if (value == 1) {
									this.identityBack = tempFilePaths[0]
									this.idcard2[0].content = base64
									this.vidcard1Next()
								}
							})
							.catch(error => {
								console.error("转化失败！")
							})
						// #endif
						// #ifdef  MP-WEIXIN
						const base64 = this.urlTobase64(tempFilePaths[0])
						if (value == 0) {
							this.identityFront = tempFilePaths[0]
							this.idcard1[0].content = base64
							this.vidcardNext()
						} else if (value == 1) {
							this.identityBack = tempFilePaths[0]
							this.idcard2[0].content = base64
							this.vidcard1Next()
						}
						// #endif
					}
				});
			},
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			// 识别身份证正面
			vidcardNext() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				console.log("读取图片后进行请求！")
				// 请求：识别身份证
				let data = {
					file: this.idcard1[0].content,
					cardTpe: 0
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证正面上传成功！")
							this.employeeNew.realName = res.data.words_result.姓名.words
							this.employeeNew.idCard = res.data.words_result.公民身份号码.words
							this.employeeNew.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeNew.hometown = res.data.words_result.住址.words
							this.employeeNew.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.employeeNew.nation = res.data.words_result.民族.words
							this.employeeNew.sex = this.getSex(res.data.words_result.性别.words)
						} else if (status === 'unknown' && res.data.idcard_number_type === 0) {
							this.employeeNew.realName = res.data.words_result.姓名.words
							this.employeeNew.idCard = res.data.words_result.公民身份号码.words
							this.employeeNew.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeNew.hometown = res.data.words_result.住址.words
							this.employeeNew.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.employeeNew.nation = res.data.words_result.民族.words
							this.employeeNew.sex = this.getSex(res.data.words_result.性别.words)
							this.$refs.uNotify.error("因第三方接口不识别，请自行输入身份证号码！")
						} else {
							let msg = '身份证认证失败，请重新上传';
							if (status != null) {
								if (status === 'reversed_side') {
									msg = '身份证应上传照片面'
								}
								if (status === 'non_idcard') {
									msg = '上传的图片中不包含身份证'
								}
								if (status === 'blurred') {
									msg = '身份证模糊'
								}
								if (status === 'other_type_card') {
									msg = '其他类型证照'
								}
								if (status === 'over_exposure') {
									msg = '身份证关键字段反光或过曝'
								}
								if (status === 'over_dark') {
									msg = '身份证欠曝（亮度过低）'
								}
							}
							this.idcard1[0].content = ""
							this.$refs.uNotify.error(msg)
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			// 识别身份证背面
			vidcard1Next() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				// 请求：识别身份证
				let data = {
					file: this.idcard2[0].content,
					cardTpe: 1
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证背面上传成功！")
							this.employeeNew.idCardTime = this.getMyTime(res.data.words_result.失效日期.words)
						} else {
							this.idcard2[0].content = ""
							this.$refs.uNotify.error("身份证背面认证失败，请检查有效期限后请重新上传")
						}
					}
				})
			},
			// 更新证件图片
			addCertificate(value, employeeId) {
				let idCardTime = this.employeeNew.idCardTime
				if (employeeId == 0) {
					return
				}

				this.$set(this.idcard1[0].certificate, 'employeeId', employeeId)
				this.$set(this.idcard2[0].certificate, 'employeeId', employeeId)
				this.idcard1[0].certificate.validity = this.getMyTime2(idCardTime)
				this.idcard2[0].certificate.validity = this.getMyTime2(idCardTime)
				let data = value == 0 ? this.idcard1[0].certificate : this.idcard2[0].certificate
				if (!data.certificateImg) {
					return
				}
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.idcard1[0].certificate.certificateImg = ""
							} else {
								this.idcard2[0].certificate.certificateImg = ""
							}
						}
					}
				})
			},
			getshengxiao(yyyy) {
				var arr = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
				return /^\d{4}$/.test(yyyy) ? arr[yyyy % 12] : null
			},
			getMyTime(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8)
				if (time.includes("长期")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime2(time) {
				if (!time) {
					return ''
				}
				if (time.includes("长期")) {
					return "3000/01/01"
				} else {
					let dateTime = time.substring(0, 4) + "/" + time.substring(5, 7) + "/" + time.substring(8, 10)
					return dateTime
				}
			},
			getSex(sex) {
				let result = 2
				if (sex == "男") {
					result = 1
				} else if (sex == "女") {
					result = 2
				}
				return result
			},
			authentication(index) {
				let data = this.list[index]
				uni.showModal({
					title: '提示',
					content: '确认执行一键认证操作吗？',
					success: function(res) {
						if (res.confirm) {
							this.http({
								url: "employeeSecurityAuth",
								header: {
									'content-type': "application/json;charset=UTF-8"
								},
								method: 'POST',
								data: {
									employeeId: data.id,
									storeId: uni.getStorageSync("storeId"),
									crePerson: uni.getStorageSync("employeeName")
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('认证成功！')
										this.list[index].securityAuth = 1
									} else {
										this.$refs.uNotify.error('已认证但未通过！请查看认证报告！')
										let timer = setTimeout(() => {
											this.getRzReport(index)
										}, 1200)
									}
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}.bind(this)
				});
			},
			// 查看认证报告
			getRzReport(index) {
				let val = this.list[index]
				uni.navigateTo({
					url: "/pages-work/operations/clew/securityAuth?id=" + val.id
				})
			},
			// 员工流转
			employeeToStaff() {
				let id = this.list[this.choiceBaomuIndex].id
				this.http({
					url: 'employeeToStaff',
					method: 'POST',
					data: {
						id: id,
						realName: uni.getStorageSync("employeeName") || ""
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceBaomuIndex)
							this.$refs.uNotify.success("员工流转成功！请至【员工】栏目查看！")
						} else {
							this.$refs.uNotify.error('流转失败，信息未填写完整，请尽量完善简历后再进行流转！' + res.msg)
						}
					}
				});
			},
			saveRemark() {
				let id = this.list[this.choiceBaomuIndex].id
				let workRemark = this.workRemark
				if (workRemark == "" || workRemark == null) {
					this.$refs.uNotify.error("备注不能为空，请重新输入！")
					return
				}

				this.http({
					url: "updateBaomu",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: id,
						workRemark: workRemark,
					},
					success: res => {
						if (res.code == 0) {
							this.list[this.choiceBaomuIndex].workRemark = workRemark
							this.popupShowRemark = false
							this.addBaomuWorkLog(1, this.choiceBaomuIndex)
							this.$refs.uNotify.success("工作备注修改成功！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			addBaomuWorkLog(value, index) {
				console.log("插入查看详情日志！")
				let id = this.list[index].baomuId
				let workRemark = this.list[index].workRemark
				let name = uni.getStorageSync("employeeName") || uni.getStorageSync("memberName")
				let data = {}
				if (value == 0) {
					data = {
						employeeId: id,
						title: "资料查看",
						workContent: "员工详情资料被查看",
						crePerson: name,
						type: 1
					}
				} else if (value == 1) {
					data = {
						employeeId: id,
						title: "修改备注",
						workContent: "修改备注为：" + workRemark,
						crePerson: name,
						type: 6
					}
				}

				this.http({
					url: "addBaomuWorkLog",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {

						}
					}
				})
			},
			// 格式化字符
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			checkSalary(str) {
				if (str == null || str == "" || str == 0) {
					return "暂无"
				} else {
					return str + "元"
				}
			},
			// 校验头像是否合格
			checkHeadPortrait(index) {
				let remark = this.list[index].remark || ""
				if (remark.includes("头像不合格") && (this.choiceIndex == 0 || this.choiceIndex == 1)) {
					return true
				} else {
					return false
				}
			},
			// 校验提醒时间
			checkRemindTime(index) {
				let remindTime = this.list[index].remindTime
				let resumeScore = this.list[index].resumeScore
				if (this.choiceIndex == 0 && remindTime != null && resumeScore < 80) {
					return true
				} else {
					return false
				}
			},
			remindTips() {
				this.openCheck(-1, "简历完善提醒", "若提醒后48小时内没有完善简历到80分，将自动解绑该员工！")
			},
			unqualifiedTips() {
				this.openCheck(-1, "头像不合格", "请尽快帮助员工重新上传规范头像，否则无法进行提审！")
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			formatLongStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			// 对请求参数中的数值进行格式化
			formatRange() {
				let scoreLow = this.searchCondition.scoreLow
				let scoreHigh = this.searchCondition.scoreHigh
				this.searchCondition.scoreLow = scoreLow == null || scoreLow == "" ? null : parseInt(scoreLow)
				this.searchCondition.scoreHigh = scoreHigh == null || scoreHigh == "" ? null : parseInt(scoreHigh)

				let salaryLow = this.searchCondition.salaryLow
				let salaryHigh = this.searchCondition.salaryHigh
				this.searchCondition.salaryLow = salaryLow == null || salaryLow == "" ? null : parseInt(salaryLow)
				this.searchCondition.salaryHigh = salaryHigh == null || salaryHigh == "" ? null : parseInt(salaryHigh)
			},
			formatRealName(index) {
				let name = this.list[index].realName
				let sex = this.list[index].sex
				let workType = this.list[index].workType
				// 非外部，显示名字
				if (this.isRole) {
					return name
				}
				let xin = name.substring(0, 1)
				let data = ['', '师傅', '阿姨', '', '']
				let call = data[sex]
				if (call == '') {
					call = '阿姨'
				}

				if (workType != null && workType.includes('护工')) {
					call = '护工'
				}

				return xin + call
			},
			formatStore(sname) {
				sname = sname || '小羽佳家政'
				if (sname.includes('(')) {
					sname = sname.replace("小羽佳家政", "").replace("(", "").replace(")", "").replace("·", "")
				}
				return sname
			},
			formatWorkType(index) {
				let workType = this.list[index].workType
				let workTypeList = ['住家', '不住家', '单餐', '育儿嫂', '月嫂', '护工', '陪读师', '陪诊师', '管家', '钟点晚餐', '钟点保洁']
				let type = ''
				// 格式化工种
				for (let item of workTypeList) {
					if (workType.includes(item)) {
						type = '（' + item + '）'
						break
					}
				}
				// 优先显示搜索词
				if (workType.includes(this.searchText) && this.searchText != '') {
					type = '（' + this.searchText + '）'
				}

				if (workType.includes(this.choiceWorType) && this.choiceWorType != '') {
					type = '（' + this.choiceWorType + '）'
				}
				return type
			},
			// 格式化学历信息
			formatEducation(education) {
				for (let item of this.educationList) {
					if (item.value == education) {
						return item.text
					}
				}
				return ""
			},
			// 格式化服务内容到标签
			formatServerContent(serverContent) {
				let resultList = []
				if (serverContent !== null) {
					for (let item of this.serverContentList) {
						if (serverContent.includes(item.text)) {
							resultList.push(item.showText)
						}
					}
				}
				return resultList
			},
			// 格式化服务内容到标签
			formatOtherSkills(otherSkills) {
				let resultList = []
				if (otherSkills !== null) {
					for (let item of this.otherSkillsList) {
						if (otherSkills.includes(item.text)) {
							resultList.push(item.showText)
						}
					}
				}
				return resultList
			},
			formatStatus(index) {
				let item = this.list[index]
				let str = item.contractId != null ? "已开单" : "未开单"
				let str1 = ""
				let statusList = ['-正在找工作', '-已有工作', '-暂不找工作']
				let statusIndex = item.status != null ? parseInt(item.status) : 0
				str1 = statusList[statusIndex]
				return str + str1
			},
			// 侧滑选项改变
			clickOptionChange(index) {
				this.choiceBaomuIndex = index
			},
			// 点击选项
			clickOption(e) {
				let choiceOption = e.index
				if (choiceOption == 0) {
					this.$refs.uNotify.error("流转后在鉴定列表将不可见！")
					this.openCheck(8, "流转员工", "流转的员工必须是保洁/搬家/清洗等标准单工种，流转后可在【员工】列表查看，确定流转该员工吗？")
				}
			},
			// 判断是否已经提审
			hasLevel(level) {
				if (level != null) {
					if (level != 0) {
						return true
					}
				} else {
					return false
				}
			},
			// 格式化保姆等级
			formatLevel(level) {
				let result = ""
				if (level != null) {
					if (level > 1 && level < 6) {
						result = this.levelList[level - 1].text
					} else {
						result = "暂无"
					}
				}
				return result
			},
			formatSiteFlag(index) {
				let item = this.list[index]
				let result = ''
				result = this.siteFlagList[(item.siteFlag || 0) + 1].showText
				if (result == '本地' && item.address) {
					result = this.formatAddress(item.address)
				}
				return result
			},
			// 格式化地址
			formatAddress(str, value) {
				let result = str
				if (str == undefined || str == null || str == "") {
					result = "暂无"
				} else {
					let addrReg = /(.{9})(.*)/; // 地址正则
					if (addrReg.test(str)) {
						let text1 = RegExp.$1
						let text2 = RegExp.$2.replace(/./g, "")
						result = text1 + text2
					}
				}
				return result
			},
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.popupShow = false
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.searchCondition.storeId = this.storeList[index].id
					this.storeName = this.storeList[index].storeName
				}
				this.showPickerMine = false
				this.popupShow = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 获取鉴定排名
			getBaomuAuthRank() {
				this.http({
					url: 'getBaomuAuthRank',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						authEmployeeId: this.employeeId
					},
					success: res => {
						if (res.code == 0) {
							this.rankTips = '今日上架' + res.data.authCount +
								'人，全国店长排名第' + res.data.rank
						} else {
							this.rankTips = '今日还未上架员工，继续努力吧！'
						}
					}
				});
			},
			// 校验员工是否符合提审条件
			checkAuthPower() {
				this.http({
					url: 'checkAuthPower',
					path: uni.getStorageSync("employeeId") || 0,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.isAllowAuth = true
							}
						}
						this.getBaomuAuthRank()
					}
				});
			},
			// 校验员工
			checkEmployeeByIdCard() {
				if (!this.employeeNew.idCard) {
					this.createNewEmployee()
				} else {
					this.http({
						url: 'checkEmployeeByIdCard',
						method: 'POST',
						data: {
							idcard: this.employeeNew.idCard
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.popupShowNew = false
								// this.openCheck(2, '该身份证已录入过系统', '确定继续添加新员工吗？')
								this.$refs.uNotify.warning("该身份证已录入过系统！无法重复添加员工！")
							} else {
								this.createNewEmployee()
							}
						},
					})
				}
			},
			// 创建新员工
			createNewEmployee() {
				const phoneReg = /^1[3456789]\d{9}$/
				const idCardReg =
					/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
				if (this.employeeNew.realName.length == 0) {
					this.$refs.uNotify.error("请输入员工姓名！")
				} else if (!phoneReg.test(this.employeeNew.phone)) {
					this.$refs.uNotify.error("请输入正确手机号！")
				} else if (!idCardReg.test(this.employeeNew.idCard)) {
					this.$refs.uNotify.error("请输入正确的身份证号！")
				} else {
					let idCardTime = this.employeeNew.idCardTime
					if ((idCardTime || '').includes("长期")) {
						this.employeeNew.idCardTime = "3000-01-01" + " 00:00:00.000"
					} else if (idCardTime && idCardTime.length < 12) {
						this.employeeNew.idCardTime = idCardTime + " 00:00:00.000"
					}

					this.http({
						url: 'createNewEmployee',
						data: this.employeeNew,
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.$refs.uNotify.success("添加成功！")
								let employeeId = res.data || 0
								this.addCertificate(0, employeeId)
								this.addCertificate(1, employeeId)
								let timer = setTimeout(() => {
									this.employeeNew = this.employeeNewOld
									this.identityFront = this.identityFrontOld
									this.identityBack = this.identityBackOld
								}, 600)
								// 流转
								if (this.choiceTypeIndex == 1) {
									let id = res.data || null
									this.http({
										url: 'employeeToStaff',
										method: 'POST',
										hideLoading: true,
										data: {
											id: id,
											realName: "邀请入驻->流转"
										},
										header: {
											'content-type': 'application/json;charset=UTF-8'
										},
										success: res => {}
									});
								}
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					});
				}
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					url: 'getAllStoreList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
						}
					}
				});
			},
			getList() {
				this.formatRange()
				let data = this.searchCondition
				// 陪跑店只能看自己门店保姆
				let storeId = uni.getStorageSync("storeId") || null
				if (storeId) {
					if (this.roleId == 110 || this.roleId == 112) {
						this.$set(data, "storeId", storeId)
					} else {
						this.$set(data, "hideRunStore", 1)
					}
				}

				this.http({
					url: 'getBaomuPage',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						let title = ''
						if (res.code == 0) {
							this.total = res.data.total
							this.list = this.list.concat(res.data.records)
							title = "找员工" + "（" + this.total + "）"
						} else {
							title = "找员工"
						}

						if (this.isOutside) {
							if (this.searchParam != '') {
								title = this.searchParam
							} else {
								title = '找员工'
							}
						}
						uni.setNavigationBarTitle({
							title: title
						})
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			popupCheck() {
				let index = this.choiceBaomuIndex
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.list[index].id
					})
				} else if (this.checkType == 1) {
					uni.navigateTo({
						url: "/pages-other/employee/workSkill?baomuId=" + this.list[index].id
					})
				} else if (this.checkType == 2) {
					this.createNewEmployee()
				} else if (this.checkType == 8) {
					this.employeeToStaff()
				}
			}
		},
		watch: {
			// employee页面开启
			loadMore: {
				handler(newValue, oldVal) {
					this.searchCondition.current++
					this.getList()
				},
				deep: true
			},
			searchText: {
				handler(newValue, oldVal) {
					console.log("搜索框变更！")
					if (this.choiceWorTypeIndex == 0) {
						this.choiceWorType = ''
						this.searchParam = ''
						uni.setNavigationBarTitle({
							title: '找员工'
						})
					}
				},
				deep: true
			},
			choiceWorTypeIndex: {
				handler(newValue, oldVal) {
					if (this.isOutside) {
						this.searchParam = ''
						uni.setNavigationBarTitle({
							title: '找员工'
						})
					}
				},
				deep: true
			},
			// find-employee页面开启
			// startLoadMore: {
			// 	handler(newValue, oldVal) {
			// 		console.log("滑动到底部加载更多...")
			// 		this.searchCondition.current++
			// 		this.getList()
			// 	},
			// 	deep: true
			// }
		},
		onReachBottom() {
			this.loadMore++
		},
		onLoad(options) {
			this.memberId = uni.getStorageSync('memberId') || 0
			// 登录后才可查看
			if (this.memberId != 0) {
				this.show = true
			}
			// 校验员工权限（鉴定师可进行快捷鉴定）
			let roleId = this.roleId
			// 权限校验
			if (roleId == 1 || roleId == 42 || roleId == 66 || roleId == 77 || roleId == 79 || roleId == 95 || roleId ==
				106 || roleId == 85 ||
				roleId == 108 ||
				roleId == 110 ||
				roleId == 112) {
				this.isRole = true
				if (roleId == 106) {
					this.isAppraisaler = true
				}
				if (roleId == 110 || roleId == 112) {
					this.canPut = true
				}
				this.checkAuthPower()
			}

			// 外部人员校验（开启敏感信息过滤）
			this.isOutside = options.isOutside || false
			if (this.isOutside) {
				this.isRole = false
			}

			this.searchParam = options.search || ""
			if (this.searchParam != "") {
				this.choiceIndex = 5
				this.searchCondition.isPush = 1
				this.searchCondition.isAppraisaler = 1
				this.searchCondition.shimingState = 1
				this.orderBy = 't1.createDate DESC'
				this.choiceWorType = this.searchParam
				uni.setNavigationBarTitle({
					title: this.searchParam
				})
				// this.startFilter()
				this.choiceTag(0)
			} else {
				if (this.isOutside) {
					this.choiceTag(0)
				} else {
					this.choiceTab(0)
				}
			}
			this.getStoreList()
			this.getCityInfo()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";

	page {
		height: auto;
		background-color: #ffffff;
		width: 100%;
	}

	.head-review {
		img {
			display: block;
			width: 600rpx;
			height: auto;
		}
	}

	/deep/ .uni-popup__wrapper {
		padding: 0;
	}

	// 保姆列表栏目
	.baomu-tab {
		width: 100%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 40rpx 0;
		display: flex;
		flex-direction: row;
	}

	.tab-left {
		width: 25%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	// 栏目左边
	.tab-left-img {
		img:nth-child(1) {
			display: block;
			width: 170rpx;
			height: 240rpx;
			border-radius: 15rpx;
			margin: 20rpx 0 0 12%;
		}

		img:nth-child(2) {
			display: block;
			width: 170rpx;
			height: 60rpx;
			margin: -60rpx auto 0rpx 22rpx;
		}
	}

	.tab-left-btn {
		display: flex;
		flex-direction: column;
		padding: 20rpx 10rpx;
		text-align: center;
		margin: 0 15%;
		width: 70%;
	}


	// 栏目右边
	.tab-right {
		float: right;
		display: flex;
		flex-direction: column;
		width: 69%;
		height: auto;
		padding: 0 3%;
	}

	.tab-name {
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		font-weight: bold;
		line-height: 80rpx;
	}

	.tab-info {
		text {
			font-size: 36rpx;
			line-height: 60rpx;
			padding: 0 5rpx;
		}
	}

	.tab-text {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}

	.tab-text1 {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}



	.skills-content {
		width: 100%;
		height: auto;
	}

	.my-tag {
		float: left;
		padding: 0;
		width: auto;
		height: auto;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 10rpx 0rpx;
		padding: 0 10rpx;
	}

	.button-left,
	.button-right {
		width: 70%;
		height: 60rpx;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		color: #f6cc70;
		background-color: #1e1848;

		text {
			height: 60rpx;
			line-height: 60rpx;
			font-size: 32rpx;
		}
	}

	.log-list {
		width: 90%;
		height: auto;
		padding: 20rpx 5%;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.small-tag {
		display: flex;
		width: 120rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #fff;
		background-color: #f6cc70;
		border-radius: 10rpx;
		margin: 10rpx 0;

		img {
			display: block;
			width: 30rpx;
			height: 30rpx;
			margin: 7rpx 0 0 10rpx;
		}

		text {
			display: block;
			width: 60%;
			text-align: center;
		}
	}

	.my-tag-choice {
		width: 140rpx;
		height: 60rpx;
		margin: 0 10rpx;
		line-height: 60rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		padding: 0 10rpx;

		text {}
	}

	.activeBox1 {
		color: #1e1848;
		background-color: #fff;
		border: #1e1848 3rpx solid;
	}

	.filter-popup {
		position: absolute;
		z-index: 888;
		width: 100%;
		top: 245rpx;
		background-color: #fff;
		border-radius: 0 0 40rpx 40rpx;
		border-bottom: 2rpx solid #dedede;
		// box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 按钮组
	.btn-group {
		width: 100%;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		padding: 40rpx 0;

		button {
			width: 40%;
			height: 80rpx;
			line-height: 80rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 弹出-选择栏
	.popup-picker {
		position: absolute;
		top: 9.5vh;
		right: 22rpx;
		z-index: 999;
		width: 180rpx;
		height: auto;
		padding: 0 0 0 0;

		.picker-triangle {
			width: 0;
			height: 0;
			border: 15rpx solid transparent;
			border-bottom-color: #f4f4f5;
			margin-left: 65%;
		}

		.picker-tab {
			width: auto;
			mine-height: 60rpx;
			line-height: 60rpx;
			color: #1e1848;
			background-color: rgba(255, 255, 255, 0.98);
			border-radius: 15rpx;
			box-shadow: 0 4rpx 20rpx #dedede;
			padding: 0 20rpx;
		}
	}

	.choice-menu1 {
		display: flex;
		width: 60%;
		height: 40rpx;
		margin: 20rpx 0;
		padding-left: 50rpx;

		text {
			font-size: 32rpx;
		}
	}

	.btnStyle {
		color: #6989df;
		background-color: #fff;
	}

	.lineStyle {
		width: 10rpx;
		height: 50rpx;
		margin-left: 5rpx;
		border-radius: 10rpx;
		background-color: #6989df;
	}

	.btn-fixed {
		font-size: 26rpx;
		text-align: center;
		position: fixed;
		right: 30rpx;
		bottom: 18%;
		color: #fdd472;
		line-height: 30rpx;
		background-color: #1f2120;
		border-radius: 50%;
		width: 80rpx;
		height: 60rpx;
		padding: 20rpx 10rpx;
	}
</style>