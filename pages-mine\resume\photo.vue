<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<view>
			<uni-popup ref="popupCheck" type="dialog">
				<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle"
					:content="checkText" @confirm="popupCheck()"></uni-popup-dialog>
			</uni-popup>
		</view>

		<!-- 上传提示标题 -->
		<view class="head">
			<text @click="openImgPreview(imgUpload)">{{uploadTips}}</text>
		</view>

		<!-- 上传图片 -->
		<view class="upload-img" v-if="!isMultiple">
			<img :src="imgUpload" alt="" @click="uploadImg" @longpress="openImgPreview(imgUpload)">
		</view>

		<!-- 上传图片列表 -->
		<!-- 		<view class="upload-img-list" v-if="isMultiple">
			<view v-for="(item,index) in certList" :key="index" class="upload-img-item">
				<img :src="item.certificateImg" @click="openImgPreview(item.certificateImg)">
				<view class="upload-img-delete">
					<uni-icons type="clear" size="24" color="#000000" @click="deleteImg(index)"></uni-icons>
				</view>
			</view>

			<view class="upload-img-item">
				<img :src="imgUpload" alt="" @click="uploadImg">
			</view>
		</view> -->

		<shmily-drag-image v-model="certList" keyName="certificateImg" v-if="isMultiple" class="w9 mg-at"
			:addImage="uploadImg" :delImage="delImage" @delDragImg="delDragImg"></shmily-drag-image>

		<view style="font-size: 30rpx;margin: 20rpx 60rpx;line-height: 40rpx;padding-bottom: 200rpx;">
			<text>*点击可预览照片</text>
		</view>

		<!-- 照片名称输入 -->
		<view class="upload-tab" v-if="certificateIndex==12">
			<text>照片名称</text>
			<text v-if="certificateName==''" style="color: #909399;">点击填写</text>
			<input class="upload-input" type="text" v-model="certificateName"></input>
		</view>


		<!-- <view style="background-color: #000;">
		<view class="upload-tips">
			<text>照片仅用于向客户展示，小羽佳保障您的信息安全</text>
		</view>
		<view class="btn-bottom">
			<button @click="trySave()">确 认</button>
		</view>
		</view> -->
		<view class="fixedB">
			<view class="textB">照片仅用于向客户展示，小羽佳保障您的信息安全</view>
			<button class="btnS" @click="trySave()">确 认</button>
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				// 是否开启多图上传
				isMultiple: true,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				certificateIndex: 0,
				certificateTitle: "",
				certificateType: 25,
				isUpload: false,
				certificateNum: "",
				certificateName: "",
				certificateImg: "",
				show: false,
				show1: false,
				nowDate: Number(new Date()),
				certificateDate: Number(new Date()),
				certificateDateEnd: Number(new Date()),
				memberId: null,
				baomuId: null,
				employeeId: null,
				changeNum: 0,

				choiceImgIndex: 0,
				certList: [],
				certUploadList: [],
				certificate: {
					title: "工作照片",
					employeeId: this.baomuId,
					certificateType: 25,
					certificateImg: null
				},
				// 照片照片
				certificate: {
					title: null,
					employeeId: this.baomuId,
					certificateImg: null,
					certificateType: null,
				},
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				imgUploadUrl: "",
				type: 0,
				uploadTips: '请上传您在客户家的工作照、菜肴等',
				certTypeList: [{
					uploadTips: '请上传您在客户家的工作照、菜肴等',
					title: '工作照片',
					barTitle: '我的照片（工作照）',
					type: 25
				}, {
					uploadTips: '请拍摄/上传您的个人生活照',
					title: '生活照片',
					barTitle: '我的照片（生活照）',
					type: 98
				}],
			}
		},
		methods: {
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			// 上传图片
			uploadImg() {
				const url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					count: 9,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						tempFilePaths.forEach(item => {
							uni.uploadFile({
								url: url,
								filePath: item,
								name: 'file',
								formData: {
									route: 'userPhotos',
									watermark: 'ture'
								},
								dataType: 'json',
								success: res => {
									let result = JSON.parse(res.data)
									this.imgUploadUrl = result.data
									// 多选时，将选项添加到图片列表
									if (this.isMultiple) {
										let data = {
											id: 0,
											employeeId: this.baomuId,
											title: this.certificateTitle,
											certificateType: this.certificateType,
											certificateImg: this.imgUploadUrl
										}
										this.certList.push(data)
									} else {
										this.imgUpload = this.imgUploadUrl
									}
								},
								fail: res => {
									this.$refs.uNotify.error("上传失败！" + res.msg)
								}
							});
						})
					}
				});
			},
			checkDate() {
				let result = false
				// 这里写上上传校验方法


				result = true
				return result
			},
			// 尝试保存
			trySave() {
				this.openCheck(0, "确定保存我的照片吗？", "越真实越好哦～")
			},
			save() {
				if (this.checkDate()) {
					this.checkBaomuAndInit()
					if (this.baomuId == null) {
						let timer = setTimeout(() => {
							if (this.baomuId !== null) {
								this.addCertificate()
							}
						}, 1500);
					} else {
						this.addCertificate()
					}
				}
			},
			delDragImg(index) {
				this.choiceImgIndex = index
			},
			// 删除个人照片
			delImage(done) {
				uni.showModal({
					content: '确定删除该照片吗，删除后不可恢复！?',
					success: res => {
						if (res.confirm) {
							this.delete(done)
						}
					}
				})
			},
			// 删除照片-旧方法
			delete(done) {
				let id = this.certList[this.choiceImgIndex].id
				if (id == 0) {
					done()
					this.$refs.uNotify.success("个人照片删除成功！")
					return
				}

				this.http({
					url: 'deleteCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: id
					},
					success: res => {
						if (res.code == 0) {
							// this.$delete(this.certList, this.choiceImgIndex)
							this.$refs.uNotify.success("个人照片删除成功！")
							done()
						} else {
							this.$refs.uNotify.error("个人照片删除失败！" + res.msg)
						}
					}
				})
			},
			// 删除图片
			deleteImg(index) {
				this.choiceImgIndex = index
				this.openCheck(1, "确定删除该照片吗？", "删除后不可恢复！")
			},
			// 确认日期选择
			confirmState() {
				this.show = false
				this.show1 = false
			},
			// 更新个人照片
			addCertificate() {
				if (this.isMultiple) {
					this.addCertificateList()
					return
				}

				this.$set(this.certificate, 'employeeId', this.baomuId)
				this.certificate.certificateType = this.certificateType
				this.certificate.certificateImg = this.imgUploadUrl
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.certificate,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("个人照片更新成功！")
							this.isUpload = true
							let timer = setTimeout(() => {
								return uni.navigateBack()
							}, 1500);
						} else {
							this.$refs.uNotify.error("图片上传失败！" + res.msg)
						}
					}
				})
			},
			// 添加个人照片列表
			addCertificateList() {
				// 更新个人照片列表的有效期
				for (let item of this.certList) {
					if (item.id != 0) {
						console.log("同步有效期！" + item.validity)
					}
				}

				this.http({
					url: 'addCertificateList',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.certList,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("个人照片更新成功！")
							this.isUpload = true
							let timer = setTimeout(() => {
								return uni.navigateBack()
							}, 1500);
						} else {
							this.$refs.uNotify.error("图片上传失败！" + res.msg)
						}
					}
				})
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				// 请求：通过会员ID获取保姆关联信息
				if (this.baomuId === null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync("baomuId", this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log("初始化的保姆ID为" + this.baomuId)
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 获取个人照片列表
			getCertList() {
				if (!this.isMultiple) {
					return
				}

				this.http({
					url: 'getCertificateByCertType',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId,
						certificateType: this.certificateType
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.certList = res.data
						} else {

						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：保存照片
				if (this.checkType == 0) {
					this.save()
				} else if (this.checkType == 1) {
					this.delete()
				}
			},

		},
		// 获取上个页面回传的数据
		onLoad(options) {
			this.baomuId = options.baomuId || null
			this.memberId = uni.getStorageSync('memberId') || 0
			let type = options.type || 0
			type = parseInt(type)
			this.type = type
			this.uploadTips = this.certTypeList[type].uploadTips
			this.certificateType = this.certTypeList[type].type
			uni.setNavigationBarTitle({
				title: this.certTypeList[type].barTitle
			})

			// 如果在上一个页面没有获取参数，则从缓存中取值
			if (this.baomuId == undefined || this.baomuId == null) {
				this.baomuId = uni.getStorageSync('baomuId')
			}
			console.log("用户ID：" + this.memberId + "保姆ID：" + this.baomuId)

			this.getCertList()
		},
		mounted() {

		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #ffffff;
	}

	.head {
		width: 100%;
	}


	.head text {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 40rpx;
		font-weight: bold;
		padding-left: 40rpx;
	}

	.tab-tips {
		width: 100%;
		line-height: 80rpx;
		height: 80rpx;
		font-size: 32rpx;
		color: #ffffff;
		background-color: #1e1848;

		text {
			margin: 0 40rpx;
		}
	}

	.upload-img {
		width: 100%;
		height: 300rpx;
		padding-left: 40rpx;
		margin-bottom: 60rpx;

		img {
			display: block;
			width: 300rpx;
			height: 300rpx;
		}
	}

	// 图片上传列表
	.upload-img-list {
		display: flex;
		flex-wrap: wrap;
		width: 92%;
		height: auto;
		padding: 20rpx 4%;
	}

	.upload-img-delete {
		position: absolute;
		margin: -216rpx 0 0 166rpx;
	}

	.upload-img-item {
		display: inline-block;
		width: 33%;

		img {
			display: block;
			margin: 20rpx auto;
			width: 200rpx;
			height: 200rpx;
		}
	}

	// 上传选择栏目
	.upload-tab {
		height: 120rpx;
		width: 100%;
		border-bottom: #f4f4f5 2px solid;
		background-color: #ffffff;

		text {
			display: block;
			line-height: 120rpx;
			height: 100%;
			font-size: 36rpx;
		}

		text:first-child {
			float: left;
			width: 300rpx;
			padding-left: 40rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			float: right;
			padding-right: 40rpx;
		}
	}

	// 照片号码输入框
	.upload-input {
		position: absolute;
		display: block;
		width: 60%;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 20rpx;
		font-size: 36rpx;
		text-align: right;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
		right: 20rpx;
	}

	.upload-tips {
		position: fixed;
		// bottom: 0;
		// position: absolute;
		height: 100rpx;
		width: 100%;
		bottom: 180rpx;

		text {
			display: block;
			font-size: 32rpx;
			color: #909399;
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
			z-index: 999;
		}
	}

	// 保存按钮
	.btn-bottom {
		button {
			position: absolute;
			bottom: 0rpx;
			left: calc(10%);
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
			z-index: 999;
		}
	}

	.fixedB {
		width: 100%;
		padding: 20rpx 0;
		position: fixed;
		bottom: 0;
		right: 0;
		background-color: #f4f3f2;
		z-index: 99999;
	}

	.textB {
		color: #999;
		text-align: center;
		line-height: 60rpx;
	}

	.btnS {
		width: 60%;
		margin: 20rpx auto;
		line-height: 80rpx;
		color: #f6cc70;
		background-color: #1e1848;
		border-radius: 50rpx;
		font-size: 36rpx;
	}
</style>