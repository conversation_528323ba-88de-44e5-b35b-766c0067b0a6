<template>
	<view class="w10 page">
		<view class="bacf">
			<u-collapse accordion :value="1">
				<!-- 基本信息 -->
				<u-collapse-item name="1" :title="item.serverMonth+'月'" v-for="(item,index) in historyLogList" :key="index">
					<u-icon name="play-right-fill" size="18" slot="icon"></u-icon>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">状态:</view>
							<u-button v-if="item.state==0" shape="circle" text="待审核" type="warning" size="large"
								customStyle="width:170rpx;height:60rpx;margin:auto 0;margin-left: 20rpx;"></u-button>
								<u-button v-if="item.state==1" shape="circle" text="已审核" type="success" size="large"
									customStyle="width:170rpx;height:60rpx;margin:auto 0;margin-left: 20rpx;"></u-button>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">交付人:</view>
							<view class="" style="margin: auto 30rpx;">{{item.realName}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">服务员工:</view>
							<view class="" style="margin: auto 30rpx;">{{item.serverEmployee+'('+item.serverEmployeeNo+')'}}
							</view>
						</view>
					</view>
					
					<view class="f16 w9 mg-at lh32" v-if="item.renewalBillNo">
						<view class="" style="display: flex;">
							<view class="c0">续签单号:</view>
							<view class="" style="margin: auto 30rpx;">
								{{item.renewalBillNo}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" v-if="item.repurchaseBillNo">
						<view class="" style="display: flex;">
							<view class="c0">复购单号:</view>
							<view class="" style="margin: auto 30rpx;">
								{{item.repurchaseBillNo}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">提交时间:</view>
							<view class="" style="margin: auto 30rpx;">
								{{item.submitTime}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="item.operateTime">
						<view class="" style="display: flex;">
							<view class="c0">评价得分:</view>
							<u-rate active-color="#ffd200" inactive-color="#999999" size="25"  :count="item.starRating"
											    v-model="item.starRating"></u-rate>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;">
						<view class="" style="display: flex;">
							<view class="c0">订单金额:</view>
							<view class="" style="margin: auto 30rpx;">
								￥{{item.orderAmount}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="item.operateTime">
						<view class="" style="display: flex;">
							<view class="c0">收入金额:</view>
							<view class="" style="margin: auto 30rpx;">
								￥{{item.settlementMoney}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="item.operateTime">
						<view class="" style="display: flex;">
							<view class="c0">评价时间:</view>
							<view class="" style="margin: auto 30rpx;">{{item.operateTime}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" style="display: flex;" v-if="item.operateTime">
						<view class="" style="display: flex;">
							<view class="c0">家务管家评价:</view>
							<view class="" style="margin: auto 30rpx;">
								{{item.remark}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32" >
						<view class="" style="display: flex;">
							<view class="c0">沟通内容:</view>
							<view class="" style="margin: auto 30rpx;">
								{{item.communicate}}
							</view>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">伴手礼:</view>
							<u-upload :maxCount="item.fileList.length" :deletable="false" :fileList="item.fileList"  name="4"
								multiple>
							</u-upload>
						</view>
					</view>
					<view class="f16 w9 mg-at lh32">
						<view class="" style="display: flex;">
							<view class="c0">家务清单表:</view>
							<u-upload :maxCount="item.fileList2.length" :deletable="false" :fileList="item.fileList2"  name="4"
								multiple>
							</u-upload>
						</view>
					</view>
					
					
				</u-collapse-item>
			</u-collapse>
		</view>
			
	</view>
</template>

<script>
	import hTimeAlert from '@/pages-work/components/h-time-alert/h-time-alert.vue';
	export default {
		components: {
			hTimeAlert
		},
		data() {
			return {
				moreOperate: false,
				showTime: false,
				storeType: uni.getStorageSync('storeType'),
				text: '',
				insurancePayUrl: '',
				showRemark: false,
				dikoMoney: 0.00,
				showModal: false,
				memberAmount: 0.00,
				changeAmount: 0,
				couponList: [],
				billRemark: '',
				isSplit: null,
				current: 0,
				memberId: null,
				yuezfFlag: false,
				yfMoney: 0.00,
				amount: 0.00,
				billNo: '',
				showModal1: false,
				orderData: {},
				groupBillNo: '',
				showModal2: false,
				baseInfo: {},
				contractInfo: {},
				splitInfo: {},
				splitCon: [],
				historyLogList: [],
				operationLog: [],
				imgCode: '',
				remarkDom: {
					type: null,
					addr: null,
					con: null,
					yq: null,
					xz: null,
					phone: uni.getStorageSync('account') + ' ' + uni.getStorageSync('employeeName'),
				},
				commentData: null,
				tsLog: null,
				timeParam: {},
				blankHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				imglist: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-bzyw.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-ygsj.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/img-xmtyy.png',
				]
			};
		},
		onLoad(option) {
			this.contractId = option.id
			this.getHistoryLog()
		},
		methods: {
			handelClose(data) {
				if (!data) {
					return this.showTime = false;
				}
				this.timeParam.startTime = data.date + " 00:00:00";
				this.timeParam.startTimeSpan = data.start;
				this.timeParam.endTimeSpan = data.end;
				this.timeParam.billNo = this.orderData.billNo;
				uni.showModal({
					title: '提示',
					content: '是否改时到' + data.all,
					success: res => {
						if (res.confirm) {
							this.updateTime();
						} else if (res.cancel) {
							this.showTime = false;
						}
					}
				});
			},
			
			// 打开弹窗进行编辑
			edit() {
				this.showModal = true
				this.moreOperate = false
			},
			// 弹窗确认按钮--点击确认后输入框内容赋值给item.other
			editConfirm1() {
				this.http({
					url: 'updateAgentRemarkByBillNo',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						billNo: this.billNo,
						agentRemark: this.billRemark
					},
					success: res => {
						uni.showToast({
							title: '更新成功'
						})
						this.agentRemark = '';
						this.showModal = false;

					}
				})

			},
			getHistoryLog(){
				this.http({
					// outsideUrl: 'http://localhost:15012/getHistoryLog',
					url: 'getHistoryLog',
					method: 'get',
					data: {
						contractId: this.contractId
					},
					success: res => {
						if (res.code == 0) {
							this.historyLogList = res.data
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
							})
						}
					}
				})
			},
			getMemberAmount() {
				this.http({
					url: 'getMemberAmount',
					method: 'get',
					data: {
						id: this.memberId
					},
					success: res => {
						if (res.code == 0) {
							this.memberAmount = res.data.amount
						} else {
							uni.showToast({
								title: '获取用户余额失败!',
								icon: 'none',
							})
						}
					}
				})
			},
			getCouponByProduct() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/order/getCouponByProduct?memberId=' + this.orderData
						.id +
						'&productId=' + this.orderData.productId + '&storeId=' + this.orderData.storeId,
					method: 'get',
					success: res => {
						if (res.code == 0) {
							this.couponList = res.data
							if (res.data.length > 0) {
								this.dikoMoney = res.data[0].value
							}
							for (var i = 0; i < this.couponList.length; i++) {
								this.couponList[i].value = i.toString()
							}
						} else {
							uni.showToast({
								title: '获取优惠券错误!',
								icon: 'none',
							})
						}
					}
				})
			},
			saveImg() {
				uni.downloadFile({ //下载文件资源到本地,返回文件的本地临时路径
					url: this.insurancePayUrl, //网络图片路径
					success: (res) => {
						var imageUrl = res.tempFilePath; //临时文件路径
						uni.saveImageToPhotosAlbum({ //保存图片到系统相册
							filePath: imageUrl,
							success: (res) => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								})
							}
						})
					}
				})
			},
			goInsurance(type) {
				this.http({
					url: 'orderBuRePayOtherPay',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: {
						billNo: this.billNo,
						type: type
					},
					success: res => {
						if (res.code === 0) {
							this.insurancePayUrl = res.data;
							this.showInsuranceModal = true;
						} else {
							return uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			callPhone(val) {
				uni.makePhoneCall({
					phoneNumber: val, //电话号码
					success: function(e) {},
					fail: function(e) {}
				});
			},
			valChange(e) {
				console.log('当前值为: ' + e.value)
			},
			showFadada() {
				this.http({
					url: 'viewContractPdfUrl',
					method: 'GET',
					data: {
						contractId: this.contractInfo.no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let info = res.data;
							uni.showModal({
								content: info, //模板中提示的内容
								confirmText: '复制链接',
								success: (res) => { //点击复制内容的后调函数
									if (res.confirm) {
										let result
										// #ifndef H5
										//uni.setClipboardData方法就是讲内容复制到粘贴板
										uni.setClipboardData({
											data: info, //要被复制的内容
											success: () => { //复制成功的回调函数
												uni.showToast({ //提示
													title: '复制成功'
												})
											}
										});
										// #endif

										// #ifdef H5
										let textarea = document.createElement("textarea")
										textarea.value = info
										textarea.readOnly = "readOnly"
										document.body.appendChild(textarea)
										textarea.select() // 选中文本内容
										textarea.setSelectionRange(0, info.length)
										uni.showToast({ //提示
											title: '复制成功'
										})
										result = document.execCommand("copy")
										textarea.remove()
										// #endif
									} else {
										console.log('取消')
									}
								}
							});
						} else {
							uni.showToast({
								title: res.msg
							})
						}

					}

				})
			},
			// 弹窗确认按钮--改价
			confirm1() {
				if (!this.changeAmount) {
					return uni.showToast({
						title: '请输入金额',
						icon: 'none'
					})
				}
				let orderDto = {
					billNo: this.billNo,
					realTotalAmount: this.changeAmount,
					oldRealTotalAmount: this.baseInfo.realTotalAmount,
					operator: uni.getStorageSync('employeeName'),
					operatorId: uni.getStorageSync("employeeId"),
				};
				this.http({
					url: 'changeRealTotalAmount',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: orderDto,
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '修改成功'
								})
							}, 1500)
							this.getorderInfo();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}

					}
				})
				this.showModal1 = false
			},
			splitOrder() {
				this.moreOperate = false
				if (this.baseInfo.totalSplitNum == null || ((this.baseInfo.totalSplitNum - this.baseInfo.splitNum) < 1)) {
					return uni.showToast({
						title: '没有拆单次数',
						icon: 'none'
					})
				}
				if (this.baseInfo.realTotalAmount - this.baseInfo.amount > 0) {
					return uni.showToast({
						title: '该订单还未付款',
						icon: 'none'
					})
				}
				let splitOrder = {
					billNo: this.billNo
				};
				this.http({
					url: 'splitOrder',
					method: 'POST',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: splitOrder,
					success: res => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '拆单成功'
								})
							}, 1500)
							this.getorderInfo();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}

					}
				})
			},
			// 打开图片浏览
			openImgPreview(index) {
				let imgs = this.commentData.serviceAcceptanceFormImgList.map(item => {
					return item.imgUrl
				})
				uni.previewImage({
					urls: imgs,
					current: index
				})
			},
			copy(info) {

				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif

				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
		},
	}
</script>

<style lang="scss">
	.page {
		min-height: 100vh;
		background-color: #f4f2f3 !important;
	}

	.main {
		display: flex;
		justify-content: center;
		align-items: center;
		border-top: 2rpx solid #eee;
		border-bottom: 2rpx solid #eee;
	}

	.part1-title {
		display: flex;
		align-items: center;
		// margin: 10rpx auto;
	}

	.splitCon {
		background-color: #f4f3f2;
		text-indent: 0.5rem;
		border-bottom: 2rpx solid #fff;
	}

	.logCon {
		background: #f4f3f2;
		text-indent: 2rem;
		border-bottom: 2rpx solid #fff;
		padding: 20rpx 0;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
	}

	/deep/ .u-button {
		border-radius: 0 !important;
	}

	.iconStyle {
		background-color: #f3efeb;
		color: #8f755d;
		padding: 0 10rpx;
		border-radius: 4rpx;
	}

	.textStyle {
		margin: 20rpx auto;
	}

	.imgBox {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.imgStyle {
		width: 30%;
		margin: 10rpx;
		border-radius: 10rpx;
	}
</style>